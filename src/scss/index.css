.toplevel_page_wp-react-form #wpcontent {
  padding-left: 0px;
}

.wp-react-form.wprf-tabs-wrapper {
  display: flex;
  flex-direction: column;
  margin: 20px 0px;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper {
  margin-bottom: 5px;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper .wprf-tab-nav {
  margin: 0;
  padding: 0;
  display: flex;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper .wprf-tab-nav .wprf-tab-nav-item {
  padding: 15px 25px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 0;
  background-color: #f8fafb;
  color: #516378;
  border-top: 2px solid #f8fafb;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper .wprf-tab-nav .wprf-tab-nav-item.wprf-active-nav {
  border-color: #6648fe;
  background-color: #FFF;
  color: #516378;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper.wprf-tab-menu-sidebar {
  flex-basis: 15%;
  background-color: #f8fafb;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper.wprf-tab-menu-sidebar .wprf-tab-nav {
  display: block;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper.wprf-tab-menu-sidebar .wprf-tab-nav .wprf-tab-nav-item.wprf-active-nav {
  border-top-width: 0px;
  border-right: 2px solid #6648fe;
}
.wp-react-form.wprf-tabs-wrapper.wprf-tab-menu-as-sidebar {
  border: 1px solid #f2f2f2;
  flex-direction: row;
  background-color: #FFF;
  padding: 0px;
}
.wp-react-form.wprf-tabs-wrapper.wprf-tab-menu-as-sidebar .wprf-tab-menu-wrapper {
  margin-bottom: 0;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-content-wrapper {
  flex: 1;
  flex-basis: 85%;
  background: #FFF;
  padding: 30px;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-content-wrapper .wprf-tab-content {
  display: none;
}
.wp-react-form.wprf-tabs-wrapper .wprf-tab-content-wrapper .wprf-tab-content.wprf-active {
  display: block;
}

.wprf-control-section:not(:last-child) {
  margin-bottom: 15px;
}

.wprf-control-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.wprf-control-wrapper:not(:last-child) {
  margin-bottom: 20px;
}
.wprf-control-wrapper.hidden {
  display: none;
}
.wprf-control-wrapper .wprf-control-label {
  flex-basis: 20%;
  font-weight: 700;
}
.wprf-control-wrapper .wprf-control-field {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  flex-basis: 80%;
}
.wprf-control-wrapper .wprf-control-field .wprf-description {
  margin-left: 10px;
  font-size: 12px;
  margin-bottom: 0;
  margin-top: 0;
}
.wprf-control-wrapper .wprf-control-field > .wprf-radio-card + .wprf-description {
  display: block;
  flex-basis: 100%;
  margin-left: 0;
  margin-top: 20px;
}
.wprf-control-wrapper .wprf-control-field .wprf-help {
  margin: 0;
  margin-top: 5px;
  display: block;
  flex-basis: 100%;
  font-size: 12px;
}
.wprf-control-wrapper.wprf-label-none .wprf-control-field {
  flex-basis: 100%;
}
.wprf-control-wrapper.wprf-top-label {
  display: block;
}
.wprf-control-wrapper.wprf-top-label div.wprf-control-field {
  display: block;
}
.wprf-control-wrapper .wprf-badge-wrapper {
  display: flex;
  width: 100%;
}

.wprf-control {
  margin-bottom: 20px;
  width: 100%;
}
.wprf-control:last-of-type {
  margin-bottom: 0px;
}
.wprf-control .wprf-control-label {
  margin-top: 0;
  margin-bottom: 0;
}
.wprf-control .wprf-input-field {
  box-shadow: none;
  border: 1px solid #f2f2f2;
  transition: border linear 0.2s, box-shadow linear 0.2s;
  padding: 8px;
  line-height: 1;
  font-size: 14px;
  margin: 0px;
}
.wprf-control .wprf-input-field.wprf-large {
  width: 100%;
}
.wprf-control .wprf-input-field:focus {
  box-shadow: none;
  outline: none;
}
.wprf-control .clearfix::after, .wprf-control .clearfix::before {
  display: table;
  content: "";
}
.wprf-control .clearfix::after {
  clear: both;
}
.wprf-control .wprf-input-label {
  display: block;
  margin-bottom: 10px;
}
.wprf-control .wprf-input-label .wprf-label-image {
  max-width: 100%;
}
.wprf-control .wprf-input-label .wprf-badge {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
}
.wprf-control .wprf-input-label .wprf-badge .wprf-badge-item {
  font-size: 14px;
  color: #ffffff;
  text-transform: uppercase;
  text-align: center;
  transform: rotate(-45deg);
  position: absolute;
  top: 8px;
  left: -20px;
  width: 80px;
  background-image: linear-gradient(-122deg, rgb(146, 113, 255) 0%, rgb(87, 37, 255) 100%);
  box-shadow: 0px 3px 7px 0px rgba(0, 5, 41, 0.1);
  color: #fff;
  height: 30px;
  line-height: 30px;
  left: -29px;
  width: 100px;
  z-index: 9;
}
.wprf-control .wprf-input-label .wprf-badge .wprf-badge-item.wprf-badge-active {
  background-color: #0ccf9f;
  background-image: none;
}
.wprf-control .wprf-row {
  margin-left: -10px;
  margin-right: -10px;
}
.wprf-control .wprf-row .wprf-column {
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: border-box;
  width: 20%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-1 {
  width: 8.3333%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-2 {
  width: 16.6667%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-3 {
  width: 25%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-4 {
  width: 33.3333%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-5 {
  width: 41.6667%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-6 {
  width: 50%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-7 {
  width: 58.3333%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-8 {
  width: 66.6667%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-9 {
  width: 75%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-10 {
  width: 83.3333%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-11 {
  width: 91.6667%;
}
.wprf-control .wprf-row .wprf-column.wprf-column-12 {
  width: 100%;
}
.wprf-control .wprf-flex {
  display: flex;
  flex-wrap: wrap;
}
.wprf-control .wprf-error-message {
  color: red;
}
.wprf-control.wprf-submit {
  margin-top: 30px;
}
.wprf-control.wprf-submit button.wprf-submit-button {
  padding: 10px 20px;
  border: none;
}

.wprf-control-section {
  margin-bottom: 20px;
  border: 1px solid #f7f7f7;
  padding-bottom: 20px;
  position: relative;
}
.wprf-control-section > div.wprf-section-title h4 {
  display: block;
  color: #516378;
  margin-bottom: 20px;
  text-transform: uppercase;
  padding-bottom: 0;
  background: #f8fafb;
  padding: 14px;
  letter-spacing: 2px;
  font-weight: 700;
  margin-top: 0px;
}
.wprf-control-section > div.wprf-section-fields {
  padding-left: 20px;
  padding-right: 20px;
}

button.wprf-btn {
  padding: 10px 20px;
  border: 0px;
  cursor: pointer;
}

.wprf-control .wprf-input-field.wprf-input-text {
  padding: 10px;
}

.wprf-input-radio-set-wrap.wprf-radio-card input {
  display: none;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option {
  min-height: 80px;
  position: relative;
  box-shadow: 0 0 25px 0 rgba(0, 9, 78, 0.08);
  margin-top: 20px;
  line-height: 60px;
  display: flex;
  align-items: center;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected:after, .wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected:before {
  content: "";
  position: absolute;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected:after {
  height: 6px;
  width: 3px;
  right: -3px;
  top: -6px;
  display: inline-block;
  transform: rotate(45deg);
  border-bottom: 3.5px solid #fff;
  border-right: 3.5px solid #fff;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected:before {
  right: -10px;
  top: -10px;
  left: auto;
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  transition: all 0.3s ease 0s;
  background: #09dca8;
  border-radius: 50%;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected .wprf-input-label {
  background-color: #0ccf9f;
  color: #fff;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected .wprf-input-label.wprf-label-has-image {
  background-color: white;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option input.wprf-input-field {
  display: none;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option .wprf-input-label {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease 0s;
  background: #fff;
  font-size: 14px;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 0;
  box-sizing: border-box;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option .wprf-input-label.wprf-label-has-image {
  padding: 0;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option .wprf-input-label.wprf-label-has-image.wprf-size-medium {
  padding: 10px;
  box-sizing: border-box;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option .wprf-input-label.wprf-label-has-image.wprf-size-medium > img {
  width: 50px;
  height: 50px;
}

.wprf-control-section .wprf-section-title {
  display: flex;
  align-items: center;
}
.wprf-control-section .wprf-section-title > h4 {
  flex: 1;
}
.wprf-control-section .wprf-section-fields {
  height: auto;
  transition: all 0.3s ease 0s;
}
.wprf-control-section.wprf-section-collapsed .wprf-section-fields {
  height: 0;
  overflow: hidden;
}

/**
 * Colors
 */
.components-visually-hidden {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  word-wrap: normal !important;
}

/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
/**
 * These are default block editor widths in case the theme doesn't provide them.
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
.wprf-control-datetime {
  position: relative;
}
.wprf-control-datetime .wprf-control-datetime-content {
  z-index: 1;
}
.wprf-control-datetime .components-button.is-tertiary {
  background-color: #f2f2f2;
  border: none;
  color: #6648fe;
  padding: 10px;
  cursor: pointer;
}
.wprf-control-datetime .components-button.is-tertiary:focus {
  outline: none;
  border: none;
}
.wprf-control-datetime .components-datetime {
  /*rtl:end:ignore*/
}
.wprf-control-datetime .components-datetime .PresetDateRangePicker_panel {
  padding: 0 22px 11px;
}
.wprf-control-datetime .components-datetime .PresetDateRangePicker_button {
  position: relative;
  height: 100%;
  text-align: center;
  background: 0 0;
  border: 2px solid #00a699;
  color: #00a699;
  padding: 4px 12px;
  margin-right: 8px;
  font: inherit;
  font-weight: 700;
  line-height: normal;
  overflow: visible;
  box-sizing: border-box;
  cursor: pointer;
}
.wprf-control-datetime .components-datetime .PresetDateRangePicker_button:active {
  outline: 0;
}
.wprf-control-datetime .components-datetime .PresetDateRangePicker_button__selected {
  color: #fff;
  background: #00a699;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput {
  display: inline-block;
  background-color: #fff;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput__withBorder {
  border-radius: 2px;
  border: 1px solid #dbdbdb;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput__rtl {
  direction: rtl;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput__disabled {
  background-color: #f2f2f2;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput__block {
  display: block;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput__showClearDate {
  padding-right: 30px;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput_clearDate {
  background: 0 0;
  border: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  cursor: pointer;
  padding: 10px;
  margin: 0 10px 0 5px;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput_clearDate__default:focus,
.wprf-control-datetime .components-datetime .SingleDatePickerInput_clearDate__default:hover {
  background: #dbdbdb;
  border-radius: 50%;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput_clearDate__small {
  padding: 6px;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput_clearDate__hide {
  visibility: hidden;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput_clearDate_svg {
  fill: #82888a;
  height: 12px;
  width: 15px;
  vertical-align: middle;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput_clearDate_svg__small {
  height: 9px;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput_calendarIcon {
  background: 0 0;
  border: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  padding: 10px;
  margin: 0 5px 0 10px;
}
.wprf-control-datetime .components-datetime .SingleDatePickerInput_calendarIcon_svg {
  fill: #82888a;
  height: 15px;
  width: 14px;
  vertical-align: middle;
}
.wprf-control-datetime .components-datetime .SingleDatePicker {
  position: relative;
  display: inline-block;
}
.wprf-control-datetime .components-datetime .SingleDatePicker__block {
  display: block;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_picker {
  z-index: 1;
  background-color: #fff;
  position: absolute;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_picker__rtl {
  direction: rtl;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_picker__directionLeft {
  left: 0;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_picker__directionRight {
  right: 0;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_picker__portal {
  background-color: rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_picker__fullScreenPortal {
  background-color: #fff;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_closeButton {
  background: 0 0;
  border: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
  padding: 15px;
  z-index: 2;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_closeButton:focus,
.wprf-control-datetime .components-datetime .SingleDatePicker_closeButton:hover {
  color: rgb(175.7572815534, 178.7475728155, 180.2427184466);
  text-decoration: none;
}
.wprf-control-datetime .components-datetime .SingleDatePicker_closeButton_svg {
  height: 15px;
  width: 15px;
  fill: #cacccd;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_buttonReset {
  background: 0 0;
  border: 0;
  border-radius: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  padding: 0;
  cursor: pointer;
  font-size: 14px;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_buttonReset:active {
  outline: 0;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_show {
  width: 22px;
  position: absolute;
  z-index: 2;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_show__bottomRight {
  border-top: 26px solid transparent;
  border-right: 33px solid #00a699;
  bottom: 0;
  right: 0;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_show__bottomRight:hover {
  border-right: 33px solid #008489;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_show__topRight {
  border-bottom: 26px solid transparent;
  border-right: 33px solid #00a699;
  top: 0;
  right: 0;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_show__topRight:hover {
  border-right: 33px solid #008489;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_show__topLeft {
  border-bottom: 26px solid transparent;
  border-left: 33px solid #00a699;
  top: 0;
  left: 0;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_show__topLeft:hover {
  border-left: 33px solid #008489;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_showSpan {
  color: #fff;
  position: absolute;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_showSpan__bottomRight {
  bottom: 0;
  right: -28px;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_showSpan__topRight {
  top: 1px;
  right: -28px;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_showSpan__topLeft {
  top: 1px;
  left: -28px;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_panel {
  overflow: auto;
  background: #fff;
  border: 1px solid #dbdbdb;
  border-radius: 2px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 2;
  padding: 22px;
  margin: 33px;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_title {
  font-size: 16px;
  font-weight: 700;
  margin: 0;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_list {
  list-style: none;
  padding: 0;
  font-size: 14px;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_close {
  position: absolute;
  right: 22px;
  top: 22px;
  z-index: 2;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_close:active {
  outline: 0;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_closeSvg {
  height: 15px;
  width: 15px;
  fill: #cacccd;
}
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_closeSvg:focus,
.wprf-control-datetime .components-datetime .DayPickerKeyboardShortcuts_closeSvg:hover {
  fill: #82888a;
}
.wprf-control-datetime .components-datetime .CalendarDay {
  box-sizing: border-box;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
}
.wprf-control-datetime .components-datetime .CalendarDay:active {
  outline: 0;
}
.wprf-control-datetime .components-datetime .CalendarDay__defaultCursor {
  cursor: default;
}
.wprf-control-datetime .components-datetime .CalendarDay__default {
  border: 1px solid #e4e7e7;
  color: #484848;
  background: #fff;
}
.wprf-control-datetime .components-datetime .CalendarDay__default:hover {
  background: #e4e7e7;
  border: 1px double #e4e7e7;
  color: inherit;
}
.wprf-control-datetime .components-datetime .CalendarDay__hovered_offset {
  background: #f4f5f5;
  border: 1px double #e4e7e7;
  color: inherit;
}
.wprf-control-datetime .components-datetime .CalendarDay__outside {
  border: 0;
  background: #fff;
  color: #484848;
}
.wprf-control-datetime .components-datetime .CalendarDay__outside:hover {
  border: 0;
}
.wprf-control-datetime .components-datetime .CalendarDay__blocked_minimum_nights {
  background: #fff;
  border: 1px solid #eceeee;
  color: #cacccd;
}
.wprf-control-datetime .components-datetime .CalendarDay__blocked_minimum_nights:active,
.wprf-control-datetime .components-datetime .CalendarDay__blocked_minimum_nights:hover {
  background: #fff;
  color: #cacccd;
}
.wprf-control-datetime .components-datetime .CalendarDay__highlighted_calendar {
  background: #ffe8bc;
  color: #484848;
}
.wprf-control-datetime .components-datetime .CalendarDay__highlighted_calendar:active,
.wprf-control-datetime .components-datetime .CalendarDay__highlighted_calendar:hover {
  background: #ffce71;
  color: #484848;
}
.wprf-control-datetime .components-datetime .CalendarDay__selected_span {
  background: #66e2da;
  border: 1px solid #33dacd;
  color: #fff;
}
.wprf-control-datetime .components-datetime .CalendarDay__selected_span:active,
.wprf-control-datetime .components-datetime .CalendarDay__selected_span:hover {
  background: #33dacd;
  border: 1px solid #33dacd;
  color: #fff;
}
.wprf-control-datetime .components-datetime .CalendarDay__last_in_range {
  border-right: #00a699;
}
.wprf-control-datetime .components-datetime .CalendarDay__selected,
.wprf-control-datetime .components-datetime .CalendarDay__selected:active,
.wprf-control-datetime .components-datetime .CalendarDay__selected:hover {
  background: #00a699;
  border: 1px solid #00a699;
  color: #fff;
}
.wprf-control-datetime .components-datetime .CalendarDay__hovered_span,
.wprf-control-datetime .components-datetime .CalendarDay__hovered_span:hover {
  background: #b2f1ec;
  border: 1px solid #80e8e0;
  color: #007a87;
}
.wprf-control-datetime .components-datetime .CalendarDay__hovered_span:active {
  background: #80e8e0;
  border: 1px solid #80e8e0;
  color: #007a87;
}
.wprf-control-datetime .components-datetime .CalendarDay__blocked_calendar,
.wprf-control-datetime .components-datetime .CalendarDay__blocked_calendar:active,
.wprf-control-datetime .components-datetime .CalendarDay__blocked_calendar:hover {
  background: #cacccd;
  border: 1px solid #cacccd;
  color: #82888a;
}
.wprf-control-datetime .components-datetime .CalendarDay__blocked_out_of_range,
.wprf-control-datetime .components-datetime .CalendarDay__blocked_out_of_range:active,
.wprf-control-datetime .components-datetime .CalendarDay__blocked_out_of_range:hover {
  background: #fff;
  border: 1px solid #e4e7e7;
  color: #cacccd;
}
.wprf-control-datetime .components-datetime .CalendarMonth {
  background: #fff;
  text-align: center;
  vertical-align: top;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.wprf-control-datetime .components-datetime .CalendarMonth_table {
  border-collapse: collapse;
  border-spacing: 0;
}
.wprf-control-datetime .components-datetime .CalendarMonth_verticalSpacing {
  border-collapse: separate;
}
.wprf-control-datetime .components-datetime .CalendarMonth_caption {
  color: #484848;
  font-size: 18px;
  text-align: center;
  padding-top: 22px;
  padding-bottom: 37px;
  caption-side: initial;
}
.wprf-control-datetime .components-datetime .CalendarMonth_caption__verticalScrollable {
  padding-top: 12px;
  padding-bottom: 7px;
}
.wprf-control-datetime .components-datetime .CalendarMonthGrid {
  background: #fff;
  text-align: left;
  z-index: 0;
}
.wprf-control-datetime .components-datetime .CalendarMonthGrid__animating {
  z-index: 1;
}
.wprf-control-datetime .components-datetime .CalendarMonthGrid__vertical {
  margin: 0 auto;
}
.wprf-control-datetime .components-datetime .CalendarMonthGrid__vertical_scrollable {
  margin: 0 auto;
  overflow-y: scroll;
}
.wprf-control-datetime .components-datetime .CalendarMonthGrid_month__horizontal {
  display: inline-block;
  vertical-align: top;
  min-height: 100%;
}
.wprf-control-datetime .components-datetime .CalendarMonthGrid_month__hideForAnimation {
  position: absolute;
  z-index: -1;
  opacity: 0;
  pointer-events: none;
}
.wprf-control-datetime .components-datetime .CalendarMonthGrid_month__hidden {
  visibility: hidden;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation {
  position: relative;
  z-index: 2;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation__horizontal {
  height: 0;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation__verticalDefault {
  position: absolute;
  width: 100%;
  height: 52px;
  bottom: 0;
  left: 0;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation__verticalScrollableDefault {
  position: relative;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_button {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 0;
  padding: 0;
  margin: 0;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_button__default {
  border: 1px solid #e4e7e7;
  background-color: #fff;
  color: #757575;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_button__default:focus,
.wprf-control-datetime .components-datetime .DayPickerNavigation_button__default:hover {
  border: 1px solid #c4c4c4;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_button__default:active {
  background: #f2f2f2;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_button__horizontalDefault {
  position: absolute;
  top: 18px;
  line-height: 0.78;
  border-radius: 3px;
  padding: 6px 9px;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_leftButton__horizontalDefault {
  left: 22px;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_rightButton__horizontalDefault {
  right: 22px;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_button__verticalDefault {
  padding: 5px;
  background: #fff;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  display: inline-block;
  height: 100%;
  width: 50%;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_nextButton__verticalDefault {
  border-left: 0;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_nextButton__verticalScrollableDefault {
  width: 100%;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_svg__horizontal {
  height: 19px;
  width: 19px;
  fill: #82888a;
  display: block;
}
.wprf-control-datetime .components-datetime .DayPickerNavigation_svg__vertical {
  height: 42px;
  width: 42px;
  fill: #484848;
  display: block;
}
.wprf-control-datetime .components-datetime .DayPicker {
  background: #fff;
  position: relative;
  text-align: left;
}
.wprf-control-datetime .components-datetime .DayPicker__horizontal {
  background: #fff;
}
.wprf-control-datetime .components-datetime .DayPicker__verticalScrollable {
  height: 100%;
}
.wprf-control-datetime .components-datetime .DayPicker__hidden {
  visibility: hidden;
}
.wprf-control-datetime .components-datetime .DayPicker__withBorder {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.07);
  border-radius: 3px;
}
.wprf-control-datetime .components-datetime .DayPicker_portal__horizontal {
  box-shadow: none;
  position: absolute;
  left: 50%;
  top: 50%;
}
.wprf-control-datetime .components-datetime .DayPicker_portal__vertical {
  position: initial;
}
.wprf-control-datetime .components-datetime .DayPicker_focusRegion {
  outline: 0;
}
.wprf-control-datetime .components-datetime .DayPicker_calendarInfo__horizontal,
.wprf-control-datetime .components-datetime .DayPicker_wrapper__horizontal {
  display: inline-block;
  vertical-align: top;
}
.wprf-control-datetime .components-datetime .DayPicker_weekHeaders {
  position: relative;
}
.wprf-control-datetime .components-datetime .DayPicker_weekHeaders__horizontal {
  margin-left: 9px;
}
.wprf-control-datetime .components-datetime .DayPicker_weekHeader {
  color: #757575;
  position: absolute;
  top: 62px;
  z-index: 2;
  text-align: left;
}
.wprf-control-datetime .components-datetime .DayPicker_weekHeader__vertical {
  left: 50%;
}
.wprf-control-datetime .components-datetime .DayPicker_weekHeader__verticalScrollable {
  top: 0;
  display: table-row;
  border-bottom: 1px solid #dbdbdb;
  background: #fff;
  margin-left: 0;
  left: 0;
  width: 100%;
  text-align: center;
}
.wprf-control-datetime .components-datetime .DayPicker_weekHeader_ul {
  list-style: none;
  margin: 1px 0;
  padding-left: 0;
  padding-right: 0;
  font-size: 14px;
}
.wprf-control-datetime .components-datetime .DayPicker_weekHeader_li {
  display: inline-block;
  text-align: center;
}
.wprf-control-datetime .components-datetime .DayPicker_transitionContainer {
  position: relative;
  overflow: hidden;
  border-radius: 3px;
}
.wprf-control-datetime .components-datetime .DayPicker_transitionContainer__horizontal {
  transition: height 0.2s ease-in-out;
}
.wprf-control-datetime .components-datetime .DayPicker_transitionContainer__vertical {
  width: 100%;
}
.wprf-control-datetime .components-datetime .DayPicker_transitionContainer__verticalScrollable {
  padding-top: 20px;
  height: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  overflow-y: scroll;
}
.wprf-control-datetime .components-datetime .DateInput {
  margin: 0;
  padding: 0;
  background: #fff;
  position: relative;
  display: inline-block;
  width: 130px;
  vertical-align: middle;
}
.wprf-control-datetime .components-datetime .DateInput__small {
  width: 97px;
}
.wprf-control-datetime .components-datetime .DateInput__block {
  width: 100%;
}
.wprf-control-datetime .components-datetime .DateInput__disabled {
  background: #f2f2f2;
  color: #dbdbdb;
}
.wprf-control-datetime .components-datetime .DateInput_input {
  font-weight: 200;
  font-size: 19px;
  line-height: 24px;
  color: #484848;
  background-color: #fff;
  width: 100%;
  padding: 11px 11px 9px;
  border: 0;
  border-top: 0;
  border-right: 0;
  border-bottom: 2px solid transparent;
  border-left: 0;
  border-radius: 0;
}
.wprf-control-datetime .components-datetime .DateInput_input__small {
  font-size: 15px;
  line-height: 18px;
  letter-spacing: 0.2px;
  padding: 7px 7px 5px;
}
.wprf-control-datetime .components-datetime .DateInput_input__regular {
  font-weight: auto;
}
.wprf-control-datetime .components-datetime .DateInput_input__readOnly {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.wprf-control-datetime .components-datetime .DateInput_input__focused {
  outline: 0;
  background: #fff;
  border: 0;
  border-top: 0;
  border-right: 0;
  border-bottom: 2px solid #008489;
  border-left: 0;
}
.wprf-control-datetime .components-datetime .DateInput_input__disabled {
  background: #f2f2f2;
  font-style: italic;
}
.wprf-control-datetime .components-datetime .DateInput_screenReaderMessage {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
.wprf-control-datetime .components-datetime .DateInput_fang {
  position: absolute;
  width: 20px;
  height: 10px;
  left: 22px;
  z-index: 2;
}
.wprf-control-datetime .components-datetime .DateInput_fangShape {
  fill: #fff;
}
.wprf-control-datetime .components-datetime .DateInput_fangStroke {
  stroke: #dbdbdb;
  fill: transparent;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput {
  background-color: #fff;
  display: inline-block;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput__disabled {
  background: #f2f2f2;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput__withBorder {
  border-radius: 2px;
  border: 1px solid #dbdbdb;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput__rtl {
  direction: rtl;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput__block {
  display: block;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput__showClearDates {
  padding-right: 30px;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_arrow {
  display: inline-block;
  vertical-align: middle;
  color: #484848;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_arrow_svg {
  vertical-align: middle;
  fill: #484848;
  height: 24px;
  width: 24px;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_clearDates {
  background: 0 0;
  border: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  cursor: pointer;
  padding: 10px;
  margin: 0 10px 0 5px;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_clearDates__small {
  padding: 6px;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_clearDates_default:focus,
.wprf-control-datetime .components-datetime .DateRangePickerInput_clearDates_default:hover {
  background: #dbdbdb;
  border-radius: 50%;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_clearDates__hide {
  visibility: hidden;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_clearDates_svg {
  fill: #82888a;
  height: 12px;
  width: 15px;
  vertical-align: middle;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_clearDates_svg__small {
  height: 9px;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_calendarIcon {
  background: 0 0;
  border: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  padding: 10px;
  margin: 0 5px 0 10px;
}
.wprf-control-datetime .components-datetime .DateRangePickerInput_calendarIcon_svg {
  fill: #82888a;
  height: 15px;
  width: 14px;
  vertical-align: middle;
}
.wprf-control-datetime .components-datetime .DateRangePicker {
  position: relative;
  display: inline-block;
}
.wprf-control-datetime .components-datetime .DateRangePicker__block {
  display: block;
}
.wprf-control-datetime .components-datetime .DateRangePicker_picker {
  z-index: 1;
  background-color: #fff;
  position: absolute;
}
.wprf-control-datetime .components-datetime .DateRangePicker_picker__rtl {
  direction: rtl;
}
.wprf-control-datetime .components-datetime .DateRangePicker_picker__directionLeft {
  left: 0;
}
.wprf-control-datetime .components-datetime .DateRangePicker_picker__directionRight {
  right: 0;
}
.wprf-control-datetime .components-datetime .DateRangePicker_picker__portal {
  background-color: rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}
.wprf-control-datetime .components-datetime .DateRangePicker_picker__fullScreenPortal {
  background-color: #fff;
}
.wprf-control-datetime .components-datetime .DateRangePicker_closeButton {
  background: 0 0;
  border: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
  padding: 15px;
  z-index: 2;
}
.wprf-control-datetime .components-datetime .DateRangePicker_closeButton:focus,
.wprf-control-datetime .components-datetime .DateRangePicker_closeButton:hover {
  color: rgb(175.7572815534, 178.7475728155, 180.2427184466);
  text-decoration: none;
}
.wprf-control-datetime .components-datetime .DateRangePicker_closeButton_svg {
  height: 15px;
  width: 15px;
  fill: #cacccd;
}
.wprf-control-datetime .components-datetime .components-datetime {
  padding: 0;
}
.wprf-control-datetime .components-datetime .components-datetime .components-datetime__calendar-help {
  padding: 16px;
}
.wprf-control-datetime .components-datetime .components-datetime .components-datetime__calendar-help h4 {
  margin: 0;
}
.wprf-control-datetime .components-datetime .components-datetime .components-datetime__buttons {
  display: flex;
  justify-content: space-between;
}
.wprf-control-datetime .components-datetime .components-datetime fieldset {
  border: 0;
  padding: 0;
  margin: 0;
}
.wprf-control-datetime .components-datetime .components-datetime select,
.wprf-control-datetime .components-datetime .components-datetime input {
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.1s linear;
  border-radius: 2px;
  border: 1px solid #757575;
}
@media (prefers-reduced-motion: reduce) {
  .wprf-control-datetime .components-datetime .components-datetime select,
  .wprf-control-datetime .components-datetime .components-datetime input {
    transition-duration: 0s;
  }
}
.wprf-control-datetime .components-datetime .components-datetime select,
.wprf-control-datetime .components-datetime .components-datetime input[type=number],
.wprf-control-datetime .components-datetime .components-datetime .components-button {
  height: 30px;
  margin-top: 0;
  margin-bottom: 0;
}
.wprf-control-datetime .components-datetime .components-datetime__date {
  min-height: 236px;
  border-top: 1px solid #ddd;
}
.wprf-control-datetime .components-datetime .components-datetime__date button {
  border: none;
}
.wprf-control-datetime .components-datetime .components-datetime__date .DayPickerNavigation_leftButton__horizontalDefault {
  left: 0;
}
.wprf-control-datetime .components-datetime .components-datetime__date .CalendarMonth_caption {
  font-size: 13px;
}
.wprf-control-datetime .components-datetime .components-datetime__date .CalendarMonth_table {
  border-collapse: separate;
  border-spacing: 2px;
}
.wprf-control-datetime .components-datetime .components-datetime__date .CalendarDay {
  font-size: 13px;
  border: none;
  border-radius: 50%;
  text-align: center;
}
.wprf-control-datetime .components-datetime .components-datetime__date .CalendarDay:focus {
  box-shadow: inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color), inset 0 0 0 3px #fff;
  outline: 2px solid transparent;
}
.wprf-control-datetime .components-datetime .components-datetime__date .CalendarDay__selected {
  background: #6648fe;
  border: 2px solid transparent;
}
.wprf-control-datetime .components-datetime .components-datetime__date .CalendarDay__selected:hover {
  background: var(--wp-admin-theme-color-darker-20);
}
.wprf-control-datetime .components-datetime .components-datetime__date .CalendarDay__selected:focus {
  box-shadow: inset 0 0 0 1px #fff;
}
.wprf-control-datetime .components-datetime .components-datetime__date .DayPickerNavigation_button__horizontalDefault {
  padding: 2px 8px;
  top: 20px;
}
.wprf-control-datetime .components-datetime .components-datetime__date .DayPickerNavigation_button__horizontalDefault:focus {
  border-color: var(--wp-admin-theme-color);
  box-shadow: 0 0 0 1px var(--wp-admin-theme-color);
  outline: 2px solid transparent;
}
.wprf-control-datetime .components-datetime .components-datetime__date .DayPicker_weekHeader {
  top: 50px;
}
.wprf-control-datetime .components-datetime .components-datetime__date .DayPicker_weekHeader .DayPicker_weekHeader_ul {
  margin: 1px 0;
  padding-left: 0;
  padding-right: 0;
}
.wprf-control-datetime .components-datetime .components-datetime__date.is-description-visible .DayPicker {
  visibility: hidden;
}
.wprf-control-datetime .components-datetime .components-datetime__time {
  padding-bottom: 16px;
}
.wprf-control-datetime .components-datetime .components-datetime__time fieldset {
  position: relative;
  margin-bottom: 0.5em;
}
.wprf-control-datetime .components-datetime .components-datetime__time fieldset input, .wprf-control-datetime .components-datetime .components-datetime__time fieldset select, .wprf-control-datetime .components-datetime .components-datetime__time fieldset button {
  height: 30px;
}
.wprf-control-datetime .components-datetime .components-datetime__time fieldset + fieldset {
  margin-bottom: 0;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-field-am-pm fieldset {
  margin-top: 0;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-wrapper {
  display: flex;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-wrapper .components-datetime__time-separator {
  display: inline-block;
  padding: 0 3px 0 0;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-wrapper .components-datetime__time-field-time {
  /*rtl:ignore*/
  direction: ltr;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-wrapper .components-datetime__time-field select {
  margin-right: 4px;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-wrapper .components-datetime__time-field select:focus {
  position: relative;
  z-index: 1;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-wrapper .components-datetime__time-field input[type=number] {
  padding: 2px;
  margin-right: 4px;
  text-align: center;
  -moz-appearance: textfield;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-wrapper .components-datetime__time-field input[type=number]:focus {
  position: relative;
  z-index: 1;
}
.wprf-control-datetime .components-datetime .components-datetime__time .components-datetime__time-wrapper .components-datetime__time-field input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.wprf-control-datetime .components-datetime .components-datetime__time.is-12-hour .components-datetime__time-field-day input {
  margin: -4px 0 0 !important;
  border-radius: 2px 0 0 2px !important;
}
.wprf-control-datetime .components-datetime .components-datetime__time.is-12-hour .components-datetime__time-field-year input {
  border-radius: 0 2px 2px 0 !important;
}
.wprf-control-datetime .components-datetime .components-datetime__timezone {
  line-height: 30px;
  margin-left: 4px;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
.wprf-control-datetime .components-datetime .components-datetime__time-legend {
  font-weight: 600;
  margin-top: 0.5em;
}
.wprf-control-datetime .components-datetime .components-datetime__time-legend.invisible {
  position: absolute;
  top: -999em;
  left: -999em;
}
.wprf-control-datetime .components-datetime .components-datetime__time-field-hours-input,
.wprf-control-datetime .components-datetime .components-datetime__time-field-minutes-input,
.wprf-control-datetime .components-datetime .components-datetime__time-field-day-input {
  width: 35px;
}
.wprf-control-datetime .components-datetime .components-datetime__time-field-year-input {
  width: 55px;
}
.wprf-control-datetime .components-datetime .components-datetime__time-field-month-select {
  max-width: 145px;
}
.wprf-control-datetime .components-popover .components-datetime__date {
  padding-left: 4px;
}
.wprf-control-datetime .components-popover .components-popover__content {
  z-index: 9999999;
  position: absolute;
  background: white;
  padding: 15px;
  border: 1px solid #f2f2f2;
  border-radius: 5px;
}
.wprf-control-datetime .components-popover .components-datetime__date-help-toggle {
  display: none;
  margin-left: auto;
}

.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field {
  margin-top: 20px;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title {
  padding: 15px;
  cursor: pointer;
  font-size: 12px;
  background-color: #f8fafb;
  display: flex;
  align-items: center;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title h4 {
  margin: 0;
  flex-basis: 100%;
  cursor: grab;
  color: #3D4F7F;
  line-height: 18px;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title h4 .dashicon,
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title h4 .icon,
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title h4 svg {
  font-size: 18px;
  height: 18px;
  line-height: 18px;
  width: 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title .wprf-repeater-field-controls {
  align-self: flex-end;
  display: flex;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title .wprf-repeater-field-controls span {
  margin-left: 10px;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title .wprf-repeater-field-controls .dashicon.dashicons-admin-page {
  color: #5f7eff;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-field-title .wprf-repeater-field-controls .dashicon.dashicons-trash {
  color: #dc4f4b;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field .wprf-repeater-inner-field {
  padding: 20px;
  background-color: #fff;
  border: 1px solid #f2f2f2;
}
.wprf-repeater-control .wprf-repeater-content .wprf-repeater-field.sortable-chosen .wprf-repeater-field-title h4 {
  cursor: grabbing;
}

.wprf-toggle-wrap {
  display: flex;
  align-items: center;
}
.wprf-toggle-wrap.wprf-card {
  padding: 15px;
  border: 1px solid #f2f2f2;
  margin-bottom: 20px;
}
.wprf-toggle-wrap.wprf-label-position-left .wprf-input-label {
  margin-left: 15px;
}
.wprf-toggle-wrap.wprf-label-position-right .wprf-input-label {
  margin-right: 15px;
}
.wprf-toggle-wrap .wprf-input-field, .wprf-toggle-wrap input {
  display: none;
}
.wprf-toggle-wrap label.wprf-input-label {
  width: 55px;
  position: relative;
  min-height: 30px;
  height: 30px;
  max-height: 30px;
  margin-bottom: 0;
  cursor: pointer;
  display: inline-block;
}
.wprf-toggle-wrap label.wprf-input-label::after, .wprf-toggle-wrap label.wprf-input-label::before {
  content: "";
  position: absolute;
}
.wprf-toggle-wrap label.wprf-input-label:before {
  background: #f0f0f0;
  width: 55px;
  height: 28px;
  border: 1px solid #e8eae9;
  left: 0;
  top: 0;
  border-radius: 20px;
  transition: all 0.4s ease;
}
.wprf-toggle-wrap label.wprf-input-label:after {
  left: 3px;
  top: 3px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #fbfbfb;
  transition: left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.3s ease, margin 0.3s ease;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 2px 0 rgba(0, 0, 0, 0.08);
}
.wprf-toggle-wrap.wprf-checked:before, .wprf-toggle-wrap input:checked + label:before {
  background: #2df16e;
}
.wprf-toggle-wrap.wprf-checked:after, .wprf-toggle-wrap input:checked + label:after {
  left: auto;
  right: 0px;
}

.wprf-toggle-wrapper > h4 {
  margin-top: 0px;
  margin-bottom: 10px;
}
.wprf-toggle-wrapper .wprf-toggle-options .wprf-toggle-wrap {
  background-color: #fff;
  padding: 15px;
  border-radius: 5px;
  box-sizing: border-box;
  margin-top: 0px;
  margin-bottom: 15px;
  display: flex;
}
.wprf-toggle-wrapper .wprf-toggle-options .wprf-toggle-wrap.wprf-label-position-right > span {
  text-align: right;
}
.wprf-toggle-wrapper .wprf-toggle-options .wprf-toggle-wrap > span {
  flex: 1;
}

.wprf-colorpicker-wrap {
  /* CURRENT COLOR COMPONENT */
  /* SATURATION COMPONENT */
  /* HUE & ALPHA BARS */
  /* INPUTS COMPONENT */
}
.wprf-colorpicker-wrap .components-color-picker {
  width: 100%;
}
.wprf-colorpicker-wrap .components-color-picker * {
  box-sizing: border-box;
}
.wprf-colorpicker-wrap .components-color-picker__saturation {
  width: 100%;
  padding-bottom: 55%;
  position: relative;
}
.wprf-colorpicker-wrap .components-color-picker__body {
  padding: 16px 16px 12px;
}
.wprf-colorpicker-wrap .components-color-picker__controls {
  display: flex;
}
.wprf-colorpicker-wrap .components-color-picker__saturation-pointer,
.wprf-colorpicker-wrap .components-color-picker__hue-pointer,
.wprf-colorpicker-wrap .components-color-picker__alpha-pointer {
  padding: 0;
  position: absolute;
  cursor: pointer;
  box-shadow: none;
  border: none;
}
.wprf-colorpicker-wrap .components-color-picker__swatch {
  margin-right: 8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  background-image: linear-gradient(45deg, #ddd 25%, transparent 25%), linear-gradient(-45deg, #ddd 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ddd 75%), linear-gradient(-45deg, transparent 75%, #ddd 75%);
  background-size: 10px 10px;
  background-position: 0 0, 0 5px, 5px -5px, -5px 0;
}
.is-alpha-disabled .wprf-colorpicker-wrap .components-color-picker__swatch {
  width: 12px;
  height: 12px;
  margin-top: 0;
}
.wprf-colorpicker-wrap .components-color-picker__active {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  z-index: 2;
}
.wprf-colorpicker-wrap .components-color-picker__saturation-color,
.wprf-colorpicker-wrap .components-color-picker__saturation-white,
.wprf-colorpicker-wrap .components-color-picker__saturation-black {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.wprf-colorpicker-wrap .components-color-picker__saturation-color {
  overflow: visible;
}
.wprf-colorpicker-wrap .components-color-picker__saturation-white {
  /*rtl:ignore*/
  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0));
}
.wprf-colorpicker-wrap .components-color-picker__saturation-black {
  background: linear-gradient(to top, #000, rgba(0, 0, 0, 0));
}
.wprf-colorpicker-wrap .components-button.components-color-picker__saturation-pointer {
  width: 14px;
  height: 14px;
  padding: 0;
  border-radius: 50%;
  background-color: transparent;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 0 1px #FFF, inset 0 0 0 1px #000, 0 0 0 2px #000;
}
.wprf-colorpicker-wrap .components-button.components-color-picker__saturation-pointer:focus:not(:disabled) {
  box-shadow: 0 0 0 2px #FFF, inset 0 0 0 1px #000, 0 0 0 3px #000;
}
.wprf-colorpicker-wrap .components-color-picker__toggles {
  flex: 1;
}
.wprf-colorpicker-wrap .components-color-picker__alpha {
  background-image: linear-gradient(45deg, #ddd 25%, transparent 25%), linear-gradient(-45deg, #ddd 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ddd 75%), linear-gradient(-45deg, transparent 75%, #ddd 75%);
  background-size: 10px 10px;
  background-position: 0 0, 0 5px, 5px -5px, -5px 0;
}
.wprf-colorpicker-wrap .components-color-picker__hue-gradient,
.wprf-colorpicker-wrap .components-color-picker__alpha-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.wprf-colorpicker-wrap .components-color-picker__hue,
.wprf-colorpicker-wrap .components-color-picker__alpha {
  height: 12px;
  position: relative;
}
.wprf-colorpicker-wrap .is-alpha-enabled .components-color-picker__hue {
  margin-bottom: 8px;
}
.wprf-colorpicker-wrap .components-color-picker__hue-bar,
.wprf-colorpicker-wrap .components-color-picker__alpha-bar {
  position: relative;
  margin: 0 3px;
  height: 100%;
  padding: 0 2px;
}
.wprf-colorpicker-wrap .components-color-picker__hue-gradient {
  /*rtl:ignore*/
  background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
}
.wprf-colorpicker-wrap .components-color-picker__hue-pointer,
.wprf-colorpicker-wrap .components-color-picker__alpha-pointer {
  /*rtl:ignore*/
  left: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.37);
  background: #fff;
  transform: translate(-7px, -1px);
}
.wprf-colorpicker-wrap .components-color-picker__hue-pointer,
.wprf-colorpicker-wrap .components-color-picker__saturation-pointer {
  transition: box-shadow 0.1s linear;
}
@media (prefers-reduced-motion: reduce) {
  .wprf-colorpicker-wrap .components-color-picker__hue-pointer,
  .wprf-colorpicker-wrap .components-color-picker__saturation-pointer {
    transition-duration: 0s;
  }
}
.wprf-colorpicker-wrap .components-color-picker__saturation-pointer:focus {
  box-shadow: 0 0 0 2px #fff, 0 0 0 4px var(--wp-admin-theme-color), 0 0 5px 0 var(--wp-admin-theme-color), inset 0 0 1px 1px rgba(0, 0, 0, 0.3), 0 0 1px 2px rgba(0, 0, 0, 0.4);
}
.wprf-colorpicker-wrap .components-color-picker__hue-pointer:focus,
.wprf-colorpicker-wrap .components-color-picker__alpha-pointer:focus {
  border-color: var(--wp-admin-theme-color);
  box-shadow: 0 0 0 2px var(--wp-admin-theme-color), 0 0 3px 0 var(--wp-admin-theme-color);
  outline: 2px solid transparent;
  outline-offset: -2px;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-wrapper {
  margin: 0 -4px;
  padding-top: 16px;
  display: flex;
  align-items: flex-end;
  min-width: 255px;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-wrapper fieldset {
  flex: 1;
  border: none;
  margin: 0;
  padding: 0;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-wrapper .components-color-picker__inputs-fields .components-text-control__input[type=number] {
  padding: 6px 3px;
  margin: 0;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-field {
  width: 100%;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-fields {
  display: flex;
  /*rtl:ignore*/
  direction: ltr;
  flex-grow: 1;
  margin-right: 4px;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-fields .components-base-control + .components-base-control {
  margin-top: 0;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-fields .components-base-control__field {
  margin: 0 2px;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-toggle {
  height: 30px;
  padding: 0 5px;
}

.wprf-colorpicker-wrap {
  position: relative;
  max-width: 300px;
  min-width: 300px;
}
.wprf-colorpicker-wrap .wprf-colorpicker-reset {
  position: absolute;
  bottom: 25px;
}
.wprf-colorpicker-wrap .wprf-picker-display {
  width: 30px;
  height: 30px;
  display: inline-block;
  margin-right: 15px;
  margin-bottom: 15px;
  cursor: pointer;
  border-radius: 50%;
  outline: none;
  border: 1px solid #ddd;
  margin-top: 10px;
}
.wprf-colorpicker-wrap > .components-color-picker {
  position: absolute;
  background-color: #fff;
  padding: 10px;
  border-radius: 5px;
  left: 0;
  top: 45px;
  z-index: 11;
}
.wprf-colorpicker-wrap .chrome-picker {
  position: absolute;
  top: 30px;
}
.wprf-colorpicker-wrap .components-color-picker__inputs-wrapper .components-color-picker__inputs-fields .components-text-control__input[type=number] {
  width: 54px;
}

.wprf-group-control .wprf-group-control-inner {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.wprf-group-control .wprf-group-control-inner > * {
  flex-basis: 30%;
}

.wprf-select-wrapper {
  min-width: 250px;
}

.wprf-media {
  display: flex;
  align-items: center;
}
.wprf-media .wprf-image-preview {
  width: 100px;
  height: auto;
  max-height: 100px;
  margin-right: 20px;
  overflow: hidden;
}
.wprf-media .wprf-image-preview img {
  max-width: 100%;
  max-height: 100%;
  display: block;
}
.wprf-media .wprf-image-uploader button {
  margin-right: 10px;
}
.wprf-media .wprf-image-uploader button.wprf-image-remove-btn {
  background-color: #f26f5a;
  color: #fff;
}

.wprf-editor {
  height: 250px;
}
.wprf-editor .wprf-editor-toolbar {
  margin-bottom: 0px;
}
.wprf-editor .wprf-editor-main {
  height: calc(100% - 46px);
  padding: 0px 15px;
  box-sizing: border-box;
  border: 1px solid #F1F1F1;
  border-top: 0px;
}

.wprf-modal {
  position: relative;
}

.components-modal__screen-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.35);
  z-index: 100000;
  animation: edit-post__fade-in-animation 0.2s ease-out 0s;
  animation-fill-mode: forwards;
}

@media (prefers-reduced-motion: reduce) {
  .components-modal__screen-overlay {
    animation-duration: 1ms;
  }
}
.components-modal__frame {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  box-sizing: border-box;
  margin: 0;
  border: 1px solid #ddd;
  background: #fff;
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.2);
  overflow: auto;
}

@media (min-width: 600px) {
  .components-modal__frame {
    top: 50%;
    right: auto;
    bottom: auto;
    left: 50%;
    min-width: 360px;
    max-width: calc(100% - 16px - 16px);
    max-height: 90%;
    transform: translate(-50%, -50%);
    animation: components-modal__appear-animation 0.1s ease-out;
    animation-fill-mode: forwards;
  }
}
@media (min-width: 600px) and (prefers-reduced-motion: reduce) {
  .components-modal__frame {
    animation-duration: 1ms;
  }
}
@keyframes components-modal__appear-animation {
  from {
    margin-top: 32px;
  }
  to {
    margin-top: 0;
  }
}
.components-modal__header {
  box-sizing: border-box;
  border-bottom: 1px solid #ddd;
  padding: 0 24px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background: #fff;
  align-items: center;
  height: 60px;
  z-index: 10;
  position: relative;
  position: sticky;
  top: 0;
  margin: 0 -24px 24px;
}

@supports (-ms-ime-align: auto) {
  .components-modal__header {
    position: fixed;
    width: 100%;
  }
}
.components-modal__header .components-modal__header-heading {
  font-size: 1rem;
  font-weight: 600;
}

.components-modal__header h1 {
  line-height: 1;
  margin: 0;
}

.components-modal__header .components-button {
  position: relative;
  left: 8px;
}

.components-modal__header-heading-container {
  align-items: center;
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  justify-content: left;
}

.components-modal__header-icon-container {
  display: inline-block;
}

.components-modal__header-icon-container svg {
  max-width: 36px;
  max-height: 36px;
  padding: 8px;
}

.components-modal__content {
  box-sizing: border-box;
  height: 100%;
  padding: 0 24px;
}

@supports (-ms-ime-align: auto) {
  .components-modal__content {
    padding-top: 60px;
  }
}
.wprf-modal-body .wprf-modal-footer {
  display: flex;
  align-items: center;
  border-top: 1px solid #f2f2f2;
  margin: 24px -24px 0px;
  padding: 15px 24px;
  padding-bottom: 0px;
}
.wprf-modal-body .wprf-modal-footer .wprf-modal-footer-left {
  flex: 1;
}

.wprf-control.wprf-button-group > div {
  margin-right: 15px;
}

.responsive-button {
  all: initial;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 5px;
  cursor: pointer;
  height: 22px;
  width: 22px;
  background: rgba(106, 75, 255, 0.1);
  border-radius: 2px;
}
.responsive-button img {
  display: inline-flex;
  filter: brightness(50%) invert(55%);
}
.responsive-button:hover img, .responsive-button.active img {
  filter: brightness(100%) invert(0%);
}

.wprf-code-viewer {
  position: relative;
}
.wprf-code-viewer textarea {
  width: 100%;
  min-height: 300px;
  background: #2e3246;
  color: #eae8eb;
  resize: none;
  border-radius: 15px;
  border-width: 15px;
  border-color: transparent;
  line-height: 1.7;
  font-style: italic;
  cursor: pointer;
}
.wprf-code-viewer textarea::-webkit-scrollbar {
  display: none;
}
.wprf-code-viewer .wprf-copy-button {
  border: none;
  position: absolute;
  top: 15px;
  right: 15px;
  background: #15c7a3;
  padding: 5px 20px;
  color: #fff;
  text-transform: uppercase;
  border-radius: 5px;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.5), inset 0 5px 5px 5px rgba(0, 0, 0, 0.05), inset 0 -5px 5px 5px rgba(255, 255, 255, 0.1);
  height: 36px;
  display: flex;
  align-items: center;
  pointer-events: none;
  font-weight: 600;
}

.wprf-code-viewer .pro-deactivated textarea {
  background: #555;
  cursor: not-allowed;
}

.wprf-json-uploader {
  display: flex;
  align-items: center;
}
.wprf-json-uploader .wprf-json-uploaderButton {
  border: none;
  background: #6a4bff;
  padding: 5px 20px;
  color: #fff;
  border-radius: 5px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  font-weight: 600;
  cursor: pointer;
  box-sizing: border-box;
}
.wprf-json-uploader .wprf-json-uploaderButton input {
  display: none;
}
.wprf-json-uploader .wpfr-json-file-name-wrapper {
  display: inline-flex;
  align-items: center;
  position: relative;
  padding: 2px 4px 2px 10px;
  background: #f3f3f3;
  border-radius: 22px;
  border: 1px solid #e2e2e2;
}
.wprf-json-uploader .wpfr-json-file-name-wrapper .wpfr-json-file-name {
  font-size: 14px;
  font-weight: 400;
  color: #7c8db5;
  margin-left: 0;
  line-height: 24px;
  margin-right: 5px;
  display: inline-flex;
}
.wprf-json-uploader .wpfr-json-file-name-wrapper .wprf-json-file-delete-button {
  cursor: pointer;
  height: 20px;
  width: 20px;
  min-width: 20px;
  border-radius: 10px;
  background: rgba(255, 0, 0, 0.1);
  color: red;
  display: flex;
  justify-content: center;
  font-size: 13px;
  line-height: 20px;
}
.wprf-json-uploader .wpfr-json-file-name-wrapper .wprf-json-file-delete-button:hover {
  background: red;
  color: white;
}

.wprf-clipboard-wrapper {
  position: relative;
}
.wprf-clipboard-wrapper input {
  padding-right: 20px !important;
  width: 100% !important;
}
.wprf-clipboard-wrapper .wprf-clipboard-tooltip {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
}
.wprf-clipboard-wrapper .wprf-clipboard-tooltip .wprf-clipboard-tooltip-text {
  position: absolute;
  max-width: 30ch;
  white-space: pre;
  bottom: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  background: #5725ff;
  padding: 5px 10px;
  line-height: 1;
  border-radius: 3px;
  color: white;
  visibility: hidden;
  opacity: 0;
}
.wprf-clipboard-wrapper .wprf-clipboard-tooltip .wprf-clipboard-tooltip-text:after {
  content: "";
  position: absolute;
  border-top: 5px solid #5725ff;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  top: 100%;
  left: 50%;
  transform: translate(-50%);
}
.wprf-clipboard-wrapper .wprf-clipboard-tooltip .wprf-copy-icon {
  cursor: pointer;
}
.wprf-clipboard-wrapper:hover .wprf-clipboard-tooltip-text, .wprf-clipboard-wrapper:focus .wprf-clipboard-tooltip-text {
  visibility: visible;
  opacity: 1;
}/*# sourceMappingURL=index.css.map */