.wprf-input-radio-set-wrap.wprf-radio-card input {
  display: none;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option {
  min-height: 80px;
  position: relative;
  box-shadow: 0 0 25px 0 rgba(0, 9, 78, 0.08);
  margin-top: 20px;
  line-height: 60px;
  display: flex;
  align-items: center;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected:after, .wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected:before {
  content: "";
  position: absolute;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected:after {
  height: 6px;
  width: 3px;
  right: -3px;
  top: -6px;
  display: inline-block;
  transform: rotate(45deg);
  border-bottom: 3.5px solid #fff;
  border-right: 3.5px solid #fff;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected:before {
  right: -10px;
  top: -10px;
  left: auto;
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  transition: all 0.3s ease 0s;
  background: #09dca8;
  border-radius: 50%;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected .wprf-input-label {
  background-color: #0ccf9f;
  color: #fff;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option.wprf-option-selected .wprf-input-label.wprf-label-has-image {
  background-color: white;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option input.wprf-input-field {
  display: none;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option .wprf-input-label {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease 0s;
  background: #fff;
  font-size: 14px;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 0;
  box-sizing: border-box;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option .wprf-input-label.wprf-label-has-image {
  padding: 0;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option .wprf-input-label.wprf-label-has-image.wprf-size-medium {
  padding: 10px;
  box-sizing: border-box;
}
.wprf-input-radio-set-wrap.wprf-radio-card .wprf-input-radio-option .wprf-input-label.wprf-label-has-image.wprf-size-medium > img {
  width: 50px;
  height: 50px;
}/*# sourceMappingURL=radio-card.css.map */