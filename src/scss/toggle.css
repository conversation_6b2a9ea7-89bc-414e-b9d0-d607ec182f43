.wprf-toggle-wrap {
  display: flex;
  align-items: center;
}
.wprf-toggle-wrap.wprf-card {
  padding: 15px;
  border: 1px solid #f2f2f2;
  margin-bottom: 20px;
}
.wprf-toggle-wrap.wprf-label-position-left .wprf-input-label {
  margin-left: 15px;
}
.wprf-toggle-wrap.wprf-label-position-right .wprf-input-label {
  margin-right: 15px;
}
.wprf-toggle-wrap .wprf-input-field, .wprf-toggle-wrap input {
  display: none;
}
.wprf-toggle-wrap label.wprf-input-label {
  width: 55px;
  position: relative;
  min-height: 30px;
  height: 30px;
  max-height: 30px;
  margin-bottom: 0;
  cursor: pointer;
  display: inline-block;
}
.wprf-toggle-wrap label.wprf-input-label::after, .wprf-toggle-wrap label.wprf-input-label::before {
  content: "";
  position: absolute;
}
.wprf-toggle-wrap label.wprf-input-label:before {
  background: #f0f0f0;
  width: 55px;
  height: 28px;
  border: 1px solid #e8eae9;
  left: 0;
  top: 0;
  border-radius: 20px;
  transition: all 0.4s ease;
}
.wprf-toggle-wrap label.wprf-input-label:after {
  left: 3px;
  top: 3px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #fbfbfb;
  transition: left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.3s ease, margin 0.3s ease;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 2px 0 rgba(0, 0, 0, 0.08);
}
.wprf-toggle-wrap.wprf-checked:before, .wprf-toggle-wrap input:checked + label:before {
  background: #2df16e;
}
.wprf-toggle-wrap.wprf-checked:after, .wprf-toggle-wrap input:checked + label:after {
  left: auto;
  right: 0px;
}

.wprf-toggle-wrapper > h4 {
  margin-top: 0px;
  margin-bottom: 10px;
}
.wprf-toggle-wrapper .wprf-toggle-options .wprf-toggle-wrap {
  background-color: #fff;
  padding: 15px;
  border-radius: 5px;
  box-sizing: border-box;
  margin-top: 0px;
  margin-bottom: 15px;
  display: flex;
}
.wprf-toggle-wrapper .wprf-toggle-options .wprf-toggle-wrap.wprf-label-position-right > span {
  text-align: right;
}
.wprf-toggle-wrapper .wprf-toggle-options .wprf-toggle-wrap > span {
  flex: 1;
}/*# sourceMappingURL=toggle.css.map */