{"translation-revision-date": "2025-09-09 15:35:40+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "nl"}, "Filters": ["Filters"], "Backdrop filters": ["Achtergrondfilters"], "Flex Size": ["Flex grootte"], "%s edited": ["%s bewerkt"], "%s$1 %s$2 edited": ["%s$1 %s$2 bewerkt"], "Flex direction": ["Flex richting"], "Flex wrap": ["Flex wrap"], "Has styles": ["<PERSON><PERSON>t stijlen"], "Has effective styles": ["Heeft effectieve stijlen"], "Has overridden styles": ["<PERSON><PERSON><PERSON> oversch<PERSON><PERSON> stijlen"], "With your current role,": ["Met je huidige rol,"], "Style origin": ["Stijl oorsprong"], "Class": ["Klass<PERSON>"], "Base": ["<PERSON><PERSON>"], "%s created": ["%s aangemaakt"], "Inherited from base styles": ["<PERSON><PERSON><PERSON><PERSON><PERSON> van basis<PERSON>"], "Inheritance item: %s": ["Erfenis item: %s"], "With your current role, you can only use existing states.": ["Met je huidige rol kun je alleen bestaande statussen gebruiken."], "you can only use existing classes.": ["je kunt alleen bestaande klassen gebruiken."], "class %s applied": ["klasse %s toegepast"], "With your current role, you can use existing classes but can’t modify them.": ["Met je huidige rol kun je bestaande klassen gebruiken, maar niet wij<PERSON>en."], "Column gap": ["<PERSON><PERSON><PERSON>"], "class %s removed": ["klasse %s verwijderd"], "Overline": ["Lijn boven tekst"], "Max width": ["<PERSON><PERSON>"], "Underline": ["Onderstreept"], "Min width": ["<PERSON><PERSON>"], "Object fit": ["Object passend"], "100 - Thin": ["100 - Dun"], "700 - Bold": ["700 - Vet"], "Text align": ["Tekst uitlijning"], "Text color": ["Tekstkleur"], "Bottom left": ["Onder links"], "Border type": ["Randtype"], "300 - Light": ["300 - <PERSON>cht"], "900 - Black": ["900 - <PERSON><PERSON>"], "Line height": ["Regelafstand"], "Text stroke": ["Lijn tekst"], "Bottom right": ["Onder rechts"], "Border color": ["<PERSON><PERSON><PERSON>"], "Border width": ["Randbreedte"], "400 - Normal": ["400 - <PERSON><PERSON>"], "500 - Medium": ["500 - Medium"], "Line-through": ["Doorgestreept"], "Border radius": ["<PERSON><PERSON><PERSON>"], "Z-index": ["Z-index"], "Word spacing": ["Woordafstand"], "Text transform": ["Tekst transformatie"], "Scale down": ["Sc<PERSON>alverkleining"], "Radial Gradient": ["<PERSON><PERSON><PERSON> verloop"], "Linear Gradient": ["Lineair verl<PERSON>"], "Letter spacing": ["Letterafstand"], "Dynamic tags": ["Dynamische tags"], "Font style": ["Lettertype stijl"], "Font family": ["Lettertype familie"], "800 - Extra bold": ["800 - Extra vet"], "600 - Semi bold": ["600 - Half vet"], "200 - Extra light": ["200 - Extra licht"], "You'll need Elementor Pro to use this feature.": ["Je hebt Elementor Pro nodig om deze functie te gebruiken."], "This value is overridden by another style": ["Deze waarde wordt overschreven door een andere stijl"], "This is the final value": ["Di<PERSON> is de uiteindelijke waarde"], "This has value from another style": ["<PERSON>t heeft waarde van een andere stijl"], "Streamline your workflow with dynamic tags": ["Stroomlijn je workflow met dynamische tags"], "Inline-flex": ["Inline-flex"], "Inline-block": ["Inline-blok"], "In-blk": ["In-blk"], "Has style": ["<PERSON><PERSON><PERSON> stijl"], "Object position": ["Object positie"], "In-flx": ["In-flx"], "Clear & try again": ["Wis & probeer opnieuw"], "Anchor offset": ["Anker offset"], "Align content": ["<PERSON><PERSON><PERSON>"], "Adjust corners": ["<PERSON><PERSON><PERSON> a<PERSON>en"], "Adjust borders": ["<PERSON><PERSON> a<PERSON>en"], "Top left": ["Linksboven"], "Top right": ["Rechtsboven"], "Type class name": ["Type klasse naam"], "You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.": ["Je hebt de limiet van 50 klassen bereikt. Verwijder een bestaande om een nieuwe klasse te maken."], "Line decoration": ["Lijn decoratie"], "Try something else.": ["<PERSON><PERSON><PERSON> i<PERSON> anders."], "States": ["<PERSON><PERSON>"], "Classes": ["Klassen"], "Open CSS Class Menu": ["CSS klasse menu openen"], "Last": ["Laatste"], "Flex": ["Flex"], "First": ["Eerste"], "Block": ["Blok"], "Space around": ["<PERSON><PERSON><PERSON><PERSON> rondom"], "No wrap": ["<PERSON><PERSON> wik<PERSON>"], "local": ["lokaal"], "Custom order": ["Aangepaste volgorde"], "Basis": ["<PERSON><PERSON>"], "Align self": ["Zelf uitlijnen"], "Space evenly": ["R<PERSON><PERSON><PERSON> gelijkmatig"], "Reversed wrap": ["Omgekeerde wikkel"], "Reversed row": ["Omgekeerde rij"], "Reversed column": ["Omgekeerde kolom"], "Justify content": ["<PERSON><PERSON><PERSON>"], "Flex child": ["Flex sub"], "Align items": ["Items uitlijnen"], "Ridge": ["<PERSON><PERSON><PERSON>"], "Inset": ["Inset"], "Box shadow": ["<PERSON><PERSON> schaduw"], "Outset": ["Outset"], "Static": ["Statisch"], "Visible": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Relative": ["Relatief"], "Effects": ["<PERSON><PERSON>"], "Remove dynamic value": ["Dynamische waarde verwijderen"], "Sorry, nothing matched": ["<PERSON>r kwam niets overeen"], "Show more": ["<PERSON><PERSON> weer<PERSON><PERSON>"], "Show less": ["<PERSON><PERSON> weer<PERSON><PERSON>"], "Search dynamic tags…": ["Dynamische tags aan het zoeken…"], "Font weight": ["Lettertype gewicht"], "Font size": ["Lettertype grootte"], "Min height": ["Minimale hoogte"], "Max height": ["Maximale hoogte"], "Dimensions": ["Afmetingen"], "Space between": ["Tussenruimte"], "Sticky": ["<PERSON>y"], "Wrap": ["Terugloop"], "Grow": ["<PERSON><PERSON><PERSON><PERSON>"], "Shrink": ["<PERSON><PERSON><PERSON>"], "Groove": ["Groove"], "Rename": ["<PERSON><PERSON><PERSON>"], "Order": ["Volgorde"], "Row": ["<PERSON><PERSON><PERSON>"], "Gaps": ["<PERSON><PERSON><PERSON><PERSON>"], "Google Fonts": ["Google Fonts"], "Fill": ["<PERSON><PERSON><PERSON>"], "Auto": ["Auto"], "Contain": ["Insluiten"], "Cover": ["Bedekken"], "Hidden": ["Verborgen"], "Overflow": ["Overflow"], "Fixed": ["Vast"], "Absolute": ["Absoluut"], "Opacity": ["Doorzichtigheid"], "Position": ["<PERSON><PERSON><PERSON>"], "Custom Fonts": ["Aangepaste lettertypen"], "Remove": ["Verwijderen"], "End": ["Einde"], "Start": ["<PERSON><PERSON>"], "Edit %s": ["%s bewerken"], "Background": ["Achtergrond"], "General": ["<PERSON><PERSON><PERSON><PERSON>"], "Direction": ["<PERSON><PERSON>"], "Spacing": ["Afstand"], "Left to right": ["<PERSON><PERSON> naar rechts"], "Display": ["Weergave"], "Settings": ["Instellingen"], "Normal": ["Normaal"], "Right": ["<PERSON><PERSON><PERSON>"], "Stretch": ["Uitrekken"], "Style": ["<PERSON><PERSON><PERSON><PERSON>"], "None": ["<PERSON><PERSON>"], "Size": ["Afmeting"], "Margin": ["Marge"], "Typography": ["<PERSON><PERSON><PERSON><PERSON>"], "Column": ["<PERSON><PERSON><PERSON>"], "Columns": ["<PERSON><PERSON><PERSON><PERSON>"], "System": ["Systeem"], "Clear": ["Wissen"], "Left": ["Links"], "Height": ["<PERSON><PERSON><PERSON>"], "Double": ["<PERSON><PERSON>"], "Dashed": ["Gestreept"], "Dotted": ["Gestippeld"], "Custom": ["Aangepast"], "Width": ["<PERSON><PERSON><PERSON>"], "Solid": ["Doorgetrokken"], "Border": ["Rand"], "Padding": ["Padding"], "Center": ["G<PERSON><PERSON>reerd"], "Bottom": ["<PERSON><PERSON>"], "Top": ["<PERSON><PERSON>"], "Layout": ["Lay-out"], "Justify": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Aspect Ratio": ["Aspect ratio"], "Lowercase": ["Kleine letters"], "Capitalize": ["Omzetten naar hoofdletters"], "Uppercase": ["<PERSON><PERSON><PERSON><PERSON>"], "Italic": ["Cursief"], "Right to left": ["Rechts naar links"]}}, "comment": {"reference": "assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js"}}