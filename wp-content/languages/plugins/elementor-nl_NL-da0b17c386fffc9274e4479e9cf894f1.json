{"translation-revision-date": "2025-09-09 15:35:40+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "nl"}, "Popup": ["Pop-up"], "location": ["locatie"], "locations": ["locaties"], "This class isn’t being used yet.": ["Deze klasse wordt nog niet gebru<PERSON>t."], "Locator": ["<PERSON><PERSON>"], "Show {{number}} {{locations}}": ["Toon {{number}} {{locations}}"], "Will permanently remove it from your project and may affect the design across all elements using it. Used %1 times across %2 pages. This action cannot be undone.": ["Zal het permanent uit je project verwijderen en kan het ontwerp beïnvloeden van alle elementen die het gebruiken. %1 keer gebruikt op %2 pagina's. Deze actie kan niet ongedaan worden gemaakt."], "You have unsaved changes": ["Je hebt niet opgeslagen wijzigingen"], "Stay here": ["<PERSON><PERSON><PERSON><PERSON> hier"], "Save changes": ["Wijzigingen opslaan"], "Save & Continue": ["Opslaan & doorgaan"], "Not now": ["Niet nu"], "Deleting": ["Aan het verwijderen"], "To open the Class Manager, save your page first. You can't continue without saving.": ["Om de klasse beheerder te openen, moet je je pagina eerst opslaan. Je kunt niet verder zonder op te slaan."], "To avoid losing your updates, save your changes before leaving.": ["Om te voorkomen dat je updates verloren gaan, moet je je wijzigingen opslaan voordat je vertrekt."], "There are no global classes yet.": ["<PERSON>r zijn nog geen globale klassen."], "CSS classes created in the editor panel will appear here. Once they are available, you can arrange their hierarchy, rename them, or delete them as needed.": ["CSS klassen die je in het editor paneel hebt gema<PERSON>t, versch<PERSON><PERSON><PERSON> hier. Zodra ze beschik<PERSON> zijn, kun je hun hiërar<PERSON>e ordenen, ze een andere naam geven of ze verwijderen als dat nodig is."], "You have unsaved changes in the Class Manager.": ["Je hebt niet-opgeslagen wijzigingen in de klasse manager."], "Delete this class?": ["Deze klasse verwijderen?"], "classes": ["klassen"], "class": ["klasse"], "Remember, when editing an item within a specific class, any changes you make will apply across all elements in that class.": ["Onthoud dat wanneer je een item in een specifieke klasse bewerkt, alle wijzigingen die je aanbrengt van toepassing zijn op alle elementen in die klasse."], "Will permanently remove it from your project and may affect the design across all elements using it. This action cannot be undone.": ["Zal het permanent uit je project verwijderen en kan het ontwerp beïnvloeden bij alle elementen die het gebruiken. Deze actie kan niet ongedaan worden gemaakt."], "The Class Manager lets you see all the classes you've created, plus adjust their priority, rename them, and delete unused classes to keep your CSS structured.": ["In de klasse beheerder kun je alle klassen zien die je hebt gemaakt, hun prioriteit aanpassen, hun naam wijzigen en ongebruikte klassen verwijderen om je CSS gestructureerd te houden."], "Clear & try again": ["Wis & probeer opnieuw"], "Try something else.": ["<PERSON><PERSON><PERSON> i<PERSON> anders."], "Class Manager": ["<PERSON><PERSON><PERSON> be<PERSON>"], "Sorry, nothing matched": ["<PERSON>r kwam niets overeen"], "Rename": ["<PERSON><PERSON><PERSON>"], "Footer": ["Footer"], "Header": ["Header"], "Something went wrong": ["Er is iets fout gegaan"], "Post": ["Bericht"], "More actions": ["Meer acties"], "Search": ["<PERSON><PERSON>"], "Page": ["<PERSON><PERSON><PERSON>"], "Delete": ["Verwijderen"], "Discard": ["Verwerpen"]}}, "comment": {"reference": "assets/js/packages/editor-global-classes/editor-global-classes.js"}}