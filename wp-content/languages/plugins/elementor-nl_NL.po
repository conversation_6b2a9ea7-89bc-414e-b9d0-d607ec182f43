# Translation of Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) in Dutch
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-09-09 15:35:40+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: nl\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)\n"

#: core/admin/menu/cloud-hosting-plans-menu-item.php:42
#: core/admin/menu/cloud-hosting-plans-menu-item.php:50
msgid "Hosting Plans"
msgstr "Hosting abonnementnen"

#: includes/widgets/heading.php:464
msgid "Make sure your page is structured with accessibility in mind. <PERSON> helps detect and fix common issues across your site."
msgstr "Zorg ervoor dat je pagina is gestructureerd met toegankelijkheid in gedachten. Ally helpt bij het opsporen en oplossen van veelvoorkomende problemen op je site."

#: core/utils/hints.php:449
msgid "Install Ally to add an accessibility widget visitors can use to navigate your site."
msgstr "Installeer Ally om een toegankelijkheidswidget toe te voegen die bezoekers kunnen gebruiken om door je site te navigeren."

#: core/utils/hints.php:452
msgid "Customize the widget's look, position and the capabilities available for your visitors."
msgstr "Pas het uiterlijk van de widget, de positie en de beschikbare rechten voor je bezoekers aan."

#: core/utils/hints.php:451
msgid "Connect the Ally plugin to your account to access all of it's accessibility features."
msgstr "Verbind de Ally plugin met je account om toegang te krijgen tot alle toegankelijkheidsfuncties."

#: core/utils/hints.php:450
msgid "Activate the Ally plugin to turn its accessibility features on across your site."
msgstr "Activeer de Ally plugin om de toegankelijkheidsfuncties in te schakelen op je site."

#: includes/widgets/heading.php:489
msgid "Accessible structure matters"
msgstr "Toegankelijke structuur is belangrijk"

#: includes/widgets/heading.php:479
msgid "Connect to Ally"
msgstr "Maak verbinding met Ally"

#: core/utils/hints.php:505
msgid "Ally web accessibility"
msgstr "Ally webtoegankelijkheid"

#: core/editor/loader/common/editor-common-scripts-settings.php:132
msgid "Ally Accessibility"
msgstr "Ally toegankelijkheid"

#. translators: %s: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:87
msgid "Toggle %s"
msgstr "Toggle %s"

#. translators: %s: Platform name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:270
msgid "Open %s"
msgstr "Open %s"

#. translators: %s: Maximum number of CTA links allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:316
msgid "Add up to %s CTA links"
msgstr "Voeg tot %s CTA links toe"

#: includes/widgets/alert.php:229
msgid "Side Border Color"
msgstr "Zijkant randkleur"

#: includes/widgets/alert.php:240
msgid "Side Border Width"
msgstr "Zijkant randbreedte"

#. translators: 1: Minimum items, 2: Items limit.
#: modules/floating-buttons/base/widget-contact-button-base.php:752
msgid "Add between %1$s to %2$s contact buttons"
msgstr "Voeg tussen %1$s en %2$s contactknoppen toe"

#. translators: %s: Items limit.
#: modules/floating-buttons/base/widget-contact-button-base.php:766
msgid "Add up to %s contact buttons"
msgstr "Voeg tot %s contactknoppen toe"

#. translators: %s: Maximum number of icons allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:572
msgid "Add up to %s icons"
msgstr "Voeg tot %s iconen toe"

#. translators: %s: Maximum number of images allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:228
msgid "Add up to %s Images"
msgstr "Voeg tot %s afbeeldingen toe"

#. translators: %s: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:112
#: modules/floating-buttons/classes/render/floating-bars-core-render.php:112
msgid "Close %s"
msgstr "Sluit %s"

#: app/modules/import-export-customization/runners/import/site-settings.php:168
#: app/modules/import-export/runners/import/site-settings.php:168
msgid "Failed to install theme: %1$s"
msgstr "Installatie van thema mislukt: %1$s"

#: app/modules/import-export-customization/runners/import/site-settings.php:161
#: app/modules/import-export/runners/import/site-settings.php:161
msgid "Theme: %1$s has already been installed and activated"
msgstr "Thema: %1$s is al geïnstalleerd en geactiveerd"

#: app/modules/import-export-customization/runners/import/site-settings.php:172
#: app/modules/import-export/runners/import/site-settings.php:172
msgid "Theme: %1$s has been successfully installed"
msgstr "Thema: %1$s is succesvol geïnstalleerd"

#: app/modules/import-export-customization/runners/import/site-settings.php:153
#: app/modules/import-export/runners/import/site-settings.php:153
msgid "Theme: %1$s is already used"
msgstr "Thema: %1$s is al in gebruik"

#: modules/variables/classes/rest-api.php:371
msgid "Variable label already exists"
msgstr "Variabele label bestaat al"

#: app/modules/import-export-customization/module.php:157
msgid "Open the Library"
msgstr "De bibliotheek openen"

#: app/modules/import-export-customization/module.php:148
msgid "Upload .zip file"
msgstr ".Zip bestand uploaden"

#: app/modules/import-export-customization/module.php:145
msgid "Apply a Website Template"
msgstr "Een Website Template toepassen"

#: app/app.php:289
msgid "Enable advanced customization options for import/export functionality."
msgstr "Inschakelen geavanceerde aanpassingsopties voor import/export functionaliteit."

#: app/app.php:288
msgid "Import/Export Customization"
msgstr "Importeren/exporteren aanpassing"

#: core/admin/admin-notices.php:494
msgid "Collecting leads is just the beginning. With Send by Elementor, you can manage contacts, launch automations, and turn form submissions into sales."
msgstr "Leads verzamelen is nog maar het begin. Met Send by Elementor kun je contacten beheren, automatiseringen starten en formulierinzendingen omzetten in verkopen."

#: core/admin/admin-notices.php:490
msgid "Turn leads into loyal shoppers"
msgstr "Verander leads in loyale shoppers"

#: modules/cloud-kit-library/connect/cloud-kits.php:117
msgid "Failed to create kit: No upload URL provided"
msgstr "Mislukt om kit te maken: geen upload URL opgegeven"

#: app/modules/import-export/module.php:147
msgid "Import website templates"
msgstr "Importeer Website Templates"

#: modules/cloud-kit-library/connect/cloud-kits.php:125
msgid "Failed to create kit: Content upload failed"
msgstr "Mislukt om kit te maken: inhoud uploaden mislukt"

#: app/modules/import-export/module.php:159
msgid "Import from library"
msgstr "Importeer uit bibliotheek"

#: app/modules/import-export-customization/module.php:226
#: app/modules/import-export/module.php:228
msgid "Remove Website Template"
msgstr "Site template verwijderen"

#: app/modules/import-export-customization/module.php:150
#: app/modules/import-export/module.php:152
msgid "You can import design and settings from a .zip file or choose from the library."
msgstr "Je kunt het ontwerp en de instellingen importeren uit een .zip bestand of kiezen uit de bibliotheek."

#: app/modules/import-export-customization/module.php:137
#: app/modules/import-export/module.php:139
msgid "Export this website"
msgstr "Deze site exporteren"

#: app/modules/import-export-customization/module.php:142
#: app/modules/import-export/module.php:144
msgid "You can download this website as a .zip file, or upload it to the library."
msgstr "Je kunt deze site downloaden als .zip bestand of uploaden naar de bibliotheek."

#: modules/cloud-kit-library/connect/cloud-kits.php:17
msgid "Cloud Kits"
msgstr "Cloud Kits"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:83
msgid "Lazy load"
msgstr "Lazy-load"

#: includes/widgets/common-base.php:160
msgid "Diamond"
msgstr "Diamant"

#: includes/widgets/common-base.php:164
msgid "Pentagon"
msgstr "Vijfhoek"

#: includes/widgets/common-base.php:176
msgid "Heptagon"
msgstr "Zevenhoek"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:84
msgid "Player controls"
msgstr "Speler besturingen"

#: core/settings/editor-preferences/model.php:161
msgid "Show launchpad checklist"
msgstr "Launchpad checklist weergeven"

#: modules/cloud-kit-library/module.php:144
msgid "Cloud-Kits is not instantiated."
msgstr "Cloud Kits zijn niet geïnitialiseerd."

#: modules/atomic-widgets/module.php:167
msgid "Enforce atomic widgets capabilities"
msgstr "Atomic widgets rechten afdwingen"

#: modules/global-classes/module.php:63
msgid "Enforce global classes capabilities"
msgstr "Globale klassen rechten afdwingen"

#: modules/atomic-widgets/module.php:168
msgid "Enforce atomic widgets capabilities."
msgstr "Atomic widgets rechten afdwingen."

#: modules/global-classes/module.php:64
msgid "Enforce global classes capabilities."
msgstr "Globale klassen rechten afdwingen."

#: modules/variables/classes/rest-api.php:214
msgid "Value cannot exceed %d characters"
msgstr "Waarde mag niet langer zijn dan %d tekens"

#: modules/cloud-kit-library/connect/cloud-kits.php:111
msgid "Failed to create kit: Invalid response"
msgstr "Kit aanmaken mislukt: ongeldige reactie"

#: core/common/modules/connect/rest/rest-api.php:92
msgid "Failed to connect to Elementor Library."
msgstr "Verbinden met Elementor bibliotheek mislukt."

#: core/common/modules/connect/rest/rest-api.php:163
msgid "Elementor Library app is not available."
msgstr "Elementor bibliotheek app is niet beschikbaar."

#: modules/variables/classes/rest-api.php:363
msgid "Reached the maximum number of variables"
msgstr "Het maximum aantal variabelen is bereikt"

#: modules/atomic-widgets/elements/has-atomic-base.php:134
msgid "ID"
msgstr "ID"

#: includes/widgets/common-base.php:180
msgid "Octagon"
msgstr "Achthoek"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:79
msgid "End time"
msgstr "Eindtijd"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:78
msgid "Start time"
msgstr "Begintijd"

#: includes/widgets/common-base.php:222
msgid "Custom Mask"
msgstr "Aangepast masker"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:75
msgid "YouTube URL"
msgstr "YouTube URL"

#: includes/widgets/common-base.php:192
msgid "Trapezoid Up"
msgstr "Trapezium omhoog"

#: includes/widgets/common-base.php:140
msgid "Oval vertical"
msgstr "Ovaal verticaal"

#: includes/widgets/common-base.php:148
msgid "Pill vertical"
msgstr "Pil verticaal"

#: includes/widgets/common-base.php:208
msgid "Hexagon Donut"
msgstr "Zeshoekige donut"

#: includes/widgets/common-base.php:196
msgid "Trapezoid Down"
msgstr "Trapezium omlaag"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:87
msgid "Related videos"
msgstr "Gerelateerde video's"

#: includes/widgets/common-base.php:144
msgid "Oval horizontal"
msgstr "Ovaal horizontaal"

#: includes/widgets/common-base.php:152
msgid "Pill horizontal"
msgstr "Pil horizontaal"

#: includes/widgets/common-base.php:168
msgid "Hexagon vertical"
msgstr "Zeshoek verticaal"

#: includes/widgets/common-base.php:172
msgid "Hexagon horizontal"
msgstr "Zeshoek horizontaal"

#: includes/widgets/common-base.php:188
msgid "Parallelogram left"
msgstr "Parallellogram links"

#: modules/variables/classes/rest-api.php:161
msgid "ID cannot be empty"
msgstr "ID kan niet leeg zijn"

#: modules/variables/classes/rest-api.php:379
msgid "Variable not found"
msgstr "Variabele niet gevonden"

#: includes/widgets/common-base.php:184
msgid "Parallelogram right"
msgstr "Parallellogram rechts"

#: modules/variables/classes/rest-api.php:188
msgid "Label cannot be empty"
msgstr "Label kan niet leeg zijn"

#: modules/variables/classes/rest-api.php:208
msgid "Value cannot be empty"
msgstr "Waarde kan niet leeg zijn"

#: modules/atomic-widgets/module.php:159
msgid "V4 Indications Popover"
msgstr "V4 indicaties popover"

#: modules/variables/classes/rest-api.php:386
msgid "Unexpected server error"
msgstr "Onverwachte serverfout"

#: modules/atomic-widgets/module.php:160
msgid "Enable V4 Indication Popovers"
msgstr "V4 indicatie popovers inschakelen"

#: modules/variables/classes/rest-api.php:194
msgid "Label cannot exceed %d characters"
msgstr "Label mag niet langer zijn dan %d tekens"

#: core/settings/editor-preferences/model.php:166
msgid "These will guide you through the first steps of creating your site."
msgstr "Deze leiden je door de eerste stappen van het maken van je site."

#: modules/variables/classes/rest-api.php:167
msgid "ID cannot exceed %d characters"
msgstr "ID kan niet langer zijn dan %d karakters"

#: core/admin/admin-notices.php:279
msgid "Opt in"
msgstr "Opt-in"

#: core/admin/admin-notices.php:218 includes/settings/settings-page.php:403
msgid "Become a super contributor by helping us understand how you use our service to enhance your experience and improve our product."
msgstr "Word een super bijdrager door ons te helpen begrijpen hoe je onze dienst gebruikt om je ervaring te verbeteren en ons product te verbeteren."

#: includes/settings/settings-page.php:396
msgid "Data Sharing"
msgstr "Gegevens delen"

#: core/admin/admin-notices.php:276
msgid "Update regarding usage data collection"
msgstr "Update over het verzamelen van gebruiksgegevens"

#: core/admin/admin-notices.php:234
msgid "Want to shape the future of web creation?"
msgstr "Wil je de toekomst van site creatie vormgeven?"

#: core/admin/admin-notices.php:272
msgid "We're updating our Terms and Conditions to include the collection of usage and behavioral data. This information helps us understand how you use Elementor so we can make informed improvements to the product."
msgstr "We updaten onze algemene voorwaarden om het verzamelen van gebruiks- en gedragsgegevens op te nemen. Deze informatie helpt ons te begrijpen hoe je Elementor gebruikt zodat we gerichte verbeteringen aan het product kunnen aanbrengen."

#: app/modules/import-export-customization/module.php:196
#: app/modules/import-export/module.php:198
msgid "Here’s where you can export this website as a .zip file, upload it to the cloud, or start the process of applying an existing template to your site."
msgstr "Hier kun je deze site exporteren als een .zip bestand, uploaden naar de cloud of beginnen met het toepassen van een bestaande template op je site."

#: app/modules/import-export-customization/module.php:219
#: app/modules/import-export/module.php:221
msgid "Remove the most recent Website Template"
msgstr "De meest recente Website Template verwijderen"

#: app/modules/import-export-customization/module.php:116
#: app/modules/import-export-customization/module.php:119
#: app/modules/import-export/module.php:120
#: app/modules/import-export/module.php:123
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:35 app/modules/kit-library/module.php:36
#: core/common/modules/finder/categories/general.php:78
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1708
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3900
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4646
msgid "Website Templates"
msgstr "Website Templates"

#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:75
msgid "Image resolution"
msgstr "Afbeelding resolutie"

#: includes/editor-templates/templates.php:166 assets/js/editor.js:8566
#: assets/js/editor.js:8579
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:117
msgid "Move"
msgstr "Verplaatsen"

#: includes/editor-templates/templates.php:317
#: includes/editor-templates/templates.php:408
msgid "Move to"
msgstr "Verplaatsen naar"

#: modules/variables/module.php:25
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:11
#: assets/js/packages/editor-variables/editor-variables.strings.js:12
#: assets/js/packages/editor-variables/editor-variables.strings.js:13
#: assets/js/packages/editor-variables/editor-variables.strings.js:25
msgid "Variables"
msgstr "Variabelen"

#: includes/editor-templates/templates.php:528
msgid "Site Templates"
msgstr "Site templates"

#: app/modules/kit-library/module.php:135
#: core/frontend/render-mode-manager.php:152
msgid "Not Authorized"
msgstr "Niet toegestaan"

#: includes/editor-templates/templates.php:147
msgid "List view"
msgstr "Lijstweergave"

#: includes/editor-templates/templates.php:553
#: includes/editor-templates/templates.php:569
#: includes/editor-templates/templates.php:583
msgid "Learn more about the"
msgstr "Lees verder over de"

#: includes/widgets/progress.php:154
msgid "Display Title"
msgstr "Toon titel"

#: includes/editor-templates/templates.php:500
msgid "Cloud Templates"
msgstr "Cloud templates"

#: includes/editor-templates/templates.php:143
msgid "Grid view"
msgstr "Raster weergave"

#: includes/editor-templates/templates.php:348
#: modules/cloud-library/module.php:77
msgid "Folder"
msgstr "Map"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:53
msgid "This is a title"
msgstr "Dit is een titel"

#: includes/editor-templates/templates.php:321
#: includes/editor-templates/templates.php:412
msgid "Copy to"
msgstr "Kopieer naar"

#: includes/editor-templates/templates.php:534
msgid "You’ve saved 100% of the templates in your plan."
msgstr "Je hebt 100% van de templates in je plan opgeslagen."

#: includes/template-library/sources/cloud.php:305
msgid "You do not have permission to create preview documents."
msgstr "Je hebt geen toestemming om voorbeelddocumenten te maken."

#: modules/promotions/pointers/birthday.php:35
msgid "View Deals"
msgstr "Aanbiedingen bekijken"

#: includes/editor-templates/templates.php:277
#: includes/editor-templates/templates.php:386
msgid "Upgrade to get more storage space or delete old templates to make room."
msgstr "Upgrade om meer opslagruimte te krijgen of verwijder oude templates om ruimte te maken."

#: includes/controls/url.php:68
#: modules/atomic-widgets/controls/types/link-control.php:23
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:76
msgid "Type or paste your URL"
msgstr "Typ of plak je URL"

#. translators: %s is the "Upgrade now" link
#: includes/editor-templates/templates.php:538
msgid "To get more space %s"
msgstr "Om meer ruimte te krijgen %s"

#: modules/cloud-library/connect/cloud-library.php:199
msgid "Failed to save preview."
msgstr "Opslaan voorbeeld mislukt."

#: includes/editor-templates/templates.php:114
#: includes/editor-templates/templates.php:632
msgid "Site templates"
msgstr "Site templates"

#: core/admin/admin-notices.php:536
msgid "Make sure your site has an accessibility statement page"
msgstr "Zorg ervoor dat je site een pagina met een toegankelijkheidsverklaring heeft"

#: modules/global-classes/global-classes-rest-api.php:177
msgid "Global classes limit exceeded. Maximum allowed: %d"
msgstr "Limiet voor globale klassen overschreden. Maximaal toegestaan: %d"

#: modules/variables/module.php:26
msgid "Enable variables. (For this feature to work - Atomic Widgets must be active)"
msgstr "Variabelen inschakelen. (Om deze functie te laten werken moeten Atomic Widgets actief zijn)"

#: modules/cloud-library/module.php:143 assets/js/editor.js:9816
msgid "Then you can find all your templates in one convenient library."
msgstr "Dan kun je al je templates in één handige bibliotheek vinden."

#: includes/editor-templates/templates.php:493
msgid "Give your template a name"
msgstr "Geef je template een naam"

#: modules/atomic-opt-in/module.php:23
msgid "Enable the settings Opt In page"
msgstr "De instellingen opt in pagina inschakelen"

#: modules/cloud-library/documents/cloud-template-preview.php:44
msgid "Cloud Template Previews"
msgstr "Cloud template voorbeelden"

#: modules/cloud-library/documents/cloud-template-preview.php:40
msgid "Cloud Template Preview"
msgstr "Cloud template voorbeeld"

#: modules/atomic-widgets/opt-in.php:42
msgid "Enable Editor V4."
msgstr "Editor V4 inschakelen."

#: modules/atomic-opt-in/module.php:22
msgid "Editor v4 (Opt In Page)"
msgstr "Editor v4 (Opt-in pagina)"

#: core/admin/admin-notices.php:537
msgid "Create a more inclusive site experience for all your visitors. With Ally, it's easy to add your statement page in just a few clicks."
msgstr "Creëer een meer inclusieve site ervaring voor alle je bezoekers. Met Ally is het eenvoudig om je statement pagina toe te voegen in slechts een paar klikken."

#: modules/cloud-library/module.php:142 assets/js/editor.js:9815
msgid "Connect to your Elementor account"
msgstr "Maak verbinding met je Elementor account"

#: modules/promotions/pointers/birthday.php:31
msgid "Celebrate Elementor’s birthday with us—exclusive deals are available now."
msgstr "Vier de verjaardag van Elementor met ons-exclusieve aanbiedingen zijn nu beschikbaar."

#: modules/promotions/pointers/birthday.php:30
msgid "Elementor’s 9th Birthday sale!"
msgstr "Elementor's 9e verjaardag uitverkoop!"

#: includes/editor-templates/templates.php:118
#: includes/editor-templates/templates.php:636
msgid "Cloud templates"
msgstr "Cloud templates"

#: includes/managers/elements.php:280
msgid "Atomic Elements"
msgstr "Atomic elementen"

#: modules/cloud-library/connect/cloud-library.php:221
msgid "Failed to mark preview as failed."
msgstr "Voorbeeld als mislukt markeren mislukt."

#: modules/cloud-library/module.php:89
msgid "Cloud Templates and Website Templates empowers you to save and manage design elements across all your projects. This feature is associated and connected to your Elementor Pro account and can be accessed from any website associated with your account."
msgstr "Cloud Templates en Website Templates stellen je in staat om ontwerpelementen op te slaan en te beheren in al je projecten. Deze functie is gekoppeld aan je Elementor Pro account en kan worden benaderd vanaf elke site die aan je account is gekoppeld."

#: includes/controls/gallery.php:126 includes/controls/media.php:322
msgid "This image isn't optimized. You need to connect your Image Optimizer account first."
msgstr "Deze afbeelding is niet geoptimaliseerd. Je moet eerst je Image Optimizer account verbinden."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/floating-buttons/module.php:352 modules/landing-pages/module.php:236
msgid "Or view %1$sTrashed Items%2$s"
msgstr "Of bekijk %1$sNaar prullenbak verplaatste items%2$s"

#: includes/editor-templates/templates.php:297
msgid "Open"
msgstr "Openen"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:49
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:66
msgid "Type your paragraph here"
msgstr "Typ je paragraaf hier"

#: includes/template-library/sources/cloud.php:23
msgid "Cloud-Library is not instantiated."
msgstr "Cloud bibliotheek is niet geïnitialiseerd."

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:32
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:65
msgid "Paragraph"
msgstr "Paragraaf"

#: includes/template-library/sources/cloud.php:122
msgid "New Folder"
msgstr "Nieuwe map"

#: includes/template-library/sources/cloud.php:36
#: modules/cloud-library/connect/cloud-library.php:15
#: modules/cloud-library/module.php:88
msgid "Cloud Library"
msgstr "Cloud bibliotheek"

#: includes/settings/tools.php:314
msgid "Elementor Cache"
msgstr "Elementor cache"

#: includes/editor-templates/templates.php:138
msgid "Create a New Folder"
msgstr "Maak een nieuwe map"

#: includes/controls/media.php:307
msgid "Image size settings don’t apply to Dynamic Images."
msgstr "Instellingen voor afbeeldingsgrootte zijn niet van toepassing op dynamische afbeeldingen."

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:67
msgid "Type your button text here"
msgstr "Typ je knoptekst hier"

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:66
msgid "Button text"
msgstr "Knoptekst"

#: includes/controls/gallery.php:127 includes/controls/media.php:323
msgid "Connect Now"
msgstr "Verbind nu"

#: includes/settings/tools.php:317 modules/admin-bar/module.php:148
msgid "Clear Files & Data"
msgstr "Bestanden & gegevens wissen"

#: includes/settings/tools.php:318
msgid "Clear outdated CSS files and cached data in the database (rendered HTML, JS/CSS assets, etc.). We'll regenerate those files the next time someone visits any page on your website."
msgstr "Wis verouderde CSS-bestanden en gecachete gegevens in de database (gerenderde HTML, JS/CSS assets, etc.). We zullen deze bestanden opnieuw genereren wanneer iemand de eerstvolgende keer een pagina op je site bezoekt."

#: includes/template-library/sources/local.php:1766
msgid "Sorry, you are not allowed to do that."
msgstr "Je hebt geen toestemming om dat te doen."

#: includes/elements/container.php:1453 includes/widgets/common-base.php:433
msgid "Row Span"
msgstr "Rij spanwijdte"

#: includes/elements/container.php:1409 includes/widgets/common-base.php:389
msgid "Column Span"
msgstr "Kolom span"

#: includes/widgets/video.php:438
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:85
msgid "Captions"
msgstr "Bijschriften"

#: modules/nested-tabs/widgets/nested-tabs.php:182
msgid "Add Tab"
msgstr "Tab toevoegen"

#: includes/managers/elements.php:298
msgid "Hello+"
msgstr "Hello+"

#: includes/elements/container.php:1401 includes/widgets/common-base.php:381
msgid "Grid Item"
msgstr "Raster item"

#: modules/ai/site-planner-connect/module.php:50
msgid "To connect your site to Site Planner, you need to generate an app password."
msgstr "Om je site te verbinden met site planner, moet je een app-wachtwoord genereren."

#: modules/element-cache/module.php:140
msgid "Element Cache"
msgstr "Elementen cache"

#: includes/widgets/image-gallery.php:303
msgid "Custom Gap"
msgstr "Aangepast ruimte"

#: modules/ai/site-planner-connect/module.php:49
msgid "Connect to Site Planner"
msgstr "Verbinding maken met site planner"

#: modules/ai/module.php:225 modules/ai/module.php:260
msgid "Animate With AI"
msgstr "Animeren met AI"

#: modules/ai/site-planner-connect/module.php:51
msgid "Approve & Connect"
msgstr "Goedkeuren & verbinden"

#: modules/atomic-widgets/elements/div-block/div-block.php:34
#: modules/library/documents/div-block.php:52 assets/js/editor.js:8485
msgid "Div Block"
msgstr "Div blok"

#: modules/promotions/promotion-data.php:93
msgid "Design Custom Carousels"
msgstr "Ontwerp aangepaste carrousels"

#: modules/promotions/promotion-data.php:45
msgid "Apply rotating effects to text."
msgstr "Pas roterende effecten toe op tekst."

#: modules/promotions/promotion-data.php:46
msgid "Fully customize your headlines."
msgstr "Pas je kopregels volledig aan."

#: modules/promotions/promotion-data.php:62
msgid "Adjust layout and playback settings."
msgstr "Pas lay-out en afspeelinstellingen aan."

#: modules/promotions/promotion-data.php:110
msgid "Upgrade Your Testimonials"
msgstr "Upgrade je aanbevelingen"

#: modules/promotions/promotion-data.php:95
msgid "Create flexible custom carousels."
msgstr "Maak flexibele aangepaste carrousels."

#: modules/promotions/promotion-data.php:78
msgid "Combine text, buttons, and images."
msgstr "Combineer tekst, knoppen en afbeeldingen."

#: modules/promotions/promotion-data.php:80
msgid "Create unique, interactive designs."
msgstr "Maak unieke, interactieve ontwerpen."

#: modules/promotions/promotion-data.php:114
msgid "Customize layouts for visual appeal."
msgstr "Pas lay-outs aan voor visuele aantrekkingskracht."

#: modules/promotions/promotion-data.php:76
msgid "Boost Conversions with CTAs"
msgstr "Verhoog conversies met CTA's"

#: modules/promotions/promotion-data.php:61
msgid "Embed videos with full control."
msgstr "Video's insluiten met volledige controle."

#: modules/promotions/promotion-data.php:63
msgid "Seamlessly customize video appearance."
msgstr "Pas de weergave van video's naadloos aan."

#: modules/promotions/promotion-data.php:96
msgid "Adjust transitions and animations."
msgstr "Pas overgangen en animaties aan."

#: modules/promotions/promotion-data.php:113
msgid "Boost credibility with dynamic testimonials."
msgstr "Vergroot geloofwaardigheid met dynamische aanbevelingen."

#: modules/promotions/promotion-data.php:112
msgid "Display reviews in a rotating carousel."
msgstr "Toon beoordelingen in een roterende carrousel."

#: modules/promotions/promotion-data.php:97
msgid "Showcase multiple items with style."
msgstr "Toon meerdere items met stijl."

#: modules/promotions/promotion-data.php:44
msgid "Highlight key messages dynamically."
msgstr "Markeer belangrijke berichten dynamisch."

#: modules/promotions/promotion-data.php:59
msgid "Showcase Video Playlists"
msgstr "Toon video afspeellijsten"

#: includes/widgets/image-carousel.php:153
msgid "Carousel Name"
msgstr "Naam carrousel"

#: modules/promotions/promotion-data.php:42
msgid "Bring Headlines to Life"
msgstr "Breng kopregels tot leven"

#: modules/ai/feature-intro/product-image-unification-intro.php:34
msgid "New! Unify pack-shots with Elementor AI"
msgstr "Nieuw! Verenig productafbeeldingen met Elementor AI"

#: includes/widgets/image-carousel.php:664
msgid "Space Between Dots"
msgstr "Ruimte tussen stippen"

#: modules/ai/module.php:321 assets/js/ai-unify-product-images.js:16715
msgid "Unify with Elementor AI"
msgstr "Verenigen met Elementor AI"

#: modules/ai/module.php:417
msgid "Image added successfully"
msgstr "Afbeelding succesvol toegevoegd"

#: modules/ai/feature-intro/product-image-unification-intro.php:35
msgid "Now you can process images in bulk and standardized the background and ratio - no manual editing required!"
msgstr "Nu kun je afbeeldingen in bulk verwerken en de achtergrond en verhouding standaardiseren - geen handmatig bewerken vereist!"

#: modules/global-classes/module.php:55
msgid "Enable global CSS classes."
msgstr "Globale CSS klassen inschakelen."

#: modules/global-classes/module.php:54
msgid "Global Classes"
msgstr "Globale klassen"

#: modules/promotions/promotion-data.php:79
msgid "Add hover animations and CSS effects."
msgstr "Voeg hover animaties en CSS effecten toe."

#: modules/cloud-library/connect/cloud-library.php:340
msgid "Not connected"
msgstr "Niet verbonden"

#: core/admin/admin-notices.php:611
msgid "Use Elementor's Site Mailer to ensure your store emails like purchase confirmations, shipping updates and more are reliably delivered."
msgstr "Gebruik Elementor's site mailer om ervoor te zorgen dat je winkel e-mails zoals aankoopbevestigingen, updates van de verzending en meer betrouwbaar worden afgeleverd."

#: core/admin/admin-notices.php:610
msgid "Improve Transactional Email Deliverability"
msgstr "Afleverbaarheid van transactionele e-mails verbeteren"

#: core/admin/admin-notices.php:589
msgid "Ensure your form emails avoid the spam folder!"
msgstr "Zorg ervoor dat je formulier e-mails de spam map vermijden!"

#: core/admin/admin-notices.php:590
msgid "Use Site Mailer for improved email deliverability, detailed email logs, and an easy setup."
msgstr "Gebruik Site Mailer voor verbeterde e-mail bezorging, gedetailleerde e-mail logs en een eenvoudige setup."

#: modules/checklist/steps/setup-header.php:70
msgid "Add a header"
msgstr "Een header toevoegen"

#: modules/checklist/steps/setup-header.php:62
msgid "Set up a header"
msgstr "Een header instellen"

#: modules/checklist/steps/set-fonts-and-colors.php:31
msgid "Global colors and fonts ensure a cohesive look across your site. Start by defining one color and one font."
msgstr "Globale kleuren en lettertypen zorgen voor een samenhangend uiterlijk op je hele site. Begin met het definiëren van één kleur en één lettertype."

#: modules/checklist/steps/set-fonts-and-colors.php:27
msgid "Set up your Global Fonts & Colors"
msgstr "Stel je globale lettertypen & kleuren in"

#: modules/checklist/steps/assign-homepage.php:31
msgid "Assign homepage"
msgstr "Homepage toewijzen"

#: modules/checklist/steps/assign-homepage.php:27
msgid "Before your launch, make sure to assign a homepage so visitors have a clear entry point into your site."
msgstr "Voordat je lanceert, zorg ervoor dat je een homepage toewijst zodat bezoekers een duidelijk toegangspunt hebben tot je site."

#: modules/checklist/steps/assign-homepage.php:23
msgid "Assign a homepage"
msgstr "Een homepage toewijzen"

#: modules/checklist/steps/add-logo.php:33
#: modules/checklist/steps/set-fonts-and-colors.php:35
msgid "Go to Site Identity"
msgstr "Ga naar site identiteit"

#: modules/checklist/steps/add-logo.php:29
msgid "Let's start by adding your logo and filling in the site identity settings. This will establish your initial presence and also improve SEO."
msgstr "Laten we beginnen met het toevoegen van je logo en het invullen van de site identiteit instellingen. Dit zorgt voor je eerste aanwezigheid en verbetert ook de SEO."

#: modules/checklist/steps/add-logo.php:25
msgid "Add your logo"
msgstr "Je logo toevoegen"

#: core/experiments/manager.php:344
msgid "Create advanced layouts and responsive designs with %1$sFlexbox%2$s and %3$sGrid%4$s container elements. Give it a try using the %5$sContainer playground%6$s."
msgstr "Maak geavanceerde lay-outs en responsive ontwerpen met %1$sFlexbox%2$s en %3$sRaster%4$s container elementen. Probeer het eens met behulp van de %5$sContainer playground%6$s."

#: modules/checklist/steps/setup-header.php:66
msgid "This element applies across different pages, so visitors can easily navigate around your site."
msgstr "Dit element geldt voor verschillende pagina's, zodat bezoekers gemakkelijk door je site kunnen navigeren."

#: modules/floating-buttons/widgets/floating-bars-var-1.php:25
msgid "Floating Bar CTA"
msgstr "Zwevende balk CTA"

#: modules/floating-buttons/module.php:47 assets/js/editor.js:51664
msgid "Floating Bars"
msgstr "Zwevende balken"

#: modules/floating-buttons/documents/floating-buttons.php:203
#: modules/floating-buttons/module.php:342
msgid "Floating Element"
msgstr "Zwevend element"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1259
msgid "Headline"
msgstr "Kopregel"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1213
msgid "Element spacing"
msgstr "Afstand tussen elementen"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1183
msgid "Align Elements"
msgstr "Elementen uitlijnen"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1049
msgid "Horizontal position"
msgstr "Horizontale positie"

#: modules/floating-buttons/base/widget-floating-bars-base.php:339
msgid "Enter your text"
msgstr "Voer je tekst in"

#: modules/floating-buttons/base/widget-floating-bars-base.php:315
msgid "Headlines"
msgstr "Kopregels"

#: modules/floating-buttons/base/widget-floating-bars-base.php:240
msgid "Pause Icon"
msgstr "Pauzeer icoon"

#: modules/floating-buttons/base/widget-floating-bars-base.php:228
#: modules/floating-buttons/base/widget-floating-bars-base.php:1130
msgid "Pause and Play"
msgstr "Pauzeren en afspelen"

#: modules/floating-buttons/base/widget-floating-bars-base.php:219
#: modules/floating-buttons/base/widget-floating-bars-base.php:1173
msgid "Floating Bar"
msgstr "Zwevende balk"

#: modules/floating-buttons/base/widget-floating-bars-base.php:199
msgid "Accessible Name"
msgstr "Toegankelijke naam"

#: modules/floating-buttons/base/widget-floating-bars-base.php:157
msgid "Shop now"
msgstr "Nu winkelen"

#: modules/floating-buttons/base/widget-floating-bars-base.php:156
#: modules/floating-buttons/base/widget-floating-bars-base.php:204
msgid "Enter text"
msgstr "Voer tekst in"

#: modules/floating-buttons/base/widget-floating-bars-base.php:143
#: modules/floating-buttons/base/widget-floating-bars-base.php:555
msgid "CTA Button"
msgstr "CTA knop"

#: modules/floating-buttons/base/widget-floating-bars-base.php:131
msgid "Enter your text here"
msgstr "Voer hier je tekst in"

#: modules/floating-buttons/base/widget-floating-bars-base.php:105
#: modules/floating-buttons/base/widget-floating-bars-base.php:391
msgid "Announcement"
msgstr "Aankondiging"

#: modules/floating-buttons/base/widget-floating-bars-base.php:69
msgid "Banner"
msgstr "Banner"

#: modules/floating-buttons/base/widget-floating-bars-base.php:64
msgid "Just in! Cool summer tees"
msgstr "Zojuist binnen! Coole zomer T-shirts"

#: modules/floating-buttons/base/widget-contact-button-base.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:722
msgid "Add accessible name"
msgstr "Toegankelijke naam toevoegen"

#: modules/floating-buttons/base/widget-contact-button-base.php:463
#: modules/floating-buttons/base/widget-contact-button-base.php:719
msgid "Accessible name"
msgstr "Toegankelijke naam"

#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:22
#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:26
#: modules/floating-buttons/documents/floating-buttons.php:207
msgid "Floating Elements"
msgstr "Zwevende elementen"

#: modules/checklist/steps/create-pages.php:32
msgid "Jumpstart your creation with professional designs from the Template Library or start from scratch."
msgstr "Start je creatie met professionele ontwerpen uit de template bibliotheek of begin helemaal opnieuw."

#: modules/checklist/steps/create-pages.php:28
msgid "Create your first 3 pages"
msgstr "Maak je eerste 3 pagina's"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:80
msgid "Tag"
msgstr "Tag"

#: modules/atomic-widgets/module.php:149
msgid "Enable atomic widgets."
msgstr "Atomic widgets inschakelen."

#: modules/atomic-widgets/module.php:148
msgid "Atomic Widgets"
msgstr "Atomic widgets"

#: modules/ai/preferences.php:73
msgid "Enable Elementor AI functionality"
msgstr "Elementor AI functionaliteit inschakelen"

#: modules/ai/preferences.php:61
msgid "Elementor - AI"
msgstr "Elementor - AI"

#: includes/settings/settings.php:466
msgid "Improve initial page load performance by lazy loading all background images except the first one."
msgstr "Verbeter de initiële laadprestaties van de pagina door alle achtergrondafbeeldingen, behalve de eerste, te lazy-loaden."

#: includes/settings/settings.php:336
msgid "Personalize the way Elementor works on your website by choosing the advanced features and how they operate."
msgstr "Personaliseer de manier waarop Elementor op je site werkt door de geavanceerde functies te kiezen en hoe ze werken."

#: includes/settings/settings.php:259
msgid "Tailor how Elementor enhances your site, from post types to other functions."
msgstr "Bepaal zelf hoe Elementor je site verbetert, van berichttypen tot andere functies."

#: includes/admin-templates/new-floating-elements.php:50
msgid "Create Floating Element"
msgstr "Zwevend element maken"

#: includes/admin-templates/new-floating-elements.php:31
msgid "Choose Floating Element"
msgstr "Zwevend element kiezen"

#: includes/admin-templates/new-floating-elements.php:21
msgid "Use floating elements to engage your visitors and increase conversions."
msgstr "Gebruik zwevende elementen om je bezoekers te betrekken en conversies te verhogen."

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-floating-elements.php:16
msgid "Floating Elements Help You %1$sWork Efficiently%2$s"
msgstr "Zwevende elementen helpen je %1$sefficiënt te werken%2$s"

#: core/debug/classes/shop-page-edit.php:23
msgid "Sorry, The content area was not been found on your page"
msgstr "Het inhoudsgebied is niet gevonden op je pagina"

#: core/debug/classes/shop-page-edit.php:15
msgid "You are trying to edit the Shop Page although it is a Product Archive. Use the Theme Builder to create your Shop Archive template instead"
msgstr "Je probeert de winkelpagina te bewerken, hoewel het een productarchief is. Gebruik de themabouwer om in plaats daarvan je winkel archief template te maken"

#: modules/checklist/steps/create-pages.php:36
msgid "Create a new page"
msgstr "Maak een nieuwe pagina"

#: modules/link-in-bio/widgets/link-in-bio.php:28
msgid "Minimalist"
msgstr "Minimalistisch"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1545
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:138
msgid "Dimensions"
msgstr "Afmetingen"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:334
msgid "CTA link"
msgstr "CTA link"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:285
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:82
msgid "Add item"
msgstr "Item toevoegen"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:243
msgid "Images Per Row"
msgstr "Afbeeldingen per rij"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:215
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1026
msgid "Image Links"
msgstr "Afbeelding links"

#: modules/floating-buttons/widgets/contact-buttons.php:26
msgid "Single Chat"
msgstr "Enkelvoudige chat"

#: modules/floating-buttons/module.php:344
msgid "Add a Floating element so your users can easily get in touch!"
msgstr "Voeg een zwevende knop toe zodat je gebruikers gemakkelijk contact kunnen opnemen!"

#: modules/floating-buttons/module.php:277
msgid "Entire Site"
msgstr "Gehele site"

#: modules/floating-buttons/module.php:191
msgid "Instances"
msgstr "Instanties"

#: modules/floating-buttons/documents/floating-buttons.php:195
msgid "Set as Entire Site"
msgstr "Instellen als gehele site"

#: modules/floating-buttons/documents/floating-buttons.php:188
msgid "Remove From Entire Site"
msgstr "Van gehele site verwijderen"

#: modules/floating-buttons/documents/floating-buttons.php:32
msgid "After publishing this widget, you will be able to set it as visible on the entire site in the Admin Table."
msgstr "Nadat je deze widget hebt gepubliceerd, kun je deze zichtbaar maken op de gehele site in de beheer tabel."

#: modules/floating-buttons/module.php:188
msgid "Click Tracking"
msgstr "Volgen van klikken"

#: modules/announcements/module.php:111
msgid "<p>With AI for text, code, image generation and editing, you can bring your vision to life faster than ever. Start your free trial now - <b>no credit card required!</b></p>"
msgstr "<p>Met AI voor het genereren en bewerken van tekst, code en afbeeldingen kun je je visie sneller dan ooit tot leven brengen. Start nu je gratis proefversie - <b>geen creditcard nodig!</b></p>"

#: modules/announcements/module.php:110
msgid "Discover your new superpowers "
msgstr "Ontdek je nieuwe superkrachten "

#: modules/floating-buttons/classes/render/contact-buttons-core-render.php:58
msgid "Links window"
msgstr "Links venster"

#: modules/floating-buttons/base/widget-contact-button-base.php:3082
#: modules/floating-buttons/base/widget-floating-bars-base.php:1488
msgid "CSS"
msgstr "CSS"

#: modules/floating-buttons/base/widget-contact-button-base.php:3069
#: modules/floating-buttons/base/widget-floating-bars-base.php:1475
msgid "Responsive visibility will take effect only on preview mode or live page, and not while editing in Elementor."
msgstr "Responsive zichtbaarheid is alleen van kracht in de voorbeeld modus of live-pagina, en niet tijdens het bewerken in Elementor."

#: modules/floating-buttons/base/widget-contact-button-base.php:3045
msgid "Full Width on Mobile"
msgstr "Volledige breedte op mobiel"

#: modules/floating-buttons/base/widget-contact-button-base.php:2481
#: modules/floating-buttons/base/widget-contact-button-base.php:2501
msgid "Text and Icon Color"
msgstr "Tekst en icoon kleur"

#: modules/floating-buttons/base/widget-contact-button-base.php:2440
msgid "Link Spacing"
msgstr "Link afstand"

#: modules/floating-buttons/base/widget-contact-button-base.php:2390
msgid "Info Links"
msgstr "Info links"

#: modules/floating-buttons/base/widget-contact-button-base.php:2256
msgid "Resource Links"
msgstr "Gegevensbronnen links"

#: modules/floating-buttons/base/widget-contact-button-base.php:2226
msgid "Adjust transition duration to change the speed of the <b>hover animation on desktop</b> and the <b>click animation on touchscreen</b>."
msgstr "Pas de overgangsduur aan om de snelheid van de <b>hoveren animatie op desktop</b> en de <b>klik animatie op touchscreen</b> te veranderen."

#: modules/floating-buttons/base/widget-contact-button-base.php:2152
msgid "Button Bar"
msgstr "Knoppenbalk"

#: modules/floating-buttons/base/widget-contact-button-base.php:2107
msgid "Tooltips"
msgstr "Tooltips"

#: modules/floating-buttons/base/widget-contact-button-base.php:2056
msgid "Buttons Spacing"
msgstr "Knoppen afstand"

#: modules/floating-buttons/base/widget-contact-button-base.php:1830
msgid "Bubble Background Color"
msgstr "Bubbel achtergrondkleur"

#: modules/floating-buttons/base/widget-contact-button-base.php:1487
msgid "Hover animation is <b>desktop only</b>"
msgstr "Hoveren animatie is <b>alleen voor desktop</b>"

#: modules/floating-buttons/base/widget-contact-button-base.php:828
msgid "Enter description"
msgstr "Voer beschrijving in"

#: modules/floating-buttons/base/widget-contact-button-base.php:813
msgid "Enter title"
msgstr "Voer titel in"

#: modules/floating-buttons/base/widget-contact-button-base.php:736
msgid "Start conversation:"
msgstr "Start gesprek:"

#: modules/floating-buttons/base/widget-contact-button-base.php:691
msgid "Typing Animation"
msgstr "Typen animatie"

#: modules/floating-buttons/base/widget-contact-button-base.php:626
msgid "Active Dot"
msgstr "Actieve stip"

#: modules/floating-buttons/base/widget-contact-button-base.php:552
msgid "Enter the text"
msgstr "Voer de tekst in"

#: modules/floating-buttons/base/widget-contact-button-base.php:546
#: modules/floating-buttons/base/widget-contact-button-base.php:734
#: modules/floating-buttons/base/widget-contact-button-base.php:1876
msgid "Call to Action Text"
msgstr "Call To Action tekst"

#: modules/floating-buttons/base/widget-contact-button-base.php:537
msgid "Call to Action"
msgstr "Call To Action"

#: modules/floating-buttons/base/widget-contact-button-base.php:532
msgid "Display Text"
msgstr "Weergavetekst"

#: modules/floating-buttons/base/widget-contact-button-base.php:516
msgid "Notification Dot"
msgstr "Melding stip"

#: modules/floating-buttons/base/widget-contact-button-base.php:405
#: modules/floating-buttons/base/widget-contact-button-base.php:967
#: modules/link-in-bio/base/widget-link-in-bio-base.php:516
#: modules/link-in-bio/base/widget-link-in-bio-base.php:762
msgid "Paste Waze link"
msgstr "Waze link plakken"

#: modules/floating-buttons/base/widget-contact-button-base.php:146
msgid "Tooltip"
msgstr "Tooltip"

#: modules/floating-buttons/base/widget-contact-button-base.php:86
msgid "Call now"
msgstr "Nu bellen"

#: modules/floating-buttons/module.php:46 assets/js/editor.js:51666
#: assets/js/editor.js:51668
msgid "Floating Buttons"
msgstr "Zwevende knoppen"

#: modules/editor-events/module.php:49
msgid "Editor events processing"
msgstr "Editor gebeurtenissen verwerking"

#: modules/editor-events/module.php:48
msgid "Elementor Editor Events"
msgstr "Elementor editor gebeurtenissen"

#. translators: 1: `<a>` opening tag, 2: `</a>` closing tag.
#: includes/widgets/video.php:364
msgid "Note: Autoplay is affected by %1$s Google’s Autoplay policy %2$s on Chrome browsers."
msgstr "Let op: autoplay wordt beïnvloed door %1$s Google's autoplay beleid %2$s op Chrome browsers."

#: includes/widgets/image-box.php:409 includes/widgets/image.php:386
msgid "Scale Down"
msgstr "Afschalen"

#: includes/editor-templates/panel.php:131
msgid "Copy and Share Link"
msgstr "Link kopiëren en delen"

#: includes/controls/repeater.php:194
msgid "In a Repeater control, if you specify a minimum number of items, you must also specify a default value that contains at least that number of items."
msgstr "Als je in een herhaler besturing een minimum aantal items opgeeft, moet je ook een standaardwaarde opgeven die ten minste dat aantal items bevat."

#: core/kits/documents/tabs/settings-background.php:75
msgid "Overscroll Behavior"
msgstr "Overscroll gedrag"

#: core/document-types/page-base.php:189
msgid "No %s found in Trash."
msgstr "Geen %s gevonden in prullenbak."

#: core/document-types/page-base.php:188
msgid "No %s found."
msgstr "Geen %s gevonden."

#: core/document-types/page-base.php:187
msgid "Search %s"
msgstr "%s zoeken"

#: core/document-types/page-base.php:185
msgid "New %s"
msgstr "Nieuwe %s"

#: core/document-types/page-base.php:181
msgid "All %s"
msgstr "Alle %s"

#: modules/floating-buttons/base/widget-contact-button-base.php:536
msgid "Contact Details"
msgstr "Contactdetails"

#. translators: %s: <head> tag.
#: includes/settings/settings.php:423
msgid "Internal Embedding places all CSS in the %s which works great for troubleshooting, while External File uses external CSS file for better performance (recommended)."
msgstr "Intern ingesloten plaatst alle CSS in de %s, wat geweldig werkt voor het oplossen van problemen, terwijl Extern bestand een extern CSS bestand gebruikt voor betere prestaties (aanbevolen)."

#: modules/element-cache/module.php:157
msgid "Specify the duration for which data is stored in the cache. Elements caching speeds up loading by serving pre-rendered copies of elements, rather than rendering them fresh each time. This control ensures efficient performance and up-to-date content."
msgstr "Geef de duur op waarvoor gegevens in de cache worden opgeslagen. Het cachen van elementen versnelt het laden door vooraf gerenderde kopieën van elementen aan te bieden, in plaats van ze elke keer opnieuw te renderen. Deze bediening zorgt voor efficiënte prestaties en actuele inhoud."

#: modules/element-cache/module.php:155
msgid "1 Year"
msgstr "1 jaar"

#: modules/element-cache/module.php:154
msgid "1 Month"
msgstr "1 maand"

#: modules/element-cache/module.php:153
msgid "2 Weeks"
msgstr "2 weken"

#: modules/element-cache/module.php:152
msgid "1 Week"
msgstr "1 week"

#: modules/element-cache/module.php:151
msgid "3 Days"
msgstr "3 dagen"

#: modules/element-cache/module.php:150
msgid "1 Day"
msgstr "1 dag"

#: modules/element-cache/module.php:149
msgid "12 Hours"
msgstr "12 uren"

#: modules/element-cache/module.php:148
msgid "6 Hours"
msgstr "6 uur"

#: modules/element-cache/module.php:147
msgid "1 Hour"
msgstr "1 uur"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:25
msgid "Create Forms and Collect Leads %s with Elementor Pro"
msgstr "Maak formulieren en verzamel leads %s met Elementor Pro"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:25
msgid "Enjoy creative freedom %s with Custom Icons"
msgstr "Geniet van creatieve vrijheid %s met aangepaste iconen"

#: modules/element-cache/module.php:119
msgid "Cache Settings"
msgstr "Cache instellingen"

#: modules/element-cache/module.php:48
msgid "Elements caching reduces loading times by serving up a copy of an element instead of rendering it fresh every time the page is loaded. When active, Elementor will determine which elements can benefit from static loading - but you can override this."
msgstr "Element caching vermindert de laadtijd door een kopie van een element aan te bieden in plaats van elke keer als de pagina wordt geladen opnieuw te renderen. Als het actief is, bepaalt Elementor welke elementen kunnen profiteren van statisch laden - maar je kunt dit overschrijven."

#: modules/element-cache/module.php:46
msgid "Element Caching"
msgstr "Element caching"

#: includes/managers/elements.php:306
msgid "Link In Bio"
msgstr "Link in bio"

#: modules/floating-buttons/base/widget-contact-button-base.php:141
msgid "Contact Buttons"
msgstr "Contact knoppen"

#: core/base/traits/shared-widget-controls-trait.php:107
msgid "Icons Per Row"
msgstr "Iconen per rij"

#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:222
msgid "Powered by Elementor"
msgstr "Aangedreven door Elementor"

#: core/base/providers/social-network-provider.php:240
msgid "Skype"
msgstr "Skype"

#: core/base/providers/social-network-provider.php:234
msgid "Viber"
msgstr "Viber"

#: core/base/providers/social-network-provider.php:228
msgid "SMS"
msgstr "SMS"

#: core/base/providers/social-network-provider.php:222
msgid "File Download"
msgstr "Bestand downloaden"

#: core/base/providers/social-network-provider.php:204
msgid "Telephone"
msgstr "Telefoon"

#: core/base/providers/social-network-provider.php:198
msgid "Messenger"
msgstr "Messenger"

#: core/base/providers/social-network-provider.php:192
msgid "Waze"
msgstr "Waze"

#: core/base/providers/social-network-provider.php:180
msgid "Dribbble"
msgstr "Dribbble"

#: core/base/providers/social-network-provider.php:174
msgid "Behance"
msgstr "Behance"

#: core/base/providers/social-network-provider.php:162
msgid "Spotify"
msgstr "Spotify"

#: core/base/providers/social-network-provider.php:156
msgid "Apple Music"
msgstr "Apple Music"

#: core/base/providers/social-network-provider.php:150
msgid "WhatsApp"
msgstr "WhatsApp"

#: core/base/providers/social-network-provider.php:144
msgid "TikTok"
msgstr "TikTok"

#: core/base/providers/social-network-provider.php:132
msgid "Pinterest"
msgstr "Pinterest"

#: core/base/providers/social-network-provider.php:126
msgid "LinkedIn"
msgstr "LinkedIn"

#: core/base/providers/social-network-provider.php:120
msgid "Instagram"
msgstr "Instagram"

#: core/base/providers/social-network-provider.php:114
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: core/base/providers/social-network-provider.php:108
msgid "Facebook"
msgstr "Facebook"

#: core/base/providers/social-network-provider.php:102
msgid "Save contact (vCard)"
msgstr "Contact opslaan (vCard)"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1738
msgid "Bottom Border"
msgstr "Onderste rand"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1034
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1710
msgid "Image Height"
msgstr "Afbeelding hoogte"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1674
msgid "Image Shape"
msgstr "Afbeeldingsvorm"

#: modules/floating-buttons/base/widget-contact-button-base.php:2525
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1199
msgid "Dividers"
msgstr "Verdelers"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:977
msgid "Profile"
msgstr "Profiel"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:973
msgid "Image style"
msgstr "Afbeeldingsstijl"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:914
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1247
msgid "Identity"
msgstr "Identiteit"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:886
msgid "About Me"
msgstr "Over mij"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:885
msgid "About"
msgstr "Over"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:880
msgid "About Heading"
msgstr "Over koptekst"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:864
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1323
msgid "Title or Tagline"
msgstr "Titel of slogan"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:855
msgid "Sara Parker"
msgstr "Sara Parker"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:841
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1287
msgid "Bio"
msgstr "Bio"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1629
msgid "Apply Full Screen Height on"
msgstr "Volledig scherm hoogte toepassen op"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1612
msgid "Full Screen Height"
msgstr "Hoogte volledig scherm"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1565
msgid "Layout Width"
msgstr "Lay-out breedte"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:800
msgid "Add Icon"
msgstr "Icoon toevoegen"

#: modules/floating-buttons/base/widget-contact-button-base.php:147
#: modules/link-in-bio/base/widget-link-in-bio-base.php:590
msgid "Enter icon text"
msgstr "Voer icoon tekst in"

#: modules/floating-buttons/base/widget-contact-button-base.php:2264
#: modules/link-in-bio/base/widget-link-in-bio-base.php:559
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1384
#: assets/js/app.js:6344
msgid "Icons"
msgstr "Iconen"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:545
msgid "Add CTA Link"
msgstr "CTA link toevoegen"

#: modules/floating-buttons/base/widget-contact-button-base.php:920
#: modules/link-in-bio/base/widget-link-in-bio-base.php:534
#: modules/link-in-bio/base/widget-link-in-bio-base.php:783
msgid "Enter your username"
msgstr "Voer je gebruikersnaam in"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:495
msgid "Enter your number"
msgstr "Voer je nummer in"

#: modules/floating-buttons/base/widget-contact-button-base.php:838
#: modules/link-in-bio/base/widget-link-in-bio-base.php:440
#: modules/link-in-bio/base/widget-link-in-bio-base.php:682
msgid "Enter your email"
msgstr "Voer je e-mail in"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:429
msgid "Mail"
msgstr "E-mail"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:419
#: modules/link-in-bio/base/widget-link-in-bio-base.php:650
msgid "Enter your link"
msgstr "Voer je link in"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:356
msgid "Link Type"
msgstr "Link type"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:335
msgid "Enter link text"
msgstr "Voer linktekst in"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:303
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1075
msgid "CTA Link Buttons"
msgstr "CTA link knoppen"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:97
msgid "Healthy Living Resources"
msgstr "Gegevensbronnen voor gezond leven"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:94
msgid "Meal Prep"
msgstr "Maaltijd voorbereiden"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:91
msgid "Top 10 Recipes"
msgstr "Top 10 recepten"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:88
msgid "Get Healthy"
msgstr "Word gezond"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:55
msgid "Join me on my journey to a healthier lifestyle"
msgstr "Ga met me mee op mijn reis naar een gezondere levensstijl"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:52
msgid "Kitchen Chronicles"
msgstr "Keuken kronieken"

#: modules/floating-buttons/base/widget-contact-button-base.php:2776
msgid "Close Animation"
msgstr "Animatie sluiten"

#: modules/floating-buttons/base/widget-contact-button-base.php:2766
msgid "Open Animation"
msgstr "Open animatie"

#: modules/floating-buttons/base/widget-contact-button-base.php:2087
#: modules/floating-buttons/base/widget-contact-button-base.php:2178
#: modules/floating-buttons/base/widget-contact-button-base.php:2871
#: modules/floating-buttons/base/widget-floating-bars-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:120
msgid "Sharp"
msgstr "Scherp"

#: modules/floating-buttons/base/widget-contact-button-base.php:2081
#: modules/floating-buttons/base/widget-contact-button-base.php:2172
#: modules/floating-buttons/base/widget-contact-button-base.php:2865
#: modules/floating-buttons/base/widget-floating-bars-base.php:840
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1159
msgid "Corners"
msgstr "Hoeken"

#: modules/floating-buttons/base/widget-contact-button-base.php:247
msgid "Chat Box"
msgstr "Chatbox"

#: modules/floating-buttons/base/widget-contact-button-base.php:1799
msgid "Time"
msgstr "Tijd"

#: modules/floating-buttons/base/widget-contact-button-base.php:1657
msgid "Close Button Color"
msgstr "Knopkleur sluiten"

#: modules/floating-buttons/base/widget-contact-button-base.php:1643
#: modules/floating-buttons/base/widget-floating-bars-base.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:1036
msgid "Close Button"
msgstr "Sluiten knop"

#: modules/floating-buttons/base/widget-contact-button-base.php:737
#: modules/floating-buttons/base/widget-contact-button-base.php:1047
msgid "Type your text here"
msgstr "Typ je tekst hier"

#: modules/floating-buttons/base/widget-contact-button-base.php:186
msgid "Click to start chat"
msgstr "Klik om chat te starten"

#: modules/floating-buttons/base/widget-contact-button-base.php:183
msgid "Send Button"
msgstr "Verzend knop"

#: modules/floating-buttons/base/widget-contact-button-base.php:682
msgid "14:20"
msgstr "14:20"

#: modules/floating-buttons/base/widget-contact-button-base.php:681
msgid "2:20 PM"
msgstr "2:20 PM"

#: modules/floating-buttons/base/widget-contact-button-base.php:669
msgid "Hey, how can I help you today?"
msgstr "Hoe kan ik je vandaag helpen?"

#: modules/floating-buttons/base/widget-contact-button-base.php:655
msgid "Rob"
msgstr "Rob"

#: modules/floating-buttons/base/widget-contact-button-base.php:645
#: modules/floating-buttons/base/widget-contact-button-base.php:1714
msgid "Message Bubble"
msgstr "Bericht bubbel"

#: modules/floating-buttons/base/widget-contact-button-base.php:613
#: modules/floating-buttons/base/widget-contact-button-base.php:1532
msgid "Profile Image"
msgstr "Profielafbeelding"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:69
#: modules/floating-buttons/base/widget-contact-button-base.php:131
msgid "Type your title here"
msgstr "Typ je titel hier"

#: modules/floating-buttons/base/widget-contact-button-base.php:130
msgid "Store Manager"
msgstr "Winkelmanager"

#: modules/floating-buttons/base/widget-contact-button-base.php:123
#: modules/floating-buttons/base/widget-contact-button-base.php:656
msgid "Type your name here"
msgstr "Typ je naam hier"

#: modules/floating-buttons/base/widget-contact-button-base.php:122
msgid "Rob Jones"
msgstr "Rob Jones"

#: modules/floating-buttons/base/widget-contact-button-base.php:380
#: modules/floating-buttons/base/widget-contact-button-base.php:982
msgid "Action"
msgstr "Actie"

#: modules/floating-buttons/base/widget-contact-button-base.php:342
#: modules/floating-buttons/base/widget-contact-button-base.php:896
#: modules/link-in-bio/base/widget-link-in-bio-base.php:737
msgid "+"
msgstr "+"

#: modules/floating-buttons/base/widget-contact-button-base.php:321
#: modules/floating-buttons/base/widget-contact-button-base.php:663
#: modules/floating-buttons/base/widget-contact-button-base.php:670
#: modules/floating-buttons/base/widget-contact-button-base.php:872
#: modules/floating-buttons/base/widget-contact-button-base.php:874
#: modules/floating-buttons/base/widget-contact-button-base.php:1768
#: modules/link-in-bio/base/widget-link-in-bio-base.php:465
#: modules/link-in-bio/base/widget-link-in-bio-base.php:476
#: modules/link-in-bio/base/widget-link-in-bio-base.php:716
#: modules/link-in-bio/base/widget-link-in-bio-base.php:718
msgid "Message"
msgstr "Bericht"

#: modules/floating-buttons/base/widget-contact-button-base.php:305
#: modules/floating-buttons/base/widget-contact-button-base.php:857
#: modules/floating-buttons/base/widget-contact-button-base.php:859
#: modules/link-in-bio/base/widget-link-in-bio-base.php:447
#: modules/link-in-bio/base/widget-link-in-bio-base.php:458
#: modules/link-in-bio/base/widget-link-in-bio-base.php:701
#: modules/link-in-bio/base/widget-link-in-bio-base.php:703
msgid "Subject"
msgstr "Onderwerp"

#: modules/floating-buttons/base/widget-contact-button-base.php:294
msgid "@"
msgstr "@"

#: core/base/providers/social-network-provider.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:836
#: modules/link-in-bio/base/widget-link-in-bio-base.php:680
msgid "Email"
msgstr "E-mail"

#: modules/floating-buttons/base/widget-contact-button-base.php:479
#: modules/floating-buttons/base/widget-contact-button-base.php:779
#: modules/link-in-bio/base/widget-link-in-bio-base.php:598
msgid "Platform"
msgstr "Platform"

#: modules/floating-buttons/base/widget-contact-button-base.php:65
msgid "Chat Button"
msgstr "Chat knop"

#: modules/apps/admin-apps-page.php:126
msgid "Cannot Install"
msgstr "Kan niet installeren"

#: modules/apps/admin-apps-page.php:119 modules/apps/admin-apps-page.php:150
msgid "Cannot Activate"
msgstr "Kan niet activeren"

#: includes/widgets/traits/button-trait.php:300
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:163
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:209
msgid "Space between"
msgstr "Tussenruimte"

#: includes/settings/settings.php:454
msgid "Reduce unnecessary render-blocking loads by dequeuing unused Gutenberg block editor scripts and styles."
msgstr "Verminder onnodige renderblokkerende belastingen door ongebruikte Gutenberg blok-editor scripts en stijlen te dequeuen."

#. translators: 1: fetchpriority attribute, 2: lazy loading attribute.
#: includes/settings/settings.php:439
msgid "Improve performance by applying %1$s on LCP image and %2$s on images below the fold."
msgstr "Verbeter de prestaties door %1$s toe te passen op LCP afbeelding en %2$s op afbeeldingen onder de fold."

#: core/settings/editor-preferences/model.php:212
msgid "Decide where you want to go when leaving the editor."
msgstr "Bepaal waar je naartoe wil als je de editor verlaat."

#: core/settings/editor-preferences/model.php:188
#: modules/styleguide/module.php:129
msgid "Temporarily overlay the canvas with the style guide to preview your changes to global colors and fonts."
msgstr "Overlap het canvas tijdelijk met de stijlgids om een voorbeeld te zien van je wijzigingen in de globale kleuren en lettertypes."

#: core/settings/editor-preferences/model.php:183
#: modules/styleguide/module.php:127
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:441
msgid "Show global settings"
msgstr "Globale instellingen weergeven"

#: core/settings/editor-preferences/model.php:144
msgid "This refers to elements you’ve hidden in the Responsive Visibility settings."
msgstr "Dit verwijst naar elementen die je hebt verborgen in de instellingen voor responsive zichtbaarheid."

#: includes/settings/settings.php:407
msgid "Improve loading times on your site by selecting the optimization tools that best fit your requirements."
msgstr "Verbeter de laadtijden op je site door het gereedschap voor optimalisatie te selecteren die het beste aan je vereisten voldoen."

#: modules/floating-buttons/base/widget-contact-button-base.php:677
msgid "Time format"
msgstr "Tijdnotatie"

#: core/admin/admin.php:1022 includes/controls/gallery.php:122
#: includes/controls/media.php:318
msgid "Optimize your images to enhance site performance by using Image Optimizer."
msgstr "Optimaliseer je afbeeldingen om de prestaties van je site te verbeteren met Image Optimizer."

#: core/settings/editor-preferences/model.php:132
msgid "This only applies while you’re working in the editor. The front end won’t be affected."
msgstr "Dit geldt alleen als je in de editor werkt. De front-end wordt niet beïnvloed."

#: core/settings/editor-preferences/model.php:127
msgid "Expand images in lightbox"
msgstr "Afbeeldingen vergroten in lightbox"

#: core/settings/editor-preferences/model.php:116
msgid "Show quick edit options"
msgstr "Snel bewerken opties weergeven"

#: core/settings/editor-preferences/model.php:83
msgid "Set light or dark mode, or auto-detect to sync with your operating system settings."
msgstr "Stel de lichte of donkere modus in, of automatische detectie om te synchroniseren met de instellingen van je besturingssysteem."

#: core/settings/editor-preferences/model.php:74
msgid "Dark mode"
msgstr "Donkere modus"

#: core/settings/editor-preferences/model.php:70
msgid "Light mode"
msgstr "Lichte modus"

#: core/settings/editor-preferences/model.php:66
msgid "Display mode"
msgstr "Weergavemodus"

#: core/settings/editor-preferences/model.php:58
msgid "Panel"
msgstr "Paneel"

#: core/experiments/manager.php:369
msgid "Optimized Markup"
msgstr "Geoptimaliseerde markup"

#: core/settings/editor-preferences/model.php:139
msgid "Show hidden elements"
msgstr "Verborgen elementen weergeven"

#: core/settings/editor-preferences/model.php:120
msgid "Show additional actions while hovering over the handle of an element."
msgstr "Toon extra acties bij hoveren over de handle van een element."

#: core/experiments/manager.php:371
msgid "Reduce the DOM size by eliminating HTML tags in various elements and widgets. This experiment includes markup changes so it might require updating custom CSS/JS code and cause compatibility issues with third party plugins."
msgstr "Verminder de DOM grootte door HTML tags te verwijderen uit verschillende elementen en widgets. Dit experiment bevat aanpassingen in de mark-up, dus het kan nodig zijn om aangepaste CSS/JS code te updaten en het kan compatibiliteitsproblemen veroorzaken met plugins van derden."

#: core/admin/admin.php:349
msgid "Discounted Upgrades Now!"
msgstr "Nu korting op upgrades!"

#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:33
msgid "Upgrade Sale Now"
msgstr "Nu upgrade uitverkoop"

#: core/admin/admin-notices.php:708
msgid "Speed up your website with Image Optimizer by Elementor"
msgstr "Versnel je site met Image Optimizer van Elementor"

#: core/admin/admin-notices.php:709
msgid "Automatically compress and optimize images, resize larger files, or convert to WebP. Optimize images individually, in bulk, or on upload."
msgstr "Comprimeer en optimaliseer afbeeldingen automatisch, verklein grotere bestanden of converteer naar WebP. Optimaliseer afbeeldingen afzonderlijk, in bulk of bij het uploaden."

#: modules/home/<USER>
msgid "Default Elementor menu page."
msgstr "Standaard Elementor menu pagina."

#: modules/home/<USER>
msgid "Elementor Home Screen"
msgstr "Elementor home scherm"

#: includes/widgets/counter.php:452
msgid "Number Gap"
msgstr "Getal ruimte"

#: includes/widgets/counter.php:424
msgid "Number Alignment"
msgstr "Getal uitlijning"

#: includes/widgets/counter.php:388
msgid "Number Position"
msgstr "Getal positie"

#: includes/widgets/counter.php:372
msgid "Title Gap"
msgstr "Titel ruimte"

#: includes/widgets/counter.php:343
msgid "Title Vertical Alignment"
msgstr "Titel verticale uitlijning"

#: includes/widgets/counter.php:314
msgid "Title Horizontal Alignment"
msgstr "Titel horizontale uitlijning"

#: includes/widgets/counter.php:275
msgid "Title Position"
msgstr "Titel positie"

#: includes/settings/settings.php:215
msgid "Home"
msgstr "Home"

#: elementor.php:96
msgid "Elementor isn’t running because WordPress is outdated."
msgstr "Elementor wordt niet uitgevoerd omdat WordPress verouderd is."

#. translators: %s: PHP version.
#. translators: %s: WordPress version.
#: elementor.php:75 elementor.php:99
msgid "Update to version %s and get back to creating!"
msgstr "Update naar versie %s en ga weer aan de slag met creëren!"

#: elementor.php:72
msgid "Elementor isn’t running because PHP is outdated."
msgstr "Elementor wordt niet uitgevoerd omdat PHP verouderd is."

#: core/files/uploads-manager.php:589
msgid "You do not have permission to upload JSON files."
msgstr "Je hebt geen toestemming om JSON bestanden te uploaden."

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:123
msgid "Install"
msgstr "Installeer"

#: core/admin/admin.php:1024
msgid "Image Optimizer"
msgstr "Afbeelding optimizer"

#: modules/site-navigation/rest-fields/page-user-can.php:28
msgid "Whether the current user can edit or delete this post"
msgstr "Of de huidige gebruiker dit bericht kan bewerken of verwijderen"

#: modules/shapes/widgets/text-path.php:149
msgid "Want to create custom text paths with SVG?"
msgstr "Wil je aangepaste tekst paden maken met SVG?"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:31
msgid "Remain GDPR compliant with Custom Fonts that let you disable %s Google Fonts from your website"
msgstr "Blijf AVG/GDPR conform met aangepaste lettertypen waarmee je %s Google Fonts van je site kunt uitschakelen"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:28
msgid "Upload any font to keep your website true to your brand"
msgstr "Upload een willekeurig lettertype om je site trouw te houden aan je merk"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:23
msgid "Stay on brand with a Custom Font"
msgstr "Blijf bij je merk met een aangepast lettertype"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:30
msgid "Leverage Elementor AI to instantly generate Custom Code for Elementor"
msgstr "Gebruik Elementor AI om direct aangepaste code voor Elementor te genereren"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:29
msgid "Use Custom Code to create sophisticated custom interactions to engage visitors"
msgstr "Gebruik aangepaste code om geavanceerde aangepaste interacties te maken om bezoekers te betrekken"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:28
msgid "Add Custom Code snippets anywhere on your website, including the header or footer to measure your page’s performance*"
msgstr "Aangepaste code snippets overal op je site toevoegen, inclusief de header of footer om de prestaties van je pagina te meten*"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:37
msgid "Add any icon, anywhere on your website"
msgstr "Voeg elk icoon toe, overal op je site"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:34
msgid "Expand your icon library beyond FontAwesome and add icon %s libraries of your choice"
msgstr "Breid je icoon bibliotheek uit tot meer dan FontAwesome en voeg icoon %s bibliotheken naar je keuze toe"

#: modules/apps/admin-pointer.php:35
msgid "Explore Add-ons"
msgstr "Add-ons ontdekken"

#: modules/apps/admin-pointer.php:29
msgid "New! Popular Add-ons"
msgstr "Nieuw! Populaire add-ons"

#: modules/apps/admin-menu-apps.php:22 modules/apps/admin-menu-apps.php:26
#: modules/apps/module.php:37 assets/js/admin-top-bar.js:183
#: assets/js/editor.js:38612
msgid "Add-ons"
msgstr "Add-ons"

#: modules/apps/admin-apps-page.php:35
msgid "Please note that certain tools and services on this page are developed by third-party companies and are not part of Elementor's suite of products or support. Before using them, we recommend independently evaluating them. Additionally, when clicking on their action buttons, you may be redirected to an external website."
msgstr "Let op dat bepaalde gereedschappen en diensten op deze pagina zijn ontwikkeld door externe bedrijven en maken geen deel uit van het pakket producten of ondersteuning van Elementor. Voordat je ze gebruikt, raden we aan om ze onafhankelijk te evalueren. Bovendien, wanneer je op hun actieknoppen klikt, word je mogelijk omgeleid naar een externe site."

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:23
msgid "Enjoy Creative Freedom with Custom Code"
msgstr "Geniet van creatieve vrijheid met aangepaste code"

#: modules/apps/module.php:70
msgid "For Elementor"
msgstr "Voor Elementor"

#: includes/widgets/text-editor.php:191
msgid "10"
msgstr "10"

#: includes/widgets/text-editor.php:190
msgid "9"
msgstr "9"

#: includes/widgets/text-editor.php:189
msgid "8"
msgstr "8"

#: includes/widgets/text-editor.php:188
msgid "7"
msgstr "7"

#: includes/widgets/text-editor.php:187
msgid "6"
msgstr "6"

#: includes/widgets/text-editor.php:186
msgid "5"
msgstr "5"

#: includes/widgets/text-editor.php:185
msgid "4"
msgstr "4"

#: includes/widgets/text-editor.php:184
msgid "3"
msgstr "3"

#: includes/widgets/text-editor.php:183
msgid "2"
msgstr "2"

#: includes/widgets/text-editor.php:182
msgid "1"
msgstr "1"

#: includes/widgets/icon-box.php:359 includes/widgets/image-box.php:324
msgid "Content Spacing"
msgstr "Inhoudafstand"

#: includes/widgets/common-base.php:1140
msgid "Explore additional Premium Shape packs and use them in your site."
msgstr "Ontdek extra Premium vorm pakketten en gebruik ze in je site."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:344
msgid "It is strongly recommended to %1$sbackup the database%2$s before using replacing URLs."
msgstr "Het wordt sterk aangeraden om %1$seen back-up van de database%2$s te maken voordat je vervangende URLs gebruikt."

#: core/role-manager/role-manager.php:215
msgid "Giving broad access to edit the HTML widget can pose a security risk to your website because it enables users to run malicious scripts, etc."
msgstr "Het geven van brede toegang om de HTML widget te bewerken kan een beveiligingsrisico vormen voor je site omdat het gebruikers in staat stelt kwaadaardige scripts uit te voeren, enzovoort."

#: core/role-manager/role-manager.php:213
msgid "Enable the option to use the HTML widget"
msgstr "Schakel de optie in om de HTML widget te gebruiken"

#: includes/controls/base-units.php:138
msgid "Custom unit"
msgstr "Aangepaste eenheid"

#: includes/editor-templates/panel-elements.php:33
msgid "Access all Pro widgets."
msgstr "Toegang tot alle Pro widgets."

#: core/utils/hints.php:167 includes/controls/notice.php:83
msgid "Don’t show again."
msgstr "Niet meer tonen."

#: includes/editor-templates/navigator.php:16
msgid "Access all Pro widgets"
msgstr "Toegang tot alle Pro widgets"

#: includes/managers/controls.php:1319
#: modules/floating-buttons/base/widget-floating-bars-base.php:1453
#: assets/js/editor.js:53688
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:144
msgid "Sticky"
msgstr "Sticky"

#: includes/managers/controls.php:1310 assets/js/editor.js:53673
msgid "Mouse Effects"
msgstr "Muis effecten"

#: includes/managers/controls.php:1301 assets/js/editor.js:53658
msgid "Scrolling Effects"
msgstr "Scrol effecten"

#: core/admin/admin-notices.php:661 includes/controls/gallery.php:123
#: includes/controls/media.php:319 includes/widgets/heading.php:473
msgid "Install Plugin"
msgstr "Installeer plugin"

#: includes/widgets/video.php:130
msgid "Get the Video Playlist widget and grow your toolbox with Elementor Pro."
msgstr "Verkrijg de Video Playlist widget en breid je gereedschapskist uit met Elementor Pro."

#: includes/widgets/video.php:129
msgid "Grab your visitors' attention"
msgstr "Trek de aandacht van je bezoekers"

#: includes/widgets/button.php:105
msgid "Get the Call to Action widget and grow your toolbox with Elementor Pro."
msgstr "Haal de Call To Action widget op en breid je gereedschapskist uit met Elementor Pro."

#: includes/widgets/button.php:104
msgid "Convert visitors into customers"
msgstr "Verander bezoekers in klanten"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:35
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:48
msgid "* Requires an Advanced subscription or higher"
msgstr "* Geavanceerd abonnement of hoger vereist"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:35
msgid "Collect lead submissions directly within your WordPress Admin to manage, analyze and perform bulk actions on the submitted lead*"
msgstr "Verzamel lead inzendingen rechtstreeks binnen je WordPress beheer om de ingediende leads te beheren, analyseren en er bulkacties op uit te voeren*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:34
msgid "Integrate your favorite marketing software*"
msgstr "Integreer je favoriete marketing software*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:33
msgid "Use any field to collect the information you need"
msgstr "Gebruik elk veld om de informatie te verzamelen die je nodig hebt"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:32
msgid "Create single or multi-step forms to engage and convert visitors"
msgstr "Maak formulieren met één of meerdere stappen om bezoekers te betrekken en te converteren"

#: includes/managers/controls.php:1270 assets/js/editor.js:53643
msgid "Display Conditions"
msgstr "Weergave voorwaarden"

#: includes/widgets/testimonial.php:197
msgid "Designer"
msgstr "Ontwerper"

#: includes/widgets/testimonial.php:182
msgid "John Doe"
msgstr "John Doe"

#: includes/editor-templates/templates.php:457
msgid "Generate Variations"
msgstr "Variaties genereren"

#: includes/controls/groups/background.php:162
msgid "Set locations and angle for each breakpoint to ensure the gradient adapts to different screen sizes."
msgstr "Stel kleuren, locaties en hoeken in voor elk breakpoint om ervoor te zorgen dat de gradiënt zich aanpast aan verschillende schermgrootten."

#: includes/template-library/sources/local.php:826
msgid "You do not have permission to export this template."
msgstr "Je hebt geen toestemming om deze template te exporteren."

#: includes/template-library/manager.php:409
#: includes/template-library/manager.php:552
#: includes/template-library/sources/local.php:822
msgid "You do not have permission to access this template."
msgstr "Je hebt geen toestemming om toegang te krijgen tot deze template."

#: includes/template-library/sources/local.php:817
msgid "Invalid template type or template does not exist."
msgstr "Ongeldig template type of template bestaat niet."

#: core/utils/hints.php:459
msgid "install Now"
msgstr "installeer nu"

#: core/role-manager/role-manager.php:197
msgid "Giving broad access to upload JSON files can pose a security risk to your website because such files may contain malicious scripts, etc."
msgstr "Brede toegang geven tot het uploaden van JSON-bestanden kan een veiligheidsrisico vormen voor je site, omdat zulke bestanden kwaadaardige scripts, etc. kunnen bevatten."

#: core/role-manager/role-manager.php:197
#: core/role-manager/role-manager.php:215
msgid "Heads up"
msgstr "Opgelet"

#: core/role-manager/role-manager.php:195
msgid "Enable the option to upload JSON files"
msgstr "Schakel de optie in om JSON bestanden te uploaden"

#: core/files/uploads-manager.php:290
msgid "Invalid file name."
msgstr "Ongeldige bestandsnaam."

#: includes/editor-templates/templates.php:176
#: assets/js/element-manager-admin.js:663
#: assets/js/element-manager-admin.js:732
msgid "Usage"
msgstr "Gebruik"

#: modules/element-manager/ajax.php:127
msgid "WordPress Widgets"
msgstr "WordPress widgets"

#: modules/element-manager/ajax.php:154
msgid "Unexpected elements data."
msgstr "Onverwachte elementengegevens."

#: modules/element-manager/ajax.php:148
msgid "No elements to save."
msgstr "Geen elementen om op te slaan."

#: modules/element-manager/ajax.php:117
msgid "Invalid nonce."
msgstr "Ongeldige nonce."

#: modules/element-manager/ajax.php:112
msgid "You do not have permission to edit these settings."
msgstr "Je hebt geen toestemming om deze instellingen te bewerken."

#: includes/template-library/sources/local.php:238
msgid "Parent Template:"
msgstr "Hoofd template:"

#: includes/template-library/sources/local.php:237
msgid "No Templates found in Trash"
msgstr "Geen templates gevonden in prullenbak"

#: includes/template-library/sources/local.php:236
msgid "No Templates found"
msgstr "Geen templates gevonden"

#: includes/template-library/sources/local.php:235
msgid "Search Template"
msgstr "Template zoeken"

#: includes/template-library/sources/local.php:234
msgid "View Template"
msgstr "Template bekijken"

#: includes/template-library/sources/local.php:233
msgid "All Templates"
msgstr "Alle templates"

#: includes/template-library/sources/local.php:229
#: includes/template-library/sources/local.php:230
msgid "Add New Template"
msgstr "Nieuwe template toevoegen"

#: includes/controls/groups/image-size.php:296 includes/controls/media.php:297
#: includes/widgets/testimonial.php:328
msgid "Image Resolution"
msgstr "Afbeelding resolutie"

#: includes/controls/groups/flex-container.php:187
msgid "No Wrap"
msgstr "Geen terugloop"

#: includes/controls/groups/flex-container.php:183
#: includes/controls/groups/flex-container.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:152
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:154
msgid "Wrap"
msgstr "Terugloop"

#: core/common/modules/finder/categories/settings.php:79
#: modules/element-manager/admin-menu-app.php:22
#: modules/element-manager/admin-menu-app.php:26
#: modules/element-manager/admin-menu-app.php:35
msgid "Element Manager"
msgstr "Element beheerder"

#. translators: %s: Widget title.
#: modules/promotions/widgets/pro-widget-promotion.php:57
msgid "This result includes the Elementor Pro %s widget. Upgrade now to unlock it and grow your web creation toolkit."
msgstr "Dit resultaat bevat de Elementor Pro %s widget. Upgrade nu om deze te ontgrendelen en je toolkit voor web creatie uit te breiden."

#: includes/widgets/image-gallery.php:108 includes/widgets/testimonial.php:114
msgid "Use interesting masonry layouts and other overlay features with Elementor's Pro Gallery widget."
msgstr "Gebruik interessante masonry lay-outs en andere overlay functies met de Pro galerij widget van Elementor."

#: includes/widgets/heading.php:159
msgid "Create captivating headings that rotate with the Animated Headline Widget."
msgstr "Maak pakkende kopteksten die roteren met de geanimeerde koptekst widget."

#: core/experiments/manager.php:361
msgid "Container-based content will be hidden from your site and may not be recoverable in all cases."
msgstr "Op containers gebaseerde inhoud wordt op je site verborgen en kan mogelijk niet in alle gevallen worden hersteld."

#: includes/widgets/image-carousel.php:128
msgid "Gain complete freedom to design every slide with Elementor\"s Pro Carousel."
msgstr "Krijg volledige vrijheid om elke slide te ontwerpen met Elementor's Pro carrousel."

#: includes/editor-templates/templates.php:539 assets/js/ai-admin.js:1035
#: assets/js/ai-admin.js:1082 assets/js/ai-admin.js:2492
#: assets/js/ai-admin.js:3219 assets/js/ai-gutenberg.js:1173
#: assets/js/ai-gutenberg.js:1220 assets/js/ai-gutenberg.js:2630
#: assets/js/ai-gutenberg.js:3357 assets/js/ai-layout.js:733
#: assets/js/ai-layout.js:780 assets/js/ai-media-library.js:1035
#: assets/js/ai-media-library.js:1082 assets/js/ai-media-library.js:2492
#: assets/js/ai-media-library.js:3219 assets/js/ai-unify-product-images.js:1035
#: assets/js/ai-unify-product-images.js:1082
#: assets/js/ai-unify-product-images.js:2492
#: assets/js/ai-unify-product-images.js:3219 assets/js/ai.js:1823
#: assets/js/ai.js:1870 assets/js/ai.js:3280 assets/js/ai.js:4007
#: assets/js/editor.js:8244 assets/js/editor.js:8246 assets/js/editor.js:9823
#: assets/js/editor.js:9824
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4265
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:467
msgid "Upgrade now"
msgstr "Upgrade nu"

#. translators: %s: Recommended PHP version.
#: modules/system-info/reporters/server.php:131
msgid "We recommend using PHP version %s or higher."
msgstr "We raden aan PHP versie %s of hoger te gebruiken."

#: includes/widgets/video.php:162
msgid "VideoPress"
msgstr "VideoPress"

#: modules/image-loading-optimization/module.php:240
msgid "An image should not be lazy-loaded and marked as high priority at the same time."
msgstr "Een afbeelding mag niet tegelijkertijd lazy-loaded zijn en als hoge prioriteit worden gemarkeerd."

#: includes/widgets/video.php:313
msgid "VideoPress URL"
msgstr "VideoPress URL"

#. translators: 1: Rating value, 2: Rating scale.
#: includes/widgets/rating.php:310
msgid "Rated %1$s out of %2$s"
msgstr "Waardering %1$s van %2$s"

#: includes/settings/settings.php:446
msgid "Optimized Gutenberg Loading"
msgstr "Geoptimaliseerd laden van Gutenberg"

#: modules/page-templates/module.php:158
msgid "Elementor Full Width"
msgstr "Elementor volledige breedte"

#: modules/page-templates/module.php:157
msgid "Elementor Canvas"
msgstr "Elementor canvas"

#: includes/widgets/star-rating.php:129
msgid "You are currently editing a Star Rating widget in its old version. Drag a new Rating widget onto your page to use a newer version, providing better capabilities."
msgstr "Je bewerkt nu een ster waardering widget in de oude versie. Sleep een nieuwe waardering widget naar je pagina om een nieuwere versie te gebruiken, die betere rechten biedt."

#: modules/nested-accordion/widgets/nested-accordion.php:320
msgid "Let Google know that this section contains an FAQ. Make sure to only use it only once per page"
msgstr "Laat Google weten dat deze sectie een FAQ bevat. Zorg ervoor dat je het maar één keer per pagina gebruikt"

#: includes/settings/settings.php:429
msgid "Optimized Image Loading"
msgstr "Geoptimaliseerd afbeeldingen laden"

#: includes/controls/groups/text-shadow.php:61
#: includes/controls/groups/text-shadow.php:85
msgid "Text Shadow"
msgstr "Tekst schaduw"

#: includes/controls/groups/typography.php:220
msgid "Letter Spacing"
msgstr "Letterafstand"

#: includes/controls/groups/flex-item.php:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:168
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:171
msgid "Grow"
msgstr "Groeien"

#: includes/controls/groups/css-filter.php:162
msgid "CSS Filters"
msgstr "CSS filters"

#: includes/controls/groups/background.php:95
msgid "Classic"
msgstr "Klassiek"

#: includes/controls/groups/background.php:301
msgid "Background Image"
msgstr "Achtergrondafbeelding"

#: includes/controls/groups/background.php:245
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:56
msgid "Angle"
msgstr "Hoek"

#: includes/controls/groups/grid-container.php:131
msgid "Justify Items"
msgstr "Items uitlijnen"

#: includes/controls/groups/flex-item.php:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:169
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:172
msgid "Shrink"
msgstr "Krimpen"

#: includes/controls/groups/background.php:201
msgid "Second Color"
msgstr "Tweede kleur"

#: includes/controls/groups/flex-container.php:41
msgid "Row - reversed"
msgstr "Rij - omgekeerd"

#: includes/controls/groups/flex-container.php:33
msgid "Row - horizontal"
msgstr "Rij - horizontaal"

#: includes/controls/groups/background.php:234
msgid "Radial"
msgstr "Radiaal"

#: includes/controls/groups/background.php:233
msgid "Linear"
msgstr "Lineair"

#: includes/controls/groups/flex-container.php:91
#: includes/controls/groups/grid-container.php:186
msgid "Justify Content"
msgstr "Inhoud uitlijnen"

#: includes/controls/groups/flex-container.php:195
msgid "Items within the container can stay in a single line (No wrap), or break into multiple lines (Wrap)."
msgstr "Items binnen de container kunnen op een enkele lijn blijven (geen wrap), of in meerdere lijnen breken (wrap)."

#: includes/controls/groups/image-size.php:301
msgid "Image Dimension"
msgstr "Grootte afbeelding"

#: includes/controls/groups/border.php:69
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:230
msgid "Groove"
msgstr "Groove"

#: includes/controls/groups/flex-item.php:173
msgid "Flex Shrink"
msgstr "Flex krimp"

#: includes/controls/groups/flex-item.php:159
msgid "Flex Grow"
msgstr "Flex groei"

#: includes/controls/groups/flex-item.php:20
msgid "Flex Basis"
msgstr "Flex basis"

#: includes/controls/groups/flex-item.php:113
msgid "Custom Order"
msgstr "Aangepaste volgorde"

#: includes/controls/groups/flex-container.php:37
msgid "Column - vertical"
msgstr "Kolom - verticaal"

#: includes/controls/groups/flex-container.php:45
msgid "Column - reversed"
msgstr "Kolom - omgekeerd"

#: includes/controls/groups/border.php:60
msgid "Border Type"
msgstr "Randtype"

#: includes/controls/groups/background.php:154
msgid "Background Type"
msgstr "Soort achtergrond"

#: includes/controls/groups/background.php:607
msgid "Background Fallback"
msgstr "Achtergrond terugval"

#: includes/controls/groups/flex-item.php:51
msgid "Align Self"
msgstr "Zelf uitlijnen"

#: includes/controls/groups/flex-container.php:128
#: includes/controls/groups/grid-container.php:159
msgid "Align Items"
msgstr "Items uitlijnen"

#: includes/controls/groups/flex-container.php:204
#: includes/controls/groups/grid-container.php:226
msgid "Align Content"
msgstr "Inhoud uitlijnen"

#: includes/settings/tools.php:165
msgid "An error occurred, the selected version is invalid. Try selecting different version."
msgstr "Er is een fout opgetreden, de geselecteerde versie is ongeldig. Probeer een andere versie te selecteren."

#: includes/controls/groups/image-size.php:380
#: modules/atomic-widgets/image/image-sizes.php:38
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:129
msgid "Full"
msgstr "Volledig"

#: includes/controls/groups/flex-item.php:76
#: includes/controls/groups/flex-item.php:109
msgid "This control will affect contained elements only."
msgstr "Deze besturing heeft alleen invloed op de elementen in de lijst."

#: includes/controls/groups/background.php:478
msgid "Display Size"
msgstr "Weergavegrootte"

#: includes/controls/groups/box-shadow.php:61
#: includes/controls/groups/box-shadow.php:96
msgid "Box Shadow"
msgstr "Vak schaduw"

#: includes/controls/groups/typography.php:198
msgid "Line Height"
msgstr "Regelafstand"

#: app/modules/onboarding/module.php:140
msgid "You do not have permission to perform this action."
msgstr "Je hebt geen toestemming om deze actie uit te voeren."

#: modules/nested-tabs/widgets/nested-tabs.php:1170
#: modules/nested-tabs/widgets/nested-tabs.php:1235
msgid "Tabs. Open items with Enter or Space, close with Escape and navigate using the Arrow keys."
msgstr "Tabs. Open items met enter of spatie, sluit af met escape en navigeer met de pijltoetsen."

#. translators: %s: Post type (e.g. Page, Post, etc.)
#: core/document-types/page-base.php:186
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:13
msgid "View %s"
msgstr "Bekijk %s"

#: modules/nested-tabs/widgets/nested-tabs.php:199
#: modules/nested-tabs/widgets/nested-tabs.php:869
msgid "Below"
msgstr "Eronder"

#: modules/nested-tabs/widgets/nested-tabs.php:195
#: modules/nested-tabs/widgets/nested-tabs.php:861
msgid "Above"
msgstr "Erboven"

#: modules/site-navigation/module.php:68
msgid "Pages Panel"
msgstr "Pagina's paneel"

#: includes/editor-templates/templates.php:334
#: includes/editor-templates/templates.php:367
#: includes/editor-templates/templates.php:424 assets/js/editor.js:8755
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:73
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:51
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:15
msgid "Rename"
msgstr "Hernoem"

#: modules/nested-accordion/widgets/nested-accordion.php:401
msgid "Space between Items"
msgstr "Ruimte tussen items"

#: modules/nested-accordion/widgets/nested-accordion.php:357
msgid "Multiple"
msgstr "Meerdere"

#: modules/nested-accordion/widgets/nested-accordion.php:356
msgid "One"
msgstr "Eén"

#: modules/nested-accordion/widgets/nested-accordion.php:353
msgid "Max Items Expanded"
msgstr "Maximale items uitgevouwen"

#: modules/nested-accordion/widgets/nested-accordion.php:343
msgid "All collapsed"
msgstr "Allen samengevouwen"

#: modules/nested-accordion/widgets/nested-accordion.php:342
msgid "First expanded"
msgstr "Eerste uitgevouwen"

#: modules/nested-accordion/widgets/nested-accordion.php:339
msgid "Default State"
msgstr "Standaard status"

#: modules/nested-accordion/widgets/nested-accordion.php:332
msgid "Interactions"
msgstr "Interacties"

#: modules/nested-accordion/widgets/nested-accordion.php:238
#: assets/js/ai-admin.js:9384 assets/js/ai-gutenberg.js:9602
#: assets/js/ai-layout.js:4872 assets/js/ai-media-library.js:9384
#: assets/js/ai-unify-product-images.js:9384 assets/js/ai.js:10936
msgid "Expand"
msgstr "Uitvouwen"

#: modules/nested-accordion/widgets/nested-accordion.php:168
msgid "Item Position"
msgstr "Positie van het item"

#: includes/widgets/video.php:941
msgid "Note: These controls have been deprecated and are only visible if they were previously in use. The video’s width and position are now set based on its aspect ratio."
msgstr "Opmerking: deze bedieningselementen zijn verouderd en zijn alleen zichtbaar als ze eerder in gebruik waren. De breedte en positie van de video worden nu ingesteld op basis van de beeldverhouding."

#: includes/widgets/video.php:256
msgid "Choose Video File"
msgstr "Kies videobestand"

#. translators: 1: Link open tag, 2: Link open tag, 3: Link close tag.
#: core/kits/documents/tabs/settings-site-identity.php:60
msgid "Changes will be reflected only after %1$s saving %3$s and %2$s reloading %3$s preview."
msgstr "Wijzigingen worden pas weerspiegeld na het %1$s opslaan van %3$s en het %2$s herladen van %3$s voorbeeld."

#: core/admin/admin.php:628
msgid "Build Smart with AI"
msgstr "Bouw slim met AI"

#: modules/apps/admin-pointer.php:30
msgid "Discover our collection of plugins and add-ons carefully selected to enhance your Elementor website and unleash your creativity."
msgstr "Ontdek onze verzameling plugins en add-ons die zorgvuldig zijn geselecteerd om je Elementor site te verbeteren en je creativiteit de vrije loop te laten."

#: modules/apps/admin-apps-page.php:25
msgid "Popular Add-ons, New Possibilities."
msgstr "Populaire add-ons, nieuwe mogelijkheden."

#. translators: 1: Slide count, 2: Total slides count.
#: includes/widgets/image-carousel.php:980
msgid "%1$s of %2$s"
msgstr "%1$s van %2$s"

#: includes/widgets/icon-box.php:229 includes/widgets/image-box.php:204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1456
msgid "Box"
msgstr "Vak"

#: modules/nested-accordion/widgets/nested-accordion.php:252
msgid "Collapse"
msgstr "Invouwen"

#: modules/apps/admin-apps-page.php:27
msgid "Learn more about this page."
msgstr "Lees verder over deze pagina."

#: modules/apps/admin-apps-page.php:26
msgid "Boost your web-creation process with add-ons, plugins, and more tools specially selected to unleash your creativity, increase productivity, and enhance your Elementor-powered website."
msgstr "Verhoog je webcreatie proces met add-ons, plugins en andere gereedschappen die speciaal zijn geselecteerd om je creativiteit te ontketenen, de productiviteit te verhogen en je Elementor aangedreven site te verbeteren."

#: includes/frontend.php:1393
msgid "Go to slide"
msgstr "Ga naar slide"

#: includes/frontend.php:1392
msgid "This is the last slide"
msgstr "Ga naar de laatste slide"

#: includes/frontend.php:1391
msgid "This is the first slide"
msgstr "Ga naar de eerste slide"

#: includes/frontend.php:1390
msgid "Next slide"
msgstr "Volgende slide"

#: includes/frontend.php:1389
msgid "Previous slide"
msgstr "Vorige slide"

#: modules/floating-buttons/base/widget-floating-bars-base.php:376
#: modules/nested-accordion/widgets/nested-accordion.php:157
msgid "Item #3"
msgstr "Item #3"

#: modules/floating-buttons/base/widget-floating-bars-base.php:373
#: modules/nested-accordion/widgets/nested-accordion.php:154
msgid "Item #2"
msgstr "Item #2"

#: modules/floating-buttons/base/widget-floating-bars-base.php:370
#: modules/nested-accordion/widgets/nested-accordion.php:151
msgid "Item #1"
msgstr "Item #1"

#: modules/nested-accordion/widgets/nested-accordion.php:66
msgid "item #%s"
msgstr "item #%s"

#: includes/editor-templates/navigator.php:68
msgid "Show/hide inner elements"
msgstr "Toon/verberg binnenste elementen"

#: includes/editor-templates/hotkeys.php:91
msgid "Panels"
msgstr "Panelen"

#: includes/controls/gallery.php:84 assets/js/editor.js:14681
msgid "Clear gallery"
msgstr "Galerij leegmaken"

#: core/document-types/page-base.php:245
#: includes/controls/groups/flex-item.php:80
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:174
msgid "Order"
msgstr "Volgorde"

#: modules/nested-tabs/widgets/nested-tabs.php:377
msgid "Horizontal Scroll"
msgstr "Horizontaal scrollen"

#: modules/floating-buttons/base/widget-floating-bars-base.php:340
#: modules/nested-accordion/widgets/nested-accordion.php:117
#: modules/nested-accordion/widgets/nested-accordion.php:118
msgid "Item Title"
msgstr "Item titel"

#: includes/widgets/image-box.php:423 includes/widgets/image.php:398
msgid "Object Position"
msgstr "Objectpositie"

#: core/document-types/page-base.php:257
msgid "Allow Comments"
msgstr "Sta reacties toe"

#: includes/editor-templates/navigator.php:81
msgid "Show/hide Element"
msgstr "Toon/verberg element"

#: includes/widgets/accordion.php:154
msgid "You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities."
msgstr "Je bent momenteel bezig met het bewerken van een accordeon widget in de oude versie. Elke nieuwe accordeon widget die naar het canvas wordt gesleept, zal de nieuwe accordeon widget zijn, met de verbeterde nested mogelijkheden."

#: includes/widgets/icon.php:326
msgid "Fit to Size"
msgstr "Op maat gemaakt"

#: includes/widgets/toggle.php:157
msgid "You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities."
msgstr "Je bent momenteel bezig met het bewerken van een toggle widget in de oude versie. Sleep een nieuwe accordeon widget naar je pagina om een nieuwere versie te gebruiken met geneste mogelijkheden."

#: includes/controls/groups/background.php:99 assets/js/ai-admin.js:11319
#: assets/js/ai-gutenberg.js:11537 assets/js/ai-media-library.js:11319
#: assets/js/ai-unify-product-images.js:11319 assets/js/ai.js:12871
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:133
msgid "Gradient"
msgstr "Gradiënt"

#: includes/editor-templates/navigator.php:58
msgid "Resize structure"
msgstr "Formaat aanpassen structuur"

#: modules/nested-tabs/widgets/nested-tabs.php:379
msgid "Note: Scroll tabs if they don’t fit into their parent container."
msgstr "Opmerking: scrol tabs als ze niet passen in hun hoofd container."

#: includes/template-library/sources/local.php:515
msgid "Invalid template type."
msgstr "Ongeldig template type."

#: core/admin/admin.php:347
msgid "Get Elementor Pro"
msgstr "Verkrijg Elementor Pro"

#: includes/editor-templates/global.php:52
msgid "Which layout would you like to use?"
msgstr "Welke lay-out wil je gebruiken?"

#: includes/editor-templates/global.php:9
msgid "Select your structure"
msgstr "Selecteer je structuur"

#: includes/controls/groups/grid-container.php:71
msgid "Rows"
msgstr "Rijen"

#: includes/controls/gaps.php:58
#: includes/controls/groups/grid-container.php:118
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:48
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:180
msgid "Row"
msgstr "Rij"

#. translators: %s: Document title.
#: core/editor/loader/v1/templates/editor-body-v1-view.php:27
#: core/editor/loader/v2/templates/editor-body-v2-view.php:27
#: includes/editor-templates/editor-wrapper.php:30
#: assets/js/packages/editor-documents/editor-documents.js:2
#: assets/js/packages/editor-documents/editor-documents.strings.js:2
msgid "Edit \"%s\" with Elementor"
msgstr "Bewerk \"%s\" met Elementor"

#: includes/controls/base-units.php:130
msgid "Switch units"
msgstr "Schakel eenheden"

#: modules/ai/connect/ai.php:27 assets/js/ai-admin.js:657
#: assets/js/ai-admin.js:7770 assets/js/ai-gutenberg.js:795
#: assets/js/ai-gutenberg.js:7988 assets/js/ai-layout.js:489
#: assets/js/ai-layout.js:3258 assets/js/ai-media-library.js:657
#: assets/js/ai-media-library.js:7770 assets/js/ai-unify-product-images.js:657
#: assets/js/ai-unify-product-images.js:7770 assets/js/ai.js:1445
#: assets/js/ai.js:9322
msgid "AI"
msgstr "AI"

#: includes/elements/container.php:372
msgid "Container Layout"
msgstr "Lay-out container"

#: includes/editor-templates/global.php:68 includes/elements/container.php:96
#: includes/elements/container.php:104 includes/elements/container.php:378
#: assets/js/editor.js:33644 assets/js/editor.js:42731
msgid "Grid"
msgstr "Raster"

#: includes/editor-templates/global.php:60 includes/elements/container.php:377
#: modules/atomic-widgets/elements/flexbox/flexbox.php:23
#: modules/library/documents/flexbox.php:52 assets/js/editor.js:8486
msgid "Flexbox"
msgstr "Flexbox"

#: includes/controls/groups/grid-container.php:115
msgid "Auto Flow"
msgstr "Automatische flow"

#: core/kits/documents/tabs/settings-layout.php:100
#: includes/controls/groups/flex-container.php:156
#: includes/controls/groups/grid-container.php:96
#: includes/elements/container.php:509
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:166
msgid "Gaps"
msgstr "Ruimtes"

#: includes/controls/groups/grid-container.php:31
msgid "Grid Outline"
msgstr "Raster overzicht"

#: includes/editor-templates/navigator.php:36
msgid "Close structure"
msgstr "Structuur sluiten"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/widgets/image-carousel.php:391
#: includes/widgets/image-gallery.php:204 includes/widgets/image.php:229
msgid "Manage your site’s lightbox settings in the %1$sLightbox panel%2$s."
msgstr "Beheer de lightbox instellingen van je site in het paneel %1$sLightbox%2$s."

#. translators: 1: Link open tag, 2: Link close tag.
#: core/document-types/page-base.php:100
msgid "Set a different selector for the title in the %1$sLayout panel%2$s."
msgstr "Stel een andere selector in voor de titel in het %1$sLay-out paneel%2$s."

#: includes/editor-templates/navigator.php:32 assets/js/editor.js:35705
msgid "Expand all elements"
msgstr "Alle elementen uitvouwen"

#: includes/editor-templates/panel-elements.php:15
msgid "Globals"
msgstr "Globalen"

#: modules/floating-buttons/base/widget-contact-button-base.php:116
msgid "Top Bar"
msgstr "Bovenbalk"

#: includes/widgets/icon-list.php:598
msgid "Adjust Vertical Position"
msgstr "Verticale positie aanpassen"

#: includes/elements/column.php:211 includes/widgets/icon-list.php:541
msgid "Horizontal Alignment"
msgstr "Horizontale uitlijning"

#: core/breakpoints/manager.php:329
msgid "Tablet Landscape"
msgstr "Tablet landschap"

#: core/breakpoints/manager.php:324
msgid "Tablet Portrait"
msgstr "Tablet portret"

#: core/admin/admin-notices.php:762 core/admin/admin.php:1036
msgid "Dismiss this notice."
msgstr "Dit bericht negeren."

#: includes/widgets/alert.php:506 includes/widgets/alert.php:561
msgid "Dismiss this alert."
msgstr "Deze waarschuwing negeren."

#: modules/generator-tag/module.php:84
msgid "A generator tag is a meta element that indicates the attributes used to create a webpage. It is used for analytical purposes."
msgstr "Een generator tag is een meta element dat de attributen aangeeft die worden gebruikt om een webpagina te maken. Het wordt gebruikt voor analytische doeleinden."

#: modules/generator-tag/module.php:76
msgid "Generator Tag"
msgstr "Generator tag"

#: core/experiments/manager.php:540
msgid "Deactivate All"
msgstr "Alles deactiveren"

#: core/experiments/manager.php:539
msgid "Activate All"
msgstr "Alles activeren"

#: core/experiments/manager.php:516
msgid "Experiments and Features"
msgstr "Experimenten en functies"

#: core/kits/documents/tabs/theme-style-form-fields.php:188
msgid "Accent Color"
msgstr "Accentkleur"

#. translators: %d: Number of rows.
#: includes/utils.php:254
msgid "%d database row affected."
msgid_plural "%d database rows affected."
msgstr[0] "%d database rijen betrokken."
msgstr[1] "%d database rijen betrokken."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/experiments/manager.php:522
msgid "Personalize your Elementor experience by controlling which features and experiments are active on your site. Help make Elementor better by %1$ssharing your experience and feedback with us%2$s."
msgstr "Personaliseer je Elementor ervaring door te bepalen welke functies en experimenten actief zijn op je site. Help Elementor beter te maken door %1$sje ervaring en je feedback met ons te delen%2$s."

#: modules/atomic-opt-in/opt-in-page.php:90
#: modules/atomic-widgets/opt-in.php:41 assets/js/editor-v4-opt-in.js:344
#: assets/js/editor-v4-opt-in.js:502
msgid "Editor V4"
msgstr "Editor V4"

#: includes/settings/settings.php:365
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:249
msgid "Google Fonts"
msgstr "Google Fonts"

#: includes/widgets/tabs.php:153
msgid "You are currently editing a Tabs Widget in its old version. Any new tabs widget dragged into the canvas will be the new Tab widget, with the improved Nested capabilities."
msgstr "Je bewerkt momenteel een tabs widget in de oude versie. Elke nieuwe tabs widget die naar het canvas wordt gesleept, zal de nieuwe tabs widget zijn, met de verbeterde geneste mogelijkheden."

#: includes/widgets/video.php:596
msgid "Preload"
msgstr "Vooraf laden"

#: includes/widgets/video.php:599
msgid "Metadata"
msgstr "Metadata"

#: modules/nested-elements/module.php:17
msgid "Nested Elements"
msgstr "Geneste elementen"

#: modules/nested-tabs/widgets/nested-tabs.php:61
msgid "Tab #%s"
msgstr "Tab #%s"

#: modules/nested-tabs/widgets/nested-tabs.php:80
msgid "Tab #%d"
msgstr "Tab #%d"

#: modules/nested-tabs/widgets/nested-tabs.php:178
msgid "Tab #3"
msgstr "Tab #3"

#: modules/nested-tabs/widgets/nested-tabs.php:342
msgid "Align Title"
msgstr "Titel uitlijnen"

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value.
#: modules/nested-tabs/widgets/nested-tabs.php:422
msgid "%1$s (%2$s %3$dpx)"
msgstr "%1$s (%2$s %3$dpx)"

#: modules/nested-accordion/widgets/nested-accordion.php:427
#: modules/nested-tabs/widgets/nested-tabs.php:469
msgid "Distance from content"
msgstr "Afstand tot de inhoud"

#: includes/widgets/video.php:865
msgid "Shadow"
msgstr "Schaduw"

#: modules/nested-tabs/widgets/nested-tabs.php:713
msgid "Titles"
msgstr "Titels"

#: includes/settings/settings.php:374
msgid "Disable this option if you want to prevent Google Fonts from being loaded. This setting is recommended when loading fonts from a different source (plugin, theme or %1$scustom fonts%2$s)."
msgstr "Schakel deze optie uit als je wil voorkomen dat Google Fonts wordt geladen. Deze instelling wordt aanbevolen wanneer lettertypes uit een andere bron worden geladen (plugin, thema of %1$saangepaste lettertypes%2$s)."

#: modules/nested-tabs/widgets/nested-tabs.php:449
msgid "Gap between tabs"
msgstr "Ruimte tussen tabs"

#: modules/nested-elements/module.php:20
msgid "Create a rich user experience by layering widgets together inside \"Nested\" Tabs, etc. When turned on, we’ll automatically enable new nested features. Your old widgets won’t be affected."
msgstr "Creëer een rijke gebruikerservaring door widgets samen te voegen in \"geneste\" tabs, enz. Indien ingeschakeld, zullen we nieuwe geneste functies automatisch inschakelen. Je oude widgets worden niet beïnvloed."

#: includes/widgets/video.php:605
msgid "Preload attribute lets you specify how the video should be loaded when the page loads."
msgstr "Het preload attribuut laat je specificeren hoe de video geladen moet worden wanneer de pagina laadt."

#: modules/nested-tabs/widgets/nested-tabs.php:434
msgid "Note: Choose at which breakpoint tabs will automatically switch to a vertical (“accordion”) layout."
msgstr "Opmerking: kies op welk breakpoint de tabs automatisch overschakelen naar een verticale (\"accordeon\") lay-out."

#: includes/settings/settings.php:458
msgid "Lazy Load Background Images"
msgstr "Lazy-load achtergrondafbeeldingen"

#: includes/elements/container.php:1939
msgid "Note: Avoid applying transform properties on sticky containers. Doing so might cause unexpected results."
msgstr "Let op: vermijd het toepassen van transformerende eigenschappen op sticky containers. Dit kan onverwachte resultaten opleveren."

#: core/admin/admin.php:1024 core/utils/hints.php:460
#: modules/apps/admin-apps-page.php:116 modules/apps/admin-apps-page.php:147
#: modules/home/<USER>/filter-plugins.php:82 assets/js/admin.js:795
#: assets/js/editor-v4-opt-in.js:355
msgid "Activate"
msgstr "Activeer"

#: includes/elements/container.php:578
msgid "(link)"
msgstr "(link)"

#: includes/controls/groups/typography.php:150
msgctxt "Typography Control"
msgid "(Black)"
msgstr "(Zwart)"

#: includes/controls/groups/typography.php:149
msgctxt "Typography Control"
msgid "(Extra Bold)"
msgstr "(Extra vet)"

#: includes/controls/groups/typography.php:148
msgctxt "Typography Control"
msgid "(Bold)"
msgstr "(Vet)"

#: includes/controls/groups/typography.php:147
msgctxt "Typography Control"
msgid "(Semi Bold)"
msgstr "(Half vet)"

#: includes/controls/groups/typography.php:146
msgctxt "Typography Control"
msgid "(Medium)"
msgstr "(Medium)"

#: includes/controls/groups/typography.php:145
msgctxt "Typography Control"
msgid "(Normal)"
msgstr "(Normaal)"

#: includes/controls/groups/typography.php:144
msgctxt "Typography Control"
msgid "(Light)"
msgstr "(Licht)"

#: includes/controls/groups/typography.php:143
msgctxt "Typography Control"
msgid "(Extra Light)"
msgstr "(Extra licht)"

#: includes/controls/groups/typography.php:142
msgctxt "Typography Control"
msgid "(Thin)"
msgstr "(Dun)"

#: core/experiments/manager.php:612
msgid "Requires"
msgstr "Vereist"

#: app/modules/import-export-customization/module.php:174
#: app/modules/import-export-customization/module.php:177
#: app/modules/import-export-customization/module.php:183
#: app/modules/import-export/module.php:176
#: app/modules/import-export/module.php:179
#: app/modules/import-export/module.php:185
msgid "imported kit"
msgstr "geïmporteerde kit"

#: app/modules/import-export-customization/module.php:182
#: app/modules/import-export/module.php:184
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s.%3$s Your original site settings will be restored."
msgstr "Verwijder alle inhoud en site instellingen die werden geleverd met \"%1$s\" Op %2$s. %3$s je oorspronkelijke site instellingen worden teruggezet."

#: app/modules/import-export-customization/module.php:173
#: app/modules/import-export/module.php:175
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s %3$s and revert to the site setting that came with \"%4$s\" on %5$s."
msgstr "Verwijder alle inhoud en site instellingen die met \"%1$s\" op %2$s %3$s werden geleverd en keer terug naar de site instellingen die met \"%4$s\" op %5$s werden geleverd."

#: core/kits/documents/tabs/settings-layout.php:91
msgid "Sets the default space inside the container (Default is 10px)"
msgstr "Stelt de standaard ruimte binnen de container in (standaard is 10px)"

#: core/kits/documents/tabs/settings-layout.php:88
msgid "Container Padding"
msgstr "Container padding"

#: core/common/modules/finder/categories/settings.php:64
#: core/experiments/manager.php:313 core/experiments/manager.php:370
#: includes/settings/settings.php:400 includes/settings/settings.php:403
#: modules/element-cache/module.php:47
msgid "Performance"
msgstr "Prestatie"

#: core/admin/admin-notices.php:407
msgid "Try it out"
msgstr "Probeer het uit"

#: core/admin/admin-notices.php:404
msgid "With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed."
msgstr "Met onze experimentele snelheidsverhogende functies kun je sneller gaan dan ooit tevoren. Zoek naar het label Prestaties op onze Experimenten pagina en activeer die experimenten om de laadsnelheid van je site te verbeteren."

#: core/admin/admin-notices.php:403
msgid "Improve your site’s performance score."
msgstr "Verbeter de prestatie score van je site."

#: includes/widgets/alert.php:394
#: modules/floating-buttons/base/widget-contact-button-base.php:2936
msgid "Horizontal Position"
msgstr "Horizontale positie"

#: includes/widgets/alert.php:376
#: modules/floating-buttons/base/widget-contact-button-base.php:2990
#: modules/floating-buttons/base/widget-floating-bars-base.php:1433
msgid "Vertical Position"
msgstr "Verticale positie"

#. Translators: %s is the current item index.
#: modules/nested-accordion/widgets/nested-accordion.php:85
#: modules/nested-elements/base/widget-nested-base.php:45
#: assets/js/editor.js:25265
msgid "Item #%d"
msgstr "Item #%d"

#: includes/editor-templates/panel.php:57
msgid "Any time you can change the settings in %1$sUser Preferences%2$s"
msgstr "Op elk moment kun je de instellingen wijzigen in %1$sGebruiker voorkeuren%2$s"

#: includes/editor-templates/panel.php:53
msgid "Now you can choose where you want to go on the site from the following options"
msgstr "Nu kun je kiezen waar je naartoe wil op de site uit de volgende opties"

#: core/settings/editor-preferences/model.php:210
msgid "WP Dashboard"
msgstr "WP dashboard"

#: core/settings/editor-preferences/model.php:209
msgid "All Posts"
msgstr "Alle berichten"

#: core/settings/editor-preferences/model.php:208
msgid "This Post"
msgstr "Dit bericht"

#: core/settings/editor-preferences/model.php:204
msgid "Exit to"
msgstr "Beëindigen naar"

#: app/modules/onboarding/module.php:158
msgid "There was a problem setting your site name."
msgstr "Er is een probleem opgetreden bij het instellen van je sitenaam."

#: includes/widgets/alert.php:161 includes/widgets/alert.php:342
msgid "Dismiss Icon"
msgstr "Negeren icoon"

#: includes/widgets/image-carousel.php:303
msgid "Next Arrow Icon"
msgstr "Volgende pijl icoon"

#: includes/widgets/image-carousel.php:248
msgid "Previous Arrow Icon"
msgstr "Vorige pijl icoon"

#: includes/elements/container.php:599
msgid "Don’t add links to elements nested in this container - it will break the layout."
msgstr "Geen links toevoegen naar elementen die in deze container zijn genest - het zal de lay-out breken."

#: core/editor/notice-bar.php:45 assets/js/element-manager-admin.js:725
msgid "Unleash the full power of Elementor's features and web creation tools."
msgstr "Ontketen de volledige kracht van Elementor's functies en web creatie gereedschappen."

#: includes/editor-templates/hotkeys.php:184 assets/js/notes.js:135
#: assets/js/notes.js:139 assets/js/notes.js:225
msgid "Notes"
msgstr "Notities"

#: core/editor/notice-bar.php:41 core/editor/promotion.php:34
#: includes/editor-templates/navigator.php:17
#: includes/editor-templates/panel-elements.php:29
#: includes/editor-templates/panel-elements.php:34
#: includes/editor-templates/panel-elements.php:103
#: includes/managers/controls.php:1161 includes/widgets/button.php:107
#: includes/widgets/heading.php:161 includes/widgets/image-carousel.php:130
#: includes/widgets/image-gallery.php:110 includes/widgets/testimonial.php:116
#: includes/widgets/video.php:132 modules/admin-top-bar/module.php:79
#: modules/checklist/steps/setup-header.php:93
#: modules/element-manager/ajax.php:73 modules/element-manager/ajax.php:80
#: modules/element-manager/ajax.php:88
#: modules/promotions/admin-menu-items/base-promotion-item.php:32
#: modules/promotions/admin-menu-items/base-promotion-template.php:37
#: modules/promotions/admin-menu-items/popups-promotion-item.php:24
#: modules/promotions/promotion-data.php:48
#: modules/promotions/promotion-data.php:65
#: modules/promotions/promotion-data.php:82
#: modules/promotions/promotion-data.php:99
#: modules/promotions/promotion-data.php:116 assets/js/app-packages.js:917
#: assets/js/app-packages.js:4293 assets/js/app-packages.js:4569
#: assets/js/app.js:1157 assets/js/checklist.js:241 assets/js/editor.js:6185
#: assets/js/editor.js:10877 assets/js/editor.js:53648
#: assets/js/editor.js:53663 assets/js/editor.js:53678
#: assets/js/editor.js:53693
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:467
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1406
msgid "Upgrade Now"
msgstr "Upgrade nu"

#: core/admin/admin.php:632 core/role-manager/role-manager.php:241
#: includes/editor-templates/panel-elements.php:40
#: includes/editor-templates/panel-elements.php:46
#: includes/editor-templates/panel-elements.php:65
#: includes/editor-templates/panel.php:317
#: includes/editor-templates/templates.php:513
#: includes/managers/controls.php:1152 includes/widgets/button.php:103
#: includes/widgets/heading.php:158 includes/widgets/image-carousel.php:127
#: includes/widgets/image-gallery.php:107 includes/widgets/testimonial.php:113
#: includes/widgets/video.php:128
#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:30
#: modules/promotions/promotion-data.php:41
#: modules/promotions/promotion-data.php:58
#: modules/promotions/promotion-data.php:75
#: modules/promotions/promotion-data.php:92
#: modules/promotions/promotion-data.php:109 assets/js/ai-admin.js:1025
#: assets/js/ai-admin.js:2961 assets/js/ai-admin.js:3089
#: assets/js/ai-gutenberg.js:1163 assets/js/ai-gutenberg.js:3099
#: assets/js/ai-gutenberg.js:3227 assets/js/ai-layout.js:723
#: assets/js/ai-layout.js:1002 assets/js/ai-media-library.js:1025
#: assets/js/ai-media-library.js:2961 assets/js/ai-media-library.js:3089
#: assets/js/ai-unify-product-images.js:1025
#: assets/js/ai-unify-product-images.js:2961
#: assets/js/ai-unify-product-images.js:3089 assets/js/ai.js:1813
#: assets/js/ai.js:3749 assets/js/ai.js:3877 assets/js/app-packages.js:4534
#: assets/js/e-react-promotions.js:196 assets/js/editor.js:4706
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1470
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3260
#: assets/js/notes.js:148
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:719
#: assets/js/styleguide.js:196
msgid "Upgrade"
msgstr "Upgrade"

#: modules/announcements/module.php:118
msgid "Let's do it"
msgstr "Laten we het doen"

#: modules/container-converter/module.php:88
#: modules/container-converter/module.php:121
msgid "Copies all of the selected sections and columns and pastes them in a container beneath the original."
msgstr "Kopieert alle geselecteerde secties en kolommen en plakt ze in een container onder het origineel."

#: modules/container-converter/module.php:86
#: modules/container-converter/module.php:119
msgid "Convert"
msgstr "Converteren"

#: includes/controls/groups/typography.php:153
msgid "Bold"
msgstr "Vet"

#: modules/container-converter/module.php:85
#: modules/container-converter/module.php:118
msgid "Convert to container"
msgstr "Converteer naar container"

#: elementor.php:78 elementor.php:102 assets/js/ai-admin.js:1064
#: assets/js/ai-gutenberg.js:1202 assets/js/ai-layout.js:762
#: assets/js/ai-media-library.js:1064 assets/js/ai-unify-product-images.js:1064
#: assets/js/ai.js:1852 assets/js/app.js:7569
msgid "Show me how"
msgstr "Toon mij hoe"

#: core/common/modules/finder/categories/tools.php:68
msgid "Import Export"
msgstr "Import export"

#: includes/widgets/image-carousel.php:635
msgid "Pagination"
msgstr "Paginering"

#: includes/elements/column.php:461 includes/elements/section.php:730
#: includes/widgets/heading.php:338
msgid "Hue"
msgstr "Tint"

#: includes/elements/column.php:460 includes/elements/section.php:729
#: includes/widgets/heading.php:337
msgid "Exclusion"
msgstr "Uitsluiting"

#: includes/elements/column.php:459 includes/elements/section.php:728
#: includes/widgets/heading.php:336
msgid "Difference"
msgstr "Verschil"

#: core/kits/views/panel.php:36
msgid "Reorder"
msgstr "Opnieuw sorteren"

#: core/kits/documents/tabs/global-typography.php:160
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:227
msgid "System Fonts"
msgstr "Systeem lettertypen"

#: core/kits/documents/tabs/global-colors.php:101
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:178
msgid "System Colors"
msgstr "Systeemkleuren"

#: includes/editor-templates/global.php:33 assets/js/editor.js:33503
msgid "Add New Container"
msgstr "Nieuwe container toevoegen"

#: core/experiments/manager.php:342 includes/elements/container.php:72
#: includes/elements/container.php:356
#: modules/library/documents/container.php:52 assets/js/editor.js:8484
#: assets/js/editor.js:33644 assets/js/editor.js:39479
msgid "Container"
msgstr "Container"

#: includes/widgets/video.php:1001
msgid "Play Video about"
msgstr "Video afspelen over"

#: includes/controls/groups/background.php:727
#: includes/widgets/image-carousel.php:434
msgid "Lazyload"
msgstr "Lazy-load"

#: core/common/modules/event-tracker/personal-data.php:24
msgid "Elementor Event Tracker"
msgstr "Elementor gebeurtenis tracker"

#: includes/elements/container.php:493
msgid "To achieve full height Container use %s."
msgstr "Gebruik %s om container op volledige hoogte te bereiken."

#: app/modules/onboarding/module.php:265 app/modules/onboarding/module.php:350
msgid "There was a problem uploading your file."
msgstr "Er is een probleem opgetreden bij het uploaden van je bestand."

#: app/modules/onboarding/module.php:215
msgid "There was a problem setting your site logo."
msgstr "Er is een probleem opgetreden bij het instellen van je sitelogo."

#: modules/library/documents/page.php:65
msgid "Add New Page Template"
msgstr "Nieuwe pagina template toevoegen"

#: core/utils/hints.php:462
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:275
msgid "Customize"
msgstr "Aanpassen"

#: core/logger/log-reporter.php:25
msgid "Log"
msgstr "Log"

#: core/experiments/manager.php:502
msgid "Stable Features"
msgstr "Stabiele functies"

#: includes/base/element-base.php:987
msgid "Perspective"
msgstr "Perspectief"

#: includes/base/element-base.php:861
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:111
#: assets/js/packages/editor-controls/editor-controls.strings.js:116
msgid "Transform"
msgstr "Transformeer"

#: includes/base/element-base.php:1346
msgid "Y Anchor Point"
msgstr "Y Ankerpunt"

#: includes/base/element-base.php:1318
msgid "X Anchor Point"
msgstr "X Ankerpunt"

#: includes/base/element-base.php:1201
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:138
msgid "Skew Y"
msgstr "Scheef Y"

#: includes/base/element-base.php:1179
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:137
msgid "Skew X"
msgstr "Scheef X"

#: includes/base/element-base.php:1167
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:115
#: assets/js/packages/editor-controls/editor-controls.strings.js:120
msgid "Skew"
msgstr "Scheefheid"

#: includes/base/element-base.php:1143
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:140
msgid "Scale Y"
msgstr "Schaal Y"

#: includes/base/element-base.php:1121
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:139
msgid "Scale X"
msgstr "Schaal X"

#: includes/base/element-base.php:1088
msgid "Keep Proportions"
msgstr "Verhoudingen behouden"

#: includes/base/element-base.php:1048
msgid "Offset Y"
msgstr "Offset Y"

#: includes/base/element-base.php:1022
msgid "Offset X"
msgstr "Offset X"

#: includes/base/element-base.php:924
msgid "3D Rotate"
msgstr "3D roteren"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:19
msgid "Custom Code"
msgstr "Aangepaste code"

#: modules/usage/usage-reporter.php:22
msgid "Elements Usage"
msgstr "Gebruik van elementen"

#: core/editor/promotion.php:31 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:4706 assets/js/notes.js:148 assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "Verbinden & activeren"

#: core/experiments/manager.php:545
msgid "Ongoing Experiments"
msgstr "Lopende experimenten"

#: includes/admin-templates/beta-tester.php:53 assets/js/ai-admin.js:6382
#: assets/js/ai-admin.js:15897 assets/js/ai-gutenberg.js:6600
#: assets/js/ai-gutenberg.js:16115 assets/js/ai-layout.js:2488
#: assets/js/ai-layout.js:5196 assets/js/ai-media-library.js:6382
#: assets/js/ai-media-library.js:15897
#: assets/js/ai-unify-product-images.js:6382
#: assets/js/ai-unify-product-images.js:15897 assets/js/ai.js:7832
#: assets/js/ai.js:7934 assets/js/ai.js:17449
msgid "Privacy Policy"
msgstr "Privacybeleid"

#: includes/admin-templates/beta-tester.php:48 assets/js/ai-admin.js:6378
#: assets/js/ai-admin.js:15893 assets/js/ai-gutenberg.js:6596
#: assets/js/ai-gutenberg.js:16111 assets/js/ai-layout.js:2484
#: assets/js/ai-layout.js:5192 assets/js/ai-media-library.js:6378
#: assets/js/ai-media-library.js:15893
#: assets/js/ai-unify-product-images.js:6378
#: assets/js/ai-unify-product-images.js:15893 assets/js/ai.js:7828
#: assets/js/ai.js:7930 assets/js/ai.js:17445
msgid "Terms of Service"
msgstr "Servicevoorwaarden"

#: includes/controls/groups/text-stroke.php:60
#: includes/controls/groups/text-stroke.php:111
msgid "Text Stroke"
msgstr "Lijn tekst"

#: includes/controls/groups/text-stroke.php:85
msgid "Stroke Color"
msgstr "Lijnkleur"

#. translators: 1: Integration settings link open tag, 2: Create API key link
#. open tag, 3: Link close tag.
#: includes/widgets/google-maps.php:140
msgid "Set your Google Maps API Key in Elementor's %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s"
msgstr "Stel je Google Maps API-sleutel in op de pagina %1$sIntegraties instellingen%3$s van Elementor. Maak hier je sleutel %2$s.%3$s"

#. translators: 1. "Terms of service" link, 2. "Privacy policy" link
#: includes/admin-templates/beta-tester.php:44
msgid "By clicking Sign Up, you agree to Elementor's %1$s and %2$s"
msgstr "Door op Aanmelden te klikken, ga je akkoord met Elementor's %1$s en %2$s"

#: includes/base/element-base.php:964
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:143
msgid "Rotate Y"
msgstr "Roteren Y"

#: includes/base/element-base.php:941
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:142
msgid "Rotate X"
msgstr "Roteren X"

#: includes/base/element-base.php:1244 includes/base/element-base.php:1248
msgid "Flip Vertical"
msgstr "Verticaal spiegelen"

#: includes/base/element-base.php:1225 includes/base/element-base.php:1229
msgid "Flip Horizontal"
msgstr "Horizontaal spiegelen"

#: core/editor/data/globals/endpoints/base.php:46
msgid "Invalid title"
msgstr "Ongeldige titel"

#: core/experiments/experiments-reporter.php:21
msgid "Elementor Experiments"
msgstr "Elementor experimenten"

#: core/experiments/manager.php:316
msgid "The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts."
msgstr "De \"Inline lettertype iconen\" zullen de iconen weergeven als inline SVG zonder de Font Awesome en de eicons bibliotheken en diens bijbehorende CSS bestanden en lettertypen te laden."

#: core/experiments/manager.php:312
msgid "Inline Font Icons"
msgstr "Inline lettertype iconen"

#: includes/settings/tools.php:158
msgid "Not allowed to rollback versions"
msgstr "Terugdraaien van versies is niet toegestaan"

#: modules/page-templates/module.php:316
msgid "The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings."
msgstr "De standaard pagina template zoals gedefinieerd in Elementor paneel → Hamburger menu → Site instellingen."

#: includes/elements/column.php:455 includes/elements/container.php:864
#: includes/elements/section.php:723 includes/widgets/heading.php:332
msgid "Lighten"
msgstr "Lichter maken"

#: includes/elements/column.php:462 includes/elements/container.php:868
#: includes/elements/section.php:727 includes/widgets/heading.php:339
msgid "Luminosity"
msgstr "Helderheid"

#: core/kits/documents/tabs/settings-page-transitions.php:19
#: includes/managers/controls.php:1120
msgid "Page Transitions"
msgstr "Paginaovergangen"

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-template.php:52
msgid "Templates Help You %1$sWork Efficiently%2$s"
msgstr "Templates helpen je %1$sEfficiënt te werken%2$s"

#. translators: %s: Device name.
#: includes/base/element-base.php:1393
msgid "Hide On %s"
msgstr "Verbergen op %s"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/editor-templates/panel.php:202
msgid "You can enable it from the %1$sElementor settings page%2$s."
msgstr "Je kunt het inschakelen vanaf de pagina %1$sElementor instellingen%2$s."

#: includes/elements/column.php:457 includes/elements/container.php:866
#: includes/elements/section.php:725 includes/widgets/heading.php:334
msgid "Saturation"
msgstr "Verzadiging"

#: includes/elements/column.php:453 includes/elements/container.php:862
#: includes/elements/section.php:721 includes/widgets/heading.php:330
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:130
msgid "Overlay"
msgstr "Overlay"

#: includes/elements/column.php:454 includes/elements/container.php:863
#: includes/elements/section.php:722 includes/widgets/heading.php:331
msgid "Darken"
msgstr "Donkerder maken"

#: includes/elements/column.php:451 includes/elements/container.php:860
#: includes/elements/section.php:719 includes/widgets/heading.php:328
msgid "Multiply"
msgstr "Vermenigvuldigen"

#: includes/elements/column.php:452 includes/elements/container.php:861
#: includes/elements/section.php:720 includes/widgets/heading.php:329
msgid "Screen"
msgstr "Scherm"

#. translators: %d: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:26
msgid "Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up"
msgstr "Breedbeeld <br> Instellingen toegevoegd voor het breedbeeldapparaat zijn van toepassing op schermformaten %dpx en groter"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/maintenance-mode.php:375
msgid "Select one or go ahead and %1$screate one%2$s now."
msgstr "Selecteer er een of ga je gang en %1$smaak er nu een%2$s."

#: includes/managers/controls.php:1138
msgid "Meet Page Transitions"
msgstr "Maak kennis met paginaovergangen"

#. translators: 1: Function argument, 2: Elementor version number, 3:
#. Replacement argument name.
#: modules/dev-tools/deprecation.php:288
msgid "The %1$s argument is deprecated since version %2$s! Use %3$s instead."
msgstr "Het argument %1$s is verouderd sinds versie %2$s! Gebruik in plaats daarvan %3$s."

#. translators: 1: Function argument, 2: Elementor version number.
#: modules/dev-tools/deprecation.php:292
msgid "The %1$s argument is deprecated since version %2$s!"
msgstr "Het argument %1$s is verouderd sinds versie %2$s!"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:261 modules/safe-mode/module.php:274
msgid "%1$sClick here%2$s to troubleshoot"
msgstr "%1$sKlik hier%2$s om problemen op te lossen"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/kits/documents/tabs/tab-base.php:80
msgid "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s."
msgstr "Om ervoor te zorgen dat het thema stijl alle relevante Elementor elementen beïnvloedt, moet je de standaard kleuren en lettertypes uitschakelen op de %1$sInstellingen pagina%2$s."

#: includes/managers/controls.php:1126
msgid "Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load."
msgstr "Met pagina transitions kun je in- en uitloopanimaties tussen pagina's opmaken en een lader weergeven totdat je pagina assets zijn geladen."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:290
msgid "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"
msgstr "Let op! We konden niet al je plugins deactiveren in de veilige modus. %1$sLees meer%2$s over dit probleem"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:403
msgid "%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s"
msgstr "%1$sKlik hier%2$s %3$som je aan te melden voor onze e-mail updates om als eerste op de hoogte te zijn.%4$s"

#: includes/elements/column.php:456 includes/elements/container.php:865
#: includes/elements/section.php:724 includes/widgets/heading.php:333
msgid "Color Dodge"
msgstr "Kleur ontwijken"

#: core/experiments/manager.php:332
msgid "Get pixel-perfect design for every screen size. You can now add up to 6 customizable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and widescreen."
msgstr "Krijg een pixel-perfect ontwerp voor elk schermformaat. Je kunt nu maximaal 6 aanpasbare breakpoints toevoegen naast de standaard desktop instelling: mobiel, mobiel extra, tablet, tablet extra, laptop en breedbeeld."

#: core/kits/documents/tabs/settings-layout.php:371
msgid "Widescreen breakpoint settings will apply from the selected value and up."
msgstr "Breedbeeld breakpoint instellingen zijn van toepassing vanaf de geselecteerde waarde en hoger."

#: core/experiments/manager.php:329
msgid "Additional Custom Breakpoints"
msgstr "Extra aangepaste breakpoints"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/history/views/revisions-panel-template.php:31
msgid "Learn more about %1$sWordPress revisions%2$s"
msgstr "Lees verder over %1$sWordPress revisies%2$s"

#: includes/template-library/sources/admin-menu-items/templates-categories-menu-item.php:23
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3801
msgid "Categories"
msgstr "Categorieën"

#: includes/managers/elements.php:328
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3281
msgid "Favorites"
msgstr "Favorieten"

#: core/common/modules/finder/categories/settings.php:74
#: core/experiments/manager.php:479
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3810
msgid "Features"
msgstr "Functies"

#: modules/nested-accordion/widgets/nested-accordion.php:554
#: assets/js/app-packages.js:4039
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:60
msgid "Header"
msgstr "Header"

#: includes/settings/tools.php:112
msgid "New kit have been created successfully"
msgstr "Nieuwe kit is succesvol aangemaakt"

#: includes/settings/tools.php:101
msgid "There's already an active kit."
msgstr "Er is al een actieve kit."

#: includes/editor-templates/panel.php:291 assets/js/editor.js:13820
msgid "Color Sampler"
msgstr "Kleur sampler"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:15
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:19
msgid "Submissions"
msgstr "Inzendingen"

#: includes/settings/tools.php:107
msgid "An error occurred while trying to create a kit."
msgstr "Er is een fout opgetreden tijdens het maken van een kit."

#: modules/library/documents/section.php:47
msgid "Sections"
msgstr "Secties"

#: includes/settings/tools.php:431 includes/settings/tools.php:434
#: assets/js/editor.js:28888
msgid "Recreate Kit"
msgstr "Maak kit opnieuw"

#: includes/settings/tools.php:435
msgid "It seems like your site doesn't have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again."
msgstr "Het lijkt erop dat je site geen actieve kit heeft. De actieve kit bevat al je site instellingen. Door je kit opnieuw te maken, kun je je site instellingen opnieuw bewerken."

#: app/modules/kit-library/connect/kit-library.php:16
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:5102
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:5420
msgid "Kit Library"
msgstr "Kit bibliotheek"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "Kit bestaat niet."

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "Kit niet gevonden"

#: app/modules/import-export-customization/module.php:148
#: app/modules/import-export/module.php:150 assets/js/app.js:8919
#: assets/js/app.js:12435
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4407
msgid "Import"
msgstr "Import"

#: core/common/modules/connect/rest/rest-api.php:139
#: modules/global-classes/global-classes-rest-api.php:223
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:29
msgid "Something went wrong"
msgstr "Er is iets fout gegaan"

#: includes/settings/tools.php:341 assets/js/app.js:8525
msgid "Important:"
msgstr "Belangrijk:"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "Compatibel"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "Niet compatibel"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "Compatibiliteit niet gespecificeerd"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "Compatibiliteit onbekend"

#: includes/settings/settings.php:307
msgid "Google Maps Embed API"
msgstr "Google Maps Embed API"

#. translators: 1: Link open tag, 2: Link close tag
#: includes/settings/settings.php:311
msgid "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."
msgstr "Google Maps Embed API is een gratis dienst van Google waarmee Google Maps in je site kan worden ingesloten. Voor meer details, bezoek Google Maps' %1$sGebruik van API-sleutels%2$s pagina."

#: includes/settings/settings.php:318
msgid "API Key"
msgstr "API-sleutel"

#: includes/widgets/common-base.php:204
msgid "Sketch"
msgstr "Schets"

#: includes/widgets/common-base.php:200
msgid "Flower"
msgstr "Bloem"

#: includes/settings/settings.php:390
msgid "Optional"
msgstr "Optioneel"

#: includes/settings/settings.php:388
msgid "Swap"
msgstr "Wissel"

#: includes/settings/settings.php:387
msgid "Blocking"
msgstr "Blokkeren"

#: includes/settings/settings.php:381
msgid "Google Fonts Load"
msgstr "Google Fonts laden"

#: core/admin/admin-notices.php:367
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "Met Elementor Pro kan je de gebruikerstoegang beheren en ervoor zorgen dat niemand je ontwerp verprutst."

#: core/admin/admin-notices.php:366
msgid "Managing a multi-user site?"
msgstr "Beheer je een multi-user site?"

#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:716
#: includes/widgets/common-base.php:1214 includes/widgets/image-box.php:434
#: includes/widgets/image.php:409
#: modules/link-in-bio/base/widget-link-in-bio-base.php:188
msgid "Bottom Right"
msgstr "Rechtsonder"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714
#: includes/widgets/common-base.php:1212 includes/widgets/image-box.php:432
#: includes/widgets/image.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:186
msgid "Bottom Center"
msgstr "Middenonder"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715
#: includes/widgets/common-base.php:1213 includes/widgets/image-box.php:433
#: includes/widgets/image.php:408
#: modules/link-in-bio/base/widget-link-in-bio-base.php:187
msgid "Bottom Left"
msgstr "Linksonder"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710
#: includes/widgets/common-base.php:1208 includes/widgets/image-box.php:428
#: includes/widgets/image.php:403
#: modules/link-in-bio/base/widget-link-in-bio-base.php:182
msgid "Center Right"
msgstr "Midden rechts"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708
#: includes/widgets/common-base.php:1206 includes/widgets/image-box.php:426
#: includes/widgets/image.php:401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:180
msgid "Center Center"
msgstr "Midden midden"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709
#: includes/widgets/common-base.php:1207 includes/widgets/image-box.php:427
#: includes/widgets/image.php:402
#: modules/link-in-bio/base/widget-link-in-bio-base.php:181
msgid "Center Left"
msgstr "Links in het midden"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713
#: includes/widgets/common-base.php:1211 includes/widgets/image-box.php:431
#: includes/widgets/image.php:406
#: modules/link-in-bio/base/widget-link-in-bio-base.php:185
msgid "Top Right"
msgstr "Rechtsboven"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712
#: includes/widgets/common-base.php:1210 includes/widgets/image-box.php:430
#: includes/widgets/image.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:184
msgid "Top Left"
msgstr "Linksboven"

#: includes/widgets/accordion.php:262 includes/widgets/toggle.php:265
#: modules/nested-accordion/widgets/nested-accordion.php:307
msgid "FAQ Schema"
msgstr "FAQ Schema"

#: includes/widgets/common-base.php:156
msgid "Triangle"
msgstr "Driehoek"

#: includes/widgets/common-base.php:212
msgid "Blob"
msgstr "Blob"

#: includes/widgets/common-base.php:1081 includes/widgets/common-base.php:1089
msgid "Mask"
msgstr "Masker"

#: includes/widgets/common-base.php:1136
msgid "Need More Shapes?"
msgstr "Meer vormen nodig?"

#: includes/widgets/common-base.php:1156
msgid "Fit"
msgstr "Passend"

#: includes/base/element-base.php:1076 includes/base/element-base.php:1099
#: includes/widgets/common-base.php:1171
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:113
#: assets/js/packages/editor-controls/editor-controls.strings.js:118
msgid "Scale"
msgstr "Schaal"

#: includes/controls/groups/background.php:385
#: includes/widgets/common-base.php:1264
msgid "Y Position"
msgstr "Y positie"

#: includes/controls/groups/background.php:464
#: includes/widgets/common-base.php:1300 includes/widgets/common-base.php:1304
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:153
#: assets/js/packages/editor-controls/editor-controls.strings.js:157
msgid "Repeat"
msgstr "Herhaling"

#: includes/controls/groups/background.php:463
#: includes/widgets/common-base.php:1303
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:156
msgid "No-repeat"
msgstr "Geen herhaling"

#: includes/widgets/common-base.php:1307
#: modules/floating-buttons/base/widget-contact-button-base.php:2085
#: modules/floating-buttons/base/widget-contact-button-base.php:2176
#: modules/floating-buttons/base/widget-contact-button-base.php:2869
#: modules/floating-buttons/base/widget-floating-bars-base.php:844
#: modules/link-in-bio/base/widget-link-in-bio-base.php:118
msgid "Round"
msgstr "Rond"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "Breedbeeld"

#. translators: %1$s: Device name, %2$s: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:32
msgid "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"
msgstr "%1$s <br> Instellingen toegevoegd voor het %1$s apparaat, zijn van toepassing op %2$spx schermen en lager"

#: includes/settings/settings.php:392
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "Font-display eigenschap definieert hoe lettertypebestanden worden geladen en weergegeven door de browser."

#: includes/controls/groups/background.php:465
#: includes/widgets/common-base.php:1305
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:154
msgid "Repeat-x"
msgstr "Herhaal-x"

#: includes/controls/groups/background.php:466
#: includes/widgets/common-base.php:1306
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:155
msgid "Repeat-y"
msgstr "Herhaal-y"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "Database update proces wordt uitgevoerd op de achtergrond. Duurt het even?"

#: includes/controls/groups/background.php:342
#: includes/widgets/common-base.php:1228
msgid "X Position"
msgstr "X positie"

#: includes/editor-templates/responsive-bar.php:62
msgid "Manage Breakpoints"
msgstr "Beheer breakpoints"

#: includes/editor-templates/responsive-bar.php:22
msgid "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"
msgstr "Desktop <br> Instellingen die aan het basisapparaat zijn toegevoegd, zijn van toepassing op alle breakpoints, tenzij ze worden bewerkt"

#: includes/settings/settings.php:392
msgid "Set the way Google Fonts are being loaded by selecting the font-display property (Recommended: Swap)."
msgstr "Stel de manier in waarop Google Fonts wordt geladen door de eigenschap lettertype-weergave te selecteren (Aanbevolen: wisselen)."

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711
#: includes/widgets/common-base.php:1209 includes/widgets/image-box.php:429
#: includes/widgets/image.php:404
#: modules/link-in-bio/base/widget-link-in-bio-base.php:183
msgid "Top Center"
msgstr "Middenboven"

#: modules/shapes/module.php:43
msgid "Wave"
msgstr "Golf"

#: modules/shapes/module.php:44
msgid "Arc"
msgstr "Boog"

#: modules/shapes/module.php:47
msgid "Oval"
msgstr "Ovaal"

#: modules/shapes/module.php:48
msgid "Spiral"
msgstr "Spiraal"

#: modules/shapes/widgets/text-path.php:51
#: modules/shapes/widgets/text-path.php:103
#: modules/shapes/widgets/text-path.php:243
msgid "Text Path"
msgstr "Tekst pad"

#: modules/shapes/widgets/text-path.php:114
msgid "Add Your Curvy Text Here"
msgstr "Voeg hier je curvy tekst toe"

#: modules/shapes/widgets/text-path.php:126
msgid "Path Type"
msgstr "Pad type"

#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:34
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:60
#: modules/shapes/widgets/text-path.php:136
msgid "SVG"
msgstr "SVG"

#: modules/shapes/widgets/text-path.php:199
msgid "Text Direction"
msgstr "Tekstrichting"

#: modules/shapes/widgets/text-path.php:204
msgid "RTL"
msgstr "RTL"

#: modules/shapes/widgets/text-path.php:205
msgid "LTR"
msgstr "LTR"

#: includes/controls/groups/typography.php:245
#: modules/shapes/widgets/text-path.php:347
msgid "Word Spacing"
msgstr "Woordafstand"

#: modules/shapes/widgets/text-path.php:382
msgid "Starting Point"
msgstr "Startpunt"

#: modules/shapes/widgets/text-path.php:485
msgid "Path"
msgstr "Pad"

#: modules/shapes/widgets/text-path.php:520
#: modules/shapes/widgets/text-path.php:591
msgid "Stroke"
msgstr "Slag"

#: core/kits/documents/tabs/settings-layout.php:217
msgid "Mobile and Tablet options cannot be deleted."
msgstr "Opties voor mobiel en tablet kunnen niet worden verwijderd."

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "Dit lijkt geen WXR bestand te zijn, het WXR versienummer ontbreekt/is ongeldig"

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "Er is een fout opgetreden bij het lezen van dit WXR bestand"

#: core/utils/import-export/wp-import.php:253
msgid "The file does not exist, please try again."
msgstr "Het bestand bestaat niet, probeer het opnieuw."

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:485
#: core/utils/import-export/wp-import.php:676
#: core/utils/import-export/wp-import.php:726
msgid "Failed to import %1$s %2$s"
msgstr "Kan %1$s %2$s niet importeren"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:584
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "Importeren van %1$s mislukt: ongeldig berichttype %2$s"

#: core/utils/import-export/wp-import.php:978
msgid "Fetching attachments is not enabled"
msgstr "Bijlagen ophalen is niet ingeschakeld"

#: core/utils/import-export/wp-import.php:995
msgid "Invalid file type"
msgstr "Ongeldig bestandstype"

#: core/utils/import-export/wp-import.php:1039
msgid "Could not create temporary file."
msgstr "Kan tijdelijk bestand niet maken."

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1055
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "Aanvraag mislukt vanwege een fout: %1$s (%2$s)"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1064
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "De externe server heeft het volgende onverwachte resultaat geretourneerd: %1$s (%2$s)"

#: core/utils/import-export/wp-import.php:1073
msgid "Remote server did not respond"
msgstr "De externe server reageerde niet"

#: core/utils/import-export/wp-import.php:1081
msgid "Zero size file downloaded"
msgstr "Bestand met nulgrootte gedownload"

#: core/utils/import-export/wp-import.php:1087
msgid "Downloaded file has incorrect size"
msgstr "Het gedownloade bestand heeft een onjuiste grootte"

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1095
msgid "Remote file is too large, limit is %s"
msgstr "Extern bestand is te groot, limiet is %s"

#: core/utils/import-export/wp-import.php:1127
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "Dit bestandstype is om veiligheidsredenen niet toegestaan."

#: core/utils/import-export/wp-import.php:1143
msgid "The uploaded file could not be moved"
msgstr "Het geüploade bestand kan niet worden verplaatst"

#: core/utils/import-export/wp-import.php:878
msgid "Menu item skipped due to missing menu slug"
msgstr "Menu-item overgeslagen vanwege ontbrekende menu slug"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:891
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "Menu-item overgeslagen vanwege ongeldige menu slug: %s"

#: modules/shapes/widgets/text-path.php:217
msgid "Show Path"
msgstr "Pad weergeven"

#: core/kits/documents/tabs/settings-layout.php:215
msgid "Active Breakpoints"
msgstr "Actieve breakpoints"

#: app/modules/import-export-customization/module.php:197
#: app/modules/import-export/module.php:199 core/admin/admin-notices.php:284
#: core/admin/admin-notices.php:412 core/admin/admin-notices.php:503
#: core/admin/admin-notices.php:551 core/admin/admin-notices.php:599
#: core/experiments/manager.php:317 core/experiments/manager.php:333
#: core/experiments/manager.php:362 core/experiments/manager.php:533
#: includes/controls/url.php:78 includes/elements/section.php:473
#: includes/settings/settings-page.php:404
#: includes/widgets/common-base.php:1141 includes/widgets/video.php:606
#: modules/ai/feature-intro/product-image-unification-intro.php:40
#: modules/checklist/steps/step-base.php:102
#: modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:150 assets/js/app.js:4734
#: assets/js/app.js:8515 assets/js/editor-v4-opt-in-alphachip.js:187
#: assets/js/editor-v4-opt-in.js:497 assets/js/editor-v4-welcome-opt-in.js:84
#: assets/js/editor.js:27990
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4449
msgid "Learn more"
msgstr "Lees verder"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:385
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "Nieuwe gebruiker aanmaken voor %s mislukt. Hun berichten worden toegewezen aan de huidige gebruiker."

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:320
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "Importeren van auteur %s mislukt. Hun berichten worden toegewezen aan de huidige gebruiker."

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:86
msgid "Privacy mode"
msgstr "Privacy-modus"

#. translators: %s Release status.
#: core/experiments/manager.php:564
msgid "Status: %s"
msgstr "Status: %s"

#: core/common/modules/finder/categories/settings.php:69
msgid "Experiments"
msgstr "Experimenten"

#: core/experiments/manager.php:464
msgid "No available experiments"
msgstr "Geen beschikbare experimenten"

#: core/experiments/manager.php:406
msgid "Stable"
msgstr "Stabiel"

#: core/experiments/manager.php:405 assets/js/ai-admin.js:658
#: assets/js/ai-admin.js:7771 assets/js/ai-gutenberg.js:796
#: assets/js/ai-gutenberg.js:7989 assets/js/ai-layout.js:490
#: assets/js/ai-layout.js:3259 assets/js/ai-media-library.js:658
#: assets/js/ai-media-library.js:7771 assets/js/ai-unify-product-images.js:658
#: assets/js/ai-unify-product-images.js:7771 assets/js/ai.js:1446
#: assets/js/ai.js:9323
msgid "Beta"
msgstr "Bèta"

#: core/experiments/manager.php:404 modules/atomic-widgets/module.php:303
#: assets/js/editor-v4-opt-in.js:345 assets/js/editor-v4-opt-in.js:492
msgid "Alpha"
msgstr "Alpha"

#: core/experiments/manager.php:403
msgid "Development"
msgstr "Ontwikkeling"

#: core/experiments/manager.php:677
msgid "Active by default"
msgstr "Standaard actief"

#: core/experiments/manager.php:678
msgid "Inactive by default"
msgstr "Standaard inactief"

#: core/experiments/manager.php:467
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "De huidige versie van Elementor heeft geen experimentele functies. Als je nieuwsgierig bent, kom dan zeker terug in toekomstige versies."

#: core/experiments/manager.php:532
msgid "To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."
msgstr "Om een experiment of functie op je site te gebruiken, klik je gewoon op de dropdown ernaast en wissel je naar actief. Je kan ze altijd weer deactiveren."

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:46 modules/landing-pages/module.php:163
#: modules/landing-pages/module.php:294 modules/landing-pages/module.php:306
#: assets/js/app.js:6328 assets/js/app.js:11352 assets/js/editor.js:53407
msgid "Landing Pages"
msgstr "Landingspagina's"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:139
msgid "Unknown"
msgstr "Onbekend"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "Getest tot versie %s"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:671
#: assets/js/element-manager-admin.js:732
msgid "Plugin"
msgstr "Plugin"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "Compatibiliteit waarschuwing"

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "Aangepaste kolomruimte"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:229 modules/landing-pages/module.php:295
msgid "Landing Page"
msgstr "Landingspagina"

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "Sommige van de plugins die je eerder gebruikte, zijn niet getest met de nieuwste versie van %1$s (%2$s). Om problemen te voorkomen, moet je ervoor zorgen dat ze allemaal up-to-date en compatibel zijn voordat je %1$s gaat updaten."

#: modules/landing-pages/module.php:47
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "Voegt een nieuw Elementor inhoudstype toe waarmee je direct prachtige landingspagina's kunt maken in een gestroomlijnde workflow."

#: modules/landing-pages/module.php:229
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "Bouw effectieve landingspagina's voor de marketingcampagnes van je bedrijf."

#: modules/landing-pages/module.php:298
msgid "Edit Landing Page"
msgstr "Bewerk landingspagina"

#: modules/landing-pages/module.php:299
msgid "New Landing Page"
msgstr "Nieuwe landingspagina"

#: modules/landing-pages/module.php:300
msgid "All Landing Pages"
msgstr "Alle landingspagina's"

#: modules/landing-pages/module.php:301
msgid "View Landing Page"
msgstr "Bekijk de landingspagina"

#: modules/landing-pages/module.php:302
msgid "Search Landing Pages"
msgstr "Zoek landingspagina's"

#: modules/landing-pages/module.php:303
msgid "No landing pages found"
msgstr "Geen landingspagina's gevonden"

#: modules/landing-pages/module.php:304
msgid "No landing pages found in trash"
msgstr "Geen landingspagina's gevonden in prullenbak"

#: modules/landing-pages/module.php:297
msgid "Add New Landing Page"
msgstr "Nieuwe landingspagina toevoegen"

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "Behoud mijn instellingen"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "Door het verwijderen van deze template zal je je gehele site instellingen verwijderen. Als deze template is verwijderd, zullen alle gerelateerde instellingen: globale kleuren & lettertypen, thema stijl, lay-out, achtergrond en lightbox instellingen verwijderd worden van je bestaande site. Deze actie kan niet ongedaan gemaakt worden."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "Weet je zeker dat je je site instellingen wil verwijderen?"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "De globale waarde die je probeert te gebruiken is niet beschikbaar."

#: includes/controls/media.php:195
msgid "Choose SVG"
msgstr "Kies SVG"

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "De Elementor sitebouwer heeft het allemaal: verslepen pagina bouwer, pixel perfect ontwerp, mobiel responsive bewerken, en meer. Ga nu aan de slag!"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43 assets/js/app.js:6344
#: assets/js/app.js:11360 assets/js/editor.js:47673
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:173
msgid "Global Colors"
msgstr "Globale kleuren"

#: core/kits/documents/tabs/settings-layout.php:350
#: modules/nested-tabs/widgets/nested-tabs.php:432
msgid "Breakpoint"
msgstr "Breakpoint"

#: core/kits/documents/tabs/settings-layout.php:183
#: modules/page-templates/module.php:159
msgid "Theme"
msgstr "Thema"

#: core/kits/documents/tabs/settings-layout.php:180
msgid "Default Page Layout"
msgstr "Standaard pagina lay-out"

#: modules/page-templates/module.php:365
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "Wijzigingen worden pas in het voorbeeld weergegeven nadat de pagina opnieuw is geladen."

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "Site identiteit"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:993
msgid "Site Name"
msgstr "Site naam"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "Kies naam"

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "Kies beschrijving"

#: core/kits/documents/tabs/settings-site-identity.php:118
msgid "Site Favicon"
msgstr "Site favicon"

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "Mobiele browser achtergrond"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "De metatag 'theme-color' ​​is alleen beschikbaar in ondersteunde browsers en apparaten."

#: core/settings/editor-preferences/model.php:174 assets/js/editor.js:48040
msgid "Design System"
msgstr "Ontwerpsysteem"

#: modules/nested-tabs/widgets/nested-tabs.php:371 assets/js/editor.js:48032
msgid "Additional Settings"
msgstr "Aanvullende instellingen"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:112
#: assets/js/app.js:11358 assets/js/app.js:11855 assets/js/editor.js:47622
#: assets/js/editor.js:47626 assets/js/editor.js:47636
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:9
msgid "Site Settings"
msgstr "Site instellingen"

#: core/settings/editor-preferences/model.php:38
#: includes/editor-templates/hotkeys.php:146 assets/js/editor.js:38592
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "User Preferences"
msgstr "Gebruiker voorkeuren"

#: core/admin/admin.php:867
msgid "Heads up, Please backup before upgrade!"
msgstr "Let op, maak een back-up voordat je een upgrade uitvoert!"

#: includes/frontend.php:1380
msgid "Download"
msgstr "Downloaden"

#: includes/widgets/image-box.php:402 includes/widgets/image.php:376
msgid "Object Fit"
msgstr "Object passend"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:180
#: modules/nested-accordion/widgets/nested-accordion.php:146
msgid "Items"
msgstr "Items"

#: includes/widgets/icon-list.php:213
msgid "Apply Link On"
msgstr "Link toepassen op"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:6344
#: assets/js/app.js:11360
msgid "Layout Settings"
msgstr "Lay-out instellingen"

#: core/kits/documents/tabs/settings-site-identity.php:125
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "Voorgestelde favicon afmetingen: 512 × 512 pixels."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/admin/admin.php:873
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"
msgstr "De nieuwste update bevat enkele substantiële wijzigingen in verschillende delen van de plugin. We raden je aan %1$som een back-up van je site te maken voordat je gaat upgraden%2$s, en zorg ervoor dat je eerst updatet in een staging omgeving"

#: app/modules/kit-library/module.php:135
#: core/frontend/render-mode-manager.php:152
#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "Fout"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "Site beschrijving"

#. translators: 1: Width number pixel, 2: Height number pixel.
#: core/kits/documents/tabs/settings-site-identity.php:102
msgid "Suggested image dimensions: %1$s × %2$s pixels."
msgstr "Voorgestelde afbeeldingafmetingen: %1$s × %2$s pixels."

#: includes/widgets/social-icons.php:485
msgid "Rows Gap"
msgstr "Rijen ruimte"

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "Fallback lettertype familie"

#: core/kits/documents/tabs/settings-layout.php:194
msgid "Breakpoints"
msgstr "Breakpoints"

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:997
msgid "Site Logo"
msgstr "Site logo"

#: includes/widgets/common-base.php:1157 includes/widgets/image-box.php:406
#: includes/widgets/image.php:383
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:131
msgid "Fill"
msgstr "Vullen"

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "Reset gegevens"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "Krijg de introductie voor Elementor door het kijken naar onze \"Getting Started\" video series. Het zal je begeleiden door de te nemen stappen om je site aan te maken. Daarna klik je om je eerste pagina aan te maken."

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "Bekijk de volledige handleiding"

#: includes/settings/settings.php:353 assets/js/admin.js:294
#: assets/js/admin.js:304 assets/js/ai-admin.js:64 assets/js/ai-admin.js:74
#: assets/js/ai-gutenberg.js:64 assets/js/ai-gutenberg.js:74
#: assets/js/ai-media-library.js:64 assets/js/ai-media-library.js:74
#: assets/js/ai-unify-product-images.js:64
#: assets/js/ai-unify-product-images.js:74 assets/js/ai.js:64
#: assets/js/ai.js:74 assets/js/common.js:64 assets/js/common.js:74
#: assets/js/editor.js:40406 assets/js/editor.js:40416
msgid "Enable Unfiltered File Uploads"
msgstr "Ongefilterde bestand uploads inschakelen"

#: includes/controls/media.php:270
msgid "Click the media icon to upload file"
msgstr "Klik op het media-icoon om het bestand te uploaden"

#: modules/safe-mode/module.php:367
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "Als je problemen ervaart tijdens het laden, schakel de veilige modus in en neem contact met je sitebeheerder."

#: includes/frontend.php:1378
msgid "Share on Twitter"
msgstr "Deel via Twitter"

#: includes/frontend.php:1377
msgid "Share on Facebook"
msgstr "Deel via Facebook"

#: includes/frontend.php:1381
msgid "Download image"
msgstr "Download afbeelding"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1382
msgid "Fullscreen"
msgstr "Volledig scherm"

#: core/experiments/manager.php:104 includes/editor-templates/global.php:27
#: includes/editor-templates/templates.php:232 assets/js/ai-admin.js:9560
#: assets/js/ai-gutenberg.js:9778 assets/js/ai-media-library.js:9560
#: assets/js/ai-unify-product-images.js:9560 assets/js/ai.js:11112
#: assets/js/app.js:7995 assets/js/editor.js:47928
msgid "Back"
msgstr "Terug"

#: core/kits/documents/kit.php:154
msgid "Draft"
msgstr "Concept"

#: includes/frontend.php:1379
msgid "Pin it"
msgstr "Pin dit"

#: includes/editor-templates/panel.php:314
msgid "You’re missing out!"
msgstr "Je loopt iets mis!"

#: includes/editor-templates/panel.php:285
msgid "Dynamic Tags"
msgstr "Dynamische tags"

#: includes/managers/controls.php:1204
msgid "Attributes"
msgstr "Attributen"

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "Klik hier om nu uit te voeren"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1384
msgid "Share"
msgstr "Delen"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "Veld"

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "Label"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "Formuliervelden"

#: core/kits/documents/tabs/theme-style-typography.php:48
msgid "Body"
msgstr "Body"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:221
msgid "Buttons"
msgstr "Knoppen"

#: includes/editor-templates/panel.php:315
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "Gebruik de ingebouwde dynamische tags van Elementor om meer dynamische mogelijkheden te krijgen."

#: includes/managers/icons.php:491
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "We raden je sterk aan om eerst een back-up van je database te maken voordat je deze upgrade uitvoert."

#: includes/managers/icons.php:490
msgid "The upgrade process includes a database update"
msgstr "De upgrade bevat ook een update voor de database"

#: core/kits/documents/kit.php:43
msgid "Kit"
msgstr "Kit"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "Focus"

#: core/isolation/elementor-adapter.php:28 core/kits/manager.php:156
#: core/kits/manager.php:174
msgid "Default Kit"
msgstr "Standaard kit"

#: includes/managers/controls.php:1214
msgid "Meet Our Attributes"
msgstr "Ontdek onze attributen"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "Stel aangepaste attributen in voor het link element. Scheid attribuut sleutels van waardes door het | (pipe) karakter te gebruiken. Scheid sleutelwaarde paren met een komma."

#: includes/controls/url.php:119
msgid "Custom Attributes"
msgstr "Aangepaste attributen"

#: core/kits/documents/tabs/theme-style-typography.php:75
#: includes/widgets/text-editor.php:306
msgid "Paragraph Spacing"
msgstr "Paragraaf afstand"

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "%s widget"

#: includes/editor-templates/panel.php:311
msgid "Elementor Dynamic Content"
msgstr "Dynamische Elementor inhoud"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "Grootte toolbar iconen"

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "Afmeting navigatie iconen"

#: includes/managers/controls.php:1216
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "Attributen maken het je mogelijk om aangepaste HTML attributen toe te voegen aan elk element."

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "Gebruik %s widget en tientallen andere Pro functies om je gereedschap uit te breiden en sneller en beter sites te bouwen."

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "Al verbonden."

#: core/settings/editor-preferences/model.php:51
msgid "Preferences"
msgstr "Voorkeuren"

#: includes/controls/groups/background.php:683
msgid "Background Size"
msgstr "Achtergrond grootte"

#: includes/widgets/image-carousel.php:473
msgid "Pause on Interaction"
msgstr "Pauzeren bij interactie"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689
#: includes/elements/container.php:561 includes/widgets/social-icons.php:299
#: includes/widgets/video.php:600
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:80
#: assets/js/packages/editor-controls/editor-controls.strings.js:148
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:129
msgid "Auto"
msgstr "Auto"

#: includes/controls/groups/background.php:702
msgid "Background Position"
msgstr "Achtergrondpositie"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:486
#: includes/controls/groups/background.php:691
#: includes/widgets/image-box.php:408 includes/widgets/image.php:385
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:150
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:133
msgid "Contain"
msgstr "Insluiten"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "Verbonden als %s"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "Verbinding maken met de bibliotheek is mislukt. Probeer de pagina opnieuw te laden en probeer het opnieuw"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "%s videospeler"

#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690
#: includes/widgets/image-box.php:407 includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:923
#: modules/link-in-bio/base/widget-link-in-bio-base.php:978
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:149
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:132
msgid "Cover"
msgstr "Bedekken"

#: core/settings/editor-preferences/model.php:78
msgid "Auto detect"
msgstr "Automatisch detecteren"

#: core/settings/editor-preferences/model.php:152 assets/js/ai-admin.js:15907
#: assets/js/ai-gutenberg.js:16125 assets/js/ai-layout.js:5206
#: assets/js/ai-media-library.js:15907
#: assets/js/ai-unify-product-images.js:15907 assets/js/ai.js:7842
#: assets/js/ai.js:17459 assets/js/editor.js:8001
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:446
msgid "Get Started"
msgstr "Ga aan de slag"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "Aangepaste iconen"

#: includes/controls/groups/background.php:655
msgid "Transition"
msgstr "Transitie"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "Wis logbestand"

#: includes/controls/groups/background.php:645
msgid "Duration"
msgstr "Looptijd"

#: includes/frontend.php:1387 assets/js/app.js:7815 assets/js/app.js:9765
#: assets/js/app.js:10710
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1627
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1678
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1957
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2233
msgid "Next"
msgstr "Volgende"

#: includes/frontend.php:1386 assets/js/app.js:8907 assets/js/app.js:9752
#: assets/js/app.js:10703
msgid "Previous"
msgstr "Vorige"

#: includes/widgets/divider.php:340 modules/shapes/module.php:46
msgid "Line"
msgstr "Lijn"

#: includes/widgets/divider.php:197
msgctxt "Shapes"
msgid "Arrows"
msgstr "Pijlen"

#: includes/widgets/divider.php:181
msgctxt "Shapes"
msgid "Wavy"
msgstr "Golvend"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:130
msgid "Basic Gallery"
msgstr "Basis galerij"

#: includes/controls/groups/background.php:754
msgid "Out"
msgstr "Uit"

#: includes/controls/groups/background.php:753
msgid "In"
msgstr "In"

#: includes/controls/groups/background.php:528
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "YouTube/Vimeo link, of link naar video bestand (mp4 wordt aanbevolen)."

#: includes/settings/tools.php:386
msgid "Reinstall"
msgstr "Installeer opnieuw"

#: core/document-types/post.php:51
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:57
msgid "Post"
msgstr "Bericht"

#: includes/controls/groups/background.php:583 includes/widgets/video.php:376
msgid "Play On Mobile"
msgstr "Afspelen op mobiel"

#: includes/controls/groups/background.php:738
msgid "Ken Burns Effect"
msgstr "Ken Burns effect"

#: includes/widgets/divider.php:213
msgctxt "Shapes"
msgid "Rhombus"
msgstr "Ruit"

#: includes/widgets/divider.php:283
msgctxt "Shapes"
msgid "Squares"
msgstr "Vierkanten"

#: includes/widgets/divider.php:140
msgctxt "Shapes"
msgid "Curly"
msgstr "Krullend"

#: includes/widgets/divider.php:221
msgctxt "Shapes"
msgid "Parallelogram"
msgstr "Parallellogram"

#: includes/widgets/divider.php:229
msgctxt "Shapes"
msgid "Rectangles"
msgstr "Rechthoeken"

#: includes/widgets/divider.php:265
msgctxt "Shapes"
msgid "Leaves"
msgstr "Bladeren"

#: includes/widgets/divider.php:274
msgctxt "Shapes"
msgid "Stripes"
msgstr "Strepen"

#: includes/widgets/divider.php:292
msgctxt "Shapes"
msgid "Trees"
msgstr "Bomen"

#: includes/widgets/divider.php:310
msgctxt "Shapes"
msgid "X"
msgstr "X"

#: includes/widgets/divider.php:148
msgctxt "Shapes"
msgid "Curved"
msgstr "Gebogen"

#: includes/widgets/divider.php:256
msgctxt "Shapes"
msgid "Half Rounds"
msgstr "Halve cirkels"

#: includes/widgets/divider.php:301
msgctxt "Shapes"
msgid "Tribal"
msgstr "Stam"

#: includes/widgets/divider.php:156
msgctxt "Shapes"
msgid "Multiple"
msgstr "Meervoudig"

#: includes/widgets/divider.php:173
msgctxt "Shapes"
msgid "Squared"
msgstr "Kwadraat"

#: includes/widgets/divider.php:164
msgctxt "Shapes"
msgid "Slashes"
msgstr "Slashes"

#: includes/widgets/divider.php:205
msgctxt "Shapes"
msgid "Pluses"
msgstr "Pluspunten"

#: includes/widgets/divider.php:238
msgctxt "Shapes"
msgid "Dots"
msgstr "Stippen"

#: includes/widgets/divider.php:247
msgctxt "Shapes"
msgid "Fir Tree"
msgstr "Dennenboom"

#: includes/widgets/divider.php:667
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:52
#: assets/js/packages/editor-controls/editor-controls.strings.js:54
#: assets/js/packages/editor-controls/editor-controls.strings.js:58
#: assets/js/packages/editor-controls/editor-controls.strings.js:60
#: assets/js/packages/editor-controls/editor-controls.strings.js:62
#: assets/js/packages/editor-controls/editor-controls.strings.js:64
msgid "Amount"
msgstr "Aantal"

#: includes/controls/groups/background.php:608
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "Deze omslagafbeelding vervangt de achtergrondvideo als de video niet kon worden geladen."

#: includes/widgets/divider.php:496
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:21
msgid "Add Element"
msgstr "Element toevoegen"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "Bestandslocatie: %s"

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "Je e-mailadres"

#: includes/template-library/sources/local.php:618
msgid "Template not exist."
msgstr "Template bestaat niet."

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:64
msgid "Sign Up"
msgstr "Registreren"

#: core/editor/editor.php:201
msgid "Document not found."
msgstr "Document niet gevonden."

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "Font Awesome - Merken"

#: includes/settings/settings.php:361
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "We adviseren je om deze optie alleen in te schakelen als je de bijkomende risico's begrijpt."

#: includes/managers/icons.php:493
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "Deze actie is niet omkeerbaar en kan niet ongedaan worden gemaakt door terug te gaan naar vorige versies."

#: includes/managers/icons.php:464
msgid "Load Font Awesome 4 Support"
msgstr "Laad Font Awesome 4 ondersteuning"

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome - Solide"

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome - Regulier"

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "Upload SVG"

#: includes/controls/media.php:192
msgid "Choose Video"
msgstr "Kies video"

#: includes/controls/media.php:283 includes/controls/media.php:285
#: assets/js/editor.js:6025
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:3
#: assets/js/packages/editor-controls/editor-controls.strings.js:40
msgid "Upload"
msgstr "Upload"

#: includes/managers/icons.php:551
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "Hoera! De upgrade naar Font Awesome 5 is succesvol afgerond."

#: includes/managers/icons.php:505
msgid "Upgrade To Font Awesome 5"
msgstr "Upgrade naar Font Awesome 5"

#: includes/controls/groups/background.php:573
msgid "Play Once"
msgstr "Eenmalig afspelen"

#: includes/managers/icons.php:479 includes/managers/icons.php:483
#: includes/managers/icons.php:498
msgid "Font Awesome Upgrade"
msgstr "Font Awesome upgrade"

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "Enkele van je themabestanden missen."

#: includes/settings/settings.php:361
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "Elementor zal proberen om de ongefilterde bestanden op te schonen door het verwijderen van potentieel gevaarlijke code en scripts."

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "Het .htaccess bestand van je site lijkt te ontbreken."

#: core/files/file-types/svg.php:73 core/files/uploads-manager.php:590
msgid "This file is not allowed for security reasons."
msgstr "Het bestand is om veiligheidsredenen niet toegestaan."

#: core/experiments/manager.php:658 includes/base/widget-base.php:1019
msgid "Deprecated"
msgstr "Verouderd"

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:27
#: includes/editor-templates/panel.php:172
msgid "Need Help"
msgstr "Hulp nodig"

#: includes/settings/settings.php:361
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "Let op! Het uploaden toestaan van alle bestanden (ook SVG & JSON bestanden) is een potentieel veiligheidsrisico."

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "Als bèta-tester, ontvang je een update met een testversie van Elementor en zijn inhoud direct in je e-mail"

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "Verkrijg bèta updates"

#: includes/managers/icons.php:248
msgid "All Icons"
msgstr "Alle iconen"

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:6606
msgid "Icon Library"
msgstr "Icoon bibliotheek"

#: includes/managers/icons.php:485
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "Krijg toegang tot 1.500+ verbazingwekkende Font Awesome 5 iconen en geniet van de betere snelheid en ontwerp flexibiliteit."

#: includes/managers/icons.php:488
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "Houd er rekening mee, dat door de upgrade sommige eerder gebruikte iconen van Font Awesome 4 er enigszins anders uitzien, als gevolg van kleine ontwerp aanpassingen die door Font Awesome zijn gemaakt."

#: includes/managers/icons.php:472
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "Font Awesome 4 ondersteuningsscript (shim.js) is een script dat er voor zorgt dat alle eerdere geselecteerde Font Awesome 4 iconen correct worden getoond bij gebruik van de Font Awesome 5 bibliotheek."

#: includes/managers/icons.php:486
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "Door te upgraden zal Elementor, wanneer je een pagina bewerkt die een Font Awesome 4 icoon bevat, dit omzetten naar het nieuwe Font Awesome 5 icoon."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/elements/column.php:934 includes/elements/container.php:1893
#: includes/elements/section.php:1401 includes/widgets/common-base.php:1342
msgid "Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor."
msgstr "Responsive zichtbaarheid wordt alleen van kracht op %1$s voorbeeldmodus %2$s of live pagina, en niet tijdens het bewerken in Elementor."

#: includes/elements/container.php:560 includes/elements/section.php:453
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:128
msgid "Hidden"
msgstr "Verborgen"

#: includes/elements/container.php:1656 includes/widgets/common-base.php:675
msgid "Vertical Orientation"
msgstr "Verticale oriëntatie"

#: includes/elements/container.php:1555 includes/widgets/common-base.php:574
msgid "Horizontal Orientation"
msgstr "Horizontale oriëntatie"

#: includes/controls/groups/flex-item.php:30
#: includes/widgets/common-base.php:359
msgid "Custom Width"
msgstr "Aangepaste breedte"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "Hulp vragen"

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:226
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "Ruimte eromheen"

#: includes/elements/section.php:420 includes/widgets/common-base.php:514
#: includes/widgets/image-carousel.php:743
msgid "Vertical Align"
msgstr "Verticale uitlijning"

#: includes/elements/container.php:555 includes/elements/section.php:448
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:126
msgid "Overflow"
msgstr "Overflow"

#: includes/elements/container.php:1536 includes/widgets/common-base.php:560
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:169
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:143
msgid "Fixed"
msgstr "Vast"

#: includes/elements/container.php:1535 includes/widgets/common-base.php:559
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:142
msgid "Absolute"
msgstr "Absoluut"

#: includes/elements/container.php:1518 includes/widgets/common-base.php:543
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "Aangepaste positionering wordt niet beschouwd als de beste methode voor responsive webdesign en zou niet te vaak gebruikt moeten worden."

#: includes/elements/column.php:869 includes/elements/container.php:1813
#: includes/elements/section.php:1310 includes/widgets/common-base.php:830
msgid "Motion Effects"
msgstr "Bewegingseffecten"

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:230
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:193 includes/elements/column.php:221
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "Gelijkmatig verdelen"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "Super beheerder"

#: includes/base/element-base.php:1010 includes/elements/container.php:1580
#: includes/elements/container.php:1618 includes/elements/container.php:1680
#: includes/elements/container.php:1717 includes/widgets/common-base.php:599
#: includes/widgets/common-base.php:637 includes/widgets/common-base.php:699
#: includes/widgets/common-base.php:736
#: modules/floating-buttons/base/widget-contact-button-base.php:2961
#: modules/floating-buttons/base/widget-contact-button-base.php:3015
msgid "Offset"
msgstr "Offset"

#: includes/elements/container.php:1517 includes/widgets/common-base.php:542
msgid "Please note!"
msgstr "Let op!"

#: modules/safe-mode/module.php:358
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "Problemen met het laden van Elementor? Schakel veilige modus in om problemen op te lossen."

#: modules/safe-mode/module.php:256
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "Het probleem is waarschijnlijk veroorzaakt door één van je plugins of thema's."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:136
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "Let op: de ID link accepteert ALLEEN deze karakters: %s"

#: includes/controls/media.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:389
msgid "Choose File"
msgstr "Kies bestand"

#: includes/widgets/video.php:245
msgid "External URL"
msgstr "Externe link"

#: includes/widgets/image-gallery.php:263
msgid "Order By"
msgstr "Sorteer op"

#: includes/widgets/read-more.php:95
msgid "Continue reading"
msgstr "Lees verder"

#: includes/widgets/read-more.php:40 includes/widgets/read-more.php:91
msgid "Read More"
msgstr "Lees verder"

#: includes/widgets/google-maps.php:154
#: modules/floating-buttons/base/widget-contact-button-base.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:958
#: modules/link-in-bio/base/widget-link-in-bio-base.php:502
#: modules/link-in-bio/base/widget-link-in-bio-base.php:753
msgid "Location"
msgstr "Locatie"

#. translators: %s: Current post name.
#: includes/frontend.php:1522
msgid "Continue reading %s"
msgstr "Lees verder %s"

#: includes/template-library/sources/local.php:322
msgctxt "Template Library"
msgid "Category"
msgstr "Categorie"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "Templates"

#: modules/safe-mode/module.php:354
msgid "Enable Safe Mode"
msgstr "Schakel veilige modus in"

#: modules/safe-mode/module.php:246 modules/safe-mode/module.php:476
msgid "Disable Safe Mode"
msgstr "Schakel veilige modus uit"

#: modules/safe-mode/module.php:244
msgid "Safe Mode ON"
msgstr "Veilige modus AAN"

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "Veilige modus"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:316
msgid "Every %d minutes"
msgstr "Elke %d minuten"

#: modules/safe-mode/module.php:352 modules/safe-mode/module.php:364
msgid "Can't Edit?"
msgstr "Kun je niets bewerken?"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1732
#: assets/js/app-packages.js:4613 assets/js/editor.js:47647
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:7
msgid "Theme Builder"
msgstr "Thema bouwer"

#: modules/safe-mode/module.php:253
msgid "Editor successfully loaded?"
msgstr "Editor succesvol geladen?"

#: modules/safe-mode/module.php:269
msgid "Still experiencing issues?"
msgstr "Heb je nog steeds problemen?"

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "Veilige modus helpt je om problemen op te lossen door alleen de Editor te laden, zonder het thema of andere plugins."

#: includes/frontend.php:1515
msgid "(more&hellip;)"
msgstr "(meer&hellip;)"

#: includes/template-library/sources/local.php:323
msgctxt "Template Library"
msgid "All Categories"
msgstr "Alle categorieën"

#: includes/template-library/sources/local.php:321
msgctxt "Template Library"
msgid "Categories"
msgstr "Categorieën"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "Veilige modus kan niet worden ingeschakeld"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "Verkrijg Popup Builder"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "Je site database moet geüpdatet worden naar de nieuwste versie."

#: core/upgrade/manager.php:51
msgid "Elementor Data Updater"
msgstr "Elementor gegevens updater"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:19
msgid "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."
msgstr "Met Popup Builder profiteer je van alle geweldige functies in Elementor, zodat je prachtige & sterk converterende pop-ups kunt maken. Ga voor Pro en begin vandaag met het ontwerpen van je pop-ups."

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "Het database update proces is klaar. Bedankt voor het updaten naar de nieuwste versie!"

#: includes/template-library/sources/local.php:1733
#: modules/promotions/admin-menu-items/popups-promotion-item.php:41
#: modules/promotions/admin-menu-items/popups-promotion-item.php:45
#: assets/js/app.js:6336 assets/js/app.js:11343
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:2649
msgid "Popups"
msgstr "Pop-ups"

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:115
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "Let op: deze widget is alleen van toepassing op thema's die `%s` gebruiken op archiefpagina's."

#: includes/template-library/sources/local.php:1455
msgctxt "Template Library"
msgid "Filter by category"
msgstr "Filteren op categorie"

#: includes/widgets/read-more.php:124
msgid "Read More Text"
msgstr "Lees verder tekst"

#: modules/library/documents/not-supported.php:56
msgid "Not Supported"
msgstr "Niet ondersteund"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "Gebruikers"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:4618
#: assets/js/app.js:6350 assets/js/app.js:11863
msgid "Plugins"
msgstr "Plugins"

#: includes/widgets/video.php:517
msgid "Any Video"
msgstr "Elke video"

#: includes/widgets/rating.php:122 includes/widgets/star-rating.php:388
msgid "Unmarked Color"
msgstr "Ongemarkeerde kleur"

#: includes/widgets/star-rating.php:318
msgid "Stars"
msgstr "Sterren"

#: includes/widgets/star-rating.php:184
msgid "Unmarked Style"
msgstr "Ongemarkeerde stijl"

#: includes/widgets/rating.php:147 includes/widgets/star-rating.php:140
msgid "Rating Scale"
msgstr "Waardering schaal"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:140
#: includes/widgets/rating.php:164 includes/widgets/star-rating.php:153
msgid "Rating"
msgstr "Waardering"

#: includes/editor-templates/hotkeys.php:160
msgid "Go To"
msgstr "Ga naar"

#: includes/editor-templates/hotkeys.php:21 assets/js/ai-admin.js:12592
#: assets/js/ai-admin.js:13678 assets/js/ai-gutenberg.js:12810
#: assets/js/ai-gutenberg.js:13896 assets/js/ai-media-library.js:12592
#: assets/js/ai-media-library.js:13678
#: assets/js/ai-unify-product-images.js:12592
#: assets/js/ai-unify-product-images.js:13678 assets/js/ai.js:14144
#: assets/js/ai.js:15230 assets/js/editor.js:9472
#: assets/js/kit-elements-defaults-editor.js:232
msgid "Undo"
msgstr "Ongedaan maken"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "Customizer"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "Dashboard"

#: core/common/modules/connect/apps/base-app.php:87
msgid "Disconnect"
msgstr "Verbinding verbreken"

#: core/common/modules/connect/apps/base-app.php:220
#: core/common/modules/connect/rest/rest-api.php:87 assets/js/editor.js:9868
#: assets/js/editor.js:9933 assets/js/editor.js:10915
msgid "Connected successfully."
msgstr "Succesvol verbonden."

#: core/base/document.php:1996
msgid "Future"
msgstr "Toekomst"

#: includes/editor-templates/hotkeys.php:96 assets/js/admin-top-bar.js:187
#: assets/js/common.js:2566 assets/js/editor.js:38604
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
msgid "Finder"
msgstr "Vinder"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:26
msgid "Homepage"
msgstr "Homepage"

#: core/common/modules/connect/apps/base-app.php:232
#: core/common/modules/connect/rest/rest-api.php:117
msgid "Disconnected successfully."
msgstr "Verbinding succesvol verbroken."

#: includes/widgets/image.php:163 includes/widgets/image.php:175
msgid "Custom Caption"
msgstr "Aangepaste bijschrift"

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: core/utils/hints.php:461 includes/editor-templates/templates.php:521
#: modules/cloud-library/module.php:144 assets/js/ai-admin.js:1073
#: assets/js/ai-admin.js:6392 assets/js/ai-gutenberg.js:1211
#: assets/js/ai-gutenberg.js:6610 assets/js/ai-layout.js:771
#: assets/js/ai-layout.js:2498 assets/js/ai-media-library.js:1073
#: assets/js/ai-media-library.js:6392 assets/js/ai-unify-product-images.js:1073
#: assets/js/ai-unify-product-images.js:6392 assets/js/ai.js:1861
#: assets/js/ai.js:7853 assets/js/ai.js:7944 assets/js/editor.js:9818
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3256
msgid "Connect"
msgstr "Verbind"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "Typ om alles te vinden in Elementor"

#: includes/editor-templates/hotkeys.php:29 assets/js/ai-admin.js:12603
#: assets/js/ai-admin.js:13689 assets/js/ai-gutenberg.js:12821
#: assets/js/ai-gutenberg.js:13907 assets/js/ai-media-library.js:12603
#: assets/js/ai-media-library.js:13689
#: assets/js/ai-unify-product-images.js:12603
#: assets/js/ai-unify-product-images.js:13689 assets/js/ai.js:14155
#: assets/js/ai.js:15241
msgid "Redo"
msgstr "Opnieuw"

#: includes/widgets/image-gallery.php:175 includes/widgets/image.php:162
msgid "Attachment Caption"
msgstr "Bijlage bijschrift"

#: includes/editor-templates/hotkeys.php:201
msgid "Quit"
msgstr "Beëindigen"

#: includes/widgets/star-rating.php:192
msgid "Outline"
msgstr "Omlijning"

#: includes/editor-templates/hotkeys.php:193 assets/js/editor.js:5336
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Keyboard Shortcuts"
msgstr "Toetsenbord sneltoetsen"

#: includes/editor-templates/hotkeys.php:104
msgid "Show / Hide Panel"
msgstr "Toon/verberg paneel"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:122
msgid "Star Rating"
msgstr "Ster waardering"

#: core/common/modules/finder/categories/create.php:27 assets/js/editor.js:8874
#: assets/js/editor.js:46245
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:24
#: assets/js/packages/editor-variables/editor-variables.strings.js:36
msgid "Create"
msgstr "Maken"

#: includes/widgets/video.php:516
msgid "Current Video Channel"
msgstr "Huidig videokanaal"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "Menu's"

#: includes/widgets/video.php:619
msgid "Poster"
msgstr "Poster"

#: includes/editor-templates/navigator.php:90
msgid "Empty"
msgstr "Leeg"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:124 assets/js/app-packages.js:1359
#: assets/js/app.js:1718
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1214
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1432
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1524
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1775
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1948
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2268
msgid "Skip"
msgstr "Overslaan"

#: core/admin/admin-notices.php:318
msgid "Congrats!"
msgstr "Gefeliciteerd!"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:28004
msgid "Inner Section"
msgstr "Inner sectie"

#: includes/editor-templates/navigator.php:95
msgid "Easy Navigation is Here!"
msgstr "Gemakkelijke navigatie is er!"

#: core/admin/admin-notices.php:322
msgid "Happy To Help"
msgstr "Blij je van dienst te zijn"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "Tint"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "Foutopsporingsbalk"

#: includes/widgets/accordion.php:171 includes/widgets/accordion.php:175
#: includes/widgets/icon-box.php:186 includes/widgets/image-box.php:161
#: includes/widgets/tabs.php:170 includes/widgets/tabs.php:174
#: includes/widgets/testimonial.php:145 includes/widgets/text-editor.php:142
#: includes/widgets/toggle.php:174 includes/widgets/toggle.php:178
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."

#: includes/editor-templates/navigator.php:96
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "Zodra je je pagina met inhoud vult, geeft dit venster je een overzicht van alle pagina-elementen. Op deze manier kan je eenvoudig door een sectie, kolom of widget navigeren."

#: includes/widgets/video.php:181 includes/widgets/video.php:206
#: includes/widgets/video.php:230 includes/widgets/video.php:290
msgid "Enter your URL"
msgstr "Voer je URL in"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "Maak je eerste pagina"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "Maak je eerste bericht"

#: includes/widgets/video.php:479
msgid "Lazy Load"
msgstr "Lazy-load"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:744
msgid "Getting Started"
msgstr "Aan de slag"

#: core/admin/admin-notices.php:319
msgid "You created over 10 pages with Elementor. Great job! If you can spare a minute, please help us by leaving a five star review on WordPress.org."
msgstr "Je hebt meer dan 10 pagina's gemaakt met Elementor. Geweldig gedaan! Als je een minuutje tijd hebt, help ons dan door een beoordeling van vijf sterren achter te laten op WordPress.org."

#: modules/floating-buttons/base/widget-contact-button-base.php:951
#: modules/floating-buttons/base/widget-floating-bars-base.php:166
#: modules/floating-buttons/base/widget-floating-bars-base.php:352
#: modules/link-in-bio/base/widget-link-in-bio-base.php:271
#: modules/shapes/widgets/text-path.php:164
msgid "Paste URL or type"
msgstr "Plak URL of typ"

#: core/admin/admin-notices.php:328
msgid "Hide Notification"
msgstr "Melding verbergen"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "Welkom bij Elementor"

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1383
#: includes/widgets/google-maps.php:174 assets/js/ai-admin.js:10220
#: assets/js/ai-admin.js:10223 assets/js/ai-gutenberg.js:10438
#: assets/js/ai-gutenberg.js:10441 assets/js/ai-media-library.js:10220
#: assets/js/ai-media-library.js:10223
#: assets/js/ai-unify-product-images.js:10220
#: assets/js/ai-unify-product-images.js:10223 assets/js/ai.js:11772
#: assets/js/ai.js:11775
msgid "Zoom"
msgstr "Zoom"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "Foutopsporingsbalk voegt een beheerdersbalkmenu toe met alle templates die worden gebruikt op een pagina die wordt weergegeven."

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "Enkelvoudig"

#: includes/widgets/video.php:451
msgid "Logo"
msgstr "Logo"

#: includes/widgets/video.php:424
msgid "Video Info"
msgstr "Video informatie"

#: core/base/providers/social-network-provider.php:216
#: includes/widgets/video.php:277 includes/widgets/video.php:301
msgid "URL"
msgstr "URL"

#: includes/widgets/video.php:155
msgid "Source"
msgstr "Bron"

#: includes/widgets/traits/button-trait.php:205
msgid "Button ID"
msgstr "Knop ID"

#: includes/managers/elements.php:317
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:310 modules/admin-bar/module.php:149
msgid "Site"
msgstr "Site"

#: includes/editor-templates/templates.php:122
#: includes/editor-templates/templates.php:640
#: includes/managers/elements.php:292
#: modules/promotions/widgets/pro-widget-promotion.php:67
#: assets/js/ai-admin.js:7987 assets/js/ai-gutenberg.js:8205
#: assets/js/ai-layout.js:3475 assets/js/ai-media-library.js:7987
#: assets/js/ai-unify-product-images.js:7987 assets/js/ai.js:9539
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3704
msgid "Pro"
msgstr "Pro"

#: includes/editor-templates/global.php:49
#: assets/js/152f977e0c1304a3b0db.bundle.js:128
msgid "Drag widget here"
msgstr "Sleep widget hierheen"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "Verzadiging"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "Contrast"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "Helderheid"

#: includes/controls/groups/background.php:561 includes/widgets/video.php:339
msgid "End Time"
msgstr "Eindtijd"

#: includes/controls/groups/background.php:549 includes/widgets/video.php:328
msgid "Start Time"
msgstr "Starttijd"

#: includes/widgets/video.php:163
msgid "Self Hosted"
msgstr "Eigen hosting"

#: includes/widgets/video.php:161
msgid "Dailymotion"
msgstr "Dailymotion"

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "Elementor Debugger"

#: includes/controls/groups/background.php:563 includes/widgets/video.php:341
msgid "Specify an end time (in seconds)"
msgstr "Geef een eindtijd op (in seconden)"

#: includes/widgets/audio.php:193
msgid "Artwork"
msgstr "Illustratiemateriaal"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "Vervagen"

#: includes/controls/groups/background.php:551 includes/widgets/video.php:330
msgid "Specify a start time (in seconds)"
msgstr "Geef een starttijd op (in seconden)"

#: core/admin/feedback.php:105
msgid "I have Elementor Pro"
msgstr "Ik heb Elementor Pro"

#: core/admin/feedback.php:107
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "Wacht! Deactiveer Elementor niet. Je moet zowel Elementor als Elementor Pro activeren om met de plugin te kunnen werken."

#: includes/editor-templates/hotkeys.php:38
#: includes/editor-templates/templates.php:165 assets/js/editor.js:8572
#: assets/js/editor.js:8586 assets/js/editor.js:30658
msgid "Copy"
msgstr "Kopiëren"

#: includes/elements/column.php:447 includes/elements/container.php:856
#: includes/elements/section.php:715 includes/widgets/heading.php:324
msgid "Blend Mode"
msgstr "Overvloeimodus"

#. translators: 1: `<code>` opening tag, 2: `</code>` closing tag.
#: includes/widgets/traits/button-trait.php:217
msgid "Please make sure the ID is unique and not used elsewhere on the page. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."
msgstr "Zorg ervoor dat het ID uniek is en niet ergens anders op de pagina wordt gebruikt. Dit veld staat %1$sA-z 0-9%2$s en underscore karakters toe zonder spaties."

#: core/admin/admin.php:219 assets/js/admin.js:2097 assets/js/gutenberg.js:147
msgid "Back to WordPress Editor"
msgstr "Terug naar de WordPress editor"

#. translators: %s: Document title.
#: core/documents-manager.php:388
msgid "Elementor %s"
msgstr "Elementor %s"

#. translators: %s: Document title.
#. translators: %s: Template type label.
#: core/base/document.php:269
#: core/common/modules/finder/categories/create.php:86
#: core/document-types/page-base.php:183
#: includes/template-library/sources/local.php:1416
msgid "Add New %s"
msgstr "Nieuwe %s toevoegen"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:416 includes/elements/column.php:490
#: includes/elements/container.php:810 includes/elements/container.php:924
#: includes/elements/section.php:669 includes/elements/section.php:773
#: includes/widgets/image-box.php:504 includes/widgets/image-box.php:539
#: includes/widgets/image.php:441 includes/widgets/image.php:475
#: modules/floating-buttons/base/widget-floating-bars-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1502
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:212
msgid "Opacity"
msgstr "Doorzichtigheid"

#: includes/widgets/image.php:319
msgid "Max Width"
msgstr "Maximale breedte"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:147
msgid "New Template"
msgstr "Nieuwe template"

#: includes/controls/groups/background.php:265
#: includes/controls/groups/background.php:313
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1530 includes/widgets/common-base.php:554
#: includes/widgets/common-base.php:1203 includes/widgets/divider.php:766
#: includes/widgets/divider.php:932 includes/widgets/image-carousel.php:583
#: includes/widgets/image-carousel.php:647 includes/widgets/tabs.php:184
#: includes/widgets/traits/button-trait.php:252
#: modules/floating-buttons/base/widget-floating-bars-base.php:448
#: modules/link-in-bio/base/widget-link-in-bio-base.php:943
#: modules/link-in-bio/base/widget-link-in-bio-base.php:998
#: modules/nested-accordion/widgets/nested-accordion.php:213
#: modules/nested-tabs/widgets/nested-tabs.php:857
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:73
#: assets/js/packages/editor-controls/editor-controls.strings.js:168
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:12
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:137
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:139
msgid "Position"
msgstr "Positie"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:408
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "Genoten van %1$s? Geef ons een %2$s waardering. We waarderen je ondersteuning!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "Kennisbank"

#: modules/page-templates/module.php:340
msgid "No header, no footer, just Elementor"
msgstr "Geen header, geen footer, alleen Elementor"

#: modules/page-templates/module.php:352
msgid "This template includes the header, full-width content and footer"
msgstr "Deze template bevat de header, inhoud over volledige breedte en footer"

#: modules/page-templates/module.php:297
msgid "Page Layout"
msgstr "Pagina lay-out"

#: modules/page-templates/module.php:328
msgid "Default Page Template from your theme."
msgstr "Standaard pagina template van je thema."

#: includes/frontend.php:1385 includes/widgets/video.php:999
msgid "Play Video"
msgstr "Video afspelen"

#: includes/widgets/common-base.php:343 includes/widgets/icon-list.php:126
#: includes/widgets/icon-list.php:217
msgid "Inline"
msgstr "Inline"

#: includes/template-library/sources/local.php:1281
msgid "All"
msgstr "Alle"

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "Mijn templates"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "Template aanmaken"

#: includes/admin-templates/new-floating-elements.php:47
#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "Naam template invoeren (optioneel)"

#: includes/admin-templates/new-floating-elements.php:44
#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "Geef je template een naam"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "Selecteer het type template waar je aan wilt werken"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "Type template kiezen"

#: includes/editor-templates/templates.php:310
#: includes/editor-templates/templates.php:355
#: includes/editor-templates/templates.php:402
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:50
msgid "More actions"
msgstr "Meer acties"

#: includes/editor-templates/templates.php:152
msgid "Search Templates:"
msgstr "Templates zoeken:"

#: core/document-types/page.php:72 modules/library/documents/page.php:61
#: assets/js/app.js:12162 assets/js/editor.js:7963
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:2621
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "Pagina's"

#: includes/editor-templates/global.php:120
msgid "This tag has no settings."
msgstr "Deze tag heeft geen instellingen."

#: core/document-types/page-base.php:230
msgid "Featured Image"
msgstr "Uitgelichte afbeelding"

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "Stijl body"

#: core/base/document.php:259
msgid "Document"
msgstr "Document"

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "Actie niet gevonden."

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "Token verlopen."

#: core/document-types/page-base.php:182
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:296 assets/js/app-packages.js:3211
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "Nieuwe toevoegen"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "Rol uitgesloten"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "Geen toegang tot editor"

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/app.js:6344
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:248
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:235
msgid "Custom Fonts"
msgstr "Aangepaste lettertypen"

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "Wil je alleen toegang geven tot de inhoud?"

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1231 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:77
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:32
msgid "%s Settings"
msgstr "%s instellingen"

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "Beheer wat je gebruikers kunnen bewerken in Elementor"

#: includes/template-library/sources/local.php:1376
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "Voeg templates toe en gebruik deze opnieuw door je gehele site. Importeer en exporteer ze eenvoudig naar elk ander project, voor een optimale workflow."

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1409
msgid "Create Your First %s"
msgstr "Je eerste %s aanmaken"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:389
msgid "Fallback"
msgstr "Fallback"

#: includes/widgets/counter.php:210
msgid "Separator"
msgstr "Scheidingsteken"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "Rol beheerder"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "Gebruik templates om de verschillende stukken van je site aan te maken, en hergebruik ze met één klik indien nodig."

#: includes/widgets/image-carousel.php:204
msgid "Set how many slides are scrolled per swipe."
msgstr "Stel in hoeveel slides per veeg worden gescrold."

#: core/admin/admin.php:477
msgid "Create New Post"
msgstr "Nieuw bericht aanmaken"

#: includes/controls/groups/background.php:448
msgid "Note: Attachment Fixed works only on desktop."
msgstr "Let op: bijlage vastzetting werkt alleen op desktop."

#: includes/fonts.php:77
msgid "Google (Early Access)"
msgstr "Google (Early Access)"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "Huidige versie"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1555
msgid "Last edited on %1$s by %2$s"
msgstr "Laatst bewerkt op %1$s door %2$s"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1548
msgid "Draft saved on %1$s by %2$s"
msgstr "Concept opgeslagen op %1$s door %2$s"

#: core/kits/documents/kit.php:155
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "Gepubliceerd"

#: includes/editor-templates/templates.php:565
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2120
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2462
msgid "or"
msgstr "of"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:10979
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:2086
msgid "No Results Found"
msgstr "Geen resultaten gevonden"

#: includes/widgets/traits/button-trait.php:57
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:50
#: assets/js/app.js:8781
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2474
msgid "Click here"
msgstr "Klik hier"

#: includes/editor-templates/templates.php:256
msgid "Favorite"
msgstr "Favoriet"

#: includes/editor-templates/templates.php:208
msgid "Creation Date"
msgstr "Aanmaakdatum"

#: includes/editor-templates/templates.php:204
msgid "Created By"
msgstr "Aangemaakt door"

#: includes/editor-templates/templates.php:153
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:89
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:21
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:14
#: assets/js/packages/editor-variables/editor-variables.strings.js:26
msgid "Search"
msgstr "Zoeken"

#: includes/editor-templates/templates.php:103
msgid "My Favorites"
msgstr "Mijn favorieten"

#: includes/editor-templates/templates.php:81
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4742
msgid "Popular"
msgstr "Populair"

#: includes/editor-templates/templates.php:77
#: includes/editor-templates/templates.php:121
#: includes/editor-templates/templates.php:639
#: assets/js/atomic-widgets-editor.js:801 assets/js/editor.js:33491
#: assets/js/editor.js:33989 assets/js/editor.js:48583
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4738
msgid "New"
msgstr "Nieuw"

#: core/kits/views/panel.php:40 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:215
#: includes/controls/media.php:217 includes/controls/media.php:279
#: includes/controls/media.php:281 includes/editor-templates/repeater.php:23
#: modules/promotions/widgets/pro-widget-promotion.php:75
#: assets/js/ai-admin.js:2309 assets/js/ai-admin.js:7478
#: assets/js/ai-gutenberg.js:2447 assets/js/ai-gutenberg.js:7696
#: assets/js/ai-layout.js:2966 assets/js/ai-media-library.js:2309
#: assets/js/ai-media-library.js:7478 assets/js/ai-unify-product-images.js:2309
#: assets/js/ai-unify-product-images.js:7478 assets/js/ai.js:3097
#: assets/js/ai.js:9030
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:748
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:86
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:68
msgid "Remove"
msgstr "Verwijderen"

#: includes/editor-templates/hotkeys.php:70
#: includes/editor-templates/repeater.php:18 assets/js/editor.js:30643
#: assets/js/editor.js:52113
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:83
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:16
msgid "Duplicate"
msgstr "Dupliceren"

#: includes/editor-templates/panel.php:122
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "Save Draft"
msgstr "Concept opslaan"

#: core/base/document.php:173 includes/editor-templates/panel.php:103
#: assets/js/editor.js:25670
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:28
msgid "Publish"
msgstr "Publiceren"

#: core/admin/admin.php:617
msgid "Blog"
msgstr "Blog"

#: core/admin/admin.php:597
msgid "(opens in a new window)"
msgstr "(opent in een nieuw venster)"

#: core/admin/admin.php:565
msgid "News & Updates"
msgstr "Nieuws & updates"

#: core/admin/admin.php:524
msgid "Recently Edited"
msgstr "Recent bewerkt"

#: core/document-types/page-base.php:215
msgid "Excerpt"
msgstr "Samenvatting"

#: includes/editor-templates/panel.php:126
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:23
msgid "Save as Template"
msgstr "Als template opslaan"

#: includes/editor-templates/panel.php:140
#: includes/editor-templates/panel.php:142 assets/js/editor.js:36900
msgid "Hide Panel"
msgstr "Paneel verbergen"

#: includes/editor-templates/templates.php:14
msgid "Import Template"
msgstr "Template importeren"

#: includes/editor-templates/templates.php:563
msgid "Import Template to Your Library"
msgstr "Template naar je bibliotheek importeren"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "Ontkoppelde waarden"

#: core/experiments/manager.php:541 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "Terug naar standaard"

#: includes/editor-templates/panel.php:91
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:30
msgid "Preview Changes"
msgstr "Voorbeeld wijzigingen"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "Decoration"
msgstr "Decoratie"

#: includes/editor-templates/templates.php:566 assets/js/app-packages.js:1031
#: assets/js/app.js:1390
msgid "Select File"
msgstr "Selecteer bestand"

#: core/base/document.php:1542
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "j M, H:i"

#: core/admin/admin.php:532
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "j M"

#: includes/editor-templates/templates.php:79
msgid "Trend"
msgstr "Trend"

#: includes/editor-templates/panel-elements.php:74
msgid "Search Widget:"
msgstr "Widget zoeken:"

#: includes/editor-templates/repeater.php:12
msgid "Drag & Drop"
msgstr "Hier naartoe slepen"

#: core/admin/admin.php:474
msgid "Create New Page"
msgstr "Nieuwe pagina aanmaken"

#: includes/controls/groups/background.php:597 includes/widgets/video.php:466
msgid "Privacy Mode"
msgstr "Privacymodus"

#: includes/widgets/shortcode.php:110
msgid "Enter your shortcode"
msgstr "Voer je shortcode in"

#: includes/widgets/html.php:107
msgid "Enter your code"
msgstr "Voer je code in"

#: includes/widgets/alert.php:150 includes/widgets/icon-box.php:187
#: includes/widgets/image-box.php:162
msgid "Enter your description"
msgstr "Voer je beschrijving in"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Underline"
msgstr "Onderstreept"

#: includes/controls/groups/typography.php:192
msgctxt "Typography Control"
msgid "Line Through"
msgstr "Doorstreept"

#. translators: %s: Document title.
#: core/base/document.php:200
msgid "Hurray! Your %s is live."
msgstr "Hoera! Je %s is live."

#: includes/widgets/heading.php:193
msgid "Add Your Heading Text Here"
msgstr "Voeg je koptekst hier toe"

#: includes/editor-templates/panel.php:108
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:26
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:27
msgid "Save Options"
msgstr "Opties opslaan"

#: includes/widgets/image.php:178
msgid "Enter your image caption"
msgstr "Voer het bijschrift van je afbeelding in"

#: includes/editor-templates/templates.php:564
msgid "Drag & drop your .JSON or .zip template file"
msgstr "Versleep je .JSON of .zip template bestand"

#: core/admin/admin.php:428
msgid "Elementor Overview"
msgstr "Elementor overzicht"

#: includes/widgets/video.php:468
msgid "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."
msgstr "Als je privacy modus inschakelt, dan slaat YouTube/Vimeo geen informatie op over bezoekers van je site, tenzij ze de video afspelen."

#: includes/widgets/accordion.php:213 includes/widgets/toggle.php:216
#: modules/nested-tabs/widgets/nested-tabs.php:137
msgid "Active Icon"
msgstr "Actief icoon"

#: includes/controls/groups/typography.php:191
msgctxt "Typography Control"
msgid "Overline"
msgstr "Lijn boven tekst"

#: includes/widgets/alert.php:137
msgid "This is an Alert"
msgstr "Dit is een waarschuwing"

#: includes/template-library/sources/local.php:496
#: includes/template-library/sources/local.php:612
#: includes/template-library/sources/local.php:762
#: includes/template-library/sources/local.php:771
msgid "Access denied."
msgstr "Toegang geweigerd."

#: includes/settings/settings.php:288
msgid "Disable Default Fonts"
msgstr "Standaard lettertypen uitschakelen"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:218
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:218 includes/widgets/accordion.php:427
#: includes/widgets/common-base.php:526 includes/widgets/counter.php:292
#: includes/widgets/counter.php:326 includes/widgets/counter.php:400
#: includes/widgets/counter.php:436 includes/widgets/icon-list.php:584
#: includes/widgets/image-carousel.php:755 includes/widgets/rating.php:211
#: includes/widgets/tabs.php:217 includes/widgets/tabs.php:247
#: includes/widgets/toggle.php:451 includes/widgets/traits/button-trait.php:151
#: includes/widgets/traits/button-trait.php:296
#: modules/floating-buttons/base/widget-floating-bars-base.php:586
#: modules/floating-buttons/base/widget-floating-bars-base.php:1197
#: modules/floating-buttons/base/widget-floating-bars-base.php:1296
#: modules/nested-accordion/widgets/nested-accordion.php:181
#: modules/nested-accordion/widgets/nested-accordion.php:221
#: modules/nested-tabs/widgets/nested-tabs.php:242
#: modules/nested-tabs/widgets/nested-tabs.php:284
#: modules/nested-tabs/widgets/nested-tabs.php:354
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:95
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:162
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:198
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:203
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:208
msgid "End"
msgstr "Einde"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:216 includes/widgets/accordion.php:423
#: includes/widgets/common-base.php:518 includes/widgets/counter.php:288
#: includes/widgets/counter.php:318 includes/widgets/counter.php:392
#: includes/widgets/counter.php:428 includes/widgets/icon-list.php:576
#: includes/widgets/image-carousel.php:747 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:209 includes/widgets/tabs.php:239
#: includes/widgets/toggle.php:447 includes/widgets/traits/button-trait.php:147
#: includes/widgets/traits/button-trait.php:288
#: modules/floating-buttons/base/widget-floating-bars-base.php:582
#: modules/floating-buttons/base/widget-floating-bars-base.php:1189
#: modules/floating-buttons/base/widget-floating-bars-base.php:1292
#: modules/nested-accordion/widgets/nested-accordion.php:173
#: modules/nested-accordion/widgets/nested-accordion.php:217
#: modules/nested-tabs/widgets/nested-tabs.php:234
#: modules/nested-tabs/widgets/nested-tabs.php:276
#: modules/nested-tabs/widgets/nested-tabs.php:346
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:93
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:160
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:196
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:201
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:206
msgid "Start"
msgstr "Begin"

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:44
msgid "The preview could not be loaded"
msgstr "Het voorbeeld kon niet worden geladen"

#: core/debug/loading-inspection-manager.php:43
msgid "We’re sorry, but something went wrong. Click on 'Learn more' and follow each of the steps to quickly solve it."
msgstr "Er is iets misgegaan. Klik op 'Lees verder' en volg alle stappen om het snel op te lossen."

#: core/admin/admin-notices.php:147 core/admin/admin-notices.php:182
msgid "Update Notification"
msgstr "Melding update"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#: includes/editor-templates/hotkeys.php:16
#: includes/editor-templates/templates.php:211
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:51840
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1307
msgid "Actions"
msgstr "Acties"

#: includes/editor-templates/hotkeys.php:137
#: includes/editor-templates/panel.php:85 assets/js/ai-admin.js:2058
#: assets/js/ai-gutenberg.js:2196 assets/js/ai-media-library.js:2058
#: assets/js/ai-unify-product-images.js:2058 assets/js/ai.js:2846
#: assets/js/editor.js:52435
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:17
msgid "History"
msgstr "Geschiedenis"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:51843
msgid "Revisions"
msgstr "Revisies"

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "Nog geen geschiedenis"

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "Schakel naar de revisies-tab voor oudere versies"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:905
msgid "UI Color"
msgstr "UI kleur"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "Zodra je begint te werken, kun je elke actie in de editor opnieuw doen / ongedaan maken."

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:917
msgid "UI Hover Color"
msgstr "UI hoveren kleur"

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "Lightbox afbeelding"

#: includes/widgets/video.php:388
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:81
msgid "Mute"
msgstr "Dempen"

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "Open alle links van afbeeldingen in een lightbox pop-up venster. De lightbox zal automatisch werken op elke link die leidt naar een afbeeldingsbestand."

#: includes/template-library/sources/local.php:1018
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "Kies een Elementor template JSON-bestand of een .zip-archief van Elementor templates en voeg ze toe aan de lijst van beschikbare templates in je bibliotheek."

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:365
msgid "Version Control"
msgstr "Versiecontrole"

#. Translators: %s: Element Name.
#. Translators: %s: Element name.
#. translators: %s: Element type title.
#: core/document-types/page-base.php:184 assets/js/atomic-widgets-editor.js:945
#: assets/js/editor.js:30438 assets/js/editor.js:30630
#: assets/js/editor.js:33069 assets/js/editor.js:33539
#: assets/js/editor.js:33640 assets/js/editor.js:33966
#: assets/js/editor.js:37493 assets/js/editor.js:48727
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:18
msgid "Edit %s"
msgstr "%s bewerken"

#: includes/controls/url.php:112
msgid "Open in new window"
msgstr "Open in nieuw venster"

#: core/common/modules/finder/categories/settings.php:54
#: includes/settings/settings.php:304
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Integrations"
msgstr "Integraties"

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:237
msgid "Widgets Space"
msgstr "Ruimte van de widget"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "Contouren"

#: includes/elements/column.php:906 includes/elements/container.php:1850
#: includes/elements/section.php:1347 includes/widgets/common-base.php:867
#: modules/floating-buttons/base/widget-contact-button-base.php:1417
#: modules/floating-buttons/base/widget-floating-bars-base.php:916
msgid "Animation Delay"
msgstr "Vertraging animatie"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1264
#: includes/controls/groups/background.php:673 includes/elements/column.php:361
#: includes/elements/column.php:521 includes/elements/column.php:629
#: includes/elements/container.php:733 includes/elements/container.php:947
#: includes/elements/container.php:1107 includes/elements/section.php:617
#: includes/elements/section.php:804 includes/elements/section.php:911
#: includes/widgets/alert.php:446 includes/widgets/common-base.php:933
#: includes/widgets/common-base.php:1048 includes/widgets/google-maps.php:252
#: includes/widgets/heading.php:400 includes/widgets/icon-box.php:482
#: includes/widgets/icon-box.php:717 includes/widgets/icon-list.php:470
#: includes/widgets/icon-list.php:697 includes/widgets/image-box.php:557
#: includes/widgets/image-box.php:685 includes/widgets/image.php:501
#: includes/widgets/text-editor.php:389
#: includes/widgets/traits/button-trait.php:451
#: modules/floating-buttons/base/widget-contact-button-base.php:1494
#: modules/floating-buttons/base/widget-contact-button-base.php:2233
#: modules/nested-tabs/widgets/nested-tabs.php:609
#: modules/shapes/widgets/text-path.php:460
#: modules/shapes/widgets/text-path.php:638
msgid "Transition Duration"
msgstr "Duur overgang"

#: includes/settings/settings.php:341
msgid "Switch Editor Loader Method"
msgstr "Wijzig de methode waarop de editor wordt geladen"

#: includes/settings/tools.php:381
msgid "Rollback Version"
msgstr "Versie terugdraaien"

#: includes/rollback.php:165 includes/settings/tools.php:193
#: includes/settings/tools.php:368 assets/js/admin.js:2291
msgid "Rollback to Previous Version"
msgstr "Naar vorige versie terugdraaien"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:372
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Ondervind je problemen met Elementor versie %s? Draai dan terug naar een vorige versie voordat het probleem verscheen."

#: includes/controls/url.php:103
msgid "Link Options"
msgstr "Linkinstellingen"

#: includes/settings/tools.php:389
msgid "Warning: Please backup your database before making the rollback."
msgstr "Waarschuwing: maak eerst een back-up van je database, voordat je terugdraait naar een vorige versie."

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "Stelt de standaard ruimte tussen widgets in (standaard: 20px)"

#: includes/settings/tools.php:420
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "Let op: we raden het af om naar een bèta versie te updaten op productie sites."

#: includes/settings/tools.php:412
msgid "Beta Tester"
msgstr "Bèta tester"

#: includes/elements/column.php:797 includes/elements/container.php:1754
#: includes/elements/section.php:1258 includes/widgets/common-base.php:773
msgid "Z-Index"
msgstr "Z-index"

#: includes/settings/tools.php:399
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "Schakel bèta tester in om meldingen te krijgen als een nieuwe bèta versie van Elementor of Elementor Pro beschikbaar is. De bèta versie zal niet automatisch installeren. Je hebt altijd de optie om het te negeren."

#: includes/settings/tools.php:396
msgid "Become a Beta Tester"
msgstr "Word bèta tester"

#: includes/controls/url.php:116
msgid "Add nofollow"
msgstr "Nofollow toevoegen"

#: includes/settings/settings.php:418
msgid "External File"
msgstr "Extern bestand"

#: includes/settings/settings.php:412
msgid "CSS Print Method"
msgstr "CSS afdruk methode"

#: includes/settings/settings.php:419
msgid "Internal Embedding"
msgstr "Interne insluiting"

#: core/debug/inspector.php:54 includes/settings/settings.php:346
#: includes/settings/settings.php:358 includes/settings/settings.php:371
#: includes/settings/settings.php:435 includes/settings/settings.php:452
#: includes/settings/settings.php:464 includes/settings/tools.php:417
#: modules/element-cache/module.php:146 modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:381
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "Uitschakelen"

#: core/debug/inspector.php:55 includes/settings/settings.php:347
#: includes/settings/settings.php:359 includes/settings/settings.php:370
#: includes/settings/settings.php:434 includes/settings/settings.php:451
#: includes/settings/settings.php:463 includes/settings/tools.php:418
#: modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:382
#: modules/safe-mode/module.php:48 assets/js/admin.js:294
#: assets/js/ai-admin.js:64 assets/js/ai-gutenberg.js:64
#: assets/js/ai-media-library.js:64 assets/js/ai-unify-product-images.js:64
#: assets/js/ai.js:64 assets/js/app-packages.js:1355 assets/js/app.js:1714
#: assets/js/common.js:64 assets/js/editor.js:40406
#: assets/js/packages/editor-controls/editor-controls.js:18
#: assets/js/packages/editor-controls/editor-controls.strings.js:109
msgid "Enable"
msgstr "Inschakelen"

#: core/base/document.php:2002 modules/ai/preferences.php:67
#: assets/js/element-manager-admin.js:655
#: assets/js/element-manager-admin.js:732
msgid "Status"
msgstr "Status"

#: includes/settings/settings.php:349
msgid "For troubleshooting server configuration conflicts."
msgstr "Voor probleemoplossing van conflicten met de serverconfiguratie."

#: includes/widgets/common-base.php:1308 includes/widgets/spacer.php:130
#: includes/widgets/text-editor.php:503
msgid "Space"
msgstr "Ruimte"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "Wie heeft toegang"

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "Onderhoudsmodus geeft de HTTP 503 code terug, dus zoekmachines weten dat ze binnenkort terug moeten komen. Het is niet aangeraden deze modus te gebruiken voor langer dan enkele dagen."

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "Onderhoud"

#: core/kits/documents/kit.php:154 includes/maintenance-mode.php:215
#: assets/js/editor.js:52112
msgid "Disabled"
msgstr "Uitgeschakeld"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "Kies modus"

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "Onderhoudsmodus"

#: core/settings/editor-preferences/model.php:107
msgid "Canvas"
msgstr "Canvas"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "Onderhoudsmodus AAN"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:10620
#: assets/js/editor.js:19318
msgid "Template"
msgstr "Template"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "Kies template"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "Om onderhoudsmodus in te schakelen moet je een template instellen voor de onderhoudsmodus pagina."

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:10576
msgid "Edit Template"
msgstr "Template bewerken"

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "Rollen"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "Ingelogd"

#: includes/widgets/text-editor.php:148 includes/widgets/text-editor.php:410
msgid "Drop Cap"
msgstr "Initiaal"

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "Komt binnenkort"

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "Selecteren"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "Titel verbergen"

#: core/kits/documents/tabs/settings-layout.php:139
msgid "Page Title Selector"
msgstr "Selector titel pagina"

#: includes/editor-templates/hotkeys.php:54
msgid "Paste Style"
msgstr "Plak stijl"

#: core/kits/documents/tabs/settings-layout.php:143
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "Elementor laat je de titel van de pagina verbergen. Dit werkt voor thema's die de \"h1.entry-title\" selector hebben. Als de selector van je thema anders is, voer die dan hierboven in."

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "Komt binnenkort geeft een HTTP 200 code, dit betekent dat de site klaar is om geïndexeerd te worden."

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "Kies tussen komt binnenkort modus (geeft HTTP 200 code) of onderhoudmodus (geeft HTTP 503 code)."

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "Stel je volledige site in als ONDERHOUDMODUS, dit betekent dat de site tijdelijk offline is voor onderhoud, of zet deze in KOMT BINNENKORT modus, dit betekent dat de site offline is totdat hij klaar is voor lancering."

#: includes/elements/container.php:1326 includes/elements/section.php:1109
msgid "Bring to Front"
msgstr "Naar voren brengen"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:387
#: includes/widgets/image-gallery.php:200 includes/widgets/image.php:225
#: includes/widgets/video.php:733 includes/widgets/video.php:881
msgid "Lightbox"
msgstr "Lightbox"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:222
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:428 includes/widgets/icon-list.php:238
#: includes/widgets/toggle.php:317
msgid "Space Between"
msgstr "Tussenruimte"

#: includes/widgets/icon-list.php:230
msgid "List"
msgstr "Lijst"

#: includes/shapes.php:149
msgctxt "Shapes"
msgid "Clouds"
msgstr "Wolken"

#: includes/shapes.php:142
msgctxt "Shapes"
msgid "Drops"
msgstr "Druppels"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "Bergen"

#: includes/shapes.php:219
msgctxt "Shapes"
msgid "Book"
msgstr "Boek"

#: includes/shapes.php:224
msgctxt "Shapes"
msgid "Split"
msgstr "Splijt"

#: includes/shapes.php:229
msgctxt "Shapes"
msgid "Arrow"
msgstr "Pijl"

#: includes/shapes.php:203
msgctxt "Shapes"
msgid "Waves"
msgstr "Golven"

#: includes/shapes.php:192
msgctxt "Shapes"
msgid "Curve"
msgstr "Curve"

#: includes/shapes.php:166
msgctxt "Shapes"
msgid "Triangle"
msgstr "Driehoek"

#: includes/shapes.php:160
msgctxt "Shapes"
msgid "Pyramids"
msgstr "Pyramides"

#: includes/shapes.php:156 includes/widgets/divider.php:189
#: includes/widgets/divider.php:319
msgctxt "Shapes"
msgid "Zigzag"
msgstr "Zigzag"

#: includes/elements/container.php:1313 includes/elements/section.php:1096
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:61
msgid "Invert"
msgstr "Omkeren"

#: includes/shapes.php:171
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "Asymmetrische driehoek"

#: includes/shapes.php:197
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "Asymmetrische curve"

#: includes/shapes.php:214
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "Golfpatroon"

#: includes/shapes.php:177
msgctxt "Shapes"
msgid "Tilt"
msgstr "Kanteling"

#: includes/shapes.php:188
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "Doorzichtige stralen"

#: includes/elements/container.php:1173 includes/elements/section.php:956
msgid "Shape Divider"
msgstr "Vormscheiding"

#: includes/shapes.php:209
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "Golven borstel"

#: includes/elements/container.php:1299 includes/elements/section.php:1082
msgid "Flip"
msgstr "Spiegelen"

#: includes/shapes.php:183
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "Kantel doorzichtigheid"

#: includes/elements/column.php:809 includes/elements/container.php:1766
#: includes/elements/section.php:1270 includes/widgets/common-base.php:784
#: modules/floating-buttons/base/widget-contact-button-base.php:3090
#: modules/floating-buttons/base/widget-floating-bars-base.php:1496
#: modules/nested-accordion/widgets/nested-accordion.php:129
#: modules/nested-tabs/widgets/nested-tabs.php:151
msgid "CSS ID"
msgstr "CSS ID"

#: includes/widgets/tabs.php:275
msgid "Navigation Width"
msgstr "Navigatie breedte"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:148
#: includes/base/element-base.php:883 includes/elements/column.php:346
#: includes/elements/column.php:475 includes/elements/column.php:594
#: includes/elements/container.php:718 includes/elements/container.php:899
#: includes/elements/container.php:1060 includes/elements/section.php:602
#: includes/elements/section.php:758 includes/elements/section.php:876
#: includes/widgets/alert.php:429 includes/widgets/common-base.php:918
#: includes/widgets/common-base.php:1013 includes/widgets/google-maps.php:237
#: includes/widgets/heading.php:382 includes/widgets/icon-box.php:440
#: includes/widgets/icon-box.php:694 includes/widgets/icon-list.php:450
#: includes/widgets/icon-list.php:678 includes/widgets/icon.php:256
#: includes/widgets/image-box.php:524 includes/widgets/image-box.php:662
#: includes/widgets/image.php:468 includes/widgets/text-editor.php:371
#: includes/widgets/traits/button-trait.php:393
#: modules/floating-buttons/base/widget-contact-button-base.php:1253
#: modules/floating-buttons/base/widget-contact-button-base.php:2012
#: modules/floating-buttons/base/widget-contact-button-base.php:2494
#: modules/floating-buttons/base/widget-contact-button-base.php:2674
#: modules/floating-buttons/base/widget-floating-bars-base.php:690
#: modules/floating-buttons/base/widget-floating-bars-base.php:1394
#: modules/nested-accordion/widgets/nested-accordion.php:663
#: modules/nested-accordion/widgets/nested-accordion.php:726
#: modules/nested-tabs/widgets/nested-tabs.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:773
#: modules/nested-tabs/widgets/nested-tabs.php:954
#: modules/shapes/widgets/text-path.php:433
#: modules/shapes/widgets/text-path.php:572
msgid "Hover"
msgstr "Hoveren"

#: includes/elements/column.php:818 includes/elements/container.php:1775
#: includes/elements/section.php:1279 includes/widgets/common-base.php:793
#: includes/widgets/traits/button-trait.php:214
#: modules/floating-buttons/base/widget-contact-button-base.php:3099
#: modules/floating-buttons/base/widget-floating-bars-base.php:1505
#: modules/nested-accordion/widgets/nested-accordion.php:138
#: modules/nested-tabs/widgets/nested-tabs.php:160
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "Voeg je aangepaste id ZONDER het hekje toe. Bijvoorbeeld: my-id"

#: includes/controls/groups/background.php:230
msgctxt "Background Control"
msgid "Type"
msgstr "Type"

#: includes/controls/groups/background.php:184
#: includes/controls/groups/background.php:213
msgctxt "Background Control"
msgid "Location"
msgstr "Locatie"

#: core/admin/admin.php:378
msgid "Video Tutorials"
msgstr "Videohandleidingen"

#: includes/settings/settings.php:284
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "Dit vakje aanvinken zal de standaardkleuren van Elementor uitschakelen en zorgt ervoor dat Elementor de kleuren van je thema overneemt."

#: core/admin/admin.php:377
msgid "Docs & FAQs"
msgstr "Docs & FAQ's"

#: core/admin/admin.php:378
msgid "View Elementor Video Tutorials"
msgstr "Videohandleidingen Elementor bekijken"

#: core/admin/admin.php:377
msgid "View Elementor Documentation"
msgstr "Documentatie Elementor bekijken"

#: includes/settings/settings.php:292
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "Dit vakje aanvinken zal de standaard lettertypen van Elementor uitschakelen en zorgt ervoor dat Elementor de lettertypes van je thema overneemt."

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "Revisie"

#: modules/apps/admin-apps-page.php:177
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "Door"

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "%1$s geleden (%2$s)"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "Automatisch opslaan"

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "Nog geen revisies opgeslagen"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:334 includes/settings/tools.php:338
#: includes/settings/tools.php:355
msgid "Replace URL"
msgstr "URL vervangen"

#: includes/settings/tools.php:356
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "Voer je oude en nieuwe URL's van je Wordpress-installatie in om alle gegevens van Elementor bij te werken (relevant voor domeinveranderingen of overgang naar 'HTTPS')."

#: includes/settings/tools.php:352
msgid "Update Site Address (URL)"
msgstr "Siteadres updaten (URL)"

#. translators: 1: Minimum recommended_memory, 2: Preferred memory, 3:
#. WordPress wp-config memory documentation.
#: modules/system-info/reporters/server.php:170
msgid "We recommend setting memory to at least %1$s. (%2$s or higher is preferred) For more information, read about <a href=\"%3$s\">how to increase memory allocated to PHP</a>."
msgstr "We raden aan om het geheugen in te stellen op ten minste %1$s. (%2$s of hoger heeft de voorkeur) Lees voor meer informatie over <a href=\"%3$s\">hoe het aan PHP toegewezen geheugen te vergroten</a>."

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "Het lijkt erop dat de bericht revisie functionaliteit niet beschikbaar is voor je site."

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "j M @ H:i"

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "Begin met het ontwerpen van je pagina en je zult de gehele revisie geschiedenis hier zien."

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "Revisiegeschiedenis laat je je vorige versies van je werk opslaan en ze op ieder moment terugzetten."

#: includes/widgets/counter.php:199
msgid "Thousand Separator"
msgstr "Scheidingsteken duizendtallen"

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "Krijg meer met Elementor Pro"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1075
msgid "Custom CSS"
msgstr "Aangepaste CSS"

#: includes/base/element-base.php:926 includes/base/element-base.php:1090
#: includes/widgets/common-base.php:1091 includes/widgets/icon-list.php:285
#: includes/widgets/icon.php:330 includes/widgets/text-editor.php:151
#: includes/widgets/video.php:737 modules/shapes/widgets/text-path.php:219
msgid "On"
msgstr "Aan"

#: includes/base/element-base.php:927 includes/base/element-base.php:1091
#: includes/widgets/common-base.php:1092 includes/widgets/icon-list.php:284
#: includes/widgets/icon.php:329 includes/widgets/text-editor.php:150
#: includes/widgets/video.php:736 modules/shapes/widgets/text-path.php:220
msgid "Off"
msgstr "Uit"

#: includes/managers/controls.php:1093
msgid "Meet Our Custom CSS"
msgstr "Ontdek onze aangepaste CSS"

#: includes/editor-templates/panel-elements.php:102
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "Met deze functionaliteit kun je een widget als globaal opslaan en het dan in meerdere gebieden toevoegen. Alle gebieden zijn dan bewerkbaar vanaf een enkele plek."

#: includes/editor-templates/panel-elements.php:101
msgid "Meet Our Global Widget"
msgstr "Ontdek onze globale widget"

#: modules/promotions/widgets/pro-widget-promotion.php:76
#: assets/js/ai-admin.js:7918 assets/js/ai-gutenberg.js:8136
#: assets/js/ai-layout.js:3406 assets/js/ai-media-library.js:7918
#: assets/js/ai-unify-product-images.js:7918 assets/js/ai.js:9470
msgid "Go Pro"
msgstr "Koop Pro"

#: includes/managers/controls.php:1081
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "Aangepaste CSS laat je CSS code aan elke widget toevoegen en toont het direct live in de editor."

#: includes/widgets/traits/button-trait.php:38
msgid "Extra Large"
msgstr "Extra groot"

#: includes/widgets/traits/button-trait.php:34
msgid "Extra Small"
msgstr "Extra klein"

#: includes/base/widget-base.php:312 includes/base/widget-base.php:321
msgid "Skin"
msgstr "Uiterlijk"

#: includes/settings/settings.php:298
msgid "Improve Elementor"
msgstr "Elementor verbeteren"

#: includes/frontend.php:1228
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "Ongeldige gegevens: het template ID kan niet hetzelfde zijn als de huidige bewerkte template. Kies een andere."

#: includes/editor-templates/panel.php:198
msgid "%s are disabled"
msgstr "%s zijn uitgeschakeld"

#: includes/editor-templates/panel.php:162
msgid "Update changes to page"
msgstr "Wijzigingen aan pagina updaten"

#: core/admin/admin-notices.php:243
msgid "No thanks"
msgstr "Nee bedankt"

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "Sectie uitrekken"

#: core/kits/documents/tabs/settings-layout.php:158
msgid "Stretched Section Fit To"
msgstr "Uitgerekte sectie past tot"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "Rek de sectie uit tot de volledige breedte van de pagina met behulp van JS."

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "Stelt de standaard breedte van het inhoud gebied in (standaard: 1140px)"

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "Voer de selector van het hoofd element in waar uitgerekte secties naar aan moeten passen (bijv. #primair / .wrapper / main, enz.) Laat leeg om aan de pagina breedte aan te passen."

#: includes/elements/section.php:1378
msgid "Reverse Columns"
msgstr "Kolommen omkeren"

#: core/admin/admin-notices.php:231
msgid "Learn more."
msgstr "Lees verder."

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "Waarden aan elkaar koppelen"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:103
msgid "Shortcode"
msgstr "Shortcode"

#: core/base/document.php:172 includes/editor-templates/global.php:21
#: includes/editor-templates/responsive-bar.php:65 includes/frontend.php:1388
#: assets/js/152f977e0c1304a3b0db.bundle.js:208 assets/js/ai-admin.js:586
#: assets/js/ai-gutenberg.js:724 assets/js/ai-layout.js:418
#: assets/js/ai-media-library.js:586 assets/js/ai-unify-product-images.js:586
#: assets/js/ai.js:1374 assets/js/app-packages.js:525
#: assets/js/app-packages.js:2564 assets/js/app-packages.js:3122
#: assets/js/app.js:593 assets/js/app.js:2835 assets/js/app.js:3257
#: assets/js/app.js:6161 assets/js/app.js:6194 assets/js/app.js:12275
#: assets/js/editor.js:47931 assets/js/import-export-admin.js:313
msgid "Close"
msgstr "Sluiten"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:9621
#: assets/js/import-export-admin.js:300
msgid "Library"
msgstr "Bibliotheek"

#: includes/editor-templates/templates.php:305
#: includes/editor-templates/templates.php:393
#: includes/editor-templates/templates.php:439
#: includes/editor-templates/templates.php:453 assets/js/ai-admin.js:6712
#: assets/js/ai-gutenberg.js:6930 assets/js/ai-media-library.js:6712
#: assets/js/ai-unify-product-images.js:6712 assets/js/ai.js:8264
#: assets/js/editor.js:6426
msgid "Insert"
msgstr "Invoegen"

#: core/document-types/page.php:58 modules/library/documents/page.php:57
#: assets/js/editor.js:8482
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:58
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "Pagina"

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "Lokaal"

#: includes/editor-templates/global.php:45
msgid "Add Template"
msgstr "Template toevoegen"

#: app/modules/import-export-customization/module.php:140
#: app/modules/import-export/module.php:142
#: includes/editor-templates/templates.php:328
#: includes/editor-templates/templates.php:361
#: includes/editor-templates/templates.php:418
#: includes/template-library/sources/local.php:1203 assets/js/app.js:5536
#: assets/js/app.js:5584 assets/js/app.js:5656 assets/js/app.js:6120
#: assets/js/app.js:12435
msgid "Export"
msgstr "Exporteren"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "Template"

#: includes/template-library/sources/local.php:280
msgctxt "Template Library"
msgid "Type"
msgstr "Type"

#: includes/template-library/sources/cloud.php:106
#: includes/template-library/sources/cloud.php:327
#: includes/template-library/sources/local.php:500
msgid "(no title)"
msgstr "(geen titel)"

#: includes/template-library/sources/local.php:988
msgid "Export Template"
msgstr "Template exporteren"

#: includes/template-library/sources/local.php:1025
msgid "Import Now"
msgstr "Nu importeren"

#: includes/template-library/sources/remote.php:61
msgid "Remote"
msgstr "Extern"

#: includes/template-library/sources/local.php:1016
msgid "Import Templates"
msgstr "Templates importeren"

#: includes/editor-templates/templates.php:41
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1633
msgid "Back to Library"
msgstr "Terug naar bibliotheek"

#: includes/editor-templates/templates.php:19 includes/settings/tools.php:322
#: includes/settings/tools.php:325
msgid "Sync Library"
msgstr "Bibliotheek synchroniseren"

#: includes/editor-templates/templates.php:221
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "Blijf kijken! Heel binnenkort komen er meer prachtige templates."

#: includes/editor-templates/templates.php:484
msgid "Enter Template Name"
msgstr "Voer naam template in"

#: includes/editor-templates/hotkeys.php:174
#: includes/editor-templates/templates.php:554
#: includes/editor-templates/templates.php:570
#: includes/editor-templates/templates.php:584
msgid "Template Library"
msgstr "Template bibliotheek"

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1731 assets/js/app.js:6336
#: assets/js/app.js:11342
msgid "Saved Templates"
msgstr "Opgeslagen templates"

#: includes/settings/tools.php:326
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "De Elementor bibliotheek wordt dagelijks automatisch geüpdatet. Je kunt deze ook handmatig updaten door op de synchroniseren knop te klikken."

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:453
msgid "Tools"
msgstr "Tools"

#. translators: %s: WordPress child themes documentation.
#: modules/system-info/reporters/theme.php:207
msgid "If you want to modify the source code of your theme, we recommend using a <a href=\"%s\">child theme</a>."
msgstr "Als je de broncode van je thema wil wijzigen, raden we je aan een <a href=\"%s\">subthema</a> te gebruiken."

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47 assets/js/app.js:6344
#: assets/js/app.js:11360 assets/js/editor.js:47682
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:222
msgid "Global Fonts"
msgstr "Globale lettertypen"

#: core/admin/admin-notices.php:371 modules/apps/admin-apps-page.php:187
#: modules/safe-mode/module.php:359 modules/safe-mode/module.php:368
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:105
#: assets/js/app-packages.js:1229 assets/js/app-packages.js:4375
#: assets/js/app-packages.js:4483 assets/js/app.js:1588 assets/js/app.js:4693
#: assets/js/app.js:6192 assets/js/app.js:7824 assets/js/app.js:8145
#: assets/js/app.js:9194 assets/js/app.js:10731 assets/js/app.js:11061
#: assets/js/app.js:11107 assets/js/app.js:12274 assets/js/editor.js:15016
#: assets/js/editor.js:28536 assets/js/editor.js:28567
#: assets/js/editor.js:41143 assets/js/element-manager-admin.js:542
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4018
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4773
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:27
msgid "Learn More"
msgstr "Lees verder"

#: modules/floating-buttons/base/widget-contact-button-base.php:2216
#: assets/js/ai-admin.js:3600 assets/js/ai-gutenberg.js:3738
#: assets/js/ai-media-library.js:3600 assets/js/ai-unify-product-images.js:3600
#: assets/js/ai.js:4421
msgid "Animation"
msgstr "Animatie"

#: core/base/traits/shared-widget-controls-trait.php:289
#: includes/widgets/icon-box.php:497 includes/widgets/icon.php:294
#: includes/widgets/image-box.php:578 includes/widgets/image.php:519
#: includes/widgets/social-icons.php:577
#: includes/widgets/traits/button-trait.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:1476
#: modules/floating-buttons/base/widget-contact-button-base.php:2512
#: modules/floating-buttons/base/widget-floating-bars-base.php:748
#: modules/nested-tabs/widgets/nested-tabs.php:601
#: modules/shapes/widgets/text-path.php:452
msgid "Hover Animation"
msgstr "Hoveren animatie"

#: includes/elements/column.php:892 includes/elements/container.php:1836
#: includes/elements/section.php:1333 includes/widgets/common-base.php:853
#: modules/floating-buttons/base/widget-contact-button-base.php:1406
#: modules/floating-buttons/base/widget-contact-button-base.php:2789
#: modules/floating-buttons/base/widget-floating-bars-base.php:887
msgid "Slow"
msgstr "Traag"

#: includes/elements/column.php:894 includes/elements/container.php:1838
#: includes/elements/section.php:1335 includes/widgets/common-base.php:855
#: modules/floating-buttons/base/widget-contact-button-base.php:1408
#: modules/floating-buttons/base/widget-contact-button-base.php:2791
#: modules/floating-buttons/base/widget-floating-bars-base.php:889
msgid "Fast"
msgstr "Snel"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:193
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:76
msgid "Horizontal"
msgstr "Horizontaal"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:189
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:77
msgid "Vertical"
msgstr "Verticaal"

#: includes/controls/box-shadow.php:83
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:79
msgid "Spread"
msgstr "Verspreiding"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "Inset"

#: includes/settings/settings.php:280
msgid "Disable Default Colors"
msgstr "Standaardkleuren uitschakelen"

#: includes/elements/column.php:879 includes/elements/container.php:1823
#: includes/elements/section.php:1320 includes/widgets/common-base.php:840
#: includes/widgets/video.php:929
#: modules/floating-buttons/base/widget-contact-button-base.php:1392
#: modules/floating-buttons/base/widget-floating-bars-base.php:873
msgid "Entrance Animation"
msgstr "Inkomende animatie"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:49
#: assets/js/packages/editor-controls/editor-controls.strings.js:78
#: assets/js/packages/editor-controls/editor-controls.strings.js:123
msgid "Blur"
msgstr "Vervagen"

#: includes/widgets/testimonial.php:46 includes/widgets/testimonial.php:132
msgid "Testimonial"
msgstr "Aanbeveling"

#: includes/widgets/icon-box.php:152 includes/widgets/icon.php:150
#: includes/widgets/social-icons.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:2086
#: modules/floating-buttons/base/widget-contact-button-base.php:2177
#: modules/floating-buttons/base/widget-contact-button-base.php:2870
#: modules/floating-buttons/base/widget-floating-bars-base.php:845
#: modules/link-in-bio/base/widget-link-in-bio-base.php:119
msgid "Rounded"
msgstr "Afgerond"

#: includes/widgets/testimonial.php:222
msgid "Aside"
msgstr "Aside"

#: includes/widgets/social-icons.php:212 includes/widgets/social-icons.php:365
msgid "Official Color"
msgstr "Officiële kleur"

#: includes/widgets/progress.php:123
msgid "My Skill"
msgstr "Mijn vaardigheid"

#: core/kits/documents/tabs/global-colors.php:123
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:185
msgid "Custom Colors"
msgstr "Aangepaste kleuren"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:107
#: includes/widgets/social-icons.php:250
msgid "Social Icons"
msgstr "Sociale iconen"

#: includes/widgets/audio.php:240
#: modules/floating-buttons/base/widget-contact-button-base.php:358
#: modules/floating-buttons/base/widget-contact-button-base.php:911
#: modules/link-in-bio/base/widget-link-in-bio-base.php:523
#: modules/link-in-bio/base/widget-link-in-bio-base.php:777
msgid "Username"
msgstr "Gebruikersnaam"

#: core/base/providers/social-network-provider.php:168
#: includes/widgets/audio.php:53 includes/widgets/audio.php:104
msgid "SoundCloud"
msgstr "SoundCloud"

#: core/admin/feedback.php:101
msgid "It's a temporary deactivation"
msgstr "Het is een tijdelijke deactivatie"

#: core/admin/feedback.php:93
msgid "I found a better plugin"
msgstr "Ik heb een betere plugin gevonden"

#: core/admin/feedback.php:110
msgid "Other"
msgstr "Anders"

#: includes/widgets/audio.php:218
msgid "Comments"
msgstr "Reacties"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "Uitgebreid"

#: core/admin/feedback.php:94
msgid "Please share which plugin"
msgstr "Vertel ons welke plugin"

#: core/admin/feedback.php:111
msgid "Please share the reason"
msgstr "Vertel ons de reden"

#: core/admin/feedback.php:127
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "Als je een momentje hebt, vertel ons dan waarom je Elementor deactiveert:"

#: includes/widgets/audio.php:130
msgid "Visual Player"
msgstr "Visuele speler"

#: core/admin/feedback.php:89
msgid "I no longer need the plugin"
msgstr "Ik heb de plugin niet langer nodig"

#: includes/widgets/audio.php:171
msgid "Like Button"
msgstr "Vind ik leuk-knop"

#: includes/widgets/audio.php:207
msgid "Share Button"
msgstr "Delen-knop"

#: includes/widgets/audio.php:229
msgid "Play Counts"
msgstr "Aantal keer afgespeeld"

#: includes/elements/column.php:388 includes/elements/container.php:772
#: includes/elements/section.php:644
#: modules/floating-buttons/base/widget-floating-bars-base.php:981
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1483
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:131
msgid "Background Overlay"
msgstr "Overlay achtergrond"

#: core/admin/feedback.php:119
msgid "Quick Feedback"
msgstr "Snelle feedback"

#: core/admin/feedback.php:97
msgid "I couldn't get the plugin to work"
msgstr "Ik kreeg de plugin niet werkend"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:138
msgid "View Elementor version %s details"
msgstr "Details Elementor versie %s bekijken"

#: core/admin/admin-notices.php:143 core/admin/admin-notices.php:151
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "Nu updaten"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:134
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "Er is een nieuwe versie van Elementor pagina bouwer beschikbaar. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">Details versie %3$s bekijken</a> of <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">nu updaten</a>."

#: includes/widgets/audio.php:182 includes/widgets/video.php:583
msgid "Download Button"
msgstr "Download knop"

#: includes/widgets/audio.php:160
msgid "Buy Button"
msgstr "Kopen knop"

#: includes/widgets/image-carousel.php:364 includes/widgets/image.php:198
msgid "Custom URL"
msgstr "Aangepaste URL"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:276 includes/elements/container.php:643
#: includes/elements/section.php:533 includes/widgets/accordion.php:326
#: includes/widgets/accordion.php:496 includes/widgets/common-base.php:891
#: includes/widgets/toggle.php:358 includes/widgets/toggle.php:520
#: modules/floating-buttons/base/widget-contact-button-base.php:1678
#: modules/floating-buttons/base/widget-floating-bars-base.php:952
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1464
#: assets/js/ai-admin.js:11315 assets/js/ai-gutenberg.js:11533
#: assets/js/ai-media-library.js:11315
#: assets/js/ai-unify-product-images.js:11315 assets/js/ai.js:12867
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:14
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:246
msgid "Background"
msgstr "Achtergrond"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "Breder"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:302
#: includes/settings/settings.php:252 includes/settings/settings.php:255
#: includes/settings/tools.php:309
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:19
msgid "General"
msgstr "Algemeen"

#: includes/editor-templates/hotkeys.php:46 assets/js/editor.js:30670
#: assets/js/editor.js:32998 assets/js/editor.js:42621
#: assets/js/editor.js:43659
msgid "Paste"
msgstr "Plakken"

#: includes/elements/container.php:547 includes/widgets/audio.php:143
#: includes/widgets/image-carousel.php:427
msgid "Additional Options"
msgstr "Extra opties"

#: includes/controls/groups/background.php:749
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:83
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:179
msgid "Direction"
msgstr "Richting"

#: includes/widgets/accordion.php:463 includes/widgets/divider.php:790
#: includes/widgets/divider.php:957 includes/widgets/image-carousel.php:771
#: includes/widgets/image-carousel.php:905
#: includes/widgets/image-gallery.php:440 includes/widgets/image.php:652
#: includes/widgets/rating.php:85 includes/widgets/social-icons.php:457
#: includes/widgets/star-rating.php:351 includes/widgets/toggle.php:487
#: modules/nested-accordion/widgets/nested-accordion.php:627
#: modules/nested-tabs/widgets/nested-tabs.php:915
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:10
msgid "Spacing"
msgstr "Afstand"

#: includes/widgets/image-carousel.php:587
#: includes/widgets/image-carousel.php:652
msgid "Inside"
msgstr "Binnen"

#: includes/widgets/image-carousel.php:588
#: includes/widgets/image-carousel.php:651
msgid "Outside"
msgstr "Buiten"

#: includes/widgets/image-carousel.php:534
msgid "Animation Speed"
msgstr "Snelheid animatie"

#: includes/widgets/image-carousel.php:219
msgid "Image Stretch"
msgstr "Afbeelding uitrekken"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "Je kunt de originele afmetingen van de afbeelding naar elk aangepaste afmetingen bijsnijden Je kunt ook een enkele waarde voor de hoogte of breedte instellen om de originele verhoudingen te behouden."

#: includes/widgets/video.php:557
msgid "Intro Byline"
msgstr "Naamregel intro"

#: includes/elements/column.php:888 includes/elements/container.php:1832
#: includes/elements/section.php:1329 includes/widgets/common-base.php:849
#: includes/widgets/counter.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:1402
#: modules/floating-buttons/base/widget-contact-button-base.php:2785
#: modules/floating-buttons/base/widget-floating-bars-base.php:883
#: modules/nested-accordion/widgets/nested-accordion.php:367
msgid "Animation Duration"
msgstr "Animatieduur"

#: includes/widgets/image-carousel.php:46
#: includes/widgets/image-carousel.php:146
#: includes/widgets/image-carousel.php:155
msgid "Image Carousel"
msgstr "Afbeelding carrousel"

#: includes/widgets/image-carousel.php:236
msgid "Arrows and Dots"
msgstr "Pijlen en stippen"

#: includes/controls/groups/background.php:103 includes/widgets/video.php:45
#: includes/widgets/video.php:148 includes/widgets/video.php:751
msgid "Video"
msgstr "Video"

#: core/base/providers/social-network-provider.php:186
#: includes/widgets/video.php:160
msgid "Vimeo"
msgstr "Vimeo"

#: includes/widgets/audio.php:251 includes/widgets/video.php:571
msgid "Controls Color"
msgstr "Kleur besturingselementen"

#: includes/widgets/video.php:529
msgid "Intro Title"
msgstr "Titel intro"

#: includes/widgets/video.php:543
msgid "Intro Portrait"
msgstr "Portret intro"

#: includes/widgets/video.php:397
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:82
msgid "Loop"
msgstr "Herhalen"

#: includes/widgets/video.php:352
msgid "Video Options"
msgstr "Video-instellingen"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "De server heeft ImageMagick of GD niet geïnstalleerd en/of ingeschakeld! Een van deze bibliotheken is nodig voor WordPress om afbeeldingen van formaat te wijzigen. Neem contact op met je serverbeheerder om dit in te schakelen voordat je verdergaat."

#: includes/controls/groups/background.php:525
msgid "Video Link"
msgstr "Video link"

#: includes/widgets/image-carousel.php:517
msgid "Effect"
msgstr "Effect"

#: includes/controls/media.php:362
msgctxt "Image Size Control"
msgid "Full"
msgstr "Volledig"

#: includes/widgets/image-carousel.php:522
msgid "Fade"
msgstr "Vervagen"

#: includes/controls/gallery.php:92
msgid "Edit gallery"
msgstr "Galerij bewerken"

#: includes/elements/column.php:183 includes/widgets/icon-box.php:265
#: includes/widgets/icon-list.php:572 includes/widgets/image-box.php:240
msgid "Vertical Alignment"
msgstr "Verticale uitlijning"

#: includes/controls/groups/background.php:634
#: includes/widgets/image-carousel.php:504
msgid "Infinite Loop"
msgstr "Oneindig herhalen"

#: includes/controls/groups/background.php:107
msgid "Slideshow"
msgstr "Slideshow"

#: includes/widgets/image-carousel.php:238
msgid "Dots"
msgstr "Stippen"

#: includes/controls/groups/typography.php:321
msgctxt "Typography Control"
msgid "Width"
msgstr "Breedte"

#: includes/widgets/accordion.php:243 includes/widgets/counter.php:242
#: includes/widgets/icon-box.php:207 includes/widgets/image-box.php:182
#: includes/widgets/progress.php:131 includes/widgets/toggle.php:246
#: modules/nested-accordion/widgets/nested-accordion.php:270
msgid "Title HTML Tag"
msgstr "HTML tag titel"

#: includes/elements/column.php:837 includes/elements/container.php:1794
#: includes/elements/section.php:1298 includes/widgets/common-base.php:811
#: modules/floating-buttons/base/widget-contact-button-base.php:3116
#: modules/floating-buttons/base/widget-floating-bars-base.php:1522
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "Voeg je aangepaste klasse toe ZONDER de punt. Bijv: my-class"

#: includes/widgets/icon-box.php:172 includes/widgets/image-box.php:147
msgid "This is the heading"
msgstr "Dit is de koptekst"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "De lijst met lettertypes die gebruikt worden als het gekozen lettertype niet beschikbaar is."

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:19954
#: assets/js/editor.js:21851 assets/js/editor.js:22259
#: assets/js/editor.js:37655
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
#: assets/js/packages/editor-canvas/editor-canvas.js:2
#: assets/js/packages/editor-canvas/editor-canvas.strings.js:6
msgid "Elements"
msgstr "Elementen"

#: includes/widgets/wordpress.php:242
msgid "Form"
msgstr "Formulier"

#: core/admin/admin.php:343 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:77
#: includes/managers/controls.php:339
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216
#: modules/atomic-widgets/elements/has-atomic-base.php:142
#: modules/usage/settings-reporter.php:13 assets/js/editor.js:7481
#: assets/js/editor.js:38588 assets/js/editor.js:48048
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:45
msgid "Settings"
msgstr "Instellingen"

#: includes/controls/groups/typography.php:179
msgctxt "Typography Control"
msgid "Italic"
msgstr "Cursief"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "Hoofdletters"

#: includes/controls/groups/typography.php:166
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "Kleine letters"

#: includes/controls/groups/typography.php:180
msgctxt "Typography Control"
msgid "Oblique"
msgstr "Schuin"

#: includes/controls/groups/typography.php:167
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "Enkele hoofdletter"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: core/base/providers/social-network-provider.php:138
#: includes/widgets/video.php:159
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:35
msgid "YouTube"
msgstr "YouTube"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:119
#: includes/base/element-base.php:883
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:168
#: includes/controls/groups/typography.php:178 includes/elements/column.php:286
#: includes/elements/column.php:401 includes/elements/column.php:450
#: includes/elements/column.php:557 includes/elements/column.php:893
#: includes/elements/container.php:656 includes/elements/container.php:785
#: includes/elements/container.php:859 includes/elements/container.php:1004
#: includes/elements/container.php:1837 includes/elements/section.php:543
#: includes/elements/section.php:654 includes/elements/section.php:718
#: includes/elements/section.php:841 includes/elements/section.php:1334
#: includes/widgets/alert.php:412 includes/widgets/common-base.php:854
#: includes/widgets/common-base.php:901 includes/widgets/common-base.php:976
#: includes/widgets/google-maps.php:221 includes/widgets/heading.php:327
#: includes/widgets/heading.php:359 includes/widgets/icon-box.php:399
#: includes/widgets/icon-box.php:670 includes/widgets/icon-list.php:425
#: includes/widgets/icon-list.php:654 includes/widgets/icon.php:213
#: includes/widgets/image-box.php:489 includes/widgets/image-box.php:638
#: includes/widgets/image.php:434 includes/widgets/text-editor.php:336
#: includes/widgets/traits/button-trait.php:340
#: modules/floating-buttons/base/widget-contact-button-base.php:1201
#: modules/floating-buttons/base/widget-contact-button-base.php:1407
#: modules/floating-buttons/base/widget-contact-button-base.php:1979
#: modules/floating-buttons/base/widget-contact-button-base.php:2474
#: modules/floating-buttons/base/widget-contact-button-base.php:2605
#: modules/floating-buttons/base/widget-contact-button-base.php:2790
#: modules/floating-buttons/base/widget-floating-bars-base.php:656
#: modules/floating-buttons/base/widget-floating-bars-base.php:888
#: modules/floating-buttons/base/widget-floating-bars-base.php:1374
#: modules/nested-accordion/widgets/nested-accordion.php:671
#: modules/nested-accordion/widgets/nested-accordion.php:721
#: modules/nested-tabs/widgets/nested-tabs.php:493
#: modules/nested-tabs/widgets/nested-tabs.php:737
#: modules/nested-tabs/widgets/nested-tabs.php:937
#: modules/shapes/widgets/text-path.php:409
#: modules/shapes/widgets/text-path.php:501
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:110
msgid "Normal"
msgstr "Normaal"

#: core/admin/admin.php:223 core/admin/admin.php:231 core/base/document.php:651
#: modules/admin-bar/module.php:124 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
#: assets/js/e-wc-product-editor.js:2678
msgid "Edit with Elementor"
msgstr "Bewerk met Elementor"

#: includes/controls/gallery.php:94 includes/widgets/image-carousel.php:162
#: includes/widgets/image-gallery.php:137
msgid "Add Images"
msgstr "Afbeeldingen toevoegen"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "Passend op scherm"

#: includes/elements/container.php:396 includes/elements/section.php:254
#: includes/widgets/common-base.php:342 includes/widgets/icon-list.php:216
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1554
msgid "Full Width"
msgstr "Volledige breedte"

#: includes/editor-templates/hotkeys.php:78
#: includes/editor-templates/templates.php:23
#: includes/editor-templates/templates.php:477
#: includes/editor-templates/templates.php:489 assets/js/e-home-screen.js:244
#: assets/js/editor.js:8560 assets/js/editor.js:45521
#: assets/js/element-manager-admin.js:785
#: assets/js/kit-elements-defaults-editor.js:597
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:21
#: assets/js/packages/editor-variables/editor-variables.strings.js:33
msgid "Save"
msgstr "Opslaan"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:95 assets/js/editor.js:8483
msgid "Section"
msgstr "Sectie"

#: includes/elements/container.php:1883 includes/elements/section.php:1390
msgid "Visibility"
msgstr "Zichtbaarheid"

#: includes/base/element-base.php:1330 includes/controls/dimensions.php:83
#: includes/elements/column.php:743 includes/elements/container.php:1547
#: includes/elements/section.php:1199 includes/widgets/common-base.php:568
#: includes/widgets/common-base.php:569 includes/widgets/divider.php:482
#: includes/widgets/divider.php:778 includes/widgets/divider.php:944
#: includes/widgets/heading.php:278 includes/widgets/icon-box.php:251
#: includes/widgets/icon-box.php:312 includes/widgets/icon-list.php:271
#: includes/widgets/icon-list.php:553 includes/widgets/icon.php:197
#: includes/widgets/image-box.php:225 includes/widgets/image-box.php:280
#: includes/widgets/image-carousel.php:550
#: includes/widgets/image-carousel.php:856
#: includes/widgets/image-gallery.php:379 includes/widgets/image.php:271
#: includes/widgets/image.php:589 includes/widgets/social-icons.php:336
#: includes/widgets/star-rating.php:227 includes/widgets/tabs.php:434
#: includes/widgets/testimonial.php:255 includes/widgets/text-editor.php:270
#: includes/widgets/traits/button-trait.php:264
#: modules/floating-buttons/base/widget-contact-button-base.php:1140
#: modules/floating-buttons/base/widget-contact-button-base.php:2406
#: modules/floating-buttons/base/widget-contact-button-base.php:2948
#: modules/floating-buttons/base/widget-floating-bars-base.php:456
#: modules/floating-buttons/base/widget-floating-bars-base.php:1057
#: modules/shapes/widgets/text-path.php:185
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:23
#: assets/js/packages/editor-controls/editor-controls.strings.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:146
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:149
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:219
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:221
msgid "Right"
msgstr "Rechts"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:404
#: includes/widgets/tabs.php:221 includes/widgets/tabs.php:251
#: includes/widgets/traits/button-trait.php:268
#: modules/floating-buttons/base/widget-floating-bars-base.php:1201
#: modules/nested-accordion/widgets/nested-accordion.php:185
#: modules/nested-tabs/widgets/nested-tabs.php:246
#: modules/nested-tabs/widgets/nested-tabs.php:288
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:199
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:204
msgid "Stretch"
msgstr "Uitrekken"

#: includes/managers/controls.php:335 includes/widgets/divider.php:385
#: includes/widgets/icon-list.php:296 assets/js/ai-admin.js:10498
#: assets/js/ai-gutenberg.js:10716 assets/js/ai-media-library.js:10498
#: assets/js/ai-unify-product-images.js:10498 assets/js/ai.js:12050
#: assets/js/editor.js:7484 assets/js/editor.js:37460
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:2
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:20
msgid "Style"
msgstr "Stijl"

#: includes/controls/groups/typography.php:173
msgctxt "Typography Control"
msgid "Style"
msgstr "Stijl"

#: core/base/document.php:1980
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:127 includes/elements/section.php:240
#: includes/widgets/accordion.php:132 includes/widgets/accordion.php:318
#: includes/widgets/alert.php:134 includes/widgets/alert.php:262
#: includes/widgets/common-base.php:301 includes/widgets/counter.php:228
#: includes/widgets/counter.php:542 includes/widgets/heading.php:184
#: includes/widgets/icon-box.php:167 includes/widgets/icon-box.php:632
#: includes/widgets/image-box.php:142 includes/widgets/image-box.php:600
#: includes/widgets/image-carousel.php:415 includes/widgets/progress.php:117
#: includes/widgets/progress.php:244 includes/widgets/star-rating.php:203
#: includes/widgets/star-rating.php:247 includes/widgets/tabs.php:127
#: includes/widgets/tabs.php:357 includes/widgets/testimonial.php:189
#: includes/widgets/testimonial.php:415 includes/widgets/toggle.php:132
#: includes/widgets/toggle.php:350
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:68
#: modules/floating-buttons/base/widget-contact-button-base.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:2314
#: modules/link-in-bio/base/widget-link-in-bio-base.php:869
#: modules/nested-accordion/widgets/nested-accordion.php:115
#: modules/nested-accordion/widgets/nested-accordion.php:563
#: modules/nested-tabs/widgets/nested-tabs.php:113
msgid "Title"
msgstr "Titel"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:133
#: includes/widgets/text-editor.php:250
msgid "Text Editor"
msgstr "Tekstbewerker"

#: includes/widgets/button.php:48 includes/widgets/button.php:115
#: includes/widgets/button.php:126
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:33
#: modules/floating-buttons/base/widget-floating-bars-base.php:567
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1098
msgid "Button"
msgstr "Knop"

#: includes/controls/media.php:189 includes/widgets/image-box.php:117
#: includes/widgets/image.php:133 includes/widgets/testimonial.php:152
#: includes/widgets/video.php:653
#: modules/link-in-bio/base/widget-link-in-bio-base.php:252
#: modules/link-in-bio/base/widget-link-in-bio-base.php:343
#: modules/link-in-bio/base/widget-link-in-bio-base.php:932
#: modules/link-in-bio/base/widget-link-in-bio-base.php:987
msgid "Choose Image"
msgstr "Afbeelding kiezen"

#: includes/widgets/image-carousel.php:363
#: includes/widgets/image-gallery.php:190 includes/widgets/image.php:197
msgid "Media File"
msgstr "Mediabestand"

#: core/experiments/manager.php:389
#: core/kits/documents/tabs/settings-background.php:78
#: includes/base/widget-base.php:297 includes/controls/animation.php:154
#: includes/controls/font.php:66 includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:432
#: includes/controls/groups/background.php:462
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/controls/groups/background.php:707
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:164
#: includes/controls/groups/typography.php:177
#: includes/controls/groups/typography.php:189
#: includes/editor-templates/panel.php:243 includes/elements/column.php:187
#: includes/elements/column.php:215 includes/elements/column.php:258
#: includes/elements/container.php:559 includes/elements/container.php:582
#: includes/elements/container.php:1534 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:386
#: includes/widgets/common-base.php:341 includes/widgets/common-base.php:558
#: includes/widgets/divider.php:835 includes/widgets/heading.php:217
#: includes/widgets/icon-box.php:133 includes/widgets/icon-list.php:122
#: includes/widgets/icon.php:134 includes/widgets/image-box.php:405
#: includes/widgets/image-carousel.php:188
#: includes/widgets/image-carousel.php:206
#: includes/widgets/image-carousel.php:397
#: includes/widgets/image-carousel.php:774
#: includes/widgets/image-gallery.php:210
#: includes/widgets/image-gallery.php:266
#: includes/widgets/image-gallery.php:289 includes/widgets/image.php:235
#: includes/widgets/image.php:382 includes/widgets/progress.php:171
#: includes/widgets/text-editor.php:181 includes/widgets/text-editor.php:424
#: includes/widgets/traits/button-trait.php:71
#: modules/element-cache/module.php:123
#: modules/floating-buttons/base/widget-contact-button-base.php:1213
#: modules/floating-buttons/base/widget-contact-button-base.php:1265
#: modules/floating-buttons/base/widget-contact-button-base.php:1352
#: modules/floating-buttons/base/widget-contact-button-base.php:1561
#: modules/floating-buttons/base/widget-contact-button-base.php:1727
#: modules/floating-buttons/base/widget-contact-button-base.php:2291
#: modules/floating-buttons/base/widget-contact-button-base.php:2617
#: modules/floating-buttons/base/widget-contact-button-base.php:2686
#: modules/floating-buttons/base/widget-contact-button-base.php:2817
#: modules/link-in-bio/base/widget-link-in-bio-base.php:179
#: modules/page-templates/module.php:301
#: modules/shapes/widgets/text-path.php:203 assets/js/editor.js:46063
#: assets/js/editor.js:46074
msgid "Default"
msgstr "Standaard"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:63
#: includes/editor-templates/templates.php:168
#: includes/editor-templates/templates.php:339
#: includes/editor-templates/templates.php:372
#: includes/editor-templates/templates.php:429 assets/js/editor.js:8928
#: assets/js/editor.js:8947 assets/js/editor.js:9108 assets/js/editor.js:28255
#: assets/js/editor.js:30735 assets/js/editor.js:48239
#: assets/js/import-export-admin.js:272
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1127
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1269
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:20
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:52
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:17
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:25
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:18
#: assets/js/packages/editor-variables/editor-variables.strings.js:30
#: assets/js/packages/editor-variables/editor-variables.strings.js:67
msgid "Delete"
msgstr "Verwijderen"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:155 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:193
#: includes/controls/hover-animation.php:129 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/widgets/divider.php:501
#: includes/widgets/image-carousel.php:239
#: includes/widgets/image-carousel.php:362
#: includes/widgets/image-carousel.php:414
#: includes/widgets/image-gallery.php:174
#: includes/widgets/image-gallery.php:192 includes/widgets/image.php:161
#: includes/widgets/image.php:196 includes/widgets/video.php:601
#: modules/nested-tabs/widgets/nested-tabs.php:406 assets/js/ai-admin.js:11288
#: assets/js/ai-admin.js:11294 assets/js/ai-admin.js:11306
#: assets/js/ai-admin.js:11317 assets/js/ai-admin.js:11328
#: assets/js/ai-admin.js:11339 assets/js/ai-admin.js:11355
#: assets/js/ai-gutenberg.js:11506 assets/js/ai-gutenberg.js:11512
#: assets/js/ai-gutenberg.js:11524 assets/js/ai-gutenberg.js:11535
#: assets/js/ai-gutenberg.js:11546 assets/js/ai-gutenberg.js:11557
#: assets/js/ai-gutenberg.js:11573 assets/js/ai-media-library.js:11288
#: assets/js/ai-media-library.js:11294 assets/js/ai-media-library.js:11306
#: assets/js/ai-media-library.js:11317 assets/js/ai-media-library.js:11328
#: assets/js/ai-media-library.js:11339 assets/js/ai-media-library.js:11355
#: assets/js/ai-unify-product-images.js:11288
#: assets/js/ai-unify-product-images.js:11294
#: assets/js/ai-unify-product-images.js:11306
#: assets/js/ai-unify-product-images.js:11317
#: assets/js/ai-unify-product-images.js:11328
#: assets/js/ai-unify-product-images.js:11339
#: assets/js/ai-unify-product-images.js:11355 assets/js/ai.js:12840
#: assets/js/ai.js:12846 assets/js/ai.js:12858 assets/js/ai.js:12869
#: assets/js/ai.js:12880 assets/js/ai.js:12891 assets/js/ai.js:12907
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:78
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:87
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:192
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:225
msgid "None"
msgstr "Geen"

#: includes/controls/icons.php:80 includes/controls/media.php:238
msgid "Add"
msgstr "Toevoegen"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2332
#: assets/js/ai-admin.js:10193 assets/js/ai-admin.js:10200
#: assets/js/ai-gutenberg.js:2470 assets/js/ai-gutenberg.js:10411
#: assets/js/ai-gutenberg.js:10418 assets/js/ai-media-library.js:2332
#: assets/js/ai-media-library.js:10193 assets/js/ai-media-library.js:10200
#: assets/js/ai-unify-product-images.js:2332
#: assets/js/ai-unify-product-images.js:10193
#: assets/js/ai-unify-product-images.js:10200 assets/js/ai.js:3120
#: assets/js/ai.js:11745 assets/js/ai.js:11752 assets/js/app.js:5022
#: assets/js/element-manager-admin.js:873
#: assets/js/element-manager-admin.js:929
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:54
msgid "Edit"
msgstr "Bewerken"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:126
#: core/kits/documents/tabs/theme-style-typography.php:155
#: core/kits/documents/tabs/theme-style-typography.php:200
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:458
#: includes/elements/container.php:867 includes/elements/container.php:1225
#: includes/elements/section.php:726 includes/elements/section.php:1008
#: includes/widgets/accordion.php:337 includes/widgets/accordion.php:439
#: includes/widgets/accordion.php:507 includes/widgets/alert.php:418
#: includes/widgets/alert.php:435 includes/widgets/divider.php:589
#: includes/widgets/divider.php:733 includes/widgets/heading.php:335
#: includes/widgets/icon-box.php:677 includes/widgets/icon-box.php:701
#: includes/widgets/icon-box.php:764 includes/widgets/icon-list.php:395
#: includes/widgets/icon-list.php:432 includes/widgets/icon-list.php:457
#: includes/widgets/icon-list.php:661 includes/widgets/icon-list.php:685
#: includes/widgets/image-box.php:645 includes/widgets/image-box.php:669
#: includes/widgets/image-box.php:732 includes/widgets/image-carousel.php:620
#: includes/widgets/image-carousel.php:704 includes/widgets/progress.php:306
#: includes/widgets/progress.php:367 includes/widgets/rating.php:110
#: includes/widgets/social-icons.php:208 includes/widgets/social-icons.php:361
#: includes/widgets/star-rating.php:376 includes/widgets/tabs.php:366
#: includes/widgets/tabs.php:459 includes/widgets/toggle.php:370
#: includes/widgets/toggle.php:463 includes/widgets/toggle.php:531
#: includes/widgets/video.php:821
#: modules/floating-buttons/base/widget-contact-button-base.php:1888
#: modules/floating-buttons/base/widget-contact-button-base.php:2287
#: modules/floating-buttons/base/widget-contact-button-base.php:2300
#: modules/floating-buttons/base/widget-contact-button-base.php:2538
#: modules/floating-buttons/base/widget-floating-bars-base.php:422
#: modules/floating-buttons/base/widget-floating-bars-base.php:540
#: modules/floating-buttons/base/widget-floating-bars-base.php:1074
#: modules/floating-buttons/base/widget-floating-bars-base.php:1275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1207
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1392
#: modules/nested-accordion/widgets/nested-accordion.php:686
#: modules/nested-accordion/widgets/nested-accordion.php:749
#: modules/nested-tabs/widgets/nested-tabs.php:744
#: modules/nested-tabs/widgets/nested-tabs.php:780
#: modules/nested-tabs/widgets/nested-tabs.php:816
#: modules/nested-tabs/widgets/nested-tabs.php:942
#: modules/nested-tabs/widgets/nested-tabs.php:959
#: modules/nested-tabs/widgets/nested-tabs.php:976
#: modules/shapes/widgets/text-path.php:416
#: modules/shapes/widgets/text-path.php:440
#: modules/shapes/widgets/text-path.php:508
#: modules/shapes/widgets/text-path.php:528
#: modules/shapes/widgets/text-path.php:579
#: modules/shapes/widgets/text-path.php:599 assets/js/editor.js:48186
#: assets/js/editor.js:48229
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:72
#: assets/js/packages/editor-controls/editor-controls.strings.js:124
#: assets/js/packages/editor-controls/editor-controls.strings.js:125
#: assets/js/packages/editor-controls/editor-controls.strings.js:134
msgid "Color"
msgstr "Kleur"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:292
#: core/admin/admin.php:409 core/admin/admin.php:487
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:384 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:47 includes/editor-templates/navigator.php:94
#: includes/editor-templates/panel-elements.php:100
#: includes/editor-templates/panel.php:197
#: includes/editor-templates/templates.php:220 includes/plugin.php:868
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:487 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:319 assets/js/app.js:387
msgid "Elementor"
msgstr "Elementor"

#: core/editor/loader/v1/templates/editor-body-v1-view.php:31
#: core/editor/loader/v2/templates/editor-body-v2-view.php:33
#: includes/editor-templates/templates.php:296 assets/js/ai-admin.js:7565
#: assets/js/ai-gutenberg.js:7783 assets/js/ai-layout.js:3053
#: assets/js/ai-media-library.js:7565 assets/js/ai-unify-product-images.js:7565
#: assets/js/ai.js:9117 assets/js/editor.js:28194
msgid "Preview"
msgstr "Voorbeeld"

#: includes/editor-templates/panel.php:66
#: includes/editor-templates/panel.php:67
msgid "Menu"
msgstr "Menu"

#: core/base/traits/shared-widget-controls-trait.php:270
#: includes/base/element-base.php:1386 includes/editor-templates/panel.php:272
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:5305
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "Desktop"

#: core/admin/admin.php:621 core/admin/menu/main.php:41 assets/js/app.js:6155
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "Hulp"

#: modules/floating-buttons/base/widget-contact-button-base.php:1209
#: modules/floating-buttons/base/widget-contact-button-base.php:1261
#: modules/floating-buttons/base/widget-contact-button-base.php:1348
#: modules/floating-buttons/base/widget-contact-button-base.php:1557
#: modules/floating-buttons/base/widget-contact-button-base.php:1723
#: modules/floating-buttons/base/widget-contact-button-base.php:2613
#: modules/floating-buttons/base/widget-contact-button-base.php:2682
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:443
msgid "Colors"
msgstr "Kleuren"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:505 includes/widgets/divider.php:523
#: includes/widgets/divider.php:722 includes/widgets/icon-list.php:142
#: includes/widgets/icon-list.php:625
#: includes/widgets/traits/button-trait.php:58
#: modules/floating-buttons/base/widget-contact-button-base.php:145
#: modules/floating-buttons/base/widget-contact-button-base.php:1044
#: modules/floating-buttons/base/widget-floating-bars-base.php:63
#: modules/floating-buttons/base/widget-floating-bars-base.php:151
#: modules/floating-buttons/base/widget-floating-bars-base.php:337
#: modules/floating-buttons/base/widget-floating-bars-base.php:1353
#: modules/link-in-bio/base/widget-link-in-bio-base.php:328
#: modules/link-in-bio/base/widget-link-in-bio-base.php:585
#: modules/shapes/widgets/text-path.php:111
#: modules/shapes/widgets/text-path.php:304 assets/js/ai-admin.js:3596
#: assets/js/ai-gutenberg.js:3734 assets/js/ai-media-library.js:3596
#: assets/js/ai-unify-product-images.js:3596 assets/js/ai.js:4417
msgid "Text"
msgstr "Tekst"

#: core/base/document.php:1972
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "Algemene instellingen"

#: includes/widgets/accordion.php:170
msgid "Accordion #1"
msgstr "Accordeon #1"

#: includes/widgets/accordion.php:174
msgid "Accordion #2"
msgstr "Accordeon #2"

#: includes/widgets/alert.php:124 includes/widgets/progress.php:174
#: includes/widgets/traits/button-trait.php:74
msgid "Warning"
msgstr "Waarschuwing"

#: includes/widgets/alert.php:123 includes/widgets/progress.php:173
#: includes/widgets/traits/button-trait.php:73
msgid "Success"
msgstr "Succes"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:279
#: modules/nested-tabs/widgets/nested-tabs.php:207
#: modules/nested-tabs/widgets/nested-tabs.php:873
msgid "Before"
msgstr "Voor"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:283
#: modules/nested-tabs/widgets/nested-tabs.php:203
#: modules/nested-tabs/widgets/nested-tabs.php:865
msgid "After"
msgstr "Na"

#: includes/widgets/heading.php:218 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1120
#: modules/floating-buttons/base/widget-contact-button-base.php:1545
#: modules/floating-buttons/base/widget-contact-button-base.php:1927
#: modules/floating-buttons/base/widget-contact-button-base.php:2277
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1407
msgid "Small"
msgstr "Klein"

#: includes/widgets/heading.php:220 includes/widgets/traits/button-trait.php:37
#: modules/floating-buttons/base/widget-contact-button-base.php:1122
#: modules/floating-buttons/base/widget-contact-button-base.php:1547
#: modules/floating-buttons/base/widget-contact-button-base.php:1929
#: modules/floating-buttons/base/widget-contact-button-base.php:2279
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1409
msgid "Large"
msgstr "Groot"

#: includes/widgets/counter.php:491
#: modules/floating-buttons/base/widget-contact-button-base.php:333
#: modules/floating-buttons/base/widget-contact-button-base.php:887
#: modules/link-in-bio/base/widget-link-in-bio-base.php:483
#: modules/link-in-bio/base/widget-link-in-bio-base.php:731
msgid "Number"
msgstr "Nummer"

#: core/kits/documents/tabs/theme-style-typography.php:109
#: includes/elements/container.php:609 includes/widgets/audio.php:111
#: includes/widgets/heading.php:200 includes/widgets/icon-box.php:195
#: includes/widgets/icon-list.php:169 includes/widgets/icon.php:164
#: includes/widgets/image-box.php:170 includes/widgets/image-carousel.php:358
#: includes/widgets/image-carousel.php:372
#: includes/widgets/image-gallery.php:186 includes/widgets/image.php:192
#: includes/widgets/image.php:209 includes/widgets/social-icons.php:194
#: includes/widgets/testimonial.php:204
#: includes/widgets/traits/button-trait.php:99 includes/widgets/video.php:172
#: includes/widgets/video.php:197 includes/widgets/video.php:221
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:75
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:108
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:79
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:74
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:68
#: modules/atomic-widgets/elements/div-block/div-block.php:103
#: modules/floating-buttons/base/widget-contact-button-base.php:418
#: modules/floating-buttons/base/widget-contact-button-base.php:933
#: modules/floating-buttons/base/widget-contact-button-base.php:1061
#: modules/floating-buttons/base/widget-floating-bars-base.php:164
#: modules/floating-buttons/base/widget-floating-bars-base.php:350
#: modules/floating-buttons/base/widget-floating-bars-base.php:568
#: modules/link-in-bio/base/widget-link-in-bio-base.php:264
#: modules/link-in-bio/base/widget-link-in-bio-base.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:643
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1099
#: modules/shapes/widgets/text-path.php:158
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:28
msgid "Link"
msgstr "Link"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:353
#: includes/widgets/common-base.php:1153 includes/widgets/divider.php:639
#: includes/widgets/divider.php:847 includes/widgets/heading.php:214
#: includes/widgets/icon-box.php:509 includes/widgets/icon-list.php:491
#: includes/widgets/icon.php:306 includes/widgets/image-carousel.php:600
#: includes/widgets/image-carousel.php:684 includes/widgets/rating.php:60
#: includes/widgets/social-icons.php:403 includes/widgets/star-rating.php:326
#: includes/widgets/text-editor.php:474
#: includes/widgets/traits/button-trait.php:114 includes/widgets/video.php:837
#: modules/floating-buttons/base/widget-contact-button-base.php:1116
#: modules/floating-buttons/base/widget-contact-button-base.php:1541
#: modules/floating-buttons/base/widget-contact-button-base.php:1923
#: modules/floating-buttons/base/widget-contact-button-base.php:2273
#: modules/floating-buttons/base/widget-floating-bars-base.php:490
#: modules/floating-buttons/base/widget-floating-bars-base.php:1088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1313
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1403
#: modules/nested-accordion/widgets/nested-accordion.php:603
#: modules/nested-tabs/widgets/nested-tabs.php:895
#: modules/shapes/widgets/text-path.php:251
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:152
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:11
msgid "Size"
msgstr "Afmeting"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: includes/widgets/heading.php:221
msgid "XL"
msgstr "XL"

#: includes/widgets/heading.php:222
msgid "XXL"
msgstr "XXL"

#: includes/controls/groups/background.php:292
#: includes/widgets/common-base.php:1116 includes/widgets/image-box.php:351
#: includes/widgets/image-carousel.php:735 includes/widgets/image.php:45
#: includes/widgets/image.php:126 includes/widgets/image.php:251
#: includes/widgets/testimonial.php:317
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:32
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:66
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:41
#: assets/js/packages/editor-controls/editor-controls.strings.js:132
msgid "Image"
msgstr "Afbeelding"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:118
#: includes/widgets/tabs.php:267 modules/nested-tabs/widgets/nested-tabs.php:34
#: modules/nested-tabs/widgets/nested-tabs.php:107
#: modules/nested-tabs/widgets/nested-tabs.php:444
msgid "Tabs"
msgstr "Tabs"

#: includes/editor-templates/templates.php:194
#: includes/widgets/testimonial.php:174 includes/widgets/testimonial.php:370
#: modules/floating-buttons/base/widget-contact-button-base.php:121
#: modules/floating-buttons/base/widget-contact-button-base.php:209
#: modules/floating-buttons/base/widget-contact-button-base.php:653
#: modules/floating-buttons/base/widget-contact-button-base.php:1737
#: assets/js/app.js:7717
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:68
msgid "Name"
msgstr "Naam"

#: includes/widgets/tabs.php:173
#: modules/nested-tabs/widgets/nested-tabs.php:175
msgid "Tab #2"
msgstr "Tab #2"

#: includes/widgets/tabs.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Tab #1"
msgstr "Tab #1"

#: includes/widgets/image-carousel.php:237
#: includes/widgets/image-carousel.php:571
msgid "Arrows"
msgstr "Pijlen"

#: includes/widgets/progress.php:188 includes/widgets/progress.php:297
msgid "Percentage"
msgstr "Percentage"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:110
#: includes/widgets/progress.php:236
msgid "Progress Bar"
msgstr "Voortgangsbalk"

#: includes/widgets/progress.php:225
msgid "Web Designer"
msgstr "Webdesigner"

#: includes/widgets/progress.php:224
msgid "e.g. Web Designer"
msgstr "bijv. Webdesigner"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:125
#: includes/widgets/google-maps.php:212
msgid "Google Maps"
msgstr "Google Maps"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:623
#: includes/widgets/image-gallery.php:278 assets/js/ai-admin.js:3597
#: assets/js/ai-gutenberg.js:3735 assets/js/ai-media-library.js:3597
#: assets/js/ai-unify-product-images.js:3597 assets/js/ai.js:4418
msgid "Images"
msgstr "Afbeeldingen"

#: includes/widgets/image-gallery.php:267
msgid "Random"
msgstr "Willekeurig"

#: includes/widgets/image-gallery.php:191
msgid "Attachment Page"
msgstr "Bijlagepagina"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:127
#: includes/widgets/counter.php:267
msgid "Counter"
msgstr "Teller"

#: includes/widgets/alert.php:125 includes/widgets/progress.php:175
#: includes/widgets/traits/button-trait.php:75
msgid "Danger"
msgstr "Gevaar"

#: includes/widgets/alert.php:122 includes/widgets/progress.php:172
#: includes/widgets/traits/button-trait.php:72 assets/js/app-packages.js:4364
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4389
msgid "Info"
msgstr "Info"

#: includes/editor-templates/templates.php:198
#: includes/elements/container.php:1196 includes/elements/section.php:979
#: includes/template-library/sources/local.php:1702
#: includes/widgets/alert.php:118 includes/widgets/progress.php:168
#: includes/widgets/traits/button-trait.php:67
#: modules/floating-buttons/base/widget-floating-bars-base.php:563
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1094
msgid "Type"
msgstr "Type"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:123
#: includes/widgets/accordion.php:273
#: modules/nested-accordion/widgets/nested-accordion.php:39
#: modules/nested-accordion/widgets/nested-accordion.php:393
msgid "Accordion"
msgstr "Accordeon"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:119
#: core/settings/editor-preferences/model.php:131
#: core/settings/editor-preferences/model.php:142
#: core/settings/editor-preferences/model.php:164
#: core/settings/editor-preferences/model.php:187
#: includes/controls/switcher.php:73 includes/managers/icons.php:469
#: includes/widgets/audio.php:135 includes/widgets/image-carousel.php:223
#: includes/widgets/image-carousel.php:399
#: includes/widgets/image-carousel.php:446
#: includes/widgets/image-carousel.php:459
#: includes/widgets/image-carousel.php:476
#: includes/widgets/image-carousel.php:507
#: includes/widgets/image-gallery.php:212 includes/widgets/image.php:237
#: modules/floating-buttons/base/widget-contact-button-base.php:3048
#: modules/floating-buttons/base/widget-floating-bars-base.php:764
#: modules/floating-buttons/base/widget-floating-bars-base.php:1456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1557
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1615
#: modules/nested-accordion/widgets/nested-accordion.php:310
#: modules/styleguide/module.php:131
msgid "No"
msgstr "Nee"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:118
#: core/settings/editor-preferences/model.php:130
#: core/settings/editor-preferences/model.php:141
#: core/settings/editor-preferences/model.php:163
#: core/settings/editor-preferences/model.php:186
#: includes/controls/switcher.php:74 includes/managers/icons.php:470
#: includes/widgets/audio.php:134 includes/widgets/image-carousel.php:224
#: includes/widgets/image-carousel.php:398
#: includes/widgets/image-carousel.php:445
#: includes/widgets/image-carousel.php:458
#: includes/widgets/image-carousel.php:475
#: includes/widgets/image-carousel.php:506
#: includes/widgets/image-gallery.php:211 includes/widgets/image.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:3047
#: modules/floating-buttons/base/widget-floating-bars-base.php:763
#: modules/floating-buttons/base/widget-floating-bars-base.php:1455
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1556
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1614
#: modules/nested-accordion/widgets/nested-accordion.php:309
#: modules/styleguide/module.php:132 assets/js/app.js:9919
msgid "Yes"
msgstr "Ja"

#: core/document-types/page-base.php:132 includes/elements/column.php:772
#: includes/elements/container.php:1376 includes/elements/section.php:1227
#: includes/widgets/common-base.php:310
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:116
msgid "Margin"
msgstr "Marge"

#: includes/elements/column.php:731 includes/elements/section.php:1187
msgid "Text Align"
msgstr "Tekstuitlijning"

#: core/kits/documents/tabs/theme-style-typography.php:18
#: core/kits/documents/tabs/theme-style-typography.php:37
#: includes/controls/groups/typography.php:436 includes/elements/column.php:674
#: includes/elements/section.php:1131 assets/js/editor-modules.js:1431
#: assets/js/editor.js:43331
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:13
msgid "Typography"
msgstr "Typografie"

#: includes/controls/gaps.php:57
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:61
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:47
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:181
msgid "Column"
msgstr "Kolom"

#: includes/editor-templates/hotkeys.php:120
#: includes/editor-templates/navigator.php:35
#: includes/editor-templates/panel.php:81 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:31919
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Structure"
msgstr "Structuur"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:160 includes/widgets/social-icons.php:295
#: includes/widgets/text-editor.php:177
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:115
msgid "Columns"
msgstr "Kolommen"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:164
#: includes/editor-templates/templates.php:446
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:8184 assets/js/editor.js:38669
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:851
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:879
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1330
msgid "Apply"
msgstr "Toepassen"

#: includes/fonts.php:76
msgid "Google"
msgstr "Google"

#: includes/fonts.php:71
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:247
msgid "System"
msgstr "Systeem"

#: includes/controls/groups/background.php:434
msgctxt "Background Control"
msgid "Fixed"
msgstr "Vast"

#: core/common/modules/finder/categories/settings.php:59
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:763
#: includes/elements/section.php:1219 includes/managers/controls.php:336
#: includes/settings/settings.php:329 includes/settings/settings.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:2921
#: modules/floating-buttons/base/widget-floating-bars-base.php:1419
#: modules/floating-buttons/module.php:79
#: modules/floating-buttons/module.php:84 assets/js/editor.js:7487
#: assets/js/editor.js:37463
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3704
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3708
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1386
msgid "Advanced"
msgstr "Geavanceerd"

#: includes/managers/controls.php:334 includes/widgets/accordion.php:145
#: includes/widgets/accordion.php:488 includes/widgets/alert.php:148
#: includes/widgets/icon-box.php:624 includes/widgets/image-box.php:592
#: includes/widgets/tabs.php:141 includes/widgets/tabs.php:450
#: includes/widgets/testimonial.php:139 includes/widgets/testimonial.php:272
#: includes/widgets/toggle.php:145 includes/widgets/toggle.php:512
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:63
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:65
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:63
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:62
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:57
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:72
#: modules/nested-accordion/widgets/nested-accordion.php:492
#: modules/nested-tabs/widgets/nested-tabs.php:990 assets/js/app.js:4615
#: assets/js/app.js:6326 assets/js/app.js:11350 assets/js/app.js:11859
#: assets/js/editor.js:37457
msgid "Content"
msgstr "Inhoud"

#: includes/base/element-base.php:1322 includes/controls/dimensions.php:85
#: includes/elements/column.php:735 includes/elements/container.php:1546
#: includes/elements/section.php:1191 includes/widgets/common-base.php:568
#: includes/widgets/common-base.php:569 includes/widgets/divider.php:474
#: includes/widgets/divider.php:770 includes/widgets/divider.php:936
#: includes/widgets/heading.php:270 includes/widgets/icon-box.php:243
#: includes/widgets/icon-box.php:304 includes/widgets/icon-list.php:263
#: includes/widgets/icon-list.php:545 includes/widgets/icon.php:189
#: includes/widgets/image-box.php:217 includes/widgets/image-box.php:272
#: includes/widgets/image-carousel.php:549
#: includes/widgets/image-carousel.php:848
#: includes/widgets/image-gallery.php:371 includes/widgets/image.php:263
#: includes/widgets/image.php:581 includes/widgets/social-icons.php:328
#: includes/widgets/star-rating.php:219 includes/widgets/tabs.php:426
#: includes/widgets/testimonial.php:247 includes/widgets/text-editor.php:262
#: includes/widgets/traits/button-trait.php:256
#: modules/floating-buttons/base/widget-contact-button-base.php:1136
#: modules/floating-buttons/base/widget-contact-button-base.php:2402
#: modules/floating-buttons/base/widget-contact-button-base.php:2940
#: modules/floating-buttons/base/widget-floating-bars-base.php:452
#: modules/floating-buttons/base/widget-floating-bars-base.php:1053
#: modules/shapes/widgets/text-path.php:177
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:22
#: assets/js/packages/editor-controls/editor-controls.strings.js:26
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:147
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:148
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:218
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:222
msgid "Left"
msgstr "Links"

#: includes/widgets/common-base.php:1100 includes/widgets/icon-box.php:148
#: includes/widgets/icon.php:146 includes/widgets/social-icons.php:280
msgid "Shape"
msgstr "Vorm"

#: includes/widgets/common-base.php:136 includes/widgets/icon-box.php:153
#: includes/widgets/icon.php:151 includes/widgets/social-icons.php:286
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1678
#: modules/shapes/module.php:45
msgid "Circle"
msgstr "Cirkel"

#: includes/widgets/icon-box.php:151 includes/widgets/icon.php:149
#: includes/widgets/social-icons.php:284
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1679
#: assets/js/ai-admin.js:11368 assets/js/ai-gutenberg.js:11586
#: assets/js/ai-media-library.js:11368
#: assets/js/ai-unify-product-images.js:11368 assets/js/ai.js:12920
msgid "Square"
msgstr "Vierkant"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1273 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1056
#: includes/widgets/google-maps.php:192 includes/widgets/icon-list.php:362
#: includes/widgets/image-box.php:390 includes/widgets/image.php:354
#: includes/widgets/progress.php:331
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:119
msgid "Height"
msgstr "Hoogte"

#: core/settings/editor-preferences/model.php:195
#: includes/widgets/image-carousel.php:232
#: includes/widgets/image-carousel.php:560
msgid "Navigation"
msgstr "Navigatie"

#: includes/managers/elements.php:351
msgid "WordPress"
msgstr "WordPress"

#: includes/widgets/divider.php:836 includes/widgets/icon-box.php:134
#: includes/widgets/icon.php:135 includes/widgets/text-editor.php:425
msgid "Stacked"
msgstr "Gestapeld"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:343
#: includes/widgets/icon-list.php:300
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:229
msgid "Double"
msgstr "Dubbel"

#: includes/widgets/google-maps.php:150
msgid "London Eye, London, United Kingdom"
msgstr "Anne Frank Huis, Amsterdam, Nederland"

#: includes/managers/elements.php:288
msgid "Basic"
msgstr "Basis"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:345
#: includes/widgets/icon-list.php:302
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:227
msgid "Dashed"
msgstr "Gestreept"

#: includes/widgets/audio.php:152 includes/widgets/image-carousel.php:443
#: includes/widgets/video.php:361
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:80
msgid "Autoplay"
msgstr "Automatisch afspelen"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "Smal"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "Familie"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "Afmeting"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:344
#: includes/widgets/icon-list.php:301
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:228
msgid "Dotted"
msgstr "Gestippeld"

#: core/kits/manager.php:139 includes/controls/groups/background.php:329
#: includes/controls/groups/background.php:487
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/container.php:1436 includes/elements/container.php:1480
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common-base.php:344 includes/widgets/common-base.php:416
#: includes/widgets/common-base.php:460 includes/widgets/common-base.php:1158
#: includes/widgets/common-base.php:1215
#: includes/widgets/image-carousel.php:775
#: includes/widgets/image-gallery.php:290 includes/widgets/social-icons.php:213
#: includes/widgets/social-icons.php:366
#: modules/floating-buttons/base/widget-contact-button-base.php:1214
#: modules/floating-buttons/base/widget-contact-button-base.php:1266
#: modules/floating-buttons/base/widget-contact-button-base.php:1353
#: modules/floating-buttons/base/widget-contact-button-base.php:1562
#: modules/floating-buttons/base/widget-contact-button-base.php:1728
#: modules/floating-buttons/base/widget-contact-button-base.php:2292
#: modules/floating-buttons/base/widget-contact-button-base.php:2618
#: modules/floating-buttons/base/widget-contact-button-base.php:2687
#: modules/floating-buttons/base/widget-contact-button-base.php:2818
#: modules/shapes/module.php:52 assets/js/editor.js:46071
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:15
#: assets/js/packages/editor-controls/editor-controls.strings.js:81
#: assets/js/packages/editor-controls/editor-controls.strings.js:151
#: assets/js/packages/editor-controls/editor-controls.strings.js:167
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:170
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:177
msgid "Custom"
msgstr "Aangepast"

#: modules/floating-buttons/base/widget-contact-button-base.php:205
#: modules/floating-buttons/base/widget-contact-button-base.php:235
#: modules/floating-buttons/base/widget-contact-button-base.php:1319
#: modules/floating-buttons/base/widget-contact-button-base.php:2019
#: modules/floating-buttons/base/widget-contact-button-base.php:2628
#: modules/floating-buttons/base/widget-contact-button-base.php:2697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1141
msgid "Icon Color"
msgstr "Icoon Kleur"

#: includes/widgets/divider.php:832 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:421
#: assets/js/editor.js:10627
msgid "View"
msgstr "Weergave"

#: includes/elements/column.php:924 includes/elements/container.php:1875
#: includes/elements/section.php:1366 includes/managers/controls.php:337
#: includes/widgets/common-base.php:1332
#: modules/floating-buttons/base/widget-contact-button-base.php:3061
#: modules/floating-buttons/base/widget-floating-bars-base.php:1467
msgid "Responsive"
msgstr "Responsive"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "Primair"

#: core/settings/editor-preferences/model.php:90
#: includes/controls/groups/background.php:499
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:405 includes/elements/container.php:1239
#: includes/elements/section.php:263 includes/elements/section.php:1022
#: includes/widgets/common-base.php:337 includes/widgets/divider.php:443
#: includes/widgets/icon-list.php:343 includes/widgets/image-box.php:362
#: includes/widgets/image.php:284
#: modules/floating-buttons/base/widget-contact-button-base.php:2842
#: modules/nested-tabs/widgets/nested-tabs.php:312
#: modules/shapes/widgets/text-path.php:540
#: modules/shapes/widgets/text-path.php:611
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:118
msgid "Width"
msgstr "Breedte"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "Secundair"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "Accent"

#: includes/widgets/menu-anchor.php:120
msgid "For Example: About"
msgstr "Bijvoorbeeld: Over ons"

#: includes/controls/groups/background.php:457
msgctxt "Background Control"
msgid "Repeat"
msgstr "Herhalen"

#: includes/controls/groups/typography.php:138
#: includes/controls/groups/typography.php:299
msgctxt "Typography Control"
msgid "Weight"
msgstr "Dikte"

#: includes/widgets/divider.php:605 includes/widgets/icon-list.php:318
#: modules/floating-buttons/base/widget-contact-button-base.php:2552
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1218
msgid "Weight"
msgstr "Dikte"

#: includes/controls/groups/typography.php:160
msgctxt "Typography Control"
msgid "Transform"
msgstr "Transformeren"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:342
#: includes/widgets/icon-list.php:299 includes/widgets/star-rating.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:226
msgid "Solid"
msgstr "Doorgetrokken"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:547 includes/elements/container.php:991
#: includes/elements/section.php:831 includes/widgets/common-base.php:966
#: modules/floating-buttons/base/widget-floating-bars-base.php:761
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:15
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:244
msgid "Border"
msgstr "Rand"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:378
#: includes/widgets/divider.php:528 includes/widgets/divider.php:578
#: includes/widgets/icon-list.php:282
#: modules/atomic-widgets/elements/atomic-divider/atomic-divider.php:35
msgid "Divider"
msgstr "Scheiding"

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:785 includes/elements/container.php:1388
#: includes/elements/section.php:1246 includes/widgets/accordion.php:394
#: includes/widgets/accordion.php:540 includes/widgets/common-base.php:322
#: includes/widgets/divider.php:870 includes/widgets/icon-box.php:527
#: includes/widgets/icon.php:343 includes/widgets/social-icons.php:422
#: includes/widgets/toggle.php:418 includes/widgets/toggle.php:564
#: includes/widgets/traits/button-trait.php:502
#: modules/floating-buttons/base/widget-contact-button-base.php:1461
#: modules/floating-buttons/base/widget-contact-button-base.php:2186
#: modules/floating-buttons/base/widget-contact-button-base.php:2201
#: modules/floating-buttons/base/widget-contact-button-base.php:2749
#: modules/floating-buttons/base/widget-contact-button-base.php:2889
#: modules/floating-buttons/base/widget-floating-bars-base.php:857
#: modules/floating-buttons/base/widget-floating-bars-base.php:1241
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1179
#: modules/nested-accordion/widgets/nested-accordion.php:474
#: modules/nested-accordion/widgets/nested-accordion.php:538
#: modules/nested-tabs/widgets/nested-tabs.php:701
#: modules/nested-tabs/widgets/nested-tabs.php:1051
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:117
msgid "Padding"
msgstr "Padding"

#: includes/base/element-base.php:1326 includes/base/element-base.php:1354
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:217 includes/elements/column.php:739
#: includes/elements/section.php:1195 includes/widgets/common-base.php:522
#: includes/widgets/counter.php:322 includes/widgets/counter.php:396
#: includes/widgets/counter.php:432 includes/widgets/divider.php:478
#: includes/widgets/divider.php:774 includes/widgets/divider.php:940
#: includes/widgets/heading.php:274 includes/widgets/icon-box.php:308
#: includes/widgets/icon-list.php:267 includes/widgets/icon-list.php:549
#: includes/widgets/icon-list.php:580 includes/widgets/icon.php:193
#: includes/widgets/image-box.php:276 includes/widgets/image-carousel.php:751
#: includes/widgets/image-carousel.php:852
#: includes/widgets/image-gallery.php:375 includes/widgets/image.php:267
#: includes/widgets/image.php:585 includes/widgets/rating.php:207
#: includes/widgets/social-icons.php:332 includes/widgets/star-rating.php:223
#: includes/widgets/tabs.php:213 includes/widgets/tabs.php:243
#: includes/widgets/tabs.php:430 includes/widgets/testimonial.php:251
#: includes/widgets/text-editor.php:266
#: includes/widgets/traits/button-trait.php:260
#: includes/widgets/traits/button-trait.php:292 includes/widgets/video.php:978
#: modules/floating-buttons/base/widget-contact-button-base.php:2944
#: modules/floating-buttons/base/widget-floating-bars-base.php:1193
#: modules/nested-accordion/widgets/nested-accordion.php:177
#: modules/nested-tabs/widgets/nested-tabs.php:238
#: modules/nested-tabs/widgets/nested-tabs.php:280
#: modules/nested-tabs/widgets/nested-tabs.php:350
#: modules/shapes/widgets/text-path.php:181
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:94
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:161
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:197
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:202
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:207
msgid "Center"
msgstr "Gecentreerd"

#: includes/widgets/divider.php:699 includes/widgets/icon-list.php:518
#: includes/widgets/image-gallery.php:286 includes/widgets/star-rating.php:291
msgid "Gap"
msgstr "Tussenruimte"

#: includes/elements/column.php:747 includes/elements/section.php:1203
#: includes/widgets/heading.php:282 includes/widgets/icon-box.php:316
#: includes/widgets/image-box.php:284 includes/widgets/image-carousel.php:860
#: includes/widgets/image-gallery.php:383 includes/widgets/image.php:593
#: includes/widgets/star-rating.php:231 includes/widgets/text-editor.php:274
msgid "Justified"
msgstr "Uitgevuld"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:123
msgid "Spacer"
msgstr "Afstandhouder"

#: includes/widgets/divider.php:837 includes/widgets/icon-box.php:135
#: includes/widgets/icon.php:136 includes/widgets/text-editor.php:426
msgid "Framed"
msgstr "Omlijst"

#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:189 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:351
#: includes/widgets/icon-box.php:273 includes/widgets/image-box.php:248
#: modules/floating-buttons/base/widget-contact-button-base.php:2998
msgid "Middle"
msgstr "Midden"

#: includes/widgets/heading.php:219 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1121
#: modules/floating-buttons/base/widget-contact-button-base.php:1546
#: modules/floating-buttons/base/widget-contact-button-base.php:1928
#: modules/floating-buttons/base/widget-contact-button-base.php:2278
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1408
msgid "Medium"
msgstr "Gemiddeld"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "Breed"

#: includes/editor-templates/hotkeys.php:128
msgid "Page Settings"
msgstr "Pagina instellingen"

#: includes/widgets/image-carousel.php:521
msgid "Slide"
msgstr "Slide"

#: includes/widgets/image-carousel.php:202
msgid "Slides to Scroll"
msgstr "Slides om te scrollen"

#: includes/widgets/image-carousel.php:185
msgid "Slides to Show"
msgstr "Slides om weer te geven"

#: includes/widgets/video.php:513
msgid "Suggested Videos"
msgstr "Aanbevolen video's"

#: includes/widgets/divider.php:915 includes/widgets/icon-box.php:422
#: includes/widgets/icon-box.php:464 includes/widgets/icon.php:237
#: includes/widgets/icon.php:277 includes/widgets/social-icons.php:235
#: includes/widgets/social-icons.php:388 includes/widgets/social-icons.php:546
#: includes/widgets/text-editor.php:451
msgid "Secondary Color"
msgstr "Secundaire kleur"

#: includes/widgets/divider.php:898 includes/widgets/icon-box.php:406
#: includes/widgets/icon-box.php:447 includes/widgets/icon.php:220
#: includes/widgets/icon.php:263 includes/widgets/social-icons.php:221
#: includes/widgets/social-icons.php:374 includes/widgets/social-icons.php:531
#: includes/widgets/text-editor.php:436
msgid "Primary Color"
msgstr "Primaire kleur"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:163 includes/widgets/alert.php:302
#: includes/widgets/icon-box.php:181 includes/widgets/icon-box.php:736
#: includes/widgets/image-box.php:156 includes/widgets/image-box.php:704
#: includes/widgets/image-carousel.php:417
#: modules/floating-buttons/base/widget-contact-button-base.php:2342
#: modules/link-in-bio/base/widget-link-in-bio-base.php:895
#: modules/link-in-bio/base/widget-link-in-bio-base.php:900
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1351
msgid "Description"
msgstr "Beschrijving"

#: includes/widgets/accordion.php:352 includes/widgets/accordion.php:451
#: includes/widgets/image-carousel.php:719 includes/widgets/tabs.php:380
#: includes/widgets/toggle.php:385 includes/widgets/toggle.php:475
msgid "Active Color"
msgstr "Actieve kleur"

#: includes/widgets/video.php:974
msgid "Content Position"
msgstr "Positie inhoud"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "Positie kolom"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:174 includes/widgets/alert.php:218
#: includes/widgets/image.php:622 includes/widgets/progress.php:320
#: includes/widgets/tabs.php:345 includes/widgets/video.php:894
#: modules/floating-buttons/base/widget-contact-button-base.php:1237
#: modules/floating-buttons/base/widget-contact-button-base.php:1289
#: modules/floating-buttons/base/widget-contact-button-base.php:1330
#: modules/floating-buttons/base/widget-contact-button-base.php:1376
#: modules/floating-buttons/base/widget-contact-button-base.php:1692
#: modules/floating-buttons/base/widget-contact-button-base.php:1962
#: modules/floating-buttons/base/widget-contact-button-base.php:1998
#: modules/floating-buttons/base/widget-contact-button-base.php:2031
#: modules/floating-buttons/base/widget-contact-button-base.php:2135
#: modules/floating-buttons/base/widget-contact-button-base.php:2161
#: modules/floating-buttons/base/widget-contact-button-base.php:2370
#: modules/floating-buttons/base/widget-contact-button-base.php:2657
#: modules/floating-buttons/base/widget-contact-button-base.php:2726
#: modules/floating-buttons/base/widget-contact-button-base.php:2813
#: modules/floating-buttons/base/widget-contact-button-base.php:2827
#: modules/floating-buttons/base/widget-floating-bars-base.php:674
#: modules/floating-buttons/base/widget-floating-bars-base.php:708
#: modules/floating-buttons/base/widget-floating-bars-base.php:1110
#: modules/floating-buttons/base/widget-floating-bars-base.php:1155
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1128
#: modules/nested-tabs/widgets/nested-tabs.php:506
#: modules/nested-tabs/widgets/nested-tabs.php:564
#: modules/nested-tabs/widgets/nested-tabs.php:648
#: modules/nested-tabs/widgets/nested-tabs.php:1003 assets/js/ai-admin.js:14057
#: assets/js/ai-admin.js:14775 assets/js/ai-gutenberg.js:14275
#: assets/js/ai-gutenberg.js:14993 assets/js/ai-media-library.js:14057
#: assets/js/ai-media-library.js:14775
#: assets/js/ai-unify-product-images.js:14057
#: assets/js/ai-unify-product-images.js:14775 assets/js/ai.js:15609
#: assets/js/ai.js:16327
msgid "Background Color"
msgstr "Achtergrondkleur"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "Geen tussenruimte"

#: includes/editor-templates/global.php:34
msgid "Add New Section"
msgstr "Nieuwe sectie toevoegen"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:1007
#: modules/floating-buttons/base/widget-floating-bars-base.php:367
#: modules/nested-accordion/widgets/nested-accordion.php:161
msgid "Add Item"
msgstr "Item toevoegen"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:55
#: includes/elements/column.php:695 includes/elements/section.php:1151
#: includes/widgets/alert.php:270 includes/widgets/alert.php:310
#: includes/widgets/counter.php:499 includes/widgets/counter.php:553
#: includes/widgets/heading.php:366 includes/widgets/image-carousel.php:874
#: includes/widgets/image-gallery.php:400 includes/widgets/image.php:607
#: includes/widgets/progress.php:255 includes/widgets/star-rating.php:258
#: includes/widgets/testimonial.php:280 includes/widgets/testimonial.php:378
#: includes/widgets/testimonial.php:423 includes/widgets/text-editor.php:343
#: includes/widgets/traits/button-trait.php:348
#: includes/widgets/traits/button-trait.php:401
#: modules/floating-buttons/base/widget-contact-button-base.php:1583
#: modules/floating-buttons/base/widget-contact-button-base.php:1615
#: modules/floating-buttons/base/widget-contact-button-base.php:1746
#: modules/floating-buttons/base/widget-contact-button-base.php:1777
#: modules/floating-buttons/base/widget-contact-button-base.php:1808
#: modules/floating-buttons/base/widget-contact-button-base.php:2116
#: modules/floating-buttons/base/widget-contact-button-base.php:2323
#: modules/floating-buttons/base/widget-contact-button-base.php:2351
#: modules/floating-buttons/base/widget-contact-button-base.php:2644
#: modules/floating-buttons/base/widget-contact-button-base.php:2713
#: modules/floating-buttons/base/widget-floating-bars-base.php:663
#: modules/floating-buttons/base/widget-floating-bars-base.php:697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1381
#: modules/floating-buttons/base/widget-floating-bars-base.php:1401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1108
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1304
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1332
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1360
msgid "Text Color"
msgstr "Tekstkleur"

#: includes/editor-templates/hotkeys.php:165
#: includes/editor-templates/panel.php:88
msgid "Responsive Mode"
msgstr "Responsive modus"

#: includes/elements/column.php:707 includes/elements/section.php:1163
#: includes/widgets/heading.php:389 includes/widgets/text-editor.php:358
#: includes/widgets/text-editor.php:378
msgid "Link Color"
msgstr "Linkkleur"

#: includes/elements/column.php:161
msgid "Column Width"
msgstr "Breedte kolom"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:202
msgid "Columns Gap"
msgstr "Tussenruimte kolommen"

#: includes/elements/container.php:484 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "Min. hoogte"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "Minimale hoogte"

#: includes/settings/settings.php:271
msgid "Post Types"
msgstr "Berichttypen"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "Rollen uitsluiten"

#: includes/base/element-base.php:1358 includes/controls/dimensions.php:84
#: includes/elements/column.php:190 includes/elements/container.php:1182
#: includes/elements/container.php:1666 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:965
#: includes/widgets/common-base.php:685 includes/widgets/counter.php:355
#: includes/widgets/icon-box.php:277 includes/widgets/image-box.php:252
#: modules/floating-buttons/base/widget-contact-button-base.php:3002
#: modules/floating-buttons/base/widget-floating-bars-base.php:1441
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:24
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:151
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:220
msgid "Bottom"
msgstr "Onder"

#: includes/base/element-base.php:1350 includes/controls/dimensions.php:82
#: includes/elements/column.php:188 includes/elements/container.php:1181
#: includes/elements/container.php:1662 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:964
#: includes/widgets/common-base.php:681 includes/widgets/counter.php:347
#: includes/widgets/icon-box.php:247 includes/widgets/icon-box.php:269
#: includes/widgets/image-box.php:221 includes/widgets/image-box.php:244
#: includes/widgets/testimonial.php:226 includes/widgets/video.php:979
#: modules/floating-buttons/base/widget-contact-button-base.php:2994
#: modules/floating-buttons/base/widget-floating-bars-base.php:1437
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:21
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:150
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:217
msgid "Top"
msgstr "Boven"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "Systeeminformatie"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "Informatie kopiëren & plakken"

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "Systeeminformatie downloaden"

#: includes/widgets/counter.php:158
msgid "Number Prefix"
msgstr "Voorvoegsel nummer"

#: includes/widgets/counter.php:173
msgid "Number Suffix"
msgstr "Achtervoegsel nummer"

#: includes/widgets/accordion.php:147
msgid "Accordion Content"
msgstr "Inhoud accordeon"

#: includes/widgets/accordion.php:134
msgid "Accordion Title"
msgstr "Titel accordeon"

#: includes/widgets/accordion.php:165
msgid "Accordion Items"
msgstr "Items accordeon"

#: includes/widgets/tabs.php:142 includes/widgets/tabs.php:143
msgid "Tab Content"
msgstr "Inhoud tab"

#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:130
#: modules/nested-tabs/widgets/nested-tabs.php:115
#: modules/nested-tabs/widgets/nested-tabs.php:116
msgid "Tab Title"
msgstr "Titel tab"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:281
#: includes/widgets/divider.php:1002 includes/widgets/icon-box.php:591
#: includes/widgets/icon.php:392 includes/widgets/tabs.php:311
#: includes/widgets/text-editor.php:551 includes/widgets/toggle.php:284
#: modules/floating-buttons/base/widget-floating-bars-base.php:777
#: modules/nested-accordion/widgets/nested-accordion.php:517
#: modules/nested-tabs/widgets/nested-tabs.php:525
#: modules/nested-tabs/widgets/nested-tabs.php:583
#: modules/nested-tabs/widgets/nested-tabs.php:667
#: modules/nested-tabs/widgets/nested-tabs.php:1019
msgid "Border Width"
msgstr "Randdikte"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:303
#: includes/widgets/social-icons.php:562 includes/widgets/tabs.php:334
#: includes/widgets/toggle.php:305 includes/widgets/traits/button-trait.php:430
#: modules/floating-buttons/base/widget-floating-bars-base.php:722
#: modules/floating-buttons/base/widget-floating-bars-base.php:814
#: modules/nested-accordion/widgets/nested-accordion.php:514
#: modules/nested-tabs/widgets/nested-tabs.php:522
#: modules/nested-tabs/widgets/nested-tabs.php:580
#: modules/nested-tabs/widgets/nested-tabs.php:664
#: modules/nested-tabs/widgets/nested-tabs.php:1016
msgid "Border Color"
msgstr "Randkleur"

#: includes/widgets/alert.php:136 includes/widgets/heading.php:192
#: includes/widgets/icon-box.php:173 includes/widgets/image-box.php:148
#: includes/widgets/progress.php:122
msgid "Enter your title"
msgstr "Voer je titel in"

#: includes/widgets/video.php:635 includes/widgets/video.php:642
#: includes/widgets/video.php:797
msgid "Image Overlay"
msgstr "Overlay afbeelding"

#: includes/widgets/image-box.php:298 includes/widgets/image-carousel.php:787
msgid "Image Spacing"
msgstr "Afstand afbeelding"

#: includes/widgets/tabs.php:164
#: modules/nested-tabs/widgets/nested-tabs.php:167
msgid "Tabs Items"
msgstr "Items tabs"

#: includes/widgets/image-carousel.php:489
msgid "Autoplay Speed"
msgstr "Snelheid automatisch afspelen"

#: includes/widgets/progress.php:219 includes/widgets/progress.php:355
msgid "Inner Text"
msgstr "Binnenste tekst"

#: includes/widgets/progress.php:204
msgid "Display Percentage"
msgstr "Weergave percentage"

#: includes/widgets/menu-anchor.php:115
msgid "The ID of Menu Anchor."
msgstr "Het ID van menu-anker."

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:108
msgid "Menu Anchor"
msgstr "Menu-anker"

#: includes/widgets/icon-list.php:145 includes/widgets/icon-list.php:146
msgid "List Item"
msgstr "Lijstitem"

#: includes/widgets/icon-list.php:199
msgid "List Item #3"
msgstr "Lijstitem #3"

#: includes/widgets/icon-list.php:192
msgid "List Item #2"
msgstr "Lijstitem #2"

#: includes/widgets/icon-list.php:185
msgid "List Item #1"
msgstr "Lijstitem #1"

#: includes/widgets/counter.php:235
msgid "Cool Number"
msgstr "Gaaf nummer"

#: core/admin/admin-notices.php:238
msgid "Sure! I'd love to help"
msgstr "Natuurlijk! Ik zou het leuk vinden om te helpen"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "Thema's"

#: includes/widgets/video.php:409
msgid "Player Controls"
msgstr "Afspeelknoppen"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:118 includes/elements/container.php:1368
#: includes/elements/section.php:231 includes/managers/controls.php:338
#: includes/managers/elements.php:284 includes/widgets/common-base.php:292
#: includes/widgets/icon-list.php:117
#: modules/floating-buttons/base/widget-contact-button-base.php:2928
#: modules/floating-buttons/base/widget-floating-bars-base.php:1425
#: modules/nested-accordion/widgets/nested-accordion.php:107
#: assets/js/editor.js:37466
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:9
msgid "Layout"
msgstr "Lay-out"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:106
msgid "Sidebar"
msgstr "Zijbalk"

#: includes/widgets/sidebar.php:91
msgid "No sidebars were found"
msgstr "Geen zijbalken gevonden"

#: includes/widgets/sidebar.php:93 includes/widgets/sidebar.php:113
msgid "Choose Sidebar"
msgstr "Kies zijbalk"

#: core/experiments/manager.php:391 core/experiments/manager.php:681
#: modules/element-cache/module.php:124 assets/js/editor.js:27984
#: assets/js/element-manager-admin.js:597
msgid "Inactive"
msgstr "Inactief"

#: core/experiments/manager.php:390 core/experiments/manager.php:680
#: modules/element-cache/module.php:125
#: modules/floating-buttons/base/widget-contact-button-base.php:1312
#: modules/nested-accordion/widgets/nested-accordion.php:667
#: modules/nested-accordion/widgets/nested-accordion.php:730
#: modules/nested-tabs/widgets/nested-tabs.php:629
#: modules/nested-tabs/widgets/nested-tabs.php:809
#: modules/nested-tabs/widgets/nested-tabs.php:971 assets/js/editor.js:27986
#: assets/js/element-manager-admin.js:594
msgid "Active"
msgstr "Actief"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592; Terug naar de WordPress editor"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "Laptop"

#: core/document-types/post.php:65
msgid "Posts"
msgstr "Berichten"

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "Je kunt onderstaande informatie eenvoudig kopiëren met Ctrl+C / Ctrl+V:"

#: includes/widgets/heading.php:49 includes/widgets/heading.php:177
#: includes/widgets/heading.php:258
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:32
#: modules/link-in-bio/base/widget-link-in-bio-base.php:849
#: modules/link-in-bio/base/widget-link-in-bio-base.php:854
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1295
msgid "Heading"
msgstr "Koptekst"

#: includes/elements/column.php:683 includes/elements/section.php:1139
msgid "Heading Color"
msgstr "Koptekst kleur"

#: includes/elements/container.php:395 includes/elements/section.php:253
msgid "Boxed"
msgstr "Boxed"

#: includes/widgets/image-box.php:212 includes/widgets/testimonial.php:217
msgid "Image Position"
msgstr "Positie van de afbeelding"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1651
msgid "Image Size"
msgstr "Afbeeldingsgrootte"

#: modules/nested-tabs/widgets/nested-tabs.php:230
#: modules/nested-tabs/widgets/nested-tabs.php:272
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:96
msgid "Justify"
msgstr "Uitlijnen"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:123
#: includes/widgets/toggle.php:276
msgid "Toggle"
msgstr "Toggle"

#: includes/widgets/toggle.php:168
msgid "Toggle Items"
msgstr "Toggle items"

#: includes/widgets/toggle.php:147
msgid "Toggle Content"
msgstr "Toggle Inhoud"

#: includes/widgets/toggle.php:173
msgid "Toggle #1"
msgstr "Toggle #1"

#: includes/widgets/toggle.php:177
msgid "Toggle #2"
msgstr "Toggle #2"

#: includes/base/element-base.php:1400
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:163 includes/widgets/audio.php:163
#: includes/widgets/audio.php:174 includes/widgets/audio.php:185
#: includes/widgets/audio.php:196 includes/widgets/audio.php:210
#: includes/widgets/audio.php:221 includes/widgets/audio.php:232
#: includes/widgets/audio.php:243 includes/widgets/counter.php:202
#: includes/widgets/progress.php:156 includes/widgets/progress.php:206
#: includes/widgets/video.php:412 includes/widgets/video.php:427
#: includes/widgets/video.php:454 includes/widgets/video.php:532
#: includes/widgets/video.php:546 includes/widgets/video.php:560
#: includes/widgets/video.php:586 includes/widgets/video.php:645
#: includes/widgets/video.php:686
#: modules/floating-buttons/base/widget-contact-button-base.php:518
#: modules/floating-buttons/base/widget-contact-button-base.php:628
#: modules/floating-buttons/base/widget-contact-button-base.php:693
#: modules/floating-buttons/base/widget-contact-button-base.php:2527
#: modules/floating-buttons/base/widget-floating-bars-base.php:230
#: modules/floating-buttons/base/widget-floating-bars-base.php:297
#: assets/js/element-manager-admin.js:449
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:84
msgid "Show"
msgstr "Toon"

#: includes/editor-templates/panel.php:71
#: includes/editor-templates/panel.php:72
msgid "Widgets Panel"
msgstr "Widgets paneel"

#: includes/widgets/menu-anchor.php:121
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "Dit ID is het CSS ID dat je op je eigen pagina moet gebruiken. Zonder #."

#: includes/base/element-base.php:890 includes/base/element-base.php:902
#: includes/widgets/divider.php:981 includes/widgets/icon-box.php:569
#: includes/widgets/icon.php:371 modules/shapes/widgets/text-path.php:283
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:114
#: assets/js/packages/editor-controls/editor-controls.strings.js:119
msgid "Rotate"
msgstr "Roteren"

#: includes/elements/column.php:719 includes/elements/section.php:1175
msgid "Link Hover Color"
msgstr "Link hoveren kleur"

#: includes/widgets/alert.php:151
msgid "I am a description. Click the edit button to change this text."
msgstr "Ik ben een beschrijving. Klik op de bewerken knop om deze tekst te wijzigen."

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:264 includes/elements/container.php:588
#: includes/elements/section.php:498 includes/widgets/divider.php:538
#: includes/widgets/heading.php:234
#: modules/atomic-widgets/elements/div-block/div-block.php:75
msgid "HTML Tag"
msgstr "HTML tag"

#: includes/widgets/html.php:97 includes/widgets/html.php:104
msgid "HTML Code"
msgstr "HTML code"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:410
#: includes/widgets/image-carousel.php:416
#: includes/widgets/image-carousel.php:833
#: includes/widgets/image-gallery.php:170
#: includes/widgets/image-gallery.php:356 includes/widgets/image.php:158
#: includes/widgets/image.php:565
msgid "Caption"
msgstr "Bijschrift"

#: includes/controls/groups/background.php:428
msgctxt "Background Control"
msgid "Attachment"
msgstr "Bijlage"

#: includes/widgets/accordion.php:419 includes/widgets/divider.php:470
#: includes/widgets/heading.php:266 includes/widgets/icon-box.php:300
#: includes/widgets/icon-list.php:259 includes/widgets/icon.php:185
#: includes/widgets/image-box.php:268 includes/widgets/image-carousel.php:844
#: includes/widgets/image-gallery.php:367 includes/widgets/image.php:259
#: includes/widgets/image.php:577 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:324 includes/widgets/star-rating.php:215
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:235
#: includes/widgets/tabs.php:422 includes/widgets/testimonial.php:242
#: includes/widgets/text-editor.php:258 includes/widgets/toggle.php:443
#: includes/widgets/traits/button-trait.php:284
#: modules/shapes/widgets/text-path.php:172
msgid "Alignment"
msgstr "Uitlijning"

#: core/admin/admin-notices.php:658 includes/controls/gallery.php:123
#: includes/controls/media.php:319 includes/widgets/heading.php:477
msgid "Activate Plugin"
msgstr "Activeer plugin"

#: includes/editor-templates/panel-elements.php:75
msgid "Search Widget..."
msgstr "Zoek widget…"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:45522
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:27
msgid "Discard"
msgstr "Verwerpen"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Scroll"
msgstr "Scrol"

#: core/breakpoints/manager.php:319
msgid "Mobile Landscape"
msgstr "Mobiel landschap"

#: core/breakpoints/manager.php:314
msgid "Mobile Portrait"
msgstr "Mobiel portret"

#: includes/widgets/image-carousel.php:456
msgid "Pause on Hover"
msgstr "Pauzeren bij hoveren"

#: includes/widgets/counter.php:134
msgid "Starting Number"
msgstr "Beginwaarde"

#: includes/widgets/counter.php:146
msgid "Ending Number"
msgstr "Eindwaarde"

#: modules/system-info/module.php:202
msgid "You do not have permission to download this file."
msgstr "Je hebt geen toestemming om dit bestand te downloaden."

#: includes/widgets/accordion.php:185 includes/widgets/accordion.php:408
#: includes/widgets/alert.php:173 includes/widgets/divider.php:509
#: includes/widgets/divider.php:561 includes/widgets/divider.php:821
#: includes/widgets/icon-box.php:117 includes/widgets/icon-box.php:386
#: includes/widgets/icon-list.php:156 includes/widgets/icon-list.php:415
#: includes/widgets/icon.php:44 includes/widgets/icon.php:111
#: includes/widgets/icon.php:118 includes/widgets/icon.php:177
#: includes/widgets/rating.php:52 includes/widgets/rating.php:177
#: includes/widgets/social-icons.php:116 includes/widgets/social-icons.php:353
#: includes/widgets/star-rating.php:168 includes/widgets/toggle.php:188
#: includes/widgets/toggle.php:432 includes/widgets/traits/button-trait.php:126
#: includes/widgets/video.php:698
#: modules/floating-buttons/base/widget-contact-button-base.php:495
#: modules/floating-buttons/base/widget-floating-bars-base.php:113
#: modules/floating-buttons/base/widget-floating-bars-base.php:181
#: modules/floating-buttons/base/widget-floating-bars-base.php:325
#: modules/floating-buttons/base/widget-floating-bars-base.php:399
#: modules/floating-buttons/base/widget-floating-bars-base.php:1267
#: modules/nested-accordion/widgets/nested-accordion.php:205
#: modules/nested-accordion/widgets/nested-accordion.php:595
#: modules/nested-tabs/widgets/nested-tabs.php:126
#: modules/nested-tabs/widgets/nested-tabs.php:847
msgid "Icon"
msgstr "Icoon"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:110
msgid "Icon List"
msgstr "Icoon lijst"

#: includes/widgets/video.php:682 includes/widgets/video.php:809
#: modules/floating-buttons/base/widget-floating-bars-base.php:267
msgid "Play Icon"
msgstr "Afspeel icoon"

#: includes/widgets/social-icons.php:523
msgid "Icon Hover"
msgstr "Hoveren icoon"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "Selecteren icoon"

#: includes/widgets/icon-box.php:330
#: includes/widgets/traits/button-trait.php:175
#: modules/floating-buttons/base/widget-contact-button-base.php:1162
#: modules/floating-buttons/base/widget-contact-button-base.php:2418
#: modules/floating-buttons/base/widget-floating-bars-base.php:606
#: modules/floating-buttons/base/widget-floating-bars-base.php:1331
msgid "Icon Spacing"
msgstr "Icoon afstand"

#: includes/widgets/icon-box.php:237
#: includes/widgets/traits/button-trait.php:142
#: modules/floating-buttons/base/widget-contact-button-base.php:1132
#: modules/floating-buttons/base/widget-contact-button-base.php:2398
#: modules/floating-buttons/base/widget-floating-bars-base.php:576
#: modules/floating-buttons/base/widget-floating-bars-base.php:1286
msgid "Icon Position"
msgstr "Icoon positie"

#: includes/widgets/video.php:759
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:124
msgid "Aspect Ratio"
msgstr "Aspect ratio"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:110
msgid "Image Box"
msgstr "Afbeelding vak"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:110
msgid "Icon Box"
msgstr "Icoon vak"

#: includes/base/element-base.php:1399
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:164 includes/widgets/audio.php:162
#: includes/widgets/audio.php:173 includes/widgets/audio.php:184
#: includes/widgets/audio.php:195 includes/widgets/audio.php:209
#: includes/widgets/audio.php:220 includes/widgets/audio.php:231
#: includes/widgets/audio.php:242 includes/widgets/counter.php:203
#: includes/widgets/progress.php:157 includes/widgets/progress.php:207
#: includes/widgets/video.php:411 includes/widgets/video.php:426
#: includes/widgets/video.php:453 includes/widgets/video.php:531
#: includes/widgets/video.php:545 includes/widgets/video.php:559
#: includes/widgets/video.php:585 includes/widgets/video.php:644
#: includes/widgets/video.php:685
#: modules/floating-buttons/base/widget-contact-button-base.php:519
#: modules/floating-buttons/base/widget-contact-button-base.php:629
#: modules/floating-buttons/base/widget-contact-button-base.php:694
#: modules/floating-buttons/base/widget-contact-button-base.php:2528
#: modules/floating-buttons/base/widget-floating-bars-base.php:231
#: modules/floating-buttons/base/widget-floating-bars-base.php:298
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:85
msgid "Hide"
msgstr "Verberg"

#: modules/floating-buttons/base/widget-contact-button-base.php:1846
msgid "Chat Background Color"
msgstr "Chat achtergrondkleur"

#: modules/floating-buttons/documents/floating-buttons.php:31
msgid "Go To Dashboard"
msgstr "Ga naar het dashboard"

#: includes/widgets/toggle.php:134
msgid "Toggle Title"
msgstr "Toggle titel"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:111
#: includes/widgets/alert.php:210
msgid "Alert"
msgstr "Waarschuwing"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:572 includes/elements/column.php:609
#: includes/elements/container.php:1036 includes/elements/container.php:1087
#: includes/elements/section.php:855 includes/elements/section.php:891
#: includes/widgets/common-base.php:991 includes/widgets/common-base.php:1028
#: includes/widgets/divider.php:1025 includes/widgets/icon-box.php:606
#: includes/widgets/icon.php:407 includes/widgets/image-box.php:459
#: includes/widgets/image-carousel.php:819
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:540
#: includes/widgets/progress.php:343 includes/widgets/social-icons.php:509
#: includes/widgets/testimonial.php:355 includes/widgets/text-editor.php:529
#: includes/widgets/traits/button-trait.php:489
#: modules/nested-accordion/widgets/nested-accordion.php:461
#: modules/nested-accordion/widgets/nested-accordion.php:526
#: modules/nested-tabs/widgets/nested-tabs.php:688
#: modules/nested-tabs/widgets/nested-tabs.php:1028
msgid "Border Radius"
msgstr "Randradius"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:391 includes/elements/section.php:249
#: includes/widgets/video.php:954
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1590
msgid "Content Width"
msgstr "Inhoud breedte"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "Resetten"

#: includes/elements/column.php:827 includes/elements/container.php:1784
#: includes/elements/section.php:1288 includes/widgets/common-base.php:802
#: modules/floating-buttons/base/widget-contact-button-base.php:3107
#: modules/floating-buttons/base/widget-floating-bars-base.php:1513
msgid "CSS Classes"
msgstr "CSS klassen"

#: core/admin/admin.php:242
#: core/editor/loader/v1/templates/editor-body-v1-view.php:23
#: core/editor/loader/v2/templates/editor-body-v2-view.php:23
#: includes/editor-templates/templates.php:54 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:1739 assets/js/ai-gutenberg.js:1877
#: assets/js/ai-media-library.js:1739 assets/js/ai-unify-product-images.js:1739
#: assets/js/ai.js:2527 assets/js/app-packages.js:3976 assets/js/app.js:1113
msgid "Loading"
msgstr "Aan het laden"