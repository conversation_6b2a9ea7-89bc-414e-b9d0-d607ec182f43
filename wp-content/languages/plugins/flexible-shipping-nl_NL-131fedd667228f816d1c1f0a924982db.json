{"translation-revision-date": "2025-09-08 17:59:55+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "nl"}, "Invalid additional cost in the rule: %s. To use this additional cost, you need the %s plugin!": ["Ongeldige extra kosten in de regel: %s. Om deze extra kosten te geb<PERSON>iken, heb je de %s plugin nodig!"], "Invalid condition in the rule: %s. To use this condition, you need the %s plugin!": ["Ongeldige voorwaarde in de regel: %s. Om deze voorwaarde te gebruiken, heb je de %s plugin nodig!"], "Rules table configuration successfully used.": ["Regels tabelconfiguratie succesvol gebruikt."], "Type your message...": ["<PERSON>p je bericht..."], "AI can make mistakes. Octolize has access to the conversation held in this chat.": ["AI kan fouten maken. Octolize heeft toegang tot het gesprek in deze chat."], "You can also select one of initial prompts below": ["Je kunt ook een van de eerste aanwijzingen hieronder selecteren"], "I can help you with configuration examples, JSON format, and more.": ["<PERSON><PERSON> kan je helpen met configurat<PERSON>, JSON format en meer."], "Ask me anything about Flexible Shipping rules settings.": ["Vraag me alles over de instellingen van flexibele verzendregels."], "Start new conversation": ["Begin een nieuw gesprek"], "Show JSON": ["Toon JSON"], "Hide JSON": ["JSON verbergen"], "Use this configuration": ["Gebruik deze configuratie"], "Rules in configuration: %d": ["Regels in configuratie: %d"], "Recommended setup": ["Aanbevolen setup"], "Flexible Shipping AI assistant": ["Flexibele AI assistent voor verzending"], "Save time with AI ✦": ["<PERSON><PERSON><PERSON> tij<PERSON> met <PERSON> ✦"], "Save JSON file": ["<PERSON><PERSON> bestand opslaan"], "Import JSON file": ["Json bestand importeren"], "Paste JSON from clipboard": ["Json plakken vanaf klembord"], "Selected rules:": ["Gese<PERSON><PERSON><PERSON> regels:"], "use one of the ready-made scenarios": ["gebruik een van de kant-en-klare scenario's"], "add the first rule": ["de eerste regel toevoegen"], "or": ["of"], "save time with AI ✦": ["<PERSON><PERSON><PERSON> tij<PERSON> met <PERSON> ✦"], "You can": ["Je kunt"], "Special actions are available only in the Flexible Shipping PRO plugin!": ["Speciale acties zijn alleen besch<PERSON> in de Flexible Shipping Pro plugin!"], "Invalid conditions in rule.": ["Ongeldige voorwaarden in regel."], "Rules table configuration is missing.": ["Regels tabelconfiguratie ontbreken."], "Invalid rules table config!": ["Ongeldige regels tabel configuratie!"], "Invalid rules table config! JSON parse failed with message: %s": ["Ongeldige regeltabel configuratie! JSON parse mislukt met bericht: %s"], "Rules table successfully imported.": ["<PERSON><PERSON> met regels succesvol geïmporteerd."], "Rules table successfully pasted.": ["<PERSON><PERSON> met re<PERSON><PERSON> succ<PERSON><PERSON><PERSON>."], "Missing Id: %s": ["Ontbrekend ID: %s"], "Learn more about PRO version →": ["<PERSON>er informatie over de PRO versie →"], "Show the options available in the PRO version.": ["<PERSON><PERSON> instellingen beschik<PERSON>ar in de Pro versie."], "Tick this checkbox to display the features and shipping cost calculation conditions coming with the plugin's PRO version.": ["Vink dit selectievakje aan om de functies en verzendkosten berekening voorwaarden komen met PRO de plugin versie weer te geven."], "PRO Features": ["PRO functies"], "Duplicate": ["<PERSON><PERSON><PERSON><PERSON>"], "Close": ["Sluiten"], "Use ready-made scenarios": ["Geb<PERSON><PERSON> de kant-en-klare scenario's"], "Use selected scenario": ["Gebruik het geselecteerde scenario"], "Select other scenario": ["Selecteer een ander scenario"], "Looking for different scenario? %1$sCheck our documentation →%2$s": ["Op zoek naar een ander scenario? %1$sBekijk onze documentatie →%2$s"], "Use rules from scenario?": ["Regels uit het scenario geb<PERSON>iken?"], "Please mind that saving the changes after using a ready-made scenario will overwrite the previously configured rules for this shipping method. However, not until the changes are saved, the prior setup is still in use.": ["Houd er rekening mee dat het opslaan van de wijzigingen na gebruik van een kant-en-klaar scenario de eerder geconfigureerde regels voor deze verzendmethode overschrijft. Totdat de wijzigingen zijn opgeslagen, blij<PERSON> de eerdere regels nog steeds in gebruik."], "Select one of the pre-made and ready to use Flexible Shipping scenarios from our library. Pick the one which fits your needs, adjust it freely and have it all configured in no time!": ["Selecteer e<PERSON> van de kant-en-klare scenario's voor Flexible Shipping uit onze bibliotheek. Kies degene die bij je past, pas hem vrij aan en heb alles in een mum van tijd geconfigureerd!"], "Select a ready-made scenario": ["Selecteer een kant-en-klaar scenario"], "Use scenario": ["Gebruik scenario"], "Read full description →": ["Lees de volledige beschrijving →"], "Rules count in scenario: %1$s": ["Aantal regels in scenario: %1$s"], "All scenarios": ["Alle scenario's"], "searching...": ["aan het zoeken..."], "Value not found": ["<PERSON><PERSON><PERSON> niet gevonden"], "Enter 3 or more characters": ["Voeg 3 of meer tekens in"], "Add rule": ["Regel toevoegen"], "and": ["en"], "When": ["<PERSON><PERSON>"], "Special action": ["Speciale actie"], "Conditions": ["Voorwaarden"], "Costs": ["<PERSON><PERSON>"], "Rules table": ["<PERSON><PERSON> met re<PERSON>s"], "Delete": ["Verwijderen"]}}, "comment": {"reference": "assets/js/rules-settings.js"}}