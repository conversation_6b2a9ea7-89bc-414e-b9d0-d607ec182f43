/*! elementor - v3.31.0 - 09-09-2025 */
(()=>{var e={3068:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRandomStyleId=function getRandomStyleId(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};do{t="e-".concat(e.id,"-").concat(elementorCommon.helpers.getUniqueId())}while(r.hasOwnProperty(t));return t}},6479:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.regenerateLocalStyleIds=function regenerateLocalStyleIds(e){!function updateElementsStyleIdsInsideOut(e){null==e||e.reverse().forEach(updateStyleId)}((0,l.getElementChildren)(e).filter(function(e){var t;return Object.keys(null!==(t=e.model.get("styles"))&&void 0!==t?t:{}).length>0}))};var o=n(r(85707)),i=n(r(18821)),l=r(82823),a=r(3068);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function updateStyleId(e){var t,r,n=e.model.get("styles"),o=null!==(t=null===(r=e.settings)||void 0===r?void 0:r.toJSON())&&void 0!==t?t:{},l=Object.entries(o).filter(function(e){return function isClassesProp(e){return e.$$type&&"classes"===e.$$type&&Array.isArray(e.value)&&e.value.length>0}((0,i.default)(e,2)[1])}),s={},u={};Object.entries(n).forEach(function(t){var r=(0,i.default)(t,2),n=r[0],o=r[1],l=(0,a.getRandomStyleId)(e,s);s[l]=structuredClone(_objectSpread(_objectSpread({},o),{},{id:l})),u[n]=l});var c=l.map(function(e){var t=(0,i.default)(e,2),r=t[0],n=t[1];return[r,_objectSpread(_objectSpread({},n),{},{value:n.value.map(function(e){var t;return null!==(t=u[e])&&void 0!==t?t:e})})]},{});$e.internal("document/elements/set-settings",{container:e,settings:Object.fromEntries(c)}),e.model.set("styles",s)}},6559:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DuplicateElement",{enumerable:!0,get:function get(){return n.DuplicateElement}}),Object.defineProperty(t,"ImportElement",{enumerable:!0,get:function get(){return i.ImportElement}}),Object.defineProperty(t,"PasteElement",{enumerable:!0,get:function get(){return o.PasteElement}});var n=r(48534),o=r(13248),i=r(26772)},7470:(e,t,r)=>{"use strict";var n=r(75206);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},10906:(e,t,r)=>{var n=r(91819),o=r(20365),i=r(37744),l=r(78687);e.exports=function _toConsumableArray(e){return n(e)||o(e)||i(e)||l()},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,r)=>{var n=r(10564).default;e.exports=function toPrimitive(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},12470:e=>{"use strict";e.exports=wp.i18n},13248:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.PasteElement=void 0;var o=n(r(39805)),i=n(r(40989)),l=n(r(15118)),a=n(r(29402)),s=n(r(87861)),u=r(6479);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.PasteElement=function(e){function PasteElement(){return(0,o.default)(this,PasteElement),function _callSuper(e,t,r){return t=(0,a.default)(t),(0,l.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,a.default)(e).constructor):t.apply(e,r))}(this,PasteElement,arguments)}return(0,s.default)(PasteElement,e),(0,i.default)(PasteElement,[{key:"getCommand",value:function getCommand(){return"document/elements/paste"}},{key:"getId",value:function getId(){return"regenerate-local-style-ids--document/elements/paste"}},{key:"apply",value:function apply(e,t){(Array.isArray(t)?t:[t]).forEach(u.regenerateLocalStyleIds)}}])}($e.modules.hookData.After)},15118:(e,t,r)=>{var n=r(10564).default,o=r(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},18791:(e,t,r)=>{"use strict";var n=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(41594));var o=_interopRequireWildcard(r(75206)),i=r(7470);function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,o=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var i,l,a={__proto__:null,default:e};if(null===e||"object"!=n(e)&&"function"!=typeof e)return a;if(i=t?o:r){if(i.has(e))return i.get(e);i.set(e,a)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((l=(i=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(l.get||l.set)?i(a,s,l):a[s]=e[s]);return a})(e,t)}t.default={render:function render(e,t){var r;try{var n=(0,i.createRoot)(t);n.render(e),r=function unmountFunction(){n.unmount()}}catch(n){o.render(e,t),r=function unmountFunction(){o.unmountComponentAtNode(t)}}return{unmount:r}}}},18821:(e,t,r)=>{var n=r(70569),o=r(65474),i=r(37744),l=r(11018);e.exports=function _slicedToArray(e,t){return n(e)||o(e,t)||i(e,t)||l()},e.exports.__esModule=!0,e.exports.default=e.exports},20365:e=>{e.exports=function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},23837:(e,t,r)=>{"use strict";var n=r(12470).__,o=r(12470).sprintf,i=r(96784),l=i(r(10906)),a=i(r(85707)),s=i(r(53537)),u=r(75504);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,a.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var c=elementor.modules.elements.views.BaseElement,d=c.extend({template:Marionette.TemplateCache.get("#tmpl-elementor-e-div-block-content"),emptyView:s.default,tagName:function tagName(){if(this.haveLink())return"a";var e=this.model.getSetting("tag");return(null==e?void 0:e.value)||e||"div"},getChildViewContainer:function getChildViewContainer(){return this.childViewContainer="",Marionette.CompositeView.prototype.getChildViewContainer.apply(this,arguments)},className:function className(){return"".concat(c.prototype.className.apply(this)," e-con ").concat(this.getClassString())},ui:function ui(){var ui=c.prototype.ui.apply(this,arguments);return ui.percentsTooltip="> .elementor-element-overlay .elementor-column-percents-tooltip",ui},attributes:function attributes(){var e,t,r=c.prototype.attributes.apply(this),n={},o=this.model.getSetting("_cssid"),i=null!==(e=null===(t=this.model.getSetting("attributes"))||void 0===t?void 0:t.value)&&void 0!==e?e:[];o&&(n.id=o.value);var l=this.getHref();return l&&(n.href=l),i.forEach(function(e){var t,r,o=null===(t=e.value)||void 0===t||null===(t=t.key)||void 0===t?void 0:t.value,i=null===(r=e.value)||void 0===r||null===(r=r.value)||void 0===r?void 0:r.value;o&&i&&(n[o]=i)}),_objectSpread(_objectSpread({},r),n)},attachElContent:function attachElContent(){c.prototype.attachElContent.apply(this,arguments);var e=jQuery("<div>",{class:"elementor-column-percents-tooltip","data-side":elementorCommon.config.isRTL?"right":"left"});this.$el.children(".elementor-element-overlay").append(e)},getPercentSize:function getPercentSize(e){return e||(e=this.el.getBoundingClientRect().width),+(e/this.$el.parent().width()*100).toFixed(3)},getPercentsForDisplay:function getPercentsForDisplay(){return(+this.model.getSetting("width")||this.getPercentSize()).toFixed(1)+"%"},renderOnChange:function renderOnChange(e){var t=this,r=e.changedAttributes();if(setTimeout(function(){t.updateHandlesPosition()}),r)if(c.prototype.renderOnChange.apply(this,e),r.attributes){for(var n,o=["id","class","href"],i=this.$el[0].attributes,l=i.length-1;l>=0;l--){var a=i[l].name;o.includes(a)||this.$el.removeAttr(a)}((null===(n=this.model.getSetting("attributes"))||void 0===n?void 0:n.value)||[]).forEach(function(e){var r,n,o=null==e||null===(r=e.value)||void 0===r||null===(r=r.key)||void 0===r?void 0:r.value,i=null==e||null===(n=e.value)||void 0===n||null===(n=n.value)||void 0===n?void 0:n.value;o&&i&&t.$el.attr(o,i)})}else r.classes?this.$el.attr("class",this.className()):r._cssid?r._cssid.value?this.$el.attr("id",r._cssid.value):this.$el.removeAttr("id"):(this.$el.addClass(this.getClasses()),this.isTagChanged(r)&&this.rerenderEntireView())},isTagChanged:function isTagChanged(e){return(void 0!==(null==e?void 0:e.tag)||void 0!==(null==e?void 0:e.link))&&this._parent&&this.tagName()!==this.el.tagName},rerenderEntireView:function rerenderEntireView(){var e=this._parent;this._parent.removeChildView(this),e.addChild(this.model,d,this._index)},onRender:function onRender(){var e=this;c.prototype.onRender.apply(this,arguments),setTimeout(function(){e.droppableInitialize(),e.updateHandlesPosition()})},haveLink:function haveLink(){var e;return!(null===(e=this.model.getSetting("link"))||void 0===e||null===(e=e.value)||void 0===e||null===(e=e.destination)||void 0===e||!e.value)},getHref:function getHref(){if(this.haveLink()){var e=this.model.getSetting("link").value.destination,t=e.$$type,r=e.value;return("number"===t?elementor.config.home_url+"/?p=":"")+r}},droppableInitialize:function droppableInitialize(){this.$el.html5Droppable(this.getDroppableOptions())},getContextMenuGroups:function getContextMenuGroups(){var e,t=this,r=c.prototype.getContextMenuGroups.apply(this,arguments),o=r.indexOf(_.findWhere(r,{name:"clipboard"}));return r.splice(o+1,0,{name:"save",actions:[{name:"save",title:n("Save as a template","elementor"),shortcut:null!==(e=elementorCommon.config.experimentalFeatures)&&void 0!==e&&e["cloud-library"]?'<span class="elementor-context-menu-list__item__shortcut__new-badge">'.concat(n("New","elementor"),"</span>"):"",callback:this.saveAsTemplate.bind(this),isEnabled:function isEnabled(){return!t.getContainer().isLocked()}}]}),r},saveAsTemplate:function saveAsTemplate(){$e.route("library/save-template",{model:this.model})},isDroppingAllowed:function isDroppingAllowed(){return!0},behaviors:function behaviors(){var behaviors=c.prototype.behaviors.apply(this,arguments);return _.extend(behaviors,{Sortable:{behaviorClass:r(83139),elChildType:"widget"}}),elementor.hooks.applyFilters("elements/e-div-block/behaviors",behaviors,this)},getSortableOptions:function getSortableOptions(){return{preventInit:!0}},getDroppableOptions:function getDroppableOptions(){var e=this;return{axis:null,items:"> .elementor-element, > .elementor-empty-view .elementor-first-add",groups:["elementor-element"],horizontalThreshold:0,isDroppingAllowed:this.isDroppingAllowed.bind(this),currentElementClass:"elementor-html5dnd-current-element",placeholderClass:"elementor-sortable-placeholder elementor-widget-placeholder",hasDraggingOnChildClass:"e-dragging-over",getDropContainer:function getDropContainer(){return e.getContainer()},onDropping:function onDropping(t,r){r.stopPropagation(),elementor.getPreviewView().onPanelElementDragEnd();var n=elementor.channels.editor.request("element:dragged"),o=null==n?void 0:n.getContainer().view.el,i=r.currentTarget.parentElement,l=Array.from((null==i?void 0:i.querySelectorAll(":scope > .elementor-element"))||[]),a=l.indexOf(r.currentTarget);if(e.isPanelElement(n,o))return e.draggingOnBottomOrRightSide(t)&&!e.emptyViewIsCurrentlyBeingDraggedOver()&&a++,void e.onDrop(r,{at:a});e.isParentElement(n.getContainer().id)||(e.emptyViewIsCurrentlyBeingDraggedOver()?e.moveDroppedItem(n,0):e.moveExistingElement(t,n,i,l,a,o))}}},moveExistingElement:function moveExistingElement(e,t,r,n,o,i){var l=n.indexOf(i);if(o!==l){var a=this.getDropIndex(r,e,o,l);this.moveDroppedItem(t,a)}},isPanelElement:function isPanelElement(e,t){return!e||!t},isParentElement:function isParentElement(e){for(var t=this.container;t;){if(t.id===e)return!0;t=t.parent}return!1},getDropIndex:function getDropIndex(e,t,r,n){var o=window.getComputedStyle(e),i=["flex","inline-flex"].includes(o.display),l=i&&["column-reverse","row-reverse"].includes(o.flexDirection),a=i&&["row-reverse","row"].includes(o.flexDirection),s=elementorCommon.config.isRTL;return(a?l!==s:l)===this.draggingOnBottomOrRightSide(t)?-1===n||n>=r-1?r:r>0?r-1:0:0<=n&&n<r?r:r+1},moveDroppedItem:function moveDroppedItem(e,t){elementor.channels.editor.reply("element:dragged",null),$e.run("document/elements/move",{container:e.getContainer(),target:this.getContainer(),options:{at:t}})},getEditButtons:function getEditButtons(){var e=elementor.getElementData(this.model),t={};return $e.components.get("document/elements").utils.allowAddingWidgets()&&(t.add={title:o(n("Add %s","elementor"),e.title),icon:"plus"},t.edit={title:o(n("Edit %s","elementor"),e.title),icon:"handle"}),this.getContainer().isLocked()||(elementor.getPreferences("edit_buttons")&&$e.components.get("document/elements").utils.allowAddingWidgets()&&(t.duplicate={title:o(n("Duplicate %s","elementor"),e.title),icon:"clone"}),t.remove={title:o(n("Delete %s","elementor"),e.title),icon:"close"}),t},draggingOnBottomOrRightSide:function draggingOnBottomOrRightSide(e){return["bottom","right"].includes(e)},emptyViewIsCurrentlyBeingDraggedOver:function emptyViewIsCurrentlyBeingDraggedOver(){return this.$el.find("> .elementor-empty-view > .elementor-first-add.elementor-html5dnd-current-element").length>0},onAddButtonClick:function onAddButtonClick(){if(!this.addSectionView||this.addSectionView.isDestroyed){var e=new elementor.modules.elements.components.AddSectionView({at:this.model.collection.indexOf(this.model)});e.render(),this.$el.before(e.$el),e.$el.hide(),setTimeout(function(){e.$el.slideDown(null,function(){jQuery(this).css("display","")})}),this.addSectionView=e}else this.addSectionView.fadeToDeath()},getClasses:function getClasses(){var e,t,r,n=null===(e=window)||void 0===e||null===(e=e.elementorV2)||void 0===e||null===(e=e.editorCanvas)||void 0===e||null===(e=e.settingsTransformersRegistry)||void 0===e||null===(t=e.get)||void 0===t?void 0:t.call(e,"classes");return n?n((null===(r=this.options)||void 0===r||null===(r=r.model)||void 0===r||null===(r=r.getSetting("classes"))||void 0===r?void 0:r.value)||[]):[]},getClassString:function getClassString(){var e=this.getClasses();return[this.getBaseClass()].concat((0,l.default)(e)).join(" ")},getBaseClass:function getBaseClass(){var e,t,r=elementor.helpers.getAtomicWidgetBaseStyles(null===(e=this.options)||void 0===e?void 0:e.model);return null!==(t=Object.keys(null!=r?r:{})[0])&&void 0!==t?t:""},isOverflowHidden:function isOverflowHidden(){var e=window.getComputedStyle(this.el),t=[e.overflowX,e.overflowY,e.overflow];return t.includes("hidden")||t.includes("auto")},updateHandlesPosition:function updateHandlesPosition(){var e=this.$el.data("element_type");(0,u.getAllElementTypes)().includes(e)&&(this.isOverflowHidden()?this.$el.addClass("e-handles-inside"):this.$el.removeClass("e-handles-inside"))}});e.exports=d},26772:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.ImportElement=void 0;var o=n(r(39805)),i=n(r(40989)),l=n(r(15118)),a=n(r(29402)),s=n(r(87861)),u=r(6479);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.ImportElement=function(e){function ImportElement(){return(0,o.default)(this,ImportElement),function _callSuper(e,t,r){return t=(0,a.default)(t),(0,l.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,a.default)(e).constructor):t.apply(e,r))}(this,ImportElement,arguments)}return(0,s.default)(ImportElement,e),(0,i.default)(ImportElement,[{key:"getCommand",value:function getCommand(){return"document/elements/import"}},{key:"getId",value:function getId(){return"regenerate-local-style-ids--document/elements/import"}},{key:"apply",value:function apply(e,t){(Array.isArray(t)?t:[t]).forEach(u.regenerateLocalStyleIds)}}])}($e.modules.hookData.After)},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},33660:(e,t,r)=>{"use strict";var n=r(96784);t.A=void 0;var o=n(r(39805)),i=n(r(40989)),l=n(r(15118)),a=n(r(29402)),s=n(r(87861)),u=n(r(64836)),c=n(r(40955)),d=n(r(23837));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.A=function(e){function AtomicContainer(){return(0,o.default)(this,AtomicContainer),function _callSuper(e,t,r){return t=(0,a.default)(t),(0,l.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,a.default)(e).constructor):t.apply(e,r))}(this,AtomicContainer,arguments)}return(0,s.default)(AtomicContainer,e),(0,i.default)(AtomicContainer,[{key:"getType",value:function getType(){return"e-div-block"}},{key:"getView",value:function getView(){return d.default}},{key:"getEmptyView",value:function getEmptyView(){return u.default}},{key:"getModel",value:function getModel(){return c.default}}])}(elementor.modules.elements.types.Base)},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var n=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40121:(e,t,r)=>{"use strict";var n=r(96784);t.A=void 0;var o=n(r(39805)),i=n(r(40989)),l=n(r(15118)),a=n(r(29402)),s=n(r(87861)),u=n(r(64836)),c=n(r(40955)),d=n(r(23837));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.A=function(e){function AtomicContainer(){return(0,o.default)(this,AtomicContainer),function _callSuper(e,t,r){return t=(0,a.default)(t),(0,l.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,a.default)(e).constructor):t.apply(e,r))}(this,AtomicContainer,arguments)}return(0,s.default)(AtomicContainer,e),(0,i.default)(AtomicContainer,[{key:"getType",value:function getType(){return"e-flexbox"}},{key:"getView",value:function getView(){return d.default}},{key:"getEmptyView",value:function getEmptyView(){return u.default}},{key:"getModel",value:function getModel(){return c.default}}])}(elementor.modules.elements.types.Base)},40955:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(39805)),i=n(r(40989)),l=n(r(15118)),a=n(r(29402)),s=n(r(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function AtomicContainer(){return(0,o.default)(this,AtomicContainer),function _callSuper(e,t,r){return t=(0,a.default)(t),(0,l.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,a.default)(e).constructor):t.apply(e,r))}(this,AtomicContainer,arguments)}return(0,s.default)(AtomicContainer,e),(0,i.default)(AtomicContainer,[{key:"isValidChild",value:function isValidChild(e){var t=e.get("elType");return"section"!==t&&"column"!==t}}])}(elementor.modules.elements.models.Element)},40989:(e,t,r)=>{var n=r(45498);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},41594:e=>{"use strict";e.exports=React},45498:(e,t,r)=>{var n=r(10564).default,o=r(11327);e.exports=function toPropertyKey(e){var t=o(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},48534:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.DuplicateElement=void 0;var o=n(r(39805)),i=n(r(40989)),l=n(r(15118)),a=n(r(29402)),s=n(r(87861)),u=r(6479);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.DuplicateElement=function(e){function DuplicateElement(){return(0,o.default)(this,DuplicateElement),function _callSuper(e,t,r){return t=(0,a.default)(t),(0,l.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,a.default)(e).constructor):t.apply(e,r))}(this,DuplicateElement,arguments)}return(0,s.default)(DuplicateElement,e),(0,i.default)(DuplicateElement,[{key:"getCommand",value:function getCommand(){return"document/elements/duplicate"}},{key:"getId",value:function getId(){return"regenerate-local-style-ids--document/elements/duplicate"}},{key:"apply",value:function apply(e,t){(Array.isArray(t)?t:[t]).forEach(u.regenerateLocalStyleIds)}}])}($e.modules.hookData.After)},53537:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(41594)),i=n(r(39805)),l=n(r(40989)),a=n(r(15118)),s=n(r(29402)),u=n(r(87861)),c=n(r(85707)),d=n(r(18791)),p=n(r(64836));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function DivBlockEmptyView(){var e;(0,i.default)(this,DivBlockEmptyView);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e=function _callSuper(e,t,r){return t=(0,s.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,s.default)(e).constructor):t.apply(e,r))}(this,DivBlockEmptyView,[].concat(r)),(0,c.default)(e,"template","<div></div>"),(0,c.default)(e,"className","elementor-empty-view"),e}return(0,u.default)(DivBlockEmptyView,e),(0,l.default)(DivBlockEmptyView,[{key:"renderReactDefaultElement",value:function renderReactDefaultElement(e){var t=d.default.render(o.default.createElement(p.default,{container:e}),this.el).unmount;this.unmount=t}},{key:"onRender",value:function onRender(){this.$el.addClass(this.className),this.renderReactDefaultElement()}},{key:"onDestroy",value:function onDestroy(){this.unmount()}}])}(Marionette.ItemView)},64836:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function EmptyComponent(){return o.default.createElement("div",{className:"elementor-first-add"},o.default.createElement("div",{className:"elementor-icon eicon-plus",onClick:function onClick(){return $e.route("panel/elements/categories")}}))};var o=n(r(41594))},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,l,a=[],s=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(a.push(n.value),a.length!==t);s=!0);}catch(e){u=!0,o=e}finally{try{if(!s&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(u)throw o}}return a}},e.exports.__esModule=!0,e.exports.default=e.exports},66735:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(r(39805)),l=n(r(40989)),a=n(r(15118)),s=n(r(29402)),u=n(r(87861)),c=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var i,l,a={__proto__:null,default:e};if(null===e||"object"!=o(e)&&"function"!=typeof e)return a;if(i=t?n:r){if(i.has(e))return i.get(e);i.set(e,a)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((l=(i=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(l.get||l.set)?i(a,s,l):a[s]=e[s]);return a}(e,t)}(r(6559));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function Component(){return(0,i.default)(this,Component),function _callSuper(e,t,r){return t=(0,s.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,s.default)(e).constructor):t.apply(e,r))}(this,Component,arguments)}return(0,u.default)(Component,e),(0,l.default)(Component,[{key:"getNamespace",value:function getNamespace(){return"document/atomic-widgets"}},{key:"defaultHooks",value:function defaultHooks(){return this.importHooks(c)}}])}($e.modules.ComponentBase)},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},75206:e=>{"use strict";e.exports=ReactDOM},75504:e=>{"use strict";var t={SECTION:"section",CONTAINER:"container",DIV_BLOCK:"e-div-block",FLEXBOX:"e-flexbox"};e.exports={ELEMENT_TYPES:t,getAllElementTypes:function getAllElementTypes(){return Object.values(t)}}},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},78687:e=>{e.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},82823:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.getElementChildren=function getElementChildren(e){var t,r,n,i=window.elementor.getContainer(e.id),l=null!==(t=(null!==(r=null===(n=i.model)||void 0===n||null===(n=n.get("elements"))||void 0===n?void 0:n.models)&&void 0!==r?r:[]).flatMap(function(e){return getElementChildren(e)}))&&void 0!==t?t:[];return[i].concat((0,o.default)(l))};var o=n(r(10906))},83139:(e,t,r)=>{"use strict";var n,o=r(96784)(r(85707));function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}n=Marionette.Behavior.extend({defaults:{elChildType:"widget"},events:{sortstart:"onSortStart",sortreceive:"onSortReceive",sortupdate:"onSortUpdate",sortover:"onSortOver",sortout:"onSortOut"},initialize:function initialize(){this.listenTo(elementor.channels.dataEditMode,"switch",this.onEditModeSwitched).listenTo(this.view.options.model,"request:sort:start",this.startSort).listenTo(this.view.options.model,"request:sort:update",this.updateSort).listenTo(this.view.options.model,"request:sort:receive",this.receiveSort)},onEditModeSwitched:function onEditModeSwitched(e){this.onToggleSortMode("edit"===e)},refresh:function refresh(){this.onEditModeSwitched(elementor.channels.dataEditMode.request("activeMode"))},onRender:function onRender(){var e=this;this.view.collection.on("update",function(){return e.refresh()}),_.defer(function(){return e.refresh()})},onDestroy:function onDestroy(){this.deactivate()},createPlaceholder:function createPlaceholder(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];e.css("display","");var n=e[0],o=n.clientWidth,i=n.clientHeight;r&&e.css("display","none"),jQuery("<div />").css(_objectSpread(_objectSpread({},e.css(["flex-basis","flex-grow","flex-shrink","position"])),{},{width:o,height:i})).addClass(t).insertAfter(e)},getSwappableOptions:function getSwappableOptions(){var e=this,t=this.getChildViewContainer(),r="e-swappable--item-placeholder";return{start:function start(n,o){t.sortable("refreshPositions"),e.createPlaceholder(o.item,r)},stop:function stop(){t.find(".".concat(r)).remove()}}},onToggleSortMode:function onToggleSortMode(e){e?this.activate():this.deactivate()},applySortable:function applySortable(){if(elementor.userCan("design")){var e=this.getChildViewContainer(),t={placeholder:"elementor-sortable-placeholder elementor-"+this.getOption("elChildType")+"-placeholder",cursorAt:{top:20,left:25},helper:this._getSortableHelper.bind(this),cancel:"input, textarea, button, select, option, .elementor-inline-editing, .elementor-tab-title",start:function start(){e.sortable("refreshPositions")}},r=_.extend(t,this.view.getSortableOptions());this.isSwappable()&&(e.addClass("e-swappable"),r=_.extend(r,this.getSwappableOptions())),r.preventInit||e.sortable(r)}},activate:function activate(){this.getChildViewContainer().sortable("instance")?this.getChildViewContainer().sortable("enable"):this.applySortable()},_getSortableHelper:function _getSortableHelper(e,t){var r=this.view.collection.get({cid:t.data("model-cid")});return'<div style="height: 84px; width: 125px;" class="elementor-sortable-helper elementor-sortable-helper-'+r.get("elType")+'"><div class="icon"><i class="'+r.getIcon()+'"></i></div><div class="title-wrapper"><div class="title">'+r.getTitle()+"</div></div></div>"},getChildViewContainer:function getChildViewContainer(){return this.view.getChildViewContainer(this.view)},getSortedElementNewIndex:function getSortedElementNewIndex(e){return Object.values(e.parent().find("> .elementor-element")).indexOf(e[0])},deactivate:function deactivate(){var e=this.getChildViewContainer();e.sortable("instance")&&e.sortable("disable")},isSwappable:function isSwappable(){return!!this.view.getSortableOptions().swappable},startSort:function startSort(e,t){e.stopPropagation();var r=elementor.getContainer(t.item.attr("data-id"));elementor.channels.data.reply("dragging:model",r.model).reply("dragging:view",r.view).reply("dragging:parent:view",this.view).trigger("drag:start",r.model).trigger(r.model.get("elType")+":drag:start")},updateSort:function updateSort(e,t){void 0===t&&(t=e.item.index());var r=elementor.channels.data.request("dragging:view").getContainer();this.moveChild(r,t)||jQuery(e.sender).sortable("cancel")},receiveSort:function receiveSort(e,t,r){if(e.stopPropagation(),this.view.isCollectionFilled())jQuery(t.sender).sortable("cancel");else{var n=elementor.channels.data.request("dragging:model"),o="section"===n.get("elType")&&n.get("isInner"),i="column"===this.view.getElementType()&&this.view.isInner();if(o&&i)jQuery(t.sender).sortable("cancel");else{void 0===r&&(r=t.item.index());var l=elementor.channels.data.request("dragging:view").getContainer();this.moveChild(l,r)||jQuery(t.sender).sortable("cancel")}}},onSortStart:function onSortStart(e,t){if("column"===this.options.elChildType){var r=t.item.data("sortableItem").items,n=0;r.forEach(function(e){if(e.item[0]===t.item[0])return n=e.height,!1}),t.placeholder.height(n)}this.startSort(e,t)},onSortOver:function onSortOver(e){e.stopPropagation();var t=elementor.channels.data.request("dragging:model");jQuery(e.target).addClass("elementor-draggable-over").attr({"data-dragged-element":t.get("elType"),"data-dragged-is-inner":t.get("isInner")}),this.$el.addClass("elementor-dragging-on-child")},onSortOut:function onSortOut(e){e.stopPropagation(),jQuery(e.target).removeClass("elementor-draggable-over").removeAttr("data-dragged-element data-dragged-is-inner"),this.$el.removeClass("elementor-dragging-on-child")},onSortReceive:function onSortReceive(e,t){this.receiveSort(e,t,this.getSortedElementNewIndex(t.item))},onSortUpdate:function onSortUpdate(e,t){e.stopPropagation(),this.getChildViewContainer()[0]===t.item.parent()[0]&&this.updateSort(t,this.getSortedElementNewIndex(t.item))},onAddChild:function onAddChild(e){e.$el.attr("data-model-cid",e.model.cid)},moveChild:function moveChild(e,t){return $e.run("document/elements/move",{container:e,target:this.view.getContainer(),options:{at:t}})}}),e.exports=n},85707:(e,t,r)=>{var n=r(45498);e.exports=function _defineProperty(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},87861:(e,t,r)=>{var n=r(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},91270:e=>{function _setPrototypeOf(t,r){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,r)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},91819:(e,t,r)=>{var n=r(78113);e.exports=function _arrayWithoutHoles(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,__webpack_require__),o.exports}(()=>{"use strict";var e=__webpack_require__(96784),t=e(__webpack_require__(39805)),r=e(__webpack_require__(40989)),n=e(__webpack_require__(15118)),o=e(__webpack_require__(29402)),i=e(__webpack_require__(87861)),l=e(__webpack_require__(66735));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}new(function(e){function Module(){return(0,t.default)(this,Module),function _callSuper(e,t,r){return t=(0,o.default)(t),(0,n.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,o.default)(e).constructor):t.apply(e,r))}(this,Module,arguments)}return(0,i.default)(Module,e),(0,r.default)(Module,[{key:"onInit",value:function onInit(){$e.components.register(new l.default),this.registerAtomicWidgetTypes()}},{key:"registerAtomicWidgetTypes",value:function registerAtomicWidgetTypes(){this.registerAtomicDivBlockType()}},{key:"registerAtomicDivBlockType",value:function registerAtomicDivBlockType(){var e=__webpack_require__(33660).A,t=__webpack_require__(40121).A;elementor.elementsManager.registerElementType(new e),elementor.elementsManager.registerElementType(new t)}}])}(elementorModules.editor.utils.Module))})()})();