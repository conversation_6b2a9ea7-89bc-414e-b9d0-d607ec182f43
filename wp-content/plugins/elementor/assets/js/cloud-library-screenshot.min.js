/*! elementor - v3.31.0 - 09-09-2025 */
(()=>{var e={10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},10906:(e,t,r)=>{var o=r(91819),n=r(20365),s=r(37744),i=r(78687);e.exports=function _toConsumableArray(e){return o(e)||n(e)||s(e)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,r)=>{var o=r(10564).default;e.exports=function toPrimitive(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},14718:(e,t,r)=>{var o=r(29402);e.exports=function _superPropBase(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=o(e)););return e},e.exports.__esModule=!0,e.exports.default=e.exports},15118:(e,t,r)=>{var o=r(10564).default,n=r(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},20365:e=>{e.exports=function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var o=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return o(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,r)=>{var o=r(45498);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}e.exports=function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},41621:(e,t,r)=>{var o=r(14718);function _get(){return e.exports=_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=o(e,t);if(n){var s=Object.getOwnPropertyDescriptor(n,t);return s.get?s.get.call(arguments.length<3?e:r):s.value}},e.exports.__esModule=!0,e.exports.default=e.exports,_get.apply(null,arguments)}e.exports=_get,e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,r)=>{var o=r(10564).default,n=r(11327);e.exports=function toPropertyKey(e){var t=n(e,"string");return"symbol"==o(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},64537:e=>{e.exports=function _readOnlyError(e){throw new TypeError('"'+e+'" is read-only')},e.exports.__esModule=!0,e.exports.default=e.exports},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o},e.exports.__esModule=!0,e.exports.default=e.exports},78687:e=>{e.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},85707:(e,t,r)=>{var o=r(45498);e.exports=function _defineProperty(e,t,r){return(t=o(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},87861:(e,t,r)=>{var o=r(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},91270:e=>{function _setPrototypeOf(t,r){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,r)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},91819:(e,t,r)=>{var o=r(78113);e.exports=function _arrayWithoutHoles(e){if(Array.isArray(e))return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(96784),t=(e(__webpack_require__(64537)),e(__webpack_require__(10906))),r=e(__webpack_require__(85707)),o=e(__webpack_require__(39805)),n=e(__webpack_require__(40989)),s=e(__webpack_require__(15118)),i=e(__webpack_require__(29402)),a=e(__webpack_require__(41621)),c=e(__webpack_require__(87861));function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var u=function(e){function Screenshot(){return(0,o.default)(this,Screenshot),function _callSuper(e,t,r){return t=(0,i.default)(t),(0,s.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,i.default)(e).constructor):t.apply(e,r))}(this,Screenshot,arguments)}return(0,c.default)(Screenshot,e),(0,n.default)(Screenshot,[{key:"getDefaultSettings",value:function getDefaultSettings(){var e,t;return function _objectSpread(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(o),!0).forEach(function(t){(0,r.default)(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}({empty_content_headline:"Empty Content.",crop:{width:(null===(e=ElementorScreenshotConfig)||void 0===e||null===(e=e.crop)||void 0===e?void 0:e.width)||1200,height:(null===(t=ElementorScreenshotConfig)||void 0===t||null===(t=t.crop)||void 0===t?void 0:t.height)||1500},excluded_external_css_urls:["https://kit-pro.fontawesome.com"],external_images_urls:["https://i.ytimg.com"],timeout:15e3,render_timeout:5e3,timerLabel:null,timer_label:"".concat(ElementorScreenshotConfig.post_id," - timer"),image_placeholder:"data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACwAAAAAAQABAAACAkQBADs=",isDebug:elementorCommonConfig.isElementorDebug,isDebugSvg:!1},ElementorScreenshotConfig)}},{key:"getDefaultElements",value:function getDefaultElements(){var e=jQuery(ElementorScreenshotConfig.selector),t=e.find(".elementor-section-wrap > .elementor-section, .elementor > .elementor-section");return{$elementor:e,$sections:t,$firstSection:t.first(),$notElementorElements:elementorCommon.elements.$body.find("> *:not(style, link)").not(e),$head:jQuery("head")}}},{key:"onInit",value:function onInit(){return function _superPropGet(e,t,r,o){var n=(0,a.default)((0,i.default)(1&o?e.prototype:e),t,r);return 2&o&&"function"==typeof n?function(e){return n.apply(r,e)}:n}(Screenshot,"onInit",this,3)([]),this.log("Screenshot init","time"),this.timeoutTimer=setTimeout(this.screenshotFailed.bind(this),this.getSettings("timeout")),this.captureScreenshot()}},{key:"captureScreenshot",value:function captureScreenshot(){return this.elements.$elementor.length||this.getSettings("kit_id")||(elementorCommon.helpers.consoleWarn("Screenshots: The content of this page is empty, the module will create a fake conent just for this screenshot."),this.createFakeContent()),this.removeUnnecessaryElements(),this.handleIFrames(),this.removeFirstSectionMargin(),this.handleLinks(),this.loadExternalCss(),this.loadExternalImages(),Promise.resolve().then(this.createImage.bind(this)).then(this.createImageElement.bind(this)).then(this.cropCanvas.bind(this)).then(this.save.bind(this)).then(this.screenshotSucceed.bind(this)).catch(this.screenshotFailed.bind(this))}},{key:"createFakeContent",value:function createFakeContent(){this.elements.$elementor=jQuery("<div>").css({height:this.getSettings("crop.height"),width:this.getSettings("crop.width"),display:"flex",alignItems:"center",justifyContent:"center"}),this.elements.$elementor.append(jQuery("<h1>").css({fontSize:"85px"}).html(this.getSettings("empty_content_headline"))),document.body.prepend(this.elements.$elementor)}},{key:"loadExternalCss",value:function loadExternalCss(){var e=this,r=[this.getSettings("home_url")].concat((0,t.default)(this.getSettings("excluded_external_css_urls"))).map(function(e){return'[href^="'.concat(e,'"]')}).join(", ");jQuery("link").not(r).each(function(t,r){var o=jQuery(r),n=o.clone();n.attr("href",e.getScreenshotProxyUrl(o.attr("href"))),e.elements.$head.append(n),o.remove()})}},{key:"loadExternalImages",value:function loadExternalImages(){var e=this,t=this.getSettings("external_images_urls").map(function(e){return'img[src^="'.concat(e,'"]')}).join(", ");jQuery(t).each(function(t,r){var o=jQuery(r);o.attr("src",e.getScreenshotProxyUrl(o.attr("src")))})}},{key:"handleIFrames",value:function handleIFrames(){this.elements.$elementor.find("iframe").each(function(e,t){var r=jQuery(t),o=jQuery("<div />",{css:{background:"gray",width:r.width(),height:r.height()}});r.before(o),r.remove()})}},{key:"removeUnnecessaryElements",value:function removeUnnecessaryElements(){var e=this,t=0;this.getSettings("kit_id")||(this.elements.$sections.filter(function(r,o){var n=!1;return t>=e.getSettings("crop.height")&&(n=!0),t+=jQuery(o).outerHeight(),n}).each(function(e,t){t.remove()}),this.elements.$notElementorElements.remove())}},{key:"handleLinks",value:function handleLinks(){elementorCommon.elements.$body.find("a").attr("href","/")}},{key:"removeFirstSectionMargin",value:function removeFirstSectionMargin(){this.elements.$firstSection.css({marginTop:0})}},{key:"createImage",value:function createImage(){var e=this,t=new Promise(function(e){window.addEventListener("load",function(){e()})}),r=new Promise(function(t){setTimeout(function(){t()},e.getSettings("render_timeout"))});return Promise.race([t,r]).then(function(){return e.log("Start creating screenshot."),e.getSettings("isDebugSvg")?(domtoimage.toSvg(document.body,{imagePlaceholder:e.getSettings("image_placeholder")}).then(function(t){return e.download(t)}),Promise.reject("Debug SVG.")):/^((?!chrome|android).)*safari/i.test(window.userAgent)?(e.log('Creating screenshot with "html2canvas"'),html2canvas(document.body).then(function(e){return e.toDataURL("image/png")})):(e.log('Creating screenshot with "dom-to-image"'),domtoimage.toPng(document.body,{imagePlaceholder:e.getSettings("image_placeholder")}).catch(function(){return html2canvas(document.body).then(function(e){return e.toDataURL("image/png")})}))})}},{key:"download",value:function download(e){var t=jQuery("<a/>",{href:e,download:"debugSvg.svg",html:"Download SVG"});elementorCommon.elements.$body.append(t),t.trigger("click")}},{key:"createImageElement",value:function createImageElement(e){var t=new Image;return t.src=e,new Promise(function(e){t.onload=function(){return e(t)}})}},{key:"cropCanvas",value:function cropCanvas(e){var t=this.getSettings("crop.width"),r=this.getSettings("crop.height"),cropCanvas=document.createElement("canvas"),o=cropCanvas.getContext("2d"),n=t/e.width;return cropCanvas.width=t,cropCanvas.height=r>e.height?e.height:r,o.drawImage(e,0,0,e.width,e.height,0,0,e.width*n,e.height*n),Promise.resolve(cropCanvas)}},{key:"save",value:function save(e){var t=this,o=this.getSaveAction(),n=o.key,s=o.action,i=(0,r.default)((0,r.default)({},n,this.getSettings(n)),"screenshot",e.toDataURL("image/png"));return new Promise(function(e,r){if("kit_id"===n)return e(i.screenshot);elementorCommon.ajax.addRequest(s,{data:i,success:function success(r){t.log("Screenshot created: ".concat(encodeURI(r))),e(r)},error:function error(){t.log("Failed to create screenshot."),r()}})})}},{key:"markAsFailed",value:function markAsFailed(e){var t=this;return new Promise(function(r,o){var n=t.getSettings("template_id"),s=t.getSettings("post_id");if(t.getSettings("kit_id"))r();else{var i=n?"template_screenshot_failed":"screenshot_failed",a=n?{template_id:n,error:e.message||e.toString()}:{post_id:s};elementorCommon.ajax.addRequest(i,{data:a,success:function success(){t.log("Marked as failed."),r()},error:function error(){t.log("Failed to mark this screenshot as failed."),o()}})}})}},{key:"getScreenshotProxyUrl",value:function getScreenshotProxyUrl(e){return"".concat(this.getSettings("home_url"),"?screenshot_proxy&nonce=").concat(this.getSettings("nonce"),"&href=").concat(e)}},{key:"screenshotSucceed",value:function screenshotSucceed(e){this.screenshotDone(!0,e)}},{key:"screenshotFailed",value:function screenshotFailed(e){var t=this;this.log(e,null),this.markAsFailed(e).then(function(){return t.screenshotDone(!1)})}},{key:"screenshotDone",value:function screenshotDone(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;clearTimeout(this.timeoutTimer),this.timeoutTimer=null;var r=this.getSaveAction(),o=r.message,n=r.key;window.parent.postMessage({name:o,success:e,id:this.getSettings(n),imageUrl:t},"*"),this.log("Screenshot ".concat(e?"Succeed":"Failed","."),"timeEnd")}},{key:"log",value:function log(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"timeLog";this.getSettings("isDebug")&&(console.log("string"==typeof e?"".concat(this.getSettings("post_id")," - ").concat(e):e),t&&console[t](this.getSettings("timer_label")))}},{key:"getSaveAction",value:function getSaveAction(){var e=this.getSettings();return e.kit_id?{message:"kit-screenshot-done",action:"update_kit_preview",key:"kit_id"}:e.template_id?{message:"library/capture-screenshot-done",action:"save_template_screenshot",key:"template_id"}:{message:"capture-screenshot-done",action:"screenshot_save",key:"post_id"}}}])}(elementorModules.ViewModule);jQuery(function(){new u})})()})();