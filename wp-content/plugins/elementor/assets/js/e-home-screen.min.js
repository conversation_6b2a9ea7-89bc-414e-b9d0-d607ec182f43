/*! elementor - v3.31.0 - 09-09-2025 */
(()=>{var e={1427:(e,t,r)=>{"use strict";var a=r(96784),n=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=a(r(78304)),o=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var l,o,i={__proto__:null,default:e};if(null===e||"object"!=n(e)&&"function"!=typeof e)return i;if(l=t?a:r){if(l.has(e))return l.get(e);l.set(e,i)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((o=(l=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(o.get||o.set)?l(i,u,o):i[u]=e[u]);return i}(e,t)}(r(41594)),i=r(86956);t.default=function SideBarCheckIcon(e){return o.createElement(i.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},e),o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.09013 3.69078C10.273 3.2008 11.5409 2.94861 12.8213 2.94861C14.1017 2.94861 15.3695 3.2008 16.5525 3.69078C17.7354 4.18077 18.8102 4.89895 19.7156 5.80432C20.621 6.70969 21.3391 7.78452 21.8291 8.96744C22.3191 10.1504 22.5713 11.4182 22.5713 12.6986C22.5713 13.979 22.3191 15.2468 21.8291 16.4298C21.3391 17.6127 20.621 18.6875 19.7156 19.5929C18.8102 20.4983 17.7354 21.2165 16.5525 21.7064C15.3695 22.1964 14.1017 22.4486 12.8213 22.4486C11.5409 22.4486 10.2731 22.1964 9.09013 21.7064C7.9072 21.2165 6.83237 20.4983 5.927 19.5929C5.02163 18.6875 4.30345 17.6127 3.81346 16.4298C3.32348 15.2468 3.07129 13.979 3.07129 12.6986C3.07129 11.4182 3.32348 10.1504 3.81346 8.96744C4.30345 7.78452 5.02163 6.70969 5.927 5.80432C6.83237 4.89895 7.9072 4.18077 9.09013 3.69078ZM12.8213 4.44861C11.7379 4.44861 10.6651 4.662 9.66415 5.0766C8.66321 5.4912 7.75374 6.09889 6.98766 6.86498C6.22157 7.63106 5.61388 8.54053 5.19928 9.54147C4.78468 10.5424 4.57129 11.6152 4.57129 12.6986C4.57129 13.782 4.78468 14.8548 5.19928 15.8557C5.61388 16.8567 6.22157 17.7662 6.98766 18.5322C7.75374 19.2983 8.66322 19.906 9.66415 20.3206C10.6651 20.7352 11.7379 20.9486 12.8213 20.9486C13.9047 20.9486 14.9775 20.7352 15.9784 20.3206C16.9794 19.906 17.8888 19.2983 18.6549 18.5322C19.421 17.7662 20.0287 16.8567 20.4433 15.8557C20.8579 14.8548 21.0713 13.782 21.0713 12.6986C21.0713 11.6152 20.8579 10.5424 20.4433 9.54147C20.0287 8.54053 19.421 7.63106 18.6549 6.86498C17.8888 6.09889 16.9794 5.4912 15.9784 5.0766C14.9775 4.662 13.9047 4.44861 12.8213 4.44861Z",fill:"#93003F"}),o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.3213 9.69424C17.6142 9.98713 17.6142 10.462 17.3213 10.7549L12.3732 15.703C12.0803 15.9959 11.6054 15.9959 11.3125 15.703L8.83851 13.2289C8.54562 12.936 8.54562 12.4612 8.83851 12.1683C9.1314 11.8754 9.60628 11.8754 9.89917 12.1683L11.8429 14.112L16.2606 9.69424C16.5535 9.40135 17.0284 9.40135 17.3213 9.69424Z",fill:"#93003F"}))}},1726:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=r(86956),i=n(r(90038)),u=n(r(34874)),d=n(r(28421)),s=n(r(1427)),c=n(r(39957)),f=function SidebarDefault(e){var t=e.header,r=e.cta,a=e.repeater;return l.default.createElement(o.Paper,{elevation:0,sx:{p:3}},l.default.createElement(o.Stack,{gap:1.5,alignItems:"center",textAlign:"center",sx:{pb:4}},l.default.createElement(o.Box,{component:"img",src:t.image}),l.default.createElement(o.Box,null,l.default.createElement(o.Typography,{variant:"h6"},t.title),l.default.createElement(o.Typography,{variant:"body2",color:"text.secondary"},t.description)),l.default.createElement(i.default,{variant:"contained",size:"medium",color:"promotion",href:r.url,startIcon:l.default.createElement(o.Box,{component:"img",src:r.image,sx:{width:"16px"}}),target:"_blank",sx:{maxWidth:"fit-content"}},r.label)),l.default.createElement(u.default,{sx:{p:0}},a.map(function(e,t){return l.default.createElement(d.default,{key:t,sx:{p:0,gap:1}},l.default.createElement(s.default,null),l.default.createElement(c.default,{primaryTypographyProps:{variant:"body2"},primary:e.title}))})))};t.default=f;f.propTypes={header:a.object.isRequired,cta:a.object.isRequired,repeater:a.array}},2979:e=>{"use strict";e.exports=elementorV2.ui.CardActions},3767:e=>{"use strict";e.exports=elementorV2.ui.TextField},4176:e=>{"use strict";e.exports=elementorV2.ui.DialogTitle},4671:e=>{"use strict";e.exports=elementorV2.ui.Divider},5986:e=>{"use strict";e.exports=elementorV2.ui.Dialog},7470:(e,t,r)=>{"use strict";var a=r(75206);t.createRoot=a.createRoot,t.hydrateRoot=a.hydrateRoot},9301:e=>{"use strict";e.exports=elementorV2.ui.ListItemButton},10442:(e,t,r)=>{"use strict";var a=r(96784),n=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=a(r(78304)),o=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var l,o,i={__proto__:null,default:e};if(null===e||"object"!=n(e)&&"function"!=typeof e)return i;if(l=t?a:r){if(l.has(e))return l.get(e);l.set(e,i)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((o=(l=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(o.get||o.set)?l(i,u,o):i[u]=e[u]);return i}(e,t)}(r(41594)),i=r(86956);t.default=function YoutubeIcon(e){return o.createElement(i.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},e),o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 5.75C5.20507 5.75 3.75 7.20507 3.75 9V15C3.75 16.7949 5.20507 18.25 7 18.25H17C18.7949 18.25 20.25 16.7949 20.25 15V9C20.25 7.20507 18.7949 5.75 17 5.75H7ZM2.25 9C2.25 6.37665 4.37665 4.25 7 4.25H17C19.6234 4.25 21.75 6.37665 21.75 9V15C21.75 17.6234 19.6234 19.75 17 19.75H7C4.37665 19.75 2.25 17.6234 2.25 15V9ZM9.63048 8.34735C9.86561 8.21422 10.1542 8.21786 10.3859 8.35688L15.3859 11.3569C15.6118 11.4924 15.75 11.7366 15.75 12C15.75 12.2634 15.6118 12.5076 15.3859 12.6431L10.3859 15.6431C10.1542 15.7821 9.86561 15.7858 9.63048 15.6526C9.39534 15.5195 9.25 15.2702 9.25 15V9C9.25 8.7298 9.39534 8.48048 9.63048 8.34735ZM10.75 10.3246V13.6754L13.5423 12L10.75 10.3246Z"}))}},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},10909:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=n(r(64126)),i=n(r(78304)),u=r(86956),d=n(r(34874)),s=n(r(73278)),c=function GetStarted(e){var t=(0,i.default)({},((0,o.default)(e),e));return l.default.createElement(u.Paper,{elevation:0,sx:{p:3,display:"flex",flexDirection:"column",gap:2}},l.default.createElement(u.Box,null,l.default.createElement(u.Typography,{variant:"h6"},t.getStartedData.header.title),l.default.createElement(u.Typography,{variant:"body2",color:"text.secondary"},t.getStartedData.header.description)),l.default.createElement(d.default,{sx:{display:"grid",gridTemplateColumns:{md:"repeat(4, 1fr)",xs:"repeat(2, 1fr)"},columnGap:{md:9,xs:7},rowGap:3}},t.getStartedData.repeater.map(function(e){return l.default.createElement(s.default,{key:e.title,item:e,image:e.image,adminUrl:t.adminUrl})})))};t.default=c;c.propTypes={getStartedData:a.object.isRequired,adminUrl:a.string.isRequired}},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},12427:(e,t,r)=>{"use strict";var a=r(12470).__,n=r(62688),l=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var n,l,i={__proto__:null,default:e};if(null===e||"object"!=o(e)&&"function"!=typeof e)return i;if(n=t?a:r){if(n.has(e))return n.get(e);n.set(e,i)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((l=(n=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(l.get||l.set)?n(i,u,l):i[u]=e[u]);return i}(e,t)}(r(41594)),u=l(r(18821)),d=l(r(44867)),s=l(r(72318)),c=l(r(4176)),f=l(r(54947)),p=l(r(29544)),m=l(r(3767)),_=l(r(69961)),x=l(r(90038)),y=l(r(5986));var h=function CreateNewPageDialog(e){var t=e.url,r=e.isOpen,n=e.closedDialogCallback,l=i.default.useState(!1),o=(0,u.default)(l,2),h=o[0],v=o[1],g=i.default.useState(""),b=(0,u.default)(g,2),E=b[0],C=b[1];(0,i.useEffect)(function(){v(r)},[r]);var w=function handleDialogClose(){v(!1),n()};return i.default.createElement(y.default,{open:h,onClose:w,maxWidth:"xs",width:"xs",fullWidth:!0},i.default.createElement(d.default,null,i.default.createElement(s.default,null,i.default.createElement(c.default,null,a("Name your page","elementor")))),i.default.createElement(f.default,{dividers:!0},i.default.createElement(p.default,{sx:{mb:2}},a("To proceed, please name your first page,","elementor"),i.default.createElement("br",null),a("or rename it later.","elementor")),i.default.createElement(m.default,{onChange:function handleChange(e){var t=new URLSearchParams;t.append("post_data[post_title]",e.target.value),C(t.toString())},fullWidth:!0,placeholder:a("New Page","elementor")})),i.default.createElement(_.default,null,i.default.createElement(x.default,{onClick:w,color:"secondary"},a("Cancel","elementor")),i.default.createElement(x.default,{variant:"contained",href:E?t+"&"+E:t,target:"_blank"},a("Save","elementor"))))};t.default=h;h.propTypes={url:n.string.isRequired,isOpen:n.bool.isRequired,closedDialogCallback:n.func.isRequired}},12470:e=>{"use strict";e.exports=wp.i18n},15925:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=r(86956),i=n(r(29134)),u=n(r(98903)),d=n(r(89186)),s=n(r(42618)),c=n(r(10909)),f=n(r(79602)),p=function HomeScreen(e){var t=e.homeScreenData.hasOwnProperty("sidebar_promotion_variants");return l.default.createElement(o.Box,{sx:{pr:1}},l.default.createElement(o.Container,{disableGutters:!0,maxWidth:"lg",sx:{display:"flex",flexDirection:"column",gap:{xs:1,md:3},pt:{xs:2,md:6},pb:2}},e.homeScreenData.top_with_licences&&l.default.createElement(i.default,{topData:e.homeScreenData.top_with_licences,buttonCtaUrl:e.homeScreenData.button_cta_url}),l.default.createElement(o.Box,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},justifyContent:"space-between",gap:3}},l.default.createElement(o.Stack,{sx:{flex:1,gap:3}},e.homeScreenData.create_with_ai&&l.default.createElement(f.default,{createWithAIData:e.homeScreenData.create_with_ai}),l.default.createElement(c.default,{getStartedData:e.homeScreenData.get_started,adminUrl:e.adminUrl}),l.default.createElement(d.default,{addonsData:e.homeScreenData.add_ons,adminUrl:e.adminUrl})),l.default.createElement(o.Container,{maxWidth:"xs",disableGutters:!0,sx:{width:{sm:"305px"},display:"flex",flexDirection:"column",gap:3}},t&&l.default.createElement(u.default,{sideData:e.homeScreenData.sidebar_promotion_variants}),l.default.createElement(s.default,{externalLinksData:e.homeScreenData.external_links})))))};p.propTypes={homeScreenData:a.object,adminUrl:a.string};t.default=p},18791:(e,t,r)=>{"use strict";var a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(41594));var n=_interopRequireWildcard(r(75206)),l=r(7470);function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var l,o,i={__proto__:null,default:e};if(null===e||"object"!=a(e)&&"function"!=typeof e)return i;if(l=t?n:r){if(l.has(e))return l.get(e);l.set(e,i)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((o=(l=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(o.get||o.set)?l(i,u,o):i[u]=e[u]);return i})(e,t)}t.default={render:function render(e,t){var r;try{var a=(0,l.createRoot)(t);a.render(e),r=function unmountFunction(){a.unmount()}}catch(a){n.render(e,t),r=function unmountFunction(){n.unmountComponentAtNode(t)}}return{unmount:r}}}},18821:(e,t,r)=>{var a=r(70569),n=r(65474),l=r(37744),o=r(11018);e.exports=function _slicedToArray(e,t){return a(e)||n(e,t)||l(e,t)||o()},e.exports.__esModule=!0,e.exports.default=e.exports},28421:e=>{"use strict";e.exports=elementorV2.ui.ListItem},29134:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=n(r(64126)),i=n(r(78304)),u=r(86956),d=n(r(72707)),s=n(r(90038)),c=n(r(10442)),f=function TopSection(e){var t=(0,i.default)({},((0,o.default)(e),e)),r=t.topData,a=t.buttonCtaUrl;if(!r)return null;var n=r.title,f=r.description,p=r.button_cta_text,m=r.button_create_page_title,_=r.youtube_embed_id,x=r.button_watch_url,y=r.button_watch_title,h=null!=p?p:m;return l.default.createElement(u.Paper,{elevation:0,sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},justifyContent:"space-between",py:{xs:3,md:3},px:{xs:3,md:4},gap:{xs:2,sm:3,lg:22}}},l.default.createElement(u.Stack,{gap:3,justifyContent:"center"},l.default.createElement(u.Box,null,l.default.createElement(d.default,{variant:"h6"},n),l.default.createElement(d.default,{variant:"body2",color:"secondary"},f)),l.default.createElement(u.Box,{sx:{display:"flex",gap:1}},l.default.createElement(s.default,{"data-testid":"e-create-button",variant:"contained",size:"small",href:a,target:"_blank"},h),l.default.createElement(s.default,{variant:"outlined",color:"secondary",size:"small",startIcon:l.default.createElement(c.default,null),href:x,target:"_blank"},y))),l.default.createElement(u.Box,{component:"iframe",src:"https://www.youtube.com/embed/".concat(_),title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0,sx:{aspectRatio:"16/9",borderRadius:1,display:"flex",width:"100%",maxWidth:"365px"}}))};f.propTypes={topData:a.object.isRequired,buttonCtaUrl:a.string.isRequired};t.default=f},29544:e=>{"use strict";e.exports=elementorV2.ui.DialogContentText},31047:e=>{"use strict";e.exports=elementorV2.ui.Box},34874:e=>{"use strict";e.exports=elementorV2.ui.List},37744:(e,t,r)=>{var a=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return a(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},38073:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=r(86956),i=n(r(63602)),u=function SidebarBanner(e){var t=e.image,r=e.link;return l.default.createElement(o.Paper,{elevation:0,sx:{overflow:"hidden"}},l.default.createElement(i.default,{target:"_blank",href:r,sx:{lineHeight:0,display:"block",width:"100%",height:"100%",boxShadow:"none","&:focus":{boxShadow:"none"},"&:active":{boxShadow:"none"}}},l.default.createElement(o.Box,{component:"img",src:t,sx:{width:"100%",height:"100%"}})))};t.default=u;u.propTypes={image:a.string.isRequired,link:a.string.isRequired}},39957:e=>{"use strict";e.exports=elementorV2.ui.ListItemText},40362:(e,t,r)=>{"use strict";var a=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,n,l,o){if(o!==a){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},41594:e=>{"use strict";e.exports=React},42408:e=>{"use strict";e.exports=elementorV2.ui.CardMedia},42618:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=n(r(64126)),i=n(r(78304)),u=r(86956),d=n(r(34874)),s=n(r(9301)),c=n(r(39957)),f=n(r(4671)),p=function ExternalLinksSection(e){var t=(0,i.default)({},((0,o.default)(e),e));return l.default.createElement(u.Paper,{elevation:0,sx:{px:3}},l.default.createElement(d.default,null,t.externalLinksData.map(function(e,r){return l.default.createElement(u.Box,{key:e.label},l.default.createElement(s.default,{href:e.url,target:"_blank",sx:{"&:hover":{backgroundColor:"initial"},gap:2,px:0,py:2}},l.default.createElement(u.Box,{component:"img",src:e.image,sx:{width:"38px"}}),l.default.createElement(c.default,{sx:{color:"text.secondary"},primary:e.label})),r<t.externalLinksData.length-1&&l.default.createElement(f.default,null))})))};t.default=p;p.propTypes={externalLinksData:a.array.isRequired}},44867:e=>{"use strict";e.exports=elementorV2.ui.DialogHeader},46168:e=>{"use strict";e.exports=elementorV2.ui.Card},54947:e=>{"use strict";e.exports=elementorV2.ui.DialogContent},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},62688:(e,t,r)=>{e.exports=r(40362)()},63602:e=>{"use strict";e.exports=elementorV2.ui.Link},64126:e=>{e.exports=function _objectDestructuringEmpty(e){if(null==e)throw new TypeError("Cannot destructure "+e)},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,l,o,i=[],u=!0,d=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(a=l.call(r)).done)&&(i.push(a.value),i.length!==t);u=!0);}catch(e){d=!0,n=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(d)throw n}}return i}},e.exports.__esModule=!0,e.exports.default=e.exports},69961:e=>{"use strict";e.exports=elementorV2.ui.DialogActions},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},72318:e=>{"use strict";e.exports=elementorV2.ui.DialogHeaderGroup},72707:e=>{"use strict";e.exports=elementorV2.ui.Typography},73278:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=n(r(18821)),i=n(r(28421)),u=n(r(39957)),d=n(r(63602)),s=n(r(31047)),c=n(r(12427)),f=function GetStartedListItem(e){var t=e.item,r=e.image,a=e.adminUrl,n=t.is_relative_url?a+t.url:t.url,f=l.default.useState(!1),p=(0,o.default)(f,2),m=p[0],_=p[1];return l.default.createElement(i.default,{alignItems:"flex-start",sx:{gap:1,p:0,maxWidth:"150px"}},l.default.createElement(s.default,{component:"img",src:r}),l.default.createElement(s.default,null,l.default.createElement(u.default,{primary:t.title,primaryTypographyProps:{variant:"subtitle1"},sx:{my:0}}),l.default.createElement(d.default,{variant:"body2",color:t.title_small_color?t.title_small_color:"text.tertiary",underline:"hover",href:n,target:"_blank",onClick:function handleLinkClick(e){t.new_page&&(e.preventDefault(),_(!0))}},t.title_small)),t.new_page&&l.default.createElement(c.default,{url:n,isOpen:m,closedDialogCallback:function closedDialogCallback(){return _(!1)}}))};t.default=f;f.propTypes={item:a.shape({title:a.string.isRequired,title_small:a.string.isRequired,url:a.string.isRequired,new_page:a.bool,is_relative_url:a.bool,title_small_color:a.string}).isRequired,adminUrl:a.string.isRequired,image:a.string}},75206:e=>{"use strict";e.exports=ReactDOM},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a},e.exports.__esModule=!0,e.exports.default=e.exports},78304:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(null,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},79602:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784),l=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var n,o,i={__proto__:null,default:e};if(null===e||"object"!=l(e)&&"function"!=typeof e)return i;if(n=t?a:r){if(n.has(e))return n.get(e);n.set(e,i)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((o=(n=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(o.get||o.set)?n(i,u,o):i[u]=e[u]);return i}(e,t)}(r(41594)),i=n(r(18821)),u=n(r(64126)),d=n(r(78304)),s=r(86956),c=n(r(72707)),f=n(r(90038));var p=function CreateWithAIBanner(e){var t=(0,d.default)({},((0,u.default)(e),e)).createWithAIData,r=(0,o.useState)(""),a=(0,i.default)(r,2),n=a[0],l=a[1];if(!t)return null;var p=t.title,m=t.description,_=t.input_placeholder,x=t.button_title,y=t.button_cta_url,h=t.background_image,v=t.utm_source,g=t.utm_medium,b=t.utm_campaign,E=function handleNavigation(){n&&(window.open(function getButtonHref(){if(!n)return y;var e=new URL(y);return e.searchParams.append("prompt",n),e.searchParams.append("utm_source",v),e.searchParams.append("utm_medium",g),e.searchParams.append("utm_campaign",b),e.toString()}(),"_blank"),l(""))};return o.default.createElement(s.Paper,{elevation:0,sx:{display:"flex",flexDirection:"column",py:3,px:4,gap:2,backgroundImage:"url(".concat(h,")"),backgroundSize:"cover",backgroundPosition:"right center",backgroundRepeat:"no-repeat"}},o.default.createElement(s.Stack,{gap:1,justifyContent:"center"},o.default.createElement(c.default,{variant:"h6"},p),o.default.createElement(c.default,{variant:"body2",color:"secondary"},m)),o.default.createElement(s.Box,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},gap:2,mt:1}},o.default.createElement(s.TextField,{fullWidth:!0,placeholder:_,variant:"outlined",color:"secondary",size:"small",sx:{flex:1},value:n,onChange:function handleInputChange(e){l(e.target.value)},onKeyDown:function handleKeyDown(e){"Enter"===e.key&&(e.preventDefault(),E())}}),o.default.createElement(f.default,{variant:"outlined",size:"small",color:"secondary",startIcon:o.default.createElement("span",{className:"eicon-ai"}),onClick:E},x)))};p.propTypes={createWithAIData:a.object};t.default=p},84093:e=>{"use strict";e.exports=elementorV2.ui.CardContent},86956:e=>{"use strict";e.exports=elementorV2.ui},89186:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=n(r(64126)),i=n(r(78304)),u=r(86956),d=n(r(34874)),s=n(r(63602)),c=n(r(90038)),f=n(r(46168)),p=n(r(2979)),m=n(r(84093)),_=n(r(42408)),x=function Addons(e){var t=(0,i.default)({},((0,o.default)(e),e)),r=t.adminUrl.replace("wp-admin/",""),a=t.addonsData.repeater,n=3===a.length?3:2;return l.default.createElement(u.Paper,{elevation:0,sx:{p:3,display:"flex",flexDirection:"column",gap:2}},l.default.createElement(u.Box,null,l.default.createElement(u.Typography,{variant:"h6"},t.addonsData.header.title),l.default.createElement(u.Typography,{variant:"body2",color:"text.secondary"},t.addonsData.header.description)),l.default.createElement(d.default,{sx:{display:"grid",gridTemplateColumns:{md:"repeat(".concat(n,", 1fr)"),xs:"repeat(1, 1fr)"},gap:2}},a.map(function(e){var t=e.hasOwnProperty("target")?e.target:"_blank";return l.default.createElement(f.default,{key:e.title,elevation:0,sx:{display:"flex",border:1,borderRadius:1,borderColor:"action.focus"}},l.default.createElement(m.default,{sx:{display:"flex",flexDirection:"column",justifyContent:"space-between",gap:3,p:3}},l.default.createElement(u.Box,null,l.default.createElement(_.default,{image:e.image,sx:{height:"58px",width:"58px",mb:2}}),l.default.createElement(u.Box,null,l.default.createElement(u.Typography,{variant:"subtitle2"},e.title),l.default.createElement(u.Typography,{variant:"body2",color:"text.secondary"},e.description))),l.default.createElement(p.default,{sx:{p:0}},l.default.createElement(c.default,{variant:"outlined",size:"small",color:"promotion",href:e.url,target:t},e.button_label))))})),l.default.createElement(s.default,{variant:"body2",color:"info.main",underline:"none",href:"".concat(r).concat(t.addonsData.footer.file_path)},t.addonsData.footer.label))};t.default=x;x.propTypes={addonsData:a.object.isRequired,adminUrl:a.string.isRequired}},90038:e=>{"use strict";e.exports=elementorV2.ui.Button},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},98903:(e,t,r)=>{"use strict";var a=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=n(r(41594)),o=n(r(38073)),i=n(r(1726)),u=function SideBarPromotion(e){var t=e.sideData;return"banner"===t.type?l.default.createElement(o.default,t.data):l.default.createElement(i.default,t.data)};t.default=u;u.propTypes={sideData:a.object.isRequired}}},t={};function __webpack_require__(r){var a=t[r];if(void 0!==a)return a.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(62688),t=__webpack_require__(96784),r=t(__webpack_require__(41594)),a=t(__webpack_require__(18791)),n=__webpack_require__(86956),l=t(__webpack_require__(15925)),o=function App(e){return r.default.createElement(n.DirectionProvider,{rtl:e.isRTL},r.default.createElement(n.LocalizationProvider,null,r.default.createElement(n.ThemeProvider,{colorScheme:"light"},r.default.createElement(l.default,{homeScreenData:e.homeScreenData,adminUrl:e.adminUrl}))))},i=elementorCommon.config.isRTL,u=elementorAppConfig.admin_url,d=document.querySelector("#e-home-screen");o.propTypes={isRTL:e.bool,adminUrl:e.string,homeScreenData:e.object},a.default.render(r.default.createElement(o,{isRTL:i,homeScreenData:elementorHomeScreenData,adminUrl:u}),d)})()})();