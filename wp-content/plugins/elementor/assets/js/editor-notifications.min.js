/*! elementor - v3.31.0 - 09-09-2025 */
(()=>{var e={7470:(e,t,r)=>{"use strict";var n=r(75206);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},8640:e=>{"use strict";e.exports=elementorV2.icons.SpeakerphoneIcon},9730:e=>{"use strict";e.exports=elementorV2.query},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},12470:e=>{"use strict";e.exports=wp.i18n},18791:(e,t,r)=>{"use strict";var n=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(41594));var o=_interopRequireWildcard(r(75206)),i=r(7470);function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,o=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var i,a,l={__proto__:null,default:e};if(null===e||"object"!=n(e)&&"function"!=typeof e)return l;if(i=t?o:r){if(i.has(e))return i.get(e);i.set(e,l)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((a=(i=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(a.get||a.set)?i(l,u,a):l[u]=e[u]);return l})(e,t)}t.default={render:function render(e,t){var r;try{var n=(0,i.createRoot)(t);n.render(e),r=function unmountFunction(){n.unmount()}}catch(n){o.render(e,t),r=function unmountFunction(){o.unmountComponentAtNode(t)}}return{unmount:r}}}},18821:(e,t,r)=>{var n=r(70569),o=r(65474),i=r(37744),a=r(11018);e.exports=function _slicedToArray(e,t){return n(e)||o(e,t)||i(e,t)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},24752:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemChips=void 0;var i=o(r(41594)),a=o(r(78304)),l=r(86956);(t.WhatsNewItemChips=function WhatsNewItemChips(e){var t=e.chipPlan,r=e.chipTags,n=e.itemIndex,o=[];return t&&o.push({color:"promotion",size:"small",label:t}),r&&r.forEach(function(e){o.push({variant:"outlined",size:"small",label:e})}),o.length?i.default.createElement(l.Stack,{direction:"row",flexWrap:"wrap",gap:1,sx:{pb:1}},o.map(function(e,t){return i.default.createElement(l.Chip,(0,a.default)({key:"chip-".concat(n).concat(t)},e))})):null}).propTypes={chipPlan:n.string,chipTags:n.array,itemIndex:n.number.isRequired}},25206:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItem=void 0;var i=o(r(41594)),a=r(86956),l=r(56971),u=r(94841),s=r(46555),c=r(24752);(t.WhatsNewItem=function WhatsNewItem(e){var t=e.item,r=e.itemIndex,n=e.itemsLength,o=e.setIsOpen;return i.default.createElement(a.Box,{key:r,display:"flex",flexDirection:"column",sx:{pt:2}},(t.topic||t.date)&&i.default.createElement(l.WhatsNewItemTopicLine,{topic:t.topic,date:t.date}),i.default.createElement(u.WrapperWithLink,{link:t.link},i.default.createElement(a.Typography,{variant:"subtitle1",sx:{pb:2}},t.title)),t.imageSrc&&i.default.createElement(s.WhatsNewItemThumbnail,{imageSrc:t.imageSrc,link:t.link,title:t.title}),i.default.createElement(c.WhatsNewItemChips,{chipPlan:t.chipPlan,chipTags:t.chipTags,itemIndex:r}),t.description&&i.default.createElement(a.Typography,{variant:"body2",color:"text.secondary",sx:{pb:2}},t.description,t.readMoreText&&i.default.createElement(i.default.Fragment,null," ",i.default.createElement(a.Link,{href:t.link,color:"info.main",target:"_blank"},t.readMoreText))),t.cta&&t.ctaLink&&i.default.createElement(a.Box,{sx:{pb:2}},i.default.createElement(a.Button,{href:t.ctaLink,target:t.ctaLink.startsWith("#")?"_self":"_blank",variant:"contained",size:"small",color:"promotion",onClick:t.ctaLink.startsWith("#")?function(){return o(!1)}:function(){}},t.cta)),r!==n-1&&i.default.createElement(a.Divider,{sx:{my:1}}))}).propTypes={item:n.object.isRequired,itemIndex:n.number.isRequired,itemsLength:n.number.isRequired,setIsOpen:n.func.isRequired}},30482:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewTopBar=void 0;var i=o(r(41594)),a=r(86956),l=r(12470),u=r(59190);(t.WhatsNewTopBar=function WhatsNewTopBar(e){var t=e.setIsOpen;return i.default.createElement(i.default.Fragment,null,i.default.createElement(a.AppBar,{elevation:0,position:"sticky",sx:{backgroundColor:"background.default"}},i.default.createElement(a.Toolbar,{variant:"dense"},i.default.createElement(a.Typography,{variant:"overline",sx:{flexGrow:1}},(0,l.__)("What's New","elementor")),i.default.createElement(a.IconButton,{"aria-label":"close",size:"small",onClick:function onClick(){return t(!1)}},i.default.createElement(u.XIcon,null)))),i.default.createElement(a.Divider,null))}).propTypes={setIsOpen:n.func.isRequired}},37744:(e,t,r)=>{var n=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},38205:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.EditorDrawer=void 0;var a=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var o,a,l={__proto__:null,default:e};if(null===e||"object"!=i(e)&&"function"!=typeof e)return l;if(o=t?n:r){if(o.has(e))return o.get(e);o.set(e,l)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((a=(o=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(a.get||a.set)?o(l,u,a):l[u]=e[u]);return l}(e,t)}(r(41594)),l=o(r(18821)),u=r(74324);(t.EditorDrawer=function EditorDrawer(e){var t=e.anchorPosition,r=void 0===t?"left":t,n=(0,a.useState)(!0),o=(0,l.default)(n,2),i=o[0],s=o[1];return(0,a.useEffect)(function(){elementor.on("elementor/editor/panel/whats-new/clicked",function(){return s(!0)})},[]),a.default.createElement(u.WhatsNew,{isOpen:i,setIsOpen:s,setIsRead:function setIsRead(){return document.body.classList.remove("e-has-notification")},anchorPosition:r})}).propTypes={anchorPosition:n.oneOf(["left","top","right","bottom"])}},39739:e=>{"use strict";e.exports=elementorV2.editorAppBar},40362:(e,t,r)=>{"use strict";var n=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,o,i,a){if(a!==n){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},41594:e=>{"use strict";e.exports=React},46120:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNotifications=void 0;t.getNotifications=function getNotifications(){return function request(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(r,n){elementorCommon.ajax.addRequest(e,{success:r,error:n,data:t})})}("notifications_get")}},46555:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemThumbnail=void 0;var i=o(r(41594)),a=r(86956),l=r(94841);(t.WhatsNewItemThumbnail=function WhatsNewItemThumbnail(e){var t=e.imageSrc,r=e.title,n=e.link;return i.default.createElement(a.Box,{sx:{pb:2}},i.default.createElement(l.WrapperWithLink,{link:n},i.default.createElement("img",{src:t,alt:r,style:{maxWidth:"100%"}})))}).propTypes={imageSrc:n.string.isRequired,title:n.string.isRequired,link:n.string}},55549:(e,t,r)=>{"use strict";var n=r(12470).__;Object.defineProperty(t,"__esModule",{value:!0}),t.editorV1=void 0;var o=r(86034);t.editorV1=function editorV1(){elementor.on("panel:init",function(){elementorNotifications.is_unread&&document.body.classList.add("e-has-notification"),elementor.getPanelView().getPages("menu").view.addItem({name:"notification-center",icon:"eicon-speakerphone",title:n("What's New","elementor"),callback:o.editorOnButtonClicked},"navigate_from_page","view-page")})}},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},56971:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemTopicLine=void 0;var i=o(r(41594)),a=r(86956);(t.WhatsNewItemTopicLine=function WhatsNewItemTopicLine(e){var t=e.topic,r=e.date;return i.default.createElement(a.Stack,{direction:"row",divider:i.default.createElement(a.Divider,{orientation:"vertical",flexItem:!0}),spacing:1,color:"text.tertiary",sx:{pb:1}},t&&i.default.createElement(a.Box,null,t),r&&i.default.createElement(a.Box,null,r))}).propTypes={topic:n.string,date:n.string}},58644:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewDrawerContent=void 0;var i=o(r(41594)),a=r(9730),l=r(46120),u=r(86956),s=r(25206);(t.WhatsNewDrawerContent=function WhatsNewDrawerContent(e){var t=e.setIsOpen,r=(0,a.useQuery)({queryKey:["e-notifications"],queryFn:l.getNotifications}),n=r.isPending,o=r.error,c=r.data;return n?i.default.createElement(u.Box,null,i.default.createElement(u.LinearProgress,{color:"secondary"})):o?i.default.createElement(u.Box,null,"An error has occurred: ",o):c.map(function(e,r){return i.default.createElement(s.WhatsNewItem,{key:r,item:e,itemIndex:r,itemsLength:c.length,setIsOpen:t})})}).propTypes={setIsOpen:n.func.isRequired}},59190:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.XIcon=void 0;var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var i,a,l={__proto__:null,default:e};if(null===e||"object"!=o(e)&&"function"!=typeof e)return l;if(i=t?n:r){if(i.has(e))return i.get(e);i.set(e,l)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((a=(i=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(a.get||a.set)?i(l,u,a):l[u]=e[u]);return l}(e,t)}(r(41594)),a=n(r(78304)),l=r(86956);t.XIcon=(0,i.forwardRef)(function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))})},62688:(e,t,r)=>{e.exports=r(40362)()},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},72854:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.editorAppBarLink=void 0;var a=_interopRequireWildcard(r(41594)),l=o(r(18821)),u=_interopRequireWildcard(r(39739)),s=r(86034),c=r(86956),d=r(12470),p=o(r(8640));function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var o,a,l={__proto__:null,default:e};if(null===e||"object"!=i(e)&&"function"!=typeof e)return l;if(o=t?n:r){if(o.has(e))return o.get(e);o.set(e,l)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((a=(o=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(a.get||a.set)?o(l,u,a):l[u]=e[u]);return l})(e,t)}var f=function IconWithBadge(e){var t=e.invisible;return a.default.createElement(c.Badge,{color:"primary",variant:"dot",invisible:t},a.default.createElement(p.default,null))};f.propTypes={invisible:n.bool};t.editorAppBarLink=function editorAppBarLink(){u.utilitiesMenu.registerLink({id:"app-bar-menu-item-whats-new",priority:10,useProps:function useProps(){var e=(0,a.useState)(!elementorNotifications.is_unread),t=(0,l.default)(e,2),r=t[0],n=t[1];return{title:(0,d.__)("What's New","elementor"),icon:function icon(){return a.default.createElement(f,{invisible:r})},onClick:function onClick(){elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.topBar.whatsNew,{location:elementor.editorEvents.config.locations.topBar,secondaryLocation:elementor.editorEvents.config.secondaryLocations["whats-new"],trigger:elementor.editorEvents.config.triggers.click,element:elementor.editorEvents.config.elements.buttonIcon}),n(!0),elementorNotifications.is_unread=!1,(0,s.editorOnButtonClicked)("right")}}}})}},74324:(e,t,r)=>{"use strict";var n=r(62688),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNew=void 0;var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var i,a,l={__proto__:null,default:e};if(null===e||"object"!=o(e)&&"function"!=typeof e)return l;if(i=t?n:r){if(i.has(e))return i.get(e);i.set(e,l)}for(var u in e)"default"!==u&&{}.hasOwnProperty.call(e,u)&&((a=(i=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,u))&&(a.get||a.set)?i(l,u,a):l[u]=e[u]);return l}(e,t)}(r(41594)),a=r(86956),l=r(9730),u=r(30482),s=r(58644);var c=new l.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}});(t.WhatsNew=function WhatsNew(e){var t,r,n=e.isOpen,o=e.setIsOpen,d=e.setIsRead,p=e.anchorPosition,f=void 0===p?"right":p;return(0,i.useEffect)(function(){n&&d(!0)},[n,d]),i.default.createElement(i.default.Fragment,null,i.default.createElement(l.QueryClientProvider,{client:c},i.default.createElement(a.DirectionProvider,{rtl:elementorCommon.config.isRTL},i.default.createElement(a.ThemeProvider,{colorScheme:(null===(t=window.elementor)||void 0===t||null===(r=t.getPreferences)||void 0===r?void 0:r.call(t,"ui_theme"))||"auto"},i.default.createElement(a.Drawer,{anchor:f,open:n,onClose:function onClose(){return o(!1)},ModalProps:{style:{zIndex:999999}}},i.default.createElement(a.Box,{sx:{width:320,backgroundColor:"background.default"},role:"presentation"},i.default.createElement(u.WhatsNewTopBar,{setIsOpen:o}),i.default.createElement(a.Box,{sx:{padding:"16px"}},i.default.createElement(s.WhatsNewDrawerContent,{setIsOpen:o}))))))))}).propTypes={isOpen:n.bool.isRequired,setIsOpen:n.func.isRequired,setIsRead:n.func.isRequired,anchorPosition:n.oneOf(["left","top","right","bottom"])}},75206:e=>{"use strict";e.exports=ReactDOM},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},78304:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(null,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},86034:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.editorOnButtonClicked=void 0;var o=n(r(41594)),i=n(r(18791)),a=r(38205),l=!1;t.editorOnButtonClicked=function editorOnButtonClicked(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"left";if(!l){l=!0;var t=document.createElement("div");return document.body.append(t),void i.default.render(o.default.createElement(a.EditorDrawer,{anchorPosition:e}),t)}elementor.trigger("elementor/editor/panel/whats-new/clicked")}},86956:e=>{"use strict";e.exports=elementorV2.ui},94841:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WrapperWithLink=void 0;var i=o(r(41594)),a=r(86956);(t.WrapperWithLink=function WrapperWithLink(e){var t=e.link,r=e.children;return t?i.default.createElement(a.Link,{href:t,target:"_blank",underline:"none",color:"inherit",sx:{"&:hover":{color:"inherit"}}},r):r}).propTypes={link:n.string,children:n.any.isRequired}},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,__webpack_require__),o.exports}(()=>{"use strict";var e,t=__webpack_require__(55549),r=__webpack_require__(72854);null!==(e=window)&&void 0!==e&&null!==(e=e.elementorV2)&&void 0!==e&&e.editorAppBar?(0,r.editorAppBarLink)():(0,t.editorV1)()})()})();