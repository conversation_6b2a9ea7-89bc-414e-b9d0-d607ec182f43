/*! elementor - v3.31.0 - 09-09-2025 */
/*! For license information please see editor-v4-opt-in.min.js.LICENSE.txt */
(()=>{var r={7470:(r,l,s)=>{"use strict";var c=s(75206);l.createRoot=c.createRoot,l.hydrateRoot=c.hydrateRoot},9535:(r,l,s)=>{var c=s(89736);function _regenerator(){var l,s,u="function"==typeof Symbol?Symbol:{},p=u.iterator||"@@iterator",m=u.toStringTag||"@@toStringTag";function i(r,u,p,m){var _=u&&u.prototype instanceof Generator?u:Generator,g=Object.create(_.prototype);return c(g,"_invoke",function(r,c,u){var p,m,_,g=0,y=u||[],x=!1,v={p:0,n:0,v:l,a:d,f:d.bind(l,4),d:function d(r,s){return p=r,m=0,_=l,v.n=s,h}};function d(r,c){for(m=r,_=c,s=0;!x&&g&&!u&&s<y.length;s++){var u,p=y[s],b=v.p,T=p[2];r>3?(u=T===c)&&(_=p[(m=p[4])?5:(m=3,3)],p[4]=p[5]=l):p[0]<=b&&((u=r<2&&b<p[1])?(m=0,v.v=c,v.n=p[1]):b<T&&(u=r<3||p[0]>c||c>T)&&(p[4]=r,p[5]=c,v.n=T,m=0))}if(u||r>1)return h;throw x=!0,c}return function(u,y,b){if(g>1)throw TypeError("Generator is already running");for(x&&1===y&&d(y,b),m=y,_=b;(s=m<2?l:_)||!x;){p||(m?m<3?(m>1&&(v.n=-1),d(m,_)):v.n=_:v.v=_);try{if(g=2,p){if(m||(u="next"),s=p[u]){if(!(s=s.call(p,_)))throw TypeError("iterator result is not an object");if(!s.done)return s;_=s.value,m<2&&(m=0)}else 1===m&&(s=p.return)&&s.call(p),m<2&&(_=TypeError("The iterator does not provide a '"+u+"' method"),m=1);p=l}else if((s=(x=v.n<0)?_:r.call(c,v))!==h)break}catch(r){p=l,m=1,_=r}finally{g=1}}return{value:s,done:x}}}(r,p,m),!0),g}var h={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}s=Object.getPrototypeOf;var _=[][p]?s(s([][p]())):(c(s={},p,function(){return this}),s),g=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(_);function f(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,GeneratorFunctionPrototype):(r.__proto__=GeneratorFunctionPrototype,c(r,m,"GeneratorFunction")),r.prototype=Object.create(g),r}return GeneratorFunction.prototype=GeneratorFunctionPrototype,c(g,"constructor",GeneratorFunctionPrototype),c(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",c(GeneratorFunctionPrototype,m,"GeneratorFunction"),c(g),c(g,m,"Generator"),c(g,p,function(){return this}),c(g,"toString",function(){return"[object Generator]"}),(r.exports=_regenerator=function _regenerator(){return{w:i,m:f}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regenerator,r.exports.__esModule=!0,r.exports.default=r.exports},10564:r=>{function _typeof(l){return r.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},r.exports.__esModule=!0,r.exports.default=r.exports,_typeof(l)}r.exports=_typeof,r.exports.__esModule=!0,r.exports.default=r.exports},10739:r=>{r.exports=function _objectWithoutPropertiesLoose(r,l){if(null==r)return{};var s={};for(var c in r)if({}.hasOwnProperty.call(r,c)){if(-1!==l.indexOf(c))continue;s[c]=r[c]}return s},r.exports.__esModule=!0,r.exports.default=r.exports},11018:r=>{r.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},11327:(r,l,s)=>{var c=s(10564).default;r.exports=function toPrimitive(r,l){if("object"!=c(r)||!r)return r;var s=r[Symbol.toPrimitive];if(void 0!==s){var u=s.call(r,l||"default");if("object"!=c(u))return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===l?String:Number)(r)},r.exports.__esModule=!0,r.exports.default=r.exports},12470:r=>{"use strict";r.exports=wp.i18n},14901:(r,l,s)=>{"use strict";var c=s(62688),u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.Message=void 0;var p=u(s(41594)),m=s(86956),h=s(44048);(l.Message=function Message(r){var l=r.action,s=r.children,c=r.severity,u=void 0===c?"message":c,_=r.onClose;return p.default.createElement(m.Snackbar,{open:!0,autoHideDuration:4e3,anchorOrigin:{vertical:"bottom",horizontal:"right"},onClose:_},"message"!==u?p.default.createElement(m.Alert,{variant:"filled",severity:u,onClose:_},s):p.default.createElement(m.SnackbarContent,{message:p.default.createElement(m.Stack,{direction:"row",gap:1.5,alignItems:"center"},p.default.createElement(h.CircleCheckFilledIcon,null),s),action:p.default.createElement(m.Stack,{direction:"row",spacing:1,alignItems:"center"},l,p.default.createElement(m.IconButton,{color:"inherit",size:"small",onClick:_},p.default.createElement(h.XIcon,{fontSize:"small"})))}))}).propTypes={action:c.node,children:c.node,severity:c.oneOf(["message","success","warning","error"]),onClose:c.func}},16305:(r,l,s)=>{"use strict";var c=s(62688),u=s(96784),p=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.Terms=void 0;var m=u(s(78304)),h=u(s(18821)),_=u(s(40453)),g=s(12470),y=s(86956),x=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var u,m,h={__proto__:null,default:r};if(null===r||"object"!=p(r)&&"function"!=typeof r)return h;if(u=l?c:s){if(u.has(r))return u.get(r);u.set(r,h)}for(var _ in r)"default"!==_&&{}.hasOwnProperty.call(r,_)&&((m=(u=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,_))&&(m.get||m.set)?u(h,_,m):h[_]=r[_]);return h}(r,l)}(s(41594)),v=s(77123),b=u(s(72318)),T=u(s(44867)),E=["onClose","onSubmit","isEnrolled"];var S={header:(0,g.__)("Editor V4","elementor"),chip:(0,g.__)("Alpha","elementor"),checkboxText:(0,g.__)("I’ve read and understood.","elementor"),optIn:{titleText:(0,g.__)("You are about to enable Editor V4 features!","elementor"),introText:(0,g.__)("By activating, you’ll get early access to the next generation of Elementor’s Editor. This is your chance to explore new capabilities and help shape the future of Elementor! ","elementor"),notesHeader:(0,g.__)(" Important notes:","elementor"),notes:{alphaPrefix:(0,g.__)("Editor V4 is currently in alpha, ","elementor"),details:[(0,g.__)("and development is still in progress. Do not use it on live sites - use a staging or development environment instead.","elementor"),(0,g.__)("When you activate Editor V4, you’ll also be activating Containers, the Top Bar, and Nested Elements. You can turn them back off by going to WP Admin > Elementor > Settings > Features.","elementor")]},activateButton:(0,g.__)("Activate","elementor"),cancelButton:(0,g.__)("Cancel","elementor")},optOut:{titleText:(0,g.__)("You’re deactivating Editor V4","elementor"),introText:(0,g.__)("We hope you enjoyed testing and building with these new features.","elementor"),notesHeader:(0,g.__)("Keep in mind:","elementor"),notes:{details:[(0,g.__)("By deactivating, you’ll lose all Editor V4 features, and any content you created with V4-specific features will no longer be available or appear on your site.","elementor"),(0,g.__)("Containers, the Top Bar, and Nested Elements will stay in their current status.","elementor")]},activateButton:(0,g.__)("Deactivate V4","elementor"),cancelButton:(0,g.__)("Cancel","elementor")}};(l.Terms=function Terms(r){var l=r.onClose,s=r.onSubmit,c=r.isEnrolled,u=(0,_.default)(r,E),p=(0,x.useState)(!1),g=(0,h.default)(p,2),w=g[0],O=g[1],A=c?"optOut":"optIn";return x.default.createElement(y.Dialog,(0,m.default)({},u,{open:!0,onClose:l}),x.default.createElement(T.default,null,x.default.createElement(b.default,null,x.default.createElement(y.DialogTitle,null,S.header),x.default.createElement(y.Chip,{label:S.chip,color:"secondary",variant:"filled",size:"small"}))),x.default.createElement(y.DialogContent,{dividers:!0},x.default.createElement(y.Stack,{gap:2.5},x.default.createElement(y.Stack,{gap:1},x.default.createElement(v.TextNode,{align:"center",variant:"h6"},S[A].titleText),x.default.createElement(v.TextNode,{align:"center",variant:"body2"},S[A].introText)),x.default.createElement(y.Stack,{gap:1},x.default.createElement(v.TextNode,{variant:"body2"},S[A].notesHeader),x.default.createElement(v.ContentList,null,x.default.createElement(v.ContentListItem,{variant:"body2"},!c&&x.default.createElement(v.TextNode,{variant:"subtitle2",component:"span"},S.optIn.notes.alphaPrefix),S[A].notes.details[0]),S[A].notes.details.slice(1).map(function(r,l){return x.default.createElement(v.ContentListItem,{key:l,variant:"body2"},r)}))),x.default.createElement(y.FormControlLabel,{control:x.default.createElement(y.Checkbox,{checked:!!w,onChange:function handleCheckboxChange(){O(function(r){return!r})},color:"secondary",size:"small"}),label:x.default.createElement(v.TextNode,{variant:"body2"},S.checkboxText)}))),x.default.createElement(y.DialogActions,null,x.default.createElement(y.Button,{variant:"text",color:"secondary",onClick:l},S[A].cancelButton),x.default.createElement(y.Button,{disabled:!w,variant:"contained",onClick:function handleSubmit(){w&&s()}},S[A].activateButton)))}).propTypes={onClose:c.func,onSubmit:c.func,isEnrolled:c.bool}},18791:(r,l,s)=>{"use strict";var c=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;_interopRequireWildcard(s(41594));var u=_interopRequireWildcard(s(75206)),p=s(7470);function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,u=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var p,m,h={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return h;if(p=l?u:s){if(p.has(r))return p.get(r);p.set(r,h)}for(var _ in r)"default"!==_&&{}.hasOwnProperty.call(r,_)&&((m=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,_))&&(m.get||m.set)?p(h,_,m):h[_]=r[_]);return h})(r,l)}l.default={render:function render(r,l){var s;try{var c=(0,p.createRoot)(l);c.render(r),s=function unmountFunction(){c.unmount()}}catch(c){u.render(r,l),s=function unmountFunction(){u.unmountComponentAtNode(l)}}return{unmount:s}}}},18821:(r,l,s)=>{var c=s(70569),u=s(65474),p=s(37744),m=s(11018);r.exports=function _slicedToArray(r,l){return c(r)||u(r,l)||p(r,l)||m()},r.exports.__esModule=!0,r.exports.default=r.exports},21698:(r,l,s)=>{"use strict";var c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.triggerOptOut=l.triggerOptIn=void 0;var u=c(s(61790)),p=c(s(58155));l.triggerOptIn=function(){var r=(0,p.default)(u.default.mark(function _callee(){return u.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",elementorCommon.ajax.addRequest("editor_v4_opt_in"));case 1:case"end":return r.stop()}},_callee)}));return function triggerOptIn(){return r.apply(this,arguments)}}(),l.triggerOptOut=function(){var r=(0,p.default)(u.default.mark(function _callee2(){return u.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",elementorCommon.ajax.addRequest("editor_v4_opt_out"));case 1:case"end":return r.stop()}},_callee2)}));return function triggerOptOut(){return r.apply(this,arguments)}}()},33929:(r,l,s)=>{var c=s(67114),u=s(89736);r.exports=function AsyncIterator(r,l){function n(s,u,p,m){try{var h=r[s](u),_=h.value;return _ instanceof c?l.resolve(_.v).then(function(r){n("next",r,p,m)},function(r){n("throw",r,p,m)}):l.resolve(_).then(function(r){h.value=r,p(h)},function(r){return n("throw",r,p,m)})}catch(r){m(r)}}var s;this.next||(u(AsyncIterator.prototype),u(AsyncIterator.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),u(this,"_invoke",function(r,c,u){function f(){return new l(function(l,s){n(r,u,l,s)})}return s=s?s.then(f,f):f()},!0)},r.exports.__esModule=!0,r.exports.default=r.exports},34915:(r,l,s)=>{"use strict";var c=s(62688),u=s(96784),p=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.OptIn=void 0;var m,h=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var u,m,h={__proto__:null,default:r};if(null===r||"object"!=p(r)&&"function"!=typeof r)return h;if(u=l?c:s){if(u.has(r))return u.get(r);u.set(r,h)}for(var _ in r)"default"!==_&&{}.hasOwnProperty.call(r,_)&&((m=(u=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,_))&&(m.get||m.set)?u(h,_,m):h[_]=r[_]);return h}(r,l)}(s(41594)),_=u(s(85707)),g=u(s(18821)),y=s(86956),x=s(44048),v=s(12470),b=s(77123),T=s(94088),E=s(14901),S=s(21698),w=u(s(94786)),O=s(16305);function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(r);l&&(c=c.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,_.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}var A="e-editor-v4-opt-in-message",k="e-editor-v4-opt-out-message",I={title:(0,v.__)("The road to Editor V4","elementor"),chip:(0,v.__)("Alpha","elementor"),welcomeText:(0,v.__)("Welcome to a new era of web creation with Editor V4. It’s faster, more flexible, and built with a fresh approach to structure & styling.","elementor"),advantagesHeader:(0,v.__)("Here’s what’s inside the alpha version:","elementor"),advantages:[(0,v.__)("Unparalleled performance - Cleaner code & a lighter CSS footprint.","elementor"),(0,v.__)("Professional tools at your fingertips - classes and states.","elementor"),(0,v.__)("Consistent styling experience - A unified Style tab for all elements.","elementor"),(0,v.__)("Fully responsive design - Customize any style property per screen.","elementor")],andMore:(0,v.__)("And much more!","elementor"),readMore:(0,v.__)("Learn more","elementor"),warning:(0,v.__)("Editor V4 is still in alpha and should not be used on live sites yet.","elementor"),feedback:(0,v.__)("We’d love your feedback!","elementor"),overToGithub:(0,v.__)("Head over to Github","elementor"),tellUsWhy:(0,v.__)("Tell us why","elementor"),image:(0,v.__)("Editor V4","elementor"),buttons:{tryOut:(0,v.__)("Try out the new experience","elementor"),optIn:(0,v.__)("Activate the new experience","elementor"),optOut:(0,v.__)("Deactivate V4","elementor")},messages:{optInSuccess:(0,v.__)("Welcome! You’ve got the newest version of the editor.","elementor"),optOut:(0,v.__)("You’ve deactivated the new Editor. Have feedback?","elementor"),error:(0,v.__)("Ouch, there was a glitch. Try activating V4 again soon.","elementor")}},N={feedbackUrl:"https://go.elementor.com/wp-dash-opt-in-v4-feedback/",readMoreUrl:"https://go.elementor.com/wp-dash-opt-in-v4-help-center/",tryOutUrl:w.default.sanitize(function decodeHtmlUrl(r){var l=document.createElement("textarea");return l.innerHTML=r,l.value}(null===(m=elementorSettingsEditor4OptIn)||void 0===m||null===(m=m.urls)||void 0===m?void 0:m.start_building))||"#"},C={src:"https://assets.elementor.com/v4-promotion/v1/images/v4_opt_in_500.png",sx:{width:"100%",maxHeight:"507px",maxWidth:"sm",height:"auto",mx:"auto",borderRadius:2}},M={src:"https://assets.elementor.com/v4-promotion/v1/images/v4_opt_in_260.png",sx:{width:"100%",height:"auto",maxHeight:"260px",mx:"auto",maxWidth:"sm",borderRadius:2}};(l.OptIn=function OptIn(r){var l,s=r.state,c=(0,h.useState)(!1),u=(0,g.default)(c,2),p=u[0],m=u[1],_=(0,h.useState)(""),v=(0,g.default)(_,2),w=v[0],R=v[1],L=(0,h.useState)(""),D=(0,g.default)(L,2),P=D[0],j=D[1],F=(0,h.useState)(""),z=(0,g.default)(F,2),H=z[0],U=z[1];(0,h.useEffect)(function(){var r=sessionStorage.getItem(A),l=sessionStorage.getItem(k);r&&(setTimeout(function(){R(r)},100),sessionStorage.removeItem(A)),l&&(setTimeout(function(){j(l)},100),sessionStorage.removeItem(k))},[]);var G=!(null==s||null===(l=s.features)||void 0===l||!l.editor_v4);return h.default.createElement(y.Container,{maxWidth:"xl",sx:{marginBlockStart:2.5,display:"flex",flexBasis:"100%",gap:3,flexDirection:{xs:"column-reverse",md:"row"}}},h.default.createElement(y.Stack,{sx:{flex:1,maxWidth:{md:"507px",sm:"600px"},gap:2.5,mx:"auto"}},h.default.createElement(y.Stack,{direction:"row",alignItems:"center",gap:1},h.default.createElement(b.TextNode,{variant:"h4",width:"fit-content"},I.title),h.default.createElement(y.Chip,{size:"small",color:"secondary",variant:"filled",label:I.chip})),h.default.createElement(y.Stack,{direction:"column",gap:3},h.default.createElement(y.Box,null,h.default.createElement(b.TextNode,null,I.welcomeText)),h.default.createElement(y.Box,null,h.default.createElement(b.TextNode,{variant:"subtitle1",sx:{mb:1.5}},I.advantagesHeader),h.default.createElement(b.AdvantagesList,null,I.advantages.map(function(r,l){return h.default.createElement(b.AdvantagesListItem,{key:l},r)}),h.default.createElement(b.AdvantagesListItem,{key:I.advantages.length},I.andMore," ",h.default.createElement(y.Link,{color:"text.primary",href:N.readMoreUrl,target:"_blank"},I.readMore))))),h.default.createElement(y.Stack,{direction:"row",alignItems:"self-start",gap:.5,sx:{mb:2.5}},h.default.createElement(x.AlertTriangleIcon,{color:"action"}),h.default.createElement(y.Box,null,h.default.createElement(b.TextNode,null,I.warning))),h.default.createElement(y.Stack,{direction:"column",width:"clamp(240px, max(340px, 75%), 340px)",maxWidth:"100%",gap:2},G?h.default.createElement(y.Button,{onClick:function onClick(){return window.location.href=N.tryOutUrl},size:"large",color:"primary",variant:"contained",sx:{flexGrow:1}},I.buttons.tryOut):h.default.createElement(y.Button,{onClick:function onClick(){m(!0)},size:"large",color:"primary",variant:"contained",sx:{flexGrow:1}},I.buttons.optIn),h.default.createElement(y.Button,{onClick:function onClick(){m(!0)},size:"large",color:"secondary",variant:"outlined",sx:{flexGrow:1,visibility:G?"visible":"hidden"}},I.buttons.optOut)),h.default.createElement(b.TextNode,null,I.feedback," ",h.default.createElement(y.Link,{underline:"hover",href:N.feedbackUrl,target:"_blank"},I.overToGithub))),h.default.createElement(y.Stack,{sx:{flex:1,px:0,maxWidth:{md:"507px",sm:"600px"},mx:"auto"}},C.src?h.default.createElement(y.Image,{src:C.src,alt:I.image,sx:_objectSpread(_objectSpread({},C.sx),{},{display:{xs:"none",md:"block"}})}):h.default.createElement(T.ImageSquarePlaceholder,{sx:_objectSpread(_objectSpread({},C.sx),{},{display:{xs:"none",md:"block"}})}),M.src?h.default.createElement(y.Image,{src:M.src,alt:I.image,sx:_objectSpread(_objectSpread({},M.sx),{},{display:{xs:"block",md:"none"}})}):h.default.createElement(T.ImageLandscapePlaceholder,{sx:_objectSpread(_objectSpread({},M.sx),{},{display:{xs:"block",md:"none"}})})),p&&h.default.createElement(O.Terms,{onClose:function handlePopoverClose(){m(!1)},onSubmit:G?function maybeOptOut(){(0,S.triggerOptOut)().then(function(){sessionStorage.setItem(k,I.messages.optOut),window.location.reload()}).catch(function(){U(I.messages.error)})}:function maybeOptIn(){(0,S.triggerOptIn)().then(function(){sessionStorage.setItem(A,I.messages.optInSuccess),window.location.reload()}).catch(function(){U(I.messages.error)})},isEnrolled:G}),w&&h.default.createElement(E.Message,{onClose:function onClose(){return R("")}},w),P&&h.default.createElement(E.Message,{onClose:function onClose(){return j("")},action:h.default.createElement(y.Link,{href:N.feedbackUrl,target:"_blank",color:"inherit",sx:{cursor:"pointer",textDecoration:"none",pl:3}},I.tellUsWhy)},P),H&&h.default.createElement(E.Message,{severity:"error",onClose:function onClose(){return U("")}},H))}).propTypes={state:c.shape({features:c.shape({editor_v4:c.bool})}).isRequired}},37744:(r,l,s)=>{var c=s(78113);r.exports=function _unsupportedIterableToArray(r,l){if(r){if("string"==typeof r)return c(r,l);var s={}.toString.call(r).slice(8,-1);return"Object"===s&&r.constructor&&(s=r.constructor.name),"Map"===s||"Set"===s?Array.from(r):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?c(r,l):void 0}},r.exports.__esModule=!0,r.exports.default=r.exports},40362:(r,l,s)=>{"use strict";var c=s(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,r.exports=function(){function shim(r,l,s,u,p,m){if(m!==c){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}function getShim(){return shim}shim.isRequired=shim;var r={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return r.PropTypes=r,r}},40453:(r,l,s)=>{var c=s(10739);r.exports=function _objectWithoutProperties(r,l){if(null==r)return{};var s,u,p=c(r,l);if(Object.getOwnPropertySymbols){var m=Object.getOwnPropertySymbols(r);for(u=0;u<m.length;u++)s=m[u],-1===l.indexOf(s)&&{}.propertyIsEnumerable.call(r,s)&&(p[s]=r[s])}return p},r.exports.__esModule=!0,r.exports.default=r.exports},41594:r=>{"use strict";r.exports=React},44048:r=>{"use strict";r.exports=elementorV2.icons},44867:r=>{"use strict";r.exports=elementorV2.ui.DialogHeader},45498:(r,l,s)=>{var c=s(10564).default,u=s(11327);r.exports=function toPropertyKey(r){var l=u(r,"string");return"symbol"==c(l)?l:l+""},r.exports.__esModule=!0,r.exports.default=r.exports},46313:(r,l,s)=>{var c=s(9535),u=s(33929);r.exports=function _regeneratorAsyncGen(r,l,s,p,m){return new u(c().w(r,l,s,p),m||Promise)},r.exports.__esModule=!0,r.exports.default=r.exports},53051:(r,l,s)=>{var c=s(67114),u=s(9535),p=s(62507),m=s(46313),h=s(33929),_=s(95315),g=s(66961);function _regeneratorRuntime(){"use strict";var l=u(),s=l.m(_regeneratorRuntime),y=(Object.getPrototypeOf?Object.getPrototypeOf(s):s.__proto__).constructor;function n(r){var l="function"==typeof r&&r.constructor;return!!l&&(l===y||"GeneratorFunction"===(l.displayName||l.name))}var x={throw:1,return:2,break:3,continue:3};function a(r){var l,s;return function(c){l||(l={stop:function stop(){return s(c.a,2)},catch:function _catch(){return c.v},abrupt:function abrupt(r,l){return s(c.a,x[r],l)},delegateYield:function delegateYield(r,u,p){return l.resultName=u,s(c.d,g(r),p)},finish:function finish(r){return s(c.f,r)}},s=function t(r,s,u){c.p=l.prev,c.n=l.next;try{return r(s,u)}finally{l.next=c.n}}),l.resultName&&(l[l.resultName]=c.v,l.resultName=void 0),l.sent=c.v,l.next=c.n;try{return r.call(this,l)}finally{c.p=l.prev,c.n=l.next}}}return(r.exports=_regeneratorRuntime=function _regeneratorRuntime(){return{wrap:function wrap(r,s,c,u){return l.w(a(r),s,c,u&&u.reverse())},isGeneratorFunction:n,mark:l.m,awrap:function awrap(r,l){return new c(r,l)},AsyncIterator:h,async:function async(r,l,s,c,u){return(n(l)?m:p)(a(r),l,s,c,u)},keys:_,values:g}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regeneratorRuntime,r.exports.__esModule=!0,r.exports.default=r.exports},56441:r=>{"use strict";r.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},58155:r=>{function asyncGeneratorStep(r,l,s,c,u,p,m){try{var h=r[p](m),_=h.value}catch(r){return void s(r)}h.done?l(_):Promise.resolve(_).then(c,u)}r.exports=function _asyncToGenerator(r){return function(){var l=this,s=arguments;return new Promise(function(c,u){var p=r.apply(l,s);function _next(r){asyncGeneratorStep(p,c,u,_next,_throw,"next",r)}function _throw(r){asyncGeneratorStep(p,c,u,_next,_throw,"throw",r)}_next(void 0)})}},r.exports.__esModule=!0,r.exports.default=r.exports},61790:(r,l,s)=>{var c=s(53051)();r.exports=c;try{regeneratorRuntime=c}catch(r){"object"==typeof globalThis?globalThis.regeneratorRuntime=c:Function("r","regeneratorRuntime = r")(c)}},62507:(r,l,s)=>{var c=s(46313);r.exports=function _regeneratorAsync(r,l,s,u,p){var m=c(r,l,s,u,p);return m.next().then(function(r){return r.done?r.value:m.next()})},r.exports.__esModule=!0,r.exports.default=r.exports},62688:(r,l,s)=>{r.exports=s(40362)()},65474:r=>{r.exports=function _iterableToArrayLimit(r,l){var s=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=s){var c,u,p,m,h=[],_=!0,g=!1;try{if(p=(s=s.call(r)).next,0===l){if(Object(s)!==s)return;_=!1}else for(;!(_=(c=p.call(s)).done)&&(h.push(c.value),h.length!==l);_=!0);}catch(r){g=!0,u=r}finally{try{if(!_&&null!=s.return&&(m=s.return(),Object(m)!==m))return}finally{if(g)throw u}}return h}},r.exports.__esModule=!0,r.exports.default=r.exports},66961:(r,l,s)=>{var c=s(10564).default;r.exports=function _regeneratorValues(r){if(null!=r){var l=r["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],s=0;if(l)return l.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return{next:function next(){return r&&s>=r.length&&(r=void 0),{value:r&&r[s++],done:!r}}}}throw new TypeError(c(r)+" is not iterable")},r.exports.__esModule=!0,r.exports.default=r.exports},67114:r=>{r.exports=function _OverloadYield(r,l){this.v=r,this.k=l},r.exports.__esModule=!0,r.exports.default=r.exports},70569:r=>{r.exports=function _arrayWithHoles(r){if(Array.isArray(r))return r},r.exports.__esModule=!0,r.exports.default=r.exports},72318:r=>{"use strict";r.exports=elementorV2.ui.DialogHeaderGroup},75206:r=>{"use strict";r.exports=ReactDOM},77123:(r,l,s)=>{"use strict";var c=s(62688),u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.TextNode=l.ContentListItem=l.ContentList=l.AdvantagesListItem=l.AdvantagesList=void 0;var p=u(s(41594)),m=u(s(78304)),h=u(s(40453)),_=s(86956),g=s(44048),y=["children"],x=["children"],v=["children"],b=["children"],T=["children"],E=l.TextNode=function TextNode(r){var l=r.children,s=(0,h.default)(r,y);return p.default.createElement(_.Typography,(0,m.default)({color:"text.primary"},s),l)};E.propTypes={children:c.node},(l.ContentList=function ContentList(r){var l=r.children,s=(0,h.default)(r,x);return p.default.createElement(_.Box,(0,m.default)({component:"ul",sx:{my:0}},s),l)}).propTypes={children:c.node},(l.ContentListItem=function ContentListItem(r){var l=r.children,s=(0,h.default)(r,v);return p.default.createElement(E,(0,m.default)({component:"li",sx:{listStyle:"disc",marginInlineStart:3}},s),l)}).propTypes={children:c.node},(l.AdvantagesList=function AdvantagesList(r){var l=r.children,s=(0,h.default)(r,b);return p.default.createElement(_.Box,(0,m.default)({component:"ul",sx:{display:"flex",flexDirection:"column",gap:.5,my:0}},s),l)}).propTypes={children:c.node},(l.AdvantagesListItem=function AdvantagesListItem(r){var l=r.children,s=(0,h.default)(r,T);return p.default.createElement(E,(0,m.default)({component:"li",sx:{listStyle:"none",marginInlineStart:0,lineHeight:"150%",display:"flex",alignItems:"flex-start",gap:.5}},s),p.default.createElement(g.CheckIcon,{fontSize:"small"}),l)}).propTypes={children:c.node}},78113:r=>{r.exports=function _arrayLikeToArray(r,l){(null==l||l>r.length)&&(l=r.length);for(var s=0,c=Array(l);s<l;s++)c[s]=r[s];return c},r.exports.__esModule=!0,r.exports.default=r.exports},78304:r=>{function _extends(){return r.exports=_extends=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var s=arguments[l];for(var c in s)({}).hasOwnProperty.call(s,c)&&(r[c]=s[c])}return r},r.exports.__esModule=!0,r.exports.default=r.exports,_extends.apply(null,arguments)}r.exports=_extends,r.exports.__esModule=!0,r.exports.default=r.exports},85707:(r,l,s)=>{var c=s(45498);r.exports=function _defineProperty(r,l,s){return(l=c(l))in r?Object.defineProperty(r,l,{value:s,enumerable:!0,configurable:!0,writable:!0}):r[l]=s,r},r.exports.__esModule=!0,r.exports.default=r.exports},86956:r=>{"use strict";r.exports=elementorV2.ui},89736:r=>{function _regeneratorDefine(l,s,c,u){var p=Object.defineProperty;try{p({},"",{})}catch(l){p=0}r.exports=_regeneratorDefine=function regeneratorDefine(r,l,s,c){if(l)p?p(r,l,{value:s,enumerable:!c,configurable:!c,writable:!c}):r[l]=s;else{var u=function o(l,s){_regeneratorDefine(r,l,function(r){return this._invoke(l,s,r)})};u("next",0),u("throw",1),u("return",2)}},r.exports.__esModule=!0,r.exports.default=r.exports,_regeneratorDefine(l,s,c,u)}r.exports=_regeneratorDefine,r.exports.__esModule=!0,r.exports.default=r.exports},94088:(r,l,s)=>{"use strict";var c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.ImageSquarePlaceholder=l.ImageLandscapePlaceholder=void 0;var u=c(s(41594)),p=c(s(78304)),m=s(86956);l.ImageLandscapePlaceholder=function ImageLandscapePlaceholder(r){return u.default.createElement(m.SvgIcon,(0,p.default)({viewBox:"0 0 600 260"},r),u.default.createElement("rect",{x:"0",y:"0",width:"600",height:"260",fill:"#d9d9d9"}))},l.ImageSquarePlaceholder=function ImageSquarePlaceholder(r){return u.default.createElement(m.SvgIcon,(0,p.default)({viewBox:"0 0 500 500"},r),u.default.createElement("rect",{x:"0",y:"0",width:"500",height:"500",fill:"#d9d9d9"}))}},94786:r=>{"use strict";const{entries:l,setPrototypeOf:s,isFrozen:c,getPrototypeOf:u,getOwnPropertyDescriptor:p}=Object;let{freeze:m,seal:h,create:_}=Object,{apply:g,construct:y}="undefined"!=typeof Reflect&&Reflect;m||(m=function freeze(r){return r}),h||(h=function seal(r){return r}),g||(g=function apply(r,l,s){return r.apply(l,s)}),y||(y=function construct(r,l){return new r(...l)});const x=unapply(Array.prototype.forEach),v=unapply(Array.prototype.lastIndexOf),b=unapply(Array.prototype.pop),T=unapply(Array.prototype.push),E=unapply(Array.prototype.splice),S=unapply(String.prototype.toLowerCase),w=unapply(String.prototype.toString),O=unapply(String.prototype.match),A=unapply(String.prototype.replace),k=unapply(String.prototype.indexOf),I=unapply(String.prototype.trim),N=unapply(Object.prototype.hasOwnProperty),C=unapply(RegExp.prototype.test),M=function unconstruct(r){return function(){for(var l=arguments.length,s=new Array(l),c=0;c<l;c++)s[c]=arguments[c];return y(r,s)}}(TypeError);function unapply(r){return function(l){l instanceof RegExp&&(l.lastIndex=0);for(var s=arguments.length,c=new Array(s>1?s-1:0),u=1;u<s;u++)c[u-1]=arguments[u];return g(r,l,c)}}function addToSet(r,l){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:S;s&&s(r,null);let p=l.length;for(;p--;){let s=l[p];if("string"==typeof s){const r=u(s);r!==s&&(c(l)||(l[p]=r),s=r)}r[s]=!0}return r}function cleanArray(r){for(let l=0;l<r.length;l++){N(r,l)||(r[l]=null)}return r}function clone(r){const s=_(null);for(const[c,u]of l(r)){N(r,c)&&(Array.isArray(u)?s[c]=cleanArray(u):u&&"object"==typeof u&&u.constructor===Object?s[c]=clone(u):s[c]=u)}return s}function lookupGetter(r,l){for(;null!==r;){const s=p(r,l);if(s){if(s.get)return unapply(s.get);if("function"==typeof s.value)return unapply(s.value)}r=u(r)}return function fallbackValue(){return null}}const R=m(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),L=m(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),D=m(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),P=m(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),j=m(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),F=m(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),z=m(["#text"]),H=m(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),U=m(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),G=m(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),W=m(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),B=h(/\{\{[\w\W]*|[\w\W]*\}\}/gm),q=h(/<%[\w\W]*|[\w\W]*%>/gm),Y=h(/\$\{[\w\W]*/gm),V=h(/^data-[\-\w.\u00B7-\uFFFF]+$/),K=h(/^aria-[\-\w]+$/),X=h(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),$=h(/^(?:\w+script|data):/i),Z=h(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=h(/^html$/i),Q=h(/^[a-z][.\w]*(-[.\w]+)+$/i);var ee=Object.freeze({__proto__:null,ARIA_ATTR:K,ATTR_WHITESPACE:Z,CUSTOM_ELEMENT:Q,DATA_ATTR:V,DOCTYPE_NAME:J,ERB_EXPR:q,IS_ALLOWED_URI:X,IS_SCRIPT_OR_DATA:$,MUSTACHE_EXPR:B,TMPLIT_EXPR:Y});const te=1,ne=3,re=7,oe=8,ae=9,ie=function getGlobal(){return"undefined"==typeof window?null:window};var le=function createDOMPurify(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ie();const DOMPurify=r=>createDOMPurify(r);if(DOMPurify.version="3.2.6",DOMPurify.removed=[],!r||!r.document||r.document.nodeType!==ae||!r.Element)return DOMPurify.isSupported=!1,DOMPurify;let{document:s}=r;const c=s,u=c.currentScript,{DocumentFragment:p,HTMLTemplateElement:h,Node:g,Element:y,NodeFilter:B,NamedNodeMap:q=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:Y,DOMParser:V,trustedTypes:K}=r,$=y.prototype,Z=lookupGetter($,"cloneNode"),Q=lookupGetter($,"remove"),le=lookupGetter($,"nextSibling"),se=lookupGetter($,"childNodes"),ce=lookupGetter($,"parentNode");if("function"==typeof h){const r=s.createElement("template");r.content&&r.content.ownerDocument&&(s=r.content.ownerDocument)}let ue,de="";const{implementation:pe,createNodeIterator:fe,createDocumentFragment:me,getElementsByTagName:he}=s,{importNode:_e}=c;let ge={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};DOMPurify.isSupported="function"==typeof l&&"function"==typeof ce&&pe&&void 0!==pe.createHTMLDocument;const{MUSTACHE_EXPR:ye,ERB_EXPR:xe,TMPLIT_EXPR:ve,DATA_ATTR:be,ARIA_ATTR:Te,IS_SCRIPT_OR_DATA:Ee,ATTR_WHITESPACE:Se,CUSTOM_ELEMENT:we}=ee;let{IS_ALLOWED_URI:Oe}=ee,Ae=null;const ke=addToSet({},[...R,...L,...D,...j,...z]);let Ie=null;const Ne=addToSet({},[...H,...U,...G,...W]);let Ce=Object.seal(_(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Me=null,Re=null,Le=!0,De=!0,Pe=!1,je=!0,Fe=!1,ze=!0,He=!1,Ue=!1,Ge=!1,We=!1,Be=!1,qe=!1,Ye=!0,Ve=!1,Ke=!0,Xe=!1,$e={},Ze=null;const Je=addToSet({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Qe=null;const et=addToSet({},["audio","video","img","source","image","track"]);let tt=null;const nt=addToSet({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),rt="http://www.w3.org/1998/Math/MathML",ot="http://www.w3.org/2000/svg",at="http://www.w3.org/1999/xhtml";let it=at,lt=!1,st=null;const ct=addToSet({},[rt,ot,at],w);let ut=addToSet({},["mi","mo","mn","ms","mtext"]),dt=addToSet({},["annotation-xml"]);const pt=addToSet({},["title","style","font","a","script"]);let ft=null;const mt=["application/xhtml+xml","text/html"];let ht=null,_t=null;const gt=s.createElement("form"),yt=function isRegexOrFunction(r){return r instanceof RegExp||r instanceof Function},xt=function _parseConfig(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!_t||_t!==r){if(r&&"object"==typeof r||(r={}),r=clone(r),ft=-1===mt.indexOf(r.PARSER_MEDIA_TYPE)?"text/html":r.PARSER_MEDIA_TYPE,ht="application/xhtml+xml"===ft?w:S,Ae=N(r,"ALLOWED_TAGS")?addToSet({},r.ALLOWED_TAGS,ht):ke,Ie=N(r,"ALLOWED_ATTR")?addToSet({},r.ALLOWED_ATTR,ht):Ne,st=N(r,"ALLOWED_NAMESPACES")?addToSet({},r.ALLOWED_NAMESPACES,w):ct,tt=N(r,"ADD_URI_SAFE_ATTR")?addToSet(clone(nt),r.ADD_URI_SAFE_ATTR,ht):nt,Qe=N(r,"ADD_DATA_URI_TAGS")?addToSet(clone(et),r.ADD_DATA_URI_TAGS,ht):et,Ze=N(r,"FORBID_CONTENTS")?addToSet({},r.FORBID_CONTENTS,ht):Je,Me=N(r,"FORBID_TAGS")?addToSet({},r.FORBID_TAGS,ht):clone({}),Re=N(r,"FORBID_ATTR")?addToSet({},r.FORBID_ATTR,ht):clone({}),$e=!!N(r,"USE_PROFILES")&&r.USE_PROFILES,Le=!1!==r.ALLOW_ARIA_ATTR,De=!1!==r.ALLOW_DATA_ATTR,Pe=r.ALLOW_UNKNOWN_PROTOCOLS||!1,je=!1!==r.ALLOW_SELF_CLOSE_IN_ATTR,Fe=r.SAFE_FOR_TEMPLATES||!1,ze=!1!==r.SAFE_FOR_XML,He=r.WHOLE_DOCUMENT||!1,We=r.RETURN_DOM||!1,Be=r.RETURN_DOM_FRAGMENT||!1,qe=r.RETURN_TRUSTED_TYPE||!1,Ge=r.FORCE_BODY||!1,Ye=!1!==r.SANITIZE_DOM,Ve=r.SANITIZE_NAMED_PROPS||!1,Ke=!1!==r.KEEP_CONTENT,Xe=r.IN_PLACE||!1,Oe=r.ALLOWED_URI_REGEXP||X,it=r.NAMESPACE||at,ut=r.MATHML_TEXT_INTEGRATION_POINTS||ut,dt=r.HTML_INTEGRATION_POINTS||dt,Ce=r.CUSTOM_ELEMENT_HANDLING||{},r.CUSTOM_ELEMENT_HANDLING&&yt(r.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ce.tagNameCheck=r.CUSTOM_ELEMENT_HANDLING.tagNameCheck),r.CUSTOM_ELEMENT_HANDLING&&yt(r.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ce.attributeNameCheck=r.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),r.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof r.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ce.allowCustomizedBuiltInElements=r.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Fe&&(De=!1),Be&&(We=!0),$e&&(Ae=addToSet({},z),Ie=[],!0===$e.html&&(addToSet(Ae,R),addToSet(Ie,H)),!0===$e.svg&&(addToSet(Ae,L),addToSet(Ie,U),addToSet(Ie,W)),!0===$e.svgFilters&&(addToSet(Ae,D),addToSet(Ie,U),addToSet(Ie,W)),!0===$e.mathMl&&(addToSet(Ae,j),addToSet(Ie,G),addToSet(Ie,W))),r.ADD_TAGS&&(Ae===ke&&(Ae=clone(Ae)),addToSet(Ae,r.ADD_TAGS,ht)),r.ADD_ATTR&&(Ie===Ne&&(Ie=clone(Ie)),addToSet(Ie,r.ADD_ATTR,ht)),r.ADD_URI_SAFE_ATTR&&addToSet(tt,r.ADD_URI_SAFE_ATTR,ht),r.FORBID_CONTENTS&&(Ze===Je&&(Ze=clone(Ze)),addToSet(Ze,r.FORBID_CONTENTS,ht)),Ke&&(Ae["#text"]=!0),He&&addToSet(Ae,["html","head","body"]),Ae.table&&(addToSet(Ae,["tbody"]),delete Me.tbody),r.TRUSTED_TYPES_POLICY){if("function"!=typeof r.TRUSTED_TYPES_POLICY.createHTML)throw M('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof r.TRUSTED_TYPES_POLICY.createScriptURL)throw M('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ue=r.TRUSTED_TYPES_POLICY,de=ue.createHTML("")}else void 0===ue&&(ue=function _createTrustedTypesPolicy(r,l){if("object"!=typeof r||"function"!=typeof r.createPolicy)return null;let s=null;const c="data-tt-policy-suffix";l&&l.hasAttribute(c)&&(s=l.getAttribute(c));const u="dompurify"+(s?"#"+s:"");try{return r.createPolicy(u,{createHTML:r=>r,createScriptURL:r=>r})}catch(r){return console.warn("TrustedTypes policy "+u+" could not be created."),null}}(K,u)),null!==ue&&"string"==typeof de&&(de=ue.createHTML(""));m&&m(r),_t=r}},vt=addToSet({},[...L,...D,...P]),bt=addToSet({},[...j,...F]),Tt=function _forceRemove(r){T(DOMPurify.removed,{element:r});try{ce(r).removeChild(r)}catch(l){Q(r)}},Et=function _removeAttribute(r,l){try{T(DOMPurify.removed,{attribute:l.getAttributeNode(r),from:l})}catch(r){T(DOMPurify.removed,{attribute:null,from:l})}if(l.removeAttribute(r),"is"===r)if(We||Be)try{Tt(l)}catch(r){}else try{l.setAttribute(r,"")}catch(r){}},St=function _initDocument(r){let l=null,c=null;if(Ge)r="<remove></remove>"+r;else{const l=O(r,/^[\r\n\t ]+/);c=l&&l[0]}"application/xhtml+xml"===ft&&it===at&&(r='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+r+"</body></html>");const u=ue?ue.createHTML(r):r;if(it===at)try{l=(new V).parseFromString(u,ft)}catch(r){}if(!l||!l.documentElement){l=pe.createDocument(it,"template",null);try{l.documentElement.innerHTML=lt?de:u}catch(r){}}const p=l.body||l.documentElement;return r&&c&&p.insertBefore(s.createTextNode(c),p.childNodes[0]||null),it===at?he.call(l,He?"html":"body")[0]:He?l.documentElement:p},wt=function _createNodeIterator(r){return fe.call(r.ownerDocument||r,r,B.SHOW_ELEMENT|B.SHOW_COMMENT|B.SHOW_TEXT|B.SHOW_PROCESSING_INSTRUCTION|B.SHOW_CDATA_SECTION,null)},Ot=function _isClobbered(r){return r instanceof Y&&("string"!=typeof r.nodeName||"string"!=typeof r.textContent||"function"!=typeof r.removeChild||!(r.attributes instanceof q)||"function"!=typeof r.removeAttribute||"function"!=typeof r.setAttribute||"string"!=typeof r.namespaceURI||"function"!=typeof r.insertBefore||"function"!=typeof r.hasChildNodes)},At=function _isNode(r){return"function"==typeof g&&r instanceof g};function _executeHooks(r,l,s){x(r,r=>{r.call(DOMPurify,l,s,_t)})}const kt=function _sanitizeElements(r){let l=null;if(_executeHooks(ge.beforeSanitizeElements,r,null),Ot(r))return Tt(r),!0;const s=ht(r.nodeName);if(_executeHooks(ge.uponSanitizeElement,r,{tagName:s,allowedTags:Ae}),ze&&r.hasChildNodes()&&!At(r.firstElementChild)&&C(/<[/\w!]/g,r.innerHTML)&&C(/<[/\w!]/g,r.textContent))return Tt(r),!0;if(r.nodeType===re)return Tt(r),!0;if(ze&&r.nodeType===oe&&C(/<[/\w]/g,r.data))return Tt(r),!0;if(!Ae[s]||Me[s]){if(!Me[s]&&Nt(s)){if(Ce.tagNameCheck instanceof RegExp&&C(Ce.tagNameCheck,s))return!1;if(Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(s))return!1}if(Ke&&!Ze[s]){const l=ce(r)||r.parentNode,s=se(r)||r.childNodes;if(s&&l){for(let c=s.length-1;c>=0;--c){const u=Z(s[c],!0);u.__removalCount=(r.__removalCount||0)+1,l.insertBefore(u,le(r))}}}return Tt(r),!0}return r instanceof y&&!function _checkValidNamespace(r){let l=ce(r);l&&l.tagName||(l={namespaceURI:it,tagName:"template"});const s=S(r.tagName),c=S(l.tagName);return!!st[r.namespaceURI]&&(r.namespaceURI===ot?l.namespaceURI===at?"svg"===s:l.namespaceURI===rt?"svg"===s&&("annotation-xml"===c||ut[c]):Boolean(vt[s]):r.namespaceURI===rt?l.namespaceURI===at?"math"===s:l.namespaceURI===ot?"math"===s&&dt[c]:Boolean(bt[s]):r.namespaceURI===at?!(l.namespaceURI===ot&&!dt[c])&&!(l.namespaceURI===rt&&!ut[c])&&!bt[s]&&(pt[s]||!vt[s]):!("application/xhtml+xml"!==ft||!st[r.namespaceURI]))}(r)?(Tt(r),!0):"noscript"!==s&&"noembed"!==s&&"noframes"!==s||!C(/<\/no(script|embed|frames)/i,r.innerHTML)?(Fe&&r.nodeType===ne&&(l=r.textContent,x([ye,xe,ve],r=>{l=A(l,r," ")}),r.textContent!==l&&(T(DOMPurify.removed,{element:r.cloneNode()}),r.textContent=l)),_executeHooks(ge.afterSanitizeElements,r,null),!1):(Tt(r),!0)},It=function _isValidAttribute(r,l,c){if(Ye&&("id"===l||"name"===l)&&(c in s||c in gt))return!1;if(De&&!Re[l]&&C(be,l));else if(Le&&C(Te,l));else if(!Ie[l]||Re[l]){if(!(Nt(r)&&(Ce.tagNameCheck instanceof RegExp&&C(Ce.tagNameCheck,r)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(r))&&(Ce.attributeNameCheck instanceof RegExp&&C(Ce.attributeNameCheck,l)||Ce.attributeNameCheck instanceof Function&&Ce.attributeNameCheck(l))||"is"===l&&Ce.allowCustomizedBuiltInElements&&(Ce.tagNameCheck instanceof RegExp&&C(Ce.tagNameCheck,c)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(c))))return!1}else if(tt[l]);else if(C(Oe,A(c,Se,"")));else if("src"!==l&&"xlink:href"!==l&&"href"!==l||"script"===r||0!==k(c,"data:")||!Qe[r]){if(Pe&&!C(Ee,A(c,Se,"")));else if(c)return!1}else;return!0},Nt=function _isBasicCustomElement(r){return"annotation-xml"!==r&&O(r,we)},Ct=function _sanitizeAttributes(r){_executeHooks(ge.beforeSanitizeAttributes,r,null);const{attributes:l}=r;if(!l||Ot(r))return;const s={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Ie,forceKeepAttr:void 0};let c=l.length;for(;c--;){const u=l[c],{name:p,namespaceURI:m,value:h}=u,_=ht(p),g=h;let y="value"===p?g:I(g);if(s.attrName=_,s.attrValue=y,s.keepAttr=!0,s.forceKeepAttr=void 0,_executeHooks(ge.uponSanitizeAttribute,r,s),y=s.attrValue,!Ve||"id"!==_&&"name"!==_||(Et(p,r),y="user-content-"+y),ze&&C(/((--!?|])>)|<\/(style|title)/i,y)){Et(p,r);continue}if(s.forceKeepAttr)continue;if(!s.keepAttr){Et(p,r);continue}if(!je&&C(/\/>/i,y)){Et(p,r);continue}Fe&&x([ye,xe,ve],r=>{y=A(y,r," ")});const v=ht(r.nodeName);if(It(v,_,y)){if(ue&&"object"==typeof K&&"function"==typeof K.getAttributeType)if(m);else switch(K.getAttributeType(v,_)){case"TrustedHTML":y=ue.createHTML(y);break;case"TrustedScriptURL":y=ue.createScriptURL(y)}if(y!==g)try{m?r.setAttributeNS(m,p,y):r.setAttribute(p,y),Ot(r)?Tt(r):b(DOMPurify.removed)}catch(l){Et(p,r)}}else Et(p,r)}_executeHooks(ge.afterSanitizeAttributes,r,null)},Mt=function _sanitizeShadowDOM(r){let l=null;const s=wt(r);for(_executeHooks(ge.beforeSanitizeShadowDOM,r,null);l=s.nextNode();)_executeHooks(ge.uponSanitizeShadowNode,l,null),kt(l),Ct(l),l.content instanceof p&&_sanitizeShadowDOM(l.content);_executeHooks(ge.afterSanitizeShadowDOM,r,null)};return DOMPurify.sanitize=function(r){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=null,u=null,m=null,h=null;if(lt=!r,lt&&(r="\x3c!--\x3e"),"string"!=typeof r&&!At(r)){if("function"!=typeof r.toString)throw M("toString is not a function");if("string"!=typeof(r=r.toString()))throw M("dirty is not a string, aborting")}if(!DOMPurify.isSupported)return r;if(Ue||xt(l),DOMPurify.removed=[],"string"==typeof r&&(Xe=!1),Xe){if(r.nodeName){const l=ht(r.nodeName);if(!Ae[l]||Me[l])throw M("root node is forbidden and cannot be sanitized in-place")}}else if(r instanceof g)s=St("\x3c!----\x3e"),u=s.ownerDocument.importNode(r,!0),u.nodeType===te&&"BODY"===u.nodeName||"HTML"===u.nodeName?s=u:s.appendChild(u);else{if(!We&&!Fe&&!He&&-1===r.indexOf("<"))return ue&&qe?ue.createHTML(r):r;if(s=St(r),!s)return We?null:qe?de:""}s&&Ge&&Tt(s.firstChild);const _=wt(Xe?r:s);for(;m=_.nextNode();)kt(m),Ct(m),m.content instanceof p&&Mt(m.content);if(Xe)return r;if(We){if(Be)for(h=me.call(s.ownerDocument);s.firstChild;)h.appendChild(s.firstChild);else h=s;return(Ie.shadowroot||Ie.shadowrootmode)&&(h=_e.call(c,h,!0)),h}let y=He?s.outerHTML:s.innerHTML;return He&&Ae["!doctype"]&&s.ownerDocument&&s.ownerDocument.doctype&&s.ownerDocument.doctype.name&&C(J,s.ownerDocument.doctype.name)&&(y="<!DOCTYPE "+s.ownerDocument.doctype.name+">\n"+y),Fe&&x([ye,xe,ve],r=>{y=A(y,r," ")}),ue&&qe?ue.createHTML(y):y},DOMPurify.setConfig=function(){xt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Ue=!0},DOMPurify.clearConfig=function(){_t=null,Ue=!1},DOMPurify.isValidAttribute=function(r,l,s){_t||xt({});const c=ht(r),u=ht(l);return It(c,u,s)},DOMPurify.addHook=function(r,l){"function"==typeof l&&T(ge[r],l)},DOMPurify.removeHook=function(r,l){if(void 0!==l){const s=v(ge[r],l);return-1===s?void 0:E(ge[r],s,1)[0]}return b(ge[r])},DOMPurify.removeHooks=function(r){ge[r]=[]},DOMPurify.removeAllHooks=function(){ge={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},DOMPurify}();r.exports=le},95315:r=>{r.exports=function _regeneratorKeys(r){var l=Object(r),s=[];for(var c in l)s.unshift(c);return function e(){for(;s.length;)if((c=s.pop())in l)return e.value=c,e.done=!1,e;return e.done=!0,e}},r.exports.__esModule=!0,r.exports.default=r.exports},96784:r=>{r.exports=function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}},r.exports.__esModule=!0,r.exports.default=r.exports}},l={};function __webpack_require__(s){var c=l[s];if(void 0!==c)return c.exports;var u=l[s]={exports:{}};return r[s](u,u.exports,__webpack_require__),u.exports}(()=>{"use strict";var r=__webpack_require__(62688),l=__webpack_require__(96784),s=l(__webpack_require__(41594)),c=l(__webpack_require__(18791)),u=__webpack_require__(86956),p=__webpack_require__(34915),m=function App(r){return s.default.createElement(u.DirectionProvider,{rtl:r.isRTL},s.default.createElement(u.LocalizationProvider,null,s.default.createElement(u.ThemeProvider,{colorScheme:"light"},s.default.createElement(p.OptIn,{state:null==r?void 0:r.state}))))};m.propTypes={isRTL:r.bool,state:r.object};!function init(){var r=document.querySelector("#page-editor-v4-opt-in");r&&c.default.render(s.default.createElement(m,{isRTL:!!elementorCommon.config.isRTL,state:elementorSettingsEditor4OptIn}),r)}()})()})();