/*! elementor - v3.31.0 - 09-09-2025 */
(()=>{"use strict";var e={12470:e=>{e.exports=wp.i18n}},t={};var n,o,i=function __webpack_require__(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,__webpack_require__),i.exports}(12470).__;n=jQuery,o={cacheElements:function cacheElements(){var e=this;e.isElementorMode=ElementorGutenbergSettings.isElementorMode,e.cache={},e.cache.$gutenberg=n("#editor"),e.cache.$switchMode=n(n("#elementor-gutenberg-button-switch-mode").html()),e.cache.$switchModeButton=e.cache.$switchMode.find("#elementor-switch-mode-button"),e.bindEvents(),e.toggleStatus(),wp.data.subscribe(function(){setTimeout(function(){e.buildPanel()},1)})},buildPanel:function buildPanel(){var e=this;if(e.cache.$gutenberg.find("#elementor-switch-mode").length||e.cache.$gutenberg.find(".edit-post-header-toolbar").append(e.cache.$switchMode),this.hasIframe()&&this.handleIframe(),!n("#elementor-editor").length){e.cache.$editorPanel=n(n("#elementor-gutenberg-panel").html());var t=e.cache.$gutenberg.find(".block-editor-writing-flow");t.length||(t=e.cache.$gutenberg.find(".is-desktop-preview")),e.cache.$gurenbergBlockList=t,e.cache.$gurenbergBlockList.append(e.cache.$editorPanel),e.cache.$editorPanelButton=e.cache.$editorPanel.find("#elementor-go-to-edit-page-link"),e.cache.$editorPanelButton.on("click",function(t){t.preventDefault(),e.handleEditButtonClick()})}},handleIframe:function handleIframe(){this.hideIframeContent(),this.buildPanelTopBar()},hasIframe:function hasIframe(){return!!this.cache.$gutenberg.find('iframe[name="editor-canvas"]').length},hideIframeContent:function hideIframeContent(){this.isElementorMode&&this.cache.$gutenberg.find('iframe[name="editor-canvas"]').contents().find("body").append("<style>\n\t\t\t\t.editor-post-text-editor,\n\t\t\t\t.block-editor-block-list__layout {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\tbody {\n\t\t\t\t\tpadding: 0 !important;\n\t\t\t\t}\n\t\t\t</style>")},buildPanelTopBar:function buildPanelTopBar(){var e=this;!n("#elementor-edit-mode-button").length&&this.isElementorMode&&(e.cache.$editorBtnTop=n(n("#elementor-gutenberg-button-tmpl").html()),e.cache.$gutenberg.find(".edit-post-header-toolbar").append(e.cache.$editorBtnTop),n("#elementor-edit-mode-button").on("click",function(t){t.preventDefault(),e.handleEditButtonClick(!1)}))},handleEditButtonClick:function handleEditButtonClick(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&this.animateLoader(),"auto-draft"===wp.data.select("core/editor").getCurrentPost().status&&(wp.data.select("core/editor").getEditedPostAttribute("title")||wp.data.dispatch("core/editor").editPost({title:"Elementor #"+n("#post_ID").val()}),wp.data.dispatch("core/editor").savePost()),this.redirectWhenSave()},bindEvents:function bindEvents(){var e=this;e.cache.$switchModeButton.on("click",function(){e.isElementorMode?elementorCommon.dialogsManager.createWidget("confirm",{message:i("Please note that you are switching to WordPress default editor. Your current layout, design and content might break.","elementor"),headerMessage:i("Back to WordPress Editor","elementor"),strings:{confirm:i("Continue","elementor"),cancel:i("Cancel","elementor")},defaultOption:"confirm",onConfirm:function onConfirm(){var t=wp.data.dispatch("core/editor");t.editPost({gutenberg_elementor_mode:!1}),t.savePost(),e.isElementorMode=!e.isElementorMode,e.toggleStatus()}}).show():(e.isElementorMode=!e.isElementorMode,e.toggleStatus(),e.cache.$editorPanelButton.trigger("click"))})},redirectWhenSave:function redirectWhenSave(){var e=this;setTimeout(function(){wp.data.select("core/editor").isSavingPost()?e.redirectWhenSave():location.href=ElementorGutenbergSettings.editLink},300)},animateLoader:function animateLoader(){this.cache.$editorPanelButton.addClass("elementor-animate")},toggleStatus:function toggleStatus(){jQuery("body").toggleClass("elementor-editor-active",this.isElementorMode).toggleClass("elementor-editor-inactive",!this.isElementorMode)},init:function init(){this.cacheElements()}},n(function(){o.init()})})();