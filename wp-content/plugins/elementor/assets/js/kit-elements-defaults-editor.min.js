/*! elementor - v3.31.0 - 09-09-2025 */
/*! For license information please see kit-elements-defaults-editor.min.js.LICENSE.txt */
(()=>{var r={1367:(r,u,l)=>{"use strict";var s=l(12470).__,c=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(l(61790)),_=c(l(58155)),v=c(l(39805)),y=c(l(40989)),m=c(l(15118)),x=c(l(29402)),b=c(l(87861)),h=l(96356),g=c(l(79129)),w=l(54545);function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.default=function(r){function Create(){return(0,v.default)(this,Create),function _callSuper(r,u,l){return u=(0,x.default)(u),(0,m.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,l||[],(0,x.default)(r).constructor):u.apply(r,l))}(this,Create,arguments)}return(0,b.default)(Create,r),(0,y.default)(Create,[{key:"validateArgs",value:function validateArgs(){this.requireContainer()}},{key:"apply",value:(u=(0,_.default)(p.default.mark(function _callee(r){var u,l,c,_,v;return p.default.wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return u=r.container,$e.internal("panel/state-loading"),l=(0,w.extractElementType)(u.model),c=(0,h.getElementDefaults)(l),_=(0,g.default)(u),p.prev=1,p.next=2,(0,h.updateElementDefaults)(l,_);case 2:elementor.notifications.showToast({message:s("Default settings changed.","elementor"),buttons:[{name:"undo",text:s("Undo","elementor"),callback:function callback(){$e.run("kit-elements-defaults/restore",{type:l,settings:c})}}]}),p.next=4;break;case 3:throw p.prev=3,v=p.catch(1),elementor.notifications.showToast({message:s("An error occurred.","elementor")}),v;case 4:return p.prev=4,$e.internal("panel/state-ready"),p.finish(4);case 5:case"end":return p.stop()}},_callee,null,[[1,3,4,5]])})),function apply(r){return u.apply(this,arguments)})}]);var u}($e.modules.editor.CommandContainerBase)},9535:(r,u,l)=>{var s=l(89736);function _regenerator(){var u,l,c="function"==typeof Symbol?Symbol:{},p=c.iterator||"@@iterator",_=c.toStringTag||"@@toStringTag";function i(r,c,p,_){var y=c&&c.prototype instanceof Generator?c:Generator,m=Object.create(y.prototype);return s(m,"_invoke",function(r,s,c){var p,_,y,m=0,x=c||[],b=!1,h={p:0,n:0,v:u,a:d,f:d.bind(u,4),d:function d(r,l){return p=r,_=0,y=u,h.n=l,v}};function d(r,s){for(_=r,y=s,l=0;!b&&m&&!c&&l<x.length;l++){var c,p=x[l],g=h.p,w=p[2];r>3?(c=w===s)&&(y=p[(_=p[4])?5:(_=3,3)],p[4]=p[5]=u):p[0]<=g&&((c=r<2&&g<p[1])?(_=0,h.v=s,h.n=p[1]):g<w&&(c=r<3||p[0]>s||s>w)&&(p[4]=r,p[5]=s,h.n=w,_=0))}if(c||r>1)return v;throw b=!0,s}return function(c,x,g){if(m>1)throw TypeError("Generator is already running");for(b&&1===x&&d(x,g),_=x,y=g;(l=_<2?u:y)||!b;){p||(_?_<3?(_>1&&(h.n=-1),d(_,y)):h.n=y:h.v=y);try{if(m=2,p){if(_||(c="next"),l=p[c]){if(!(l=l.call(p,y)))throw TypeError("iterator result is not an object");if(!l.done)return l;y=l.value,_<2&&(_=0)}else 1===_&&(l=p.return)&&l.call(p),_<2&&(y=TypeError("The iterator does not provide a '"+c+"' method"),_=1);p=u}else if((l=(b=h.n<0)?y:r.call(s,h))!==v)break}catch(r){p=u,_=1,y=r}finally{m=1}}return{value:l,done:b}}}(r,p,_),!0),m}var v={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}l=Object.getPrototypeOf;var y=[][p]?l(l([][p]())):(s(l={},p,function(){return this}),l),m=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(y);function f(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,GeneratorFunctionPrototype):(r.__proto__=GeneratorFunctionPrototype,s(r,_,"GeneratorFunction")),r.prototype=Object.create(m),r}return GeneratorFunction.prototype=GeneratorFunctionPrototype,s(m,"constructor",GeneratorFunctionPrototype),s(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",s(GeneratorFunctionPrototype,_,"GeneratorFunction"),s(m),s(m,_,"Generator"),s(m,p,function(){return this}),s(m,"toString",function(){return"[object Generator]"}),(r.exports=_regenerator=function _regenerator(){return{w:i,m:f}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regenerator,r.exports.__esModule=!0,r.exports.default=r.exports},10564:r=>{function _typeof(u){return r.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},r.exports.__esModule=!0,r.exports.default=r.exports,_typeof(u)}r.exports=_typeof,r.exports.__esModule=!0,r.exports.default=r.exports},10906:(r,u,l)=>{var s=l(91819),c=l(20365),p=l(37744),_=l(78687);r.exports=function _toConsumableArray(r){return s(r)||c(r)||p(r)||_()},r.exports.__esModule=!0,r.exports.default=r.exports},11018:r=>{r.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},11327:(r,u,l)=>{var s=l(10564).default;r.exports=function toPrimitive(r,u){if("object"!=s(r)||!r)return r;var l=r[Symbol.toPrimitive];if(void 0!==l){var c=l.call(r,u||"default");if("object"!=s(c))return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===u?String:Number)(r)},r.exports.__esModule=!0,r.exports.default=r.exports},12470:r=>{"use strict";r.exports=wp.i18n},15118:(r,u,l)=>{var s=l(10564).default,c=l(36417);r.exports=function _possibleConstructorReturn(r,u){if(u&&("object"==s(u)||"function"==typeof u))return u;if(void 0!==u)throw new TypeError("Derived constructors may only return object or undefined");return c(r)},r.exports.__esModule=!0,r.exports.default=r.exports},18821:(r,u,l)=>{var s=l(70569),c=l(65474),p=l(37744),_=l(11018);r.exports=function _slicedToArray(r,u){return s(r)||c(r,u)||p(r,u)||_()},r.exports.__esModule=!0,r.exports.default=r.exports},20282:(r,u,l)=>{"use strict";var s=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.Index=void 0;var c=s(l(39805)),p=s(l(40989)),_=s(l(15118)),v=s(l(29402)),y=s(l(87861));function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.Index=function(r){function Index(){return(0,c.default)(this,Index),function _callSuper(r,u,l){return u=(0,v.default)(u),(0,_.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,l||[],(0,v.default)(r).constructor):u.apply(r,l))}(this,Index,arguments)}return(0,y.default)(Index,r),(0,p.default)(Index,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"kit-elements-defaults/{type}"}}])}($e.modules.CommandData)},20365:r=>{r.exports=function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)},r.exports.__esModule=!0,r.exports.default=r.exports},28860:(r,u,l)=>{"use strict";var s=l(12470).__,c=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(l(61790)),_=c(l(58155)),v=c(l(39805)),y=c(l(40989)),m=c(l(15118)),x=c(l(29402)),b=c(l(87861)),h=l(96356);function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.default=function(r){function Delete(){return(0,v.default)(this,Delete),function _callSuper(r,u,l){return u=(0,x.default)(u),(0,m.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,l||[],(0,x.default)(r).constructor):u.apply(r,l))}(this,Delete,arguments)}return(0,b.default)(Delete,r),(0,y.default)(Delete,[{key:"apply",value:(u=(0,_.default)(p.default.mark(function _callee(r){var u,l;return p.default.wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return u=r.type,$e.internal("panel/state-loading"),c.prev=1,c.next=2,(0,h.deleteElementDefaults)(u);case 2:elementor.notifications.showToast({message:s("Default settings has been reset.","elementor")}),c.next=4;break;case 3:throw c.prev=3,l=c.catch(1),elementor.notifications.showToast({message:s("An error occurred.","elementor")}),l;case 4:return c.prev=4,$e.internal("panel/state-ready"),c.finish(4);case 5:case"end":return c.stop()}},_callee,null,[[1,3,4,5]])})),function apply(r){return u.apply(this,arguments)})}]);var u}($e.modules.CommandBase)},28975:(r,u,l)=>{"use strict";var s=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),Object.defineProperty(u,"ConfirmCreation",{enumerable:!0,get:function get(){return c.default}}),Object.defineProperty(u,"Create",{enumerable:!0,get:function get(){return p.default}}),Object.defineProperty(u,"Delete",{enumerable:!0,get:function get(){return _.default}}),Object.defineProperty(u,"Restore",{enumerable:!0,get:function get(){return v.default}});var c=s(l(71501)),p=s(l(1367)),_=s(l(28860)),v=s(l(30513))},29402:r=>{function _getPrototypeOf(u){return r.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},r.exports.__esModule=!0,r.exports.default=r.exports,_getPrototypeOf(u)}r.exports=_getPrototypeOf,r.exports.__esModule=!0,r.exports.default=r.exports},30513:(r,u,l)=>{"use strict";var s=l(12470).__,c=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(l(61790)),_=c(l(58155)),v=c(l(39805)),y=c(l(40989)),m=c(l(15118)),x=c(l(29402)),b=c(l(87861)),h=l(96356);function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.default=function(r){function Restore(){return(0,v.default)(this,Restore),function _callSuper(r,u,l){return u=(0,x.default)(u),(0,m.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,l||[],(0,x.default)(r).constructor):u.apply(r,l))}(this,Restore,arguments)}return(0,b.default)(Restore,r),(0,y.default)(Restore,[{key:"apply",value:(u=(0,_.default)(p.default.mark(function _callee(r){var u,l,c;return p.default.wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return u=r.type,l=r.settings,$e.internal("panel/state-loading"),p.prev=1,p.next=2,(0,h.updateElementDefaults)(u,l);case 2:elementor.notifications.showToast({message:s("Previous settings restored.","elementor")}),p.next=4;break;case 3:throw p.prev=3,c=p.catch(1),elementor.notifications.showToast({message:s("An error occurred.","elementor")}),c;case 4:return p.prev=4,$e.internal("panel/state-ready"),p.finish(4);case 5:case"end":return p.stop()}},_callee,null,[[1,3,4,5]])})),function apply(r){return u.apply(this,arguments)})}]);var u}($e.modules.CommandBase)},33929:(r,u,l)=>{var s=l(67114),c=l(89736);r.exports=function AsyncIterator(r,u){function n(l,c,p,_){try{var v=r[l](c),y=v.value;return y instanceof s?u.resolve(y.v).then(function(r){n("next",r,p,_)},function(r){n("throw",r,p,_)}):u.resolve(y).then(function(r){v.value=r,p(v)},function(r){return n("throw",r,p,_)})}catch(r){_(r)}}var l;this.next||(c(AsyncIterator.prototype),c(AsyncIterator.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),c(this,"_invoke",function(r,s,c){function f(){return new u(function(u,l){n(r,c,u,l)})}return l=l?l.then(f,f):f()},!0)},r.exports.__esModule=!0,r.exports.default=r.exports},36417:r=>{r.exports=function _assertThisInitialized(r){if(void 0===r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r},r.exports.__esModule=!0,r.exports.default=r.exports},37744:(r,u,l)=>{var s=l(78113);r.exports=function _unsupportedIterableToArray(r,u){if(r){if("string"==typeof r)return s(r,u);var l={}.toString.call(r).slice(8,-1);return"Object"===l&&r.constructor&&(l=r.constructor.name),"Map"===l||"Set"===l?Array.from(r):"Arguments"===l||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?s(r,u):void 0}},r.exports.__esModule=!0,r.exports.default=r.exports},39805:r=>{r.exports=function _classCallCheck(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")},r.exports.__esModule=!0,r.exports.default=r.exports},40989:(r,u,l)=>{var s=l(45498);function _defineProperties(r,u){for(var l=0;l<u.length;l++){var c=u[l];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(r,s(c.key),c)}}r.exports=function _createClass(r,u,l){return u&&_defineProperties(r.prototype,u),l&&_defineProperties(r,l),Object.defineProperty(r,"prototype",{writable:!1}),r},r.exports.__esModule=!0,r.exports.default=r.exports},45498:(r,u,l)=>{var s=l(10564).default,c=l(11327);r.exports=function toPropertyKey(r){var u=c(r,"string");return"symbol"==s(u)?u:u+""},r.exports.__esModule=!0,r.exports.default=r.exports},46313:(r,u,l)=>{var s=l(9535),c=l(33929);r.exports=function _regeneratorAsyncGen(r,u,l,p,_){return new c(s().w(r,u,l,p),_||Promise)},r.exports.__esModule=!0,r.exports.default=r.exports},53051:(r,u,l)=>{var s=l(67114),c=l(9535),p=l(62507),_=l(46313),v=l(33929),y=l(95315),m=l(66961);function _regeneratorRuntime(){"use strict";var u=c(),l=u.m(_regeneratorRuntime),x=(Object.getPrototypeOf?Object.getPrototypeOf(l):l.__proto__).constructor;function n(r){var u="function"==typeof r&&r.constructor;return!!u&&(u===x||"GeneratorFunction"===(u.displayName||u.name))}var b={throw:1,return:2,break:3,continue:3};function a(r){var u,l;return function(s){u||(u={stop:function stop(){return l(s.a,2)},catch:function _catch(){return s.v},abrupt:function abrupt(r,u){return l(s.a,b[r],u)},delegateYield:function delegateYield(r,c,p){return u.resultName=c,l(s.d,m(r),p)},finish:function finish(r){return l(s.f,r)}},l=function t(r,l,c){s.p=u.prev,s.n=u.next;try{return r(l,c)}finally{u.next=s.n}}),u.resultName&&(u[u.resultName]=s.v,u.resultName=void 0),u.sent=s.v,u.next=s.n;try{return r.call(this,u)}finally{s.p=u.prev,s.n=u.next}}}return(r.exports=_regeneratorRuntime=function _regeneratorRuntime(){return{wrap:function wrap(r,l,s,c){return u.w(a(r),l,s,c&&c.reverse())},isGeneratorFunction:n,mark:u.m,awrap:function awrap(r,u){return new s(r,u)},AsyncIterator:v,async:function async(r,u,l,s,c){return(n(u)?_:p)(a(r),u,l,s,c)},keys:y,values:m}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regeneratorRuntime,r.exports.__esModule=!0,r.exports.default=r.exports},53559:(r,u,l)=>{"use strict";var s=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),Object.defineProperty(u,"FillDefaultsOnDrop",{enumerable:!0,get:function get(){return c.default}});var c=s(l(95506))},54545:(r,u,l)=>{"use strict";var s=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.extractElementType=function extractElementType(r){var u=(r=r.attributes||r).widgetType||r.elType;"section"===u&&r.isInner&&(u="inner-section");return u},u.isPopulatedObject=function isPopulatedObject(r){return r&&"object"===(0,c.default)(r)&&!Array.isArray(r)&&Object.keys(r).length>0};var c=s(l(10564))},58155:r=>{function asyncGeneratorStep(r,u,l,s,c,p,_){try{var v=r[p](_),y=v.value}catch(r){return void l(r)}v.done?u(y):Promise.resolve(y).then(s,c)}r.exports=function _asyncToGenerator(r){return function(){var u=this,l=arguments;return new Promise(function(s,c){var p=r.apply(u,l);function _next(r){asyncGeneratorStep(p,s,c,_next,_throw,"next",r)}function _throw(r){asyncGeneratorStep(p,s,c,_next,_throw,"throw",r)}_next(void 0)})}},r.exports.__esModule=!0,r.exports.default=r.exports},61790:(r,u,l)=>{var s=l(53051)();r.exports=s;try{regeneratorRuntime=s}catch(r){"object"==typeof globalThis?globalThis.regeneratorRuntime=s:Function("r","regeneratorRuntime = r")(s)}},62507:(r,u,l)=>{var s=l(46313);r.exports=function _regeneratorAsync(r,u,l,c,p){var _=s(r,u,l,c,p);return _.next().then(function(r){return r.done?r.value:_.next()})},r.exports.__esModule=!0,r.exports.default=r.exports},65474:r=>{r.exports=function _iterableToArrayLimit(r,u){var l=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=l){var s,c,p,_,v=[],y=!0,m=!1;try{if(p=(l=l.call(r)).next,0===u){if(Object(l)!==l)return;y=!1}else for(;!(y=(s=p.call(l)).done)&&(v.push(s.value),v.length!==u);y=!0);}catch(r){m=!0,c=r}finally{try{if(!y&&null!=l.return&&(_=l.return(),Object(_)!==_))return}finally{if(m)throw c}}return v}},r.exports.__esModule=!0,r.exports.default=r.exports},66961:(r,u,l)=>{var s=l(10564).default;r.exports=function _regeneratorValues(r){if(null!=r){var u=r["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],l=0;if(u)return u.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return{next:function next(){return r&&l>=r.length&&(r=void 0),{value:r&&r[l++],done:!r}}}}throw new TypeError(s(r)+" is not iterable")},r.exports.__esModule=!0,r.exports.default=r.exports},67114:r=>{r.exports=function _OverloadYield(r,u){this.v=r,this.k=u},r.exports.__esModule=!0,r.exports.default=r.exports},69175:(r,u,l)=>{"use strict";var s=l(96784),c=l(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=s(l(39805)),_=s(l(40989)),v=s(l(15118)),y=s(l(29402)),m=s(l(87861)),x=_interopRequireWildcard(l(53559)),b=_interopRequireWildcard(l(20282)),h=_interopRequireWildcard(l(28975));function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var l=new WeakMap,s=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var p,_,v={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return v;if(p=u?s:l){if(p.has(r))return p.get(r);p.set(r,v)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?p(v,y,_):v[y]=r[y]);return v})(r,u)}function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.default=function(r){function Component(){return(0,p.default)(this,Component),function _callSuper(r,u,l){return u=(0,y.default)(u),(0,v.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,l||[],(0,y.default)(r).constructor):u.apply(r,l))}(this,Component,arguments)}return(0,m.default)(Component,r),(0,_.default)(Component,[{key:"getNamespace",value:function getNamespace(){return"kit-elements-defaults"}},{key:"defaultHooks",value:function defaultHooks(){return this.importHooks(x)}},{key:"defaultData",value:function defaultData(){return this.importCommands(b)}},{key:"defaultCommands",value:function defaultCommands(){return this.importCommands(h)}}])}($e.modules.ComponentBase)},70569:r=>{r.exports=function _arrayWithHoles(r){if(Array.isArray(r))return r},r.exports.__esModule=!0,r.exports.default=r.exports},71501:(r,u,l)=>{"use strict";var s=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var c=s(l(61790)),p=s(l(58155)),_=s(l(39805)),v=s(l(40989)),y=s(l(15118)),m=s(l(29402)),x=s(l(87861)),b=l(83849);function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.default=function(r){function ConfirmCreation(){return(0,_.default)(this,ConfirmCreation),function _callSuper(r,u,l){return u=(0,m.default)(u),(0,y.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,l||[],(0,m.default)(r).constructor):u.apply(r,l))}(this,ConfirmCreation,arguments)}return(0,x.default)(ConfirmCreation,r),(0,v.default)(ConfirmCreation,[{key:"validateArgs",value:function validateArgs(){this.requireContainer()}},{key:"apply",value:(u=(0,p.default)(c.default.mark(function _callee(r){var u,l;return c.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(u=r.container,!(l=(0,b.getConfirmCreationDialog)({onConfirm:function onConfirm(){return $e.run("kit-elements-defaults/create",{container:u})}})).doNotShowAgain){s.next=1;break}return $e.run("kit-elements-defaults/create",{container:u}),s.abrupt("return");case 1:l.show();case 2:case"end":return s.stop()}},_callee)})),function apply(r){return u.apply(this,arguments)})}]);var u}($e.modules.editor.CommandContainerBase)},78113:r=>{r.exports=function _arrayLikeToArray(r,u){(null==u||u>r.length)&&(u=r.length);for(var l=0,s=Array(u);l<u;l++)s[l]=r[l];return s},r.exports.__esModule=!0,r.exports.default=r.exports},78687:r=>{r.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},79129:(r,u,l)=>{"use strict";var s=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=function extractContainerSettings(r){var u=r.settings,l=u.controls,s=u.toJSON({remove:["default"]}),c=extractSettings(s,l);return _objectSpread(_objectSpread({},c),function extractSpecialSettings(r,u,l){return v.reduce(function(s,c){var v=extractSettings((null==r?void 0:r[c])||{},u,l);return(0,_.isPopulatedObject)(v)?_objectSpread(_objectSpread({},s),{},(0,p.default)({},c,v)):s},{})}(s,l,c))};var c=s(l(18821)),p=s(l(85707)),_=l(54545);function ownKeys(r,u){var l=Object.keys(r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(r);u&&(s=s.filter(function(u){return Object.getOwnPropertyDescriptor(r,u).enumerable})),l.push.apply(l,s)}return l}function _objectSpread(r){for(var u=1;u<arguments.length;u++){var l=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(l),!0).forEach(function(u){(0,p.default)(r,u,l[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(l)):ownKeys(Object(l)).forEach(function(u){Object.defineProperty(r,u,Object.getOwnPropertyDescriptor(l,u))})}return r}var v=["__dynamic__","__globals__"];function extractSettings(r,u){var l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=Object.entries(r).filter(function(r){var s=(0,c.default)(r,1)[0];return!!u[s]&&!Object.prototype.hasOwnProperty.call(l,s)});return Object.fromEntries(s)}},83849:(r,u,l)=>{"use strict";var s=l(12470).__;Object.defineProperty(u,"__esModule",{value:!0}),u.getConfirmCreationDialog=function getConfirmCreationDialog(r){var l=r.onConfirm;if(!p){var _;u.introductionManager=p=function createIntroductionManager(){var r,u,l="e-kit-elements-defaults-create-dialog",p=new elementorModules.editor.utils.Introduction({introductionKey:c,dialogType:"confirm",dialogOptions:{id:l,headerMessage:s("Sure you want to change default settings?","elementor"),message:s("Your changes will automatically be saved for future uses of this element. %1$sNote:%2$s This includes sensitive information like emails, API keys, etc.","elementor").replace("%1$s","<strong>").replace("%2$s","</strong>"),effects:{show:"fadeIn",hide:"fadeOut"},hide:{onBackgroundClick:!0},strings:{confirm:s("Save","elementor"),cancel:s("Cancel","elementor")},onShow:function onShow(){var r;null===(r=this.getElements("checkbox-dont-show-again"))||void 0===r||r.prop("checked",!0)}}}),_=function createCheckboxAndLabel(r){var u="".concat(r,"-dont-show-again"),l=document.createElement("input");l.type="checkbox",l.name=u,l.id=u,l.checked=!0;var c=document.createElement("label");return c.htmlFor=u,c.textContent=s("Do not show this message again","elementor"),c.prepend(l),{checkbox:l,label:c}}(l),v=_.checkbox,y=_.label;return p.getDialog().addElement("checkbox-dont-show-again",v),null===(r=p.getDialog().getElements("message"))||void 0===r||null===(u=r.append)||void 0===u||u.call(r,y),p}(),p.introductionViewed=!(null===(_=elementor.config.user.introduction)||void 0===_||!_[c])}var v=p.getDialog();return v.onConfirm=function(){v.getElements("checkbox-dont-show-again").prop("checked")&&p.setViewed(),l()},{doNotShowAgain:!!p.introductionViewed,show:function show(){return p.show()}}},u.introductionManager=u.introductionKey=void 0,u.removeConfirmCreationDialog=function removeConfirmCreationDialog(){u.introductionManager=p=null};var c=u.introductionKey="kit_elements_defaults_create_dialog",p=u.introductionManager=null},85707:(r,u,l)=>{var s=l(45498);r.exports=function _defineProperty(r,u,l){return(u=s(u))in r?Object.defineProperty(r,u,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[u]=l,r},r.exports.__esModule=!0,r.exports.default=r.exports},87861:(r,u,l)=>{var s=l(91270);r.exports=function _inherits(r,u){if("function"!=typeof u&&null!==u)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),u&&s(r,u)},r.exports.__esModule=!0,r.exports.default=r.exports},89736:r=>{function _regeneratorDefine(u,l,s,c){var p=Object.defineProperty;try{p({},"",{})}catch(u){p=0}r.exports=_regeneratorDefine=function regeneratorDefine(r,u,l,s){if(u)p?p(r,u,{value:l,enumerable:!s,configurable:!s,writable:!s}):r[u]=l;else{var c=function o(u,l){_regeneratorDefine(r,u,function(r){return this._invoke(u,l,r)})};c("next",0),c("throw",1),c("return",2)}},r.exports.__esModule=!0,r.exports.default=r.exports,_regeneratorDefine(u,l,s,c)}r.exports=_regeneratorDefine,r.exports.__esModule=!0,r.exports.default=r.exports},91270:r=>{function _setPrototypeOf(u,l){return r.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,u){return r.__proto__=u,r},r.exports.__esModule=!0,r.exports.default=r.exports,_setPrototypeOf(u,l)}r.exports=_setPrototypeOf,r.exports.__esModule=!0,r.exports.default=r.exports},91819:(r,u,l)=>{var s=l(78113);r.exports=function _arrayWithoutHoles(r){if(Array.isArray(r))return s(r)},r.exports.__esModule=!0,r.exports.default=r.exports},95315:r=>{r.exports=function _regeneratorKeys(r){var u=Object(r),l=[];for(var s in u)l.unshift(s);return function e(){for(;l.length;)if((s=l.pop())in u)return e.value=s,e.done=!1,e;return e.done=!0,e}},r.exports.__esModule=!0,r.exports.default=r.exports},95506:(r,u,l)=>{"use strict";var s=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var c=s(l(85707)),p=s(l(39805)),_=s(l(40989)),v=s(l(15118)),y=s(l(29402)),m=s(l(87861)),x=l(96356),b=l(54545);function ownKeys(r,u){var l=Object.keys(r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(r);u&&(s=s.filter(function(u){return Object.getOwnPropertyDescriptor(r,u).enumerable})),l.push.apply(l,s)}return l}function _objectSpread(r){for(var u=1;u<arguments.length;u++){var l=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(l),!0).forEach(function(u){(0,c.default)(r,u,l[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(l)):ownKeys(Object(l)).forEach(function(u){Object.defineProperty(r,u,Object.getOwnPropertyDescriptor(l,u))})}return r}function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.default=function(r){function FillDefaultsOnDrop(){return(0,p.default)(this,FillDefaultsOnDrop),function _callSuper(r,u,l){return u=(0,y.default)(u),(0,v.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,l||[],(0,y.default)(r).constructor):u.apply(r,l))}(this,FillDefaultsOnDrop,arguments)}return(0,m.default)(FillDefaultsOnDrop,r),(0,_.default)(FillDefaultsOnDrop,[{key:"getCommand",value:function getCommand(){return"preview/drop"}},{key:"getId",value:function getId(){return"fill-defaults-on-drop"}},{key:"getConditions",value:function getConditions(r){var u,l;return(null===(u=r.model)||void 0===u?void 0:u.widgetType)||(null===(l=r.model)||void 0===l?void 0:l.elType)}},{key:"apply",value:function apply(r){var u=r.model,l=(0,x.getElementDefaults)((0,b.extractElementType)(u));if(!(0,b.isPopulatedObject)(l))return!0;var s=_objectSpread(_objectSpread({},l),r.model.settings||{});return["__dynamic__","__globals__"].forEach(function(u){var c;(0,b.isPopulatedObject)(l[u])&&(s[u]=_objectSpread(_objectSpread({},l[u]||{}),(null===(c=r.model.settings)||void 0===c?void 0:c[u])||{}))}),r.model=_objectSpread(_objectSpread({},r.model),{},{settings:s}),!0}}])}($e.modules.hookData.Dependency)},96246:(r,u,l)=>{"use strict";var s=l(12470).__,c=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(l(10906)),_=c(l(39805)),v=c(l(40989)),y=c(l(15118)),m=c(l(29402)),x=c(l(87861)),b=c(l(69175)),h=l(96356),g=l(54545);function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}function _classPrivateMethodInitSpec(r,u){(function _checkPrivateRedeclaration(r,u){if(u.has(r))throw new TypeError("Cannot initialize the same private elements twice on an object")})(r,u),u.add(r)}var w=new WeakSet;u.default=function(r){function Module(){var r;(0,_.default)(this,Module);for(var u=arguments.length,l=new Array(u),s=0;s<u;s++)l[s]=arguments[s];return _classPrivateMethodInitSpec(r=function _callSuper(r,u,l){return u=(0,m.default)(u),(0,y.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,l||[],(0,m.default)(r).constructor):u.apply(r,l))}(this,Module,[].concat(l)),w),r}return(0,x.default)(Module,r),(0,v.default)(Module,[{key:"onElementorInit",value:function onElementorInit(){(0,h.loadElementsDefaults)(),function _assertClassBrand(r,u,l){if("function"==typeof r?r===u:r.has(u))return arguments.length<3?u:l;throw new TypeError("Private element is not present on this object")}(w,this,_addContextMenuItem).call(this)}},{key:"onElementorInitComponents",value:function onElementorInitComponents(){window.$e.components.register(new b.default)}}])}(elementorModules.editor.utils.Module);function _addContextMenuItem(){var r;null!==(r=elementor.config)&&void 0!==r&&null!==(r=r.user)&&void 0!==r&&r.is_administrator&&["widget","container","section"].forEach(function(r){elementor.hooks.addFilter("elements/".concat(r,"/contextMenuGroups"),function(r,u){var l;return"section"===(0,g.extractElementType)((null===(l=u.options)||void 0===l?void 0:l.model)||{})?r:r.map(function(r){return"save"!==r.name||(r.actions=[].concat((0,p.default)(r.actions),[{name:"save-as-default",title:s("Save as default","elementor"),isEnabled:function isEnabled(){return!u.getContainer().isLocked()},callback:function callback(){$e.run("kit-elements-defaults/confirm-creation",{container:u.getContainer()})}}])),r})})})}},96356:(r,u,l)=>{"use strict";var s=l(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.deleteElementDefaults=function deleteElementDefaults(r){return _deleteElementDefaults.apply(this,arguments)},u.getElementDefaults=function getElementDefaults(r){return($e.data.cache.storage.getItem(_)||{})[r]||{}},u.loadElementsDefaults=loadElementsDefaults,u.updateElementDefaults=function updateElementDefaults(r,u){return _updateElementDefaults.apply(this,arguments)};var c=s(l(61790)),p=s(l(58155)),_="kit-elements-defaults";function loadElementsDefaults(){return _loadElementsDefaults.apply(this,arguments)}function _loadElementsDefaults(){return(_loadElementsDefaults=(0,p.default)(c.default.mark(function _callee(){return c.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return $e.data.cache.storage.removeItem(_),r.abrupt("return",$e.data.get("".concat(_,"/index")));case 1:case"end":return r.stop()}},_callee)}))).apply(this,arguments)}function _updateElementDefaults(){return(_updateElementDefaults=(0,p.default)(c.default.mark(function _callee2(r,u){return c.default.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.next=1,$e.data.update("".concat(_,"/index"),{settings:u},{type:r});case 1:return l.next=2,loadElementsDefaults();case 2:case"end":return l.stop()}},_callee2)}))).apply(this,arguments)}function _deleteElementDefaults(){return(_deleteElementDefaults=(0,p.default)(c.default.mark(function _callee3(r){return c.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=1,$e.data.delete("".concat(_,"/index"),{type:r});case 1:return u.next=2,loadElementsDefaults();case 2:case"end":return u.stop()}},_callee3)}))).apply(this,arguments)}},96784:r=>{r.exports=function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}},r.exports.__esModule=!0,r.exports.default=r.exports}},u={};function __webpack_require__(l){var s=u[l];if(void 0!==s)return s.exports;var c=u[l]={exports:{}};return r[l](c,c.exports,__webpack_require__),c.exports}(()=>{"use strict";new(__webpack_require__(96784)(__webpack_require__(96246)).default)})()})();