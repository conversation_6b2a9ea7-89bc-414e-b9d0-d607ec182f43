/*! elementor - v3.31.0 - 09-09-2025 */
"use strict";(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[1352],{2078:(e,t,n)=>{var o=n(12470).__,a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function SiteLogo(){var e,t=(0,i.useContext)(s.OnboardingContext),n=t.state,a=t.updateState,r=t.getStateObjectToUpdate,g=(0,i.useState)(n.siteLogo.id?n.siteLogo:null),v=(0,l.default)(g,2),b=v[0],_=v[1],h=(0,i.useState)(!1),y=(0,l.default)(h,2),C=y[0],E=y[1],k=(0,i.useState)(!1),P=(0,l.default)(k,2),w=P[0],O=P[1],S=(0,i.useState)(),j=(0,l.default)(S,2),x=j[0],N=j[1],A=(0,i.useState)(null),T=(0,l.default)(A,2),M=T[0],W=T[1],R=(0,u.default)(),L=R.ajaxState,B=R.setAjax,q=(0,u.default)(),I=q.ajaxState,F=q.setAjax,D="siteLogo",H="goodToGo",U=(0,c.useNavigate)(),G={role:"button",onClick:function onClick(){if(elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),b.id)if(b.id!==n.siteLogo.id)z();else{var e=r(n,"steps",D,"completed");a(e),U("onboarding/"+H)}}};"completed"!==n.steps[D]&&(e={text:o("Skip","elementor")});G.text=C?i.default.createElement(i.default.Fragment,null,i.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"})):o("Next","elementor");b||(G.className="e-onboarding__button--disabled");var z=(0,i.useCallback)(function(){E(!0),B({data:{action:"elementor_update_site_logo",data:JSON.stringify({attachmentId:b.id})}})},[b]),J=function uploadSiteLogo(e){E(!0),F({data:{action:"elementor_upload_site_logo",fileToUpload:e}})},K=function dismissUnfilteredFilesCallback(){E(!1),_(null),O(!1)};return(0,i.useEffect)(function(){var e;"initial"!==I.status&&("success"===I.status&&null!==(e=I.response)&&void 0!==e&&null!==(e=e.imageAttachment)&&void 0!==e&&e.id?(elementorCommon.events.dispatchEvent({event:"logo image uploaded",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:x}}),E(!1),_(I.response.imageAttachment),M&&W(null)):"error"===I.status&&(E(!1),_(null),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,action_state:"failure",action:"logo image upload"}}),W({type:"error",icon:"eicon-warning",message:"That didn't work. Try uploading your file again."})))},[I.status]),(0,i.useEffect)(function(){var e;if("initial"!==L.status)if("success"===L.status&&null!==(e=L.response)&&void 0!==e&&e.siteLogoUpdated){elementorCommon.events.dispatchEvent({event:"logo image updated",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:x}}),E(!1),M&&W(null);var t=r(n,"steps",D,"completed");t.siteLogo={id:b.id,url:b.url},a(t),U("onboarding/"+H)}else"error"===L.status&&(E(!1),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"update site logo"}}),W({type:"error",icon:"eicon-warning",message:"That didn't work. Try uploading your file again."}))},[L.status]),i.default.createElement(f.default,{pageId:D,nextStep:H},i.default.createElement(m.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Setup.svg",title:o("Have a logo? Add it here.","elementor"),actionButton:G,skipButton:e,noticeState:M},i.default.createElement("span",null,o("Otherwise, you can skip this and add one later.","elementor")),b&&!w?i.default.createElement("div",{className:"e-onboarding__logo-container"+(C?" e-onboarding__is-uploading":"")},i.default.createElement("div",{className:"e-onboarding__logo-remove",onClick:function onClick(){return function onImageRemoveClick(){elementorCommon.events.dispatchEvent({event:"remove selected logo",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement}}),_(null)}()}},i.default.createElement("i",{className:"eicon-trash-o"})),i.default.createElement("img",{src:b.url,alt:o("Potential Site Logo","elementor")})):i.default.createElement(i.default.Fragment,null,i.default.createElement(d.default,{className:"e-onboarding__drop-zone",heading:o("Drop image here","elementor"),secondaryText:o("or","elementor"),buttonText:o("Open Media Library","elementor"),buttonVariant:"outlined",buttonColor:"cta",icon:"",type:"wp-media",filetypes:["jpg","jpeg","png","svg"],onFileSelect:function onFileSelect(e){return function onFileSelect(e){N("drop"),"image/svg+xml"!==e.type||elementorAppConfig.onboarding.isUnfilteredFilesEnabled?(_(e),W(null),J(e)):(_(e),E(!0),O(!0))}(e)},onWpMediaSelect:function onWpMediaSelect(e){var t=e.state().get("selection").first().toJSON();N("browse"),_(t),W(null)},onButtonClick:function onButtonClick(){elementorCommon.events.dispatchEvent({event:"browse file click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})},onError:function onError(e){"file_not_allowed"===e.id&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"logo upload format"}}),W({type:"error",icon:"eicon-warning",message:o("This file type is not supported. Try a different type of file","elementor")}))}})),i.default.createElement(p.default,{show:w,setShow:O,confirmModalText:o("This allows Elementor to scan your SVGs for malicious content. If you do not wish to allow this, use a different image format.","elementor"),errorModalText:o("There was a problem with enabling SVG uploads. Try again, or use another image format.","elementor"),onReady:function onReady(){O(!1),elementorAppConfig.onboarding.isUnfilteredFilesEnabled=!0,J(b)},onDismiss:function onDismiss(){return K()},onCancel:function onCancel(){return K()}})))};var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=a(n(18821)),s=n(79871),c=n(83040),u=a(n(73921)),d=a(n(39970)),p=a(n(53441)),f=a(n(12720)),m=a(n(42036))},12720:(e,t,n)=>{var o=n(12470).__,a=n(62688),r=n(96784),i=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Layout;var l=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,r,l={__proto__:null,default:e};if(null===e||"object"!=i(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((r=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(r.get||r.set)?a(l,s,r):l[s]=e[s]);return l}(e,t)}(n(41594)),s=n(79871),c=r(n(28931)),u=r(n(19634)),d=r(n(25368)),p=r(n(20948));function Layout(e){(0,l.useEffect)(function(){elementorCommon.events.dispatchEvent({event:"modal load",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.pageId,user_state:elementorCommon.config.library_connect.is_connected?"logged":"anon"}}),a({currentStep:e.pageId,nextStep:e.nextStep||"",proNotice:null})},[e.pageId]);var t=(0,l.useContext)(s.OnboardingContext),n=t.state,a=t.updateState,r=[],i=(0,l.useRef)(),f={id:"create-account",text:o("Create Account","elementor"),hideText:!1,elRef:(0,l.useRef)(),url:elementorAppConfig.onboarding.urls.signUp+elementorAppConfig.onboarding.utms.connectTopBar,target:"_blank",rel:"opener",onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"create account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,source:"header"}})}};return n.isLibraryConnected?r.push({id:"my-elementor",text:o("My Elementor","elementor"),hideText:!1,icon:"eicon-user-circle-o",url:"https://my.elementor.com/websites/?utm_source=onboarding-wizard&utm_medium=wp-dash&utm_campaign=my-account&utm_content=top-bar&utm_term="+elementorAppConfig.onboarding.onboardingVersion,target:"_blank",onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"my elementor click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,source:"header"}})}}):r.push(f),n.hasPro||r.push({id:"go-pro",text:o("Upgrade","elementor"),hideText:!1,className:"eps-button__go-pro-btn",url:"https://elementor.com/pro/?utm_source=onboarding-wizard&utm_campaign=gopro&utm_medium=wp-dash&utm_content=top-bar&utm_term="+elementorAppConfig.onboarding.onboardingVersion,target:"_blank",elRef:i,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"go pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})}}),l.default.createElement("div",{className:"eps-app__lightbox"},l.default.createElement("div",{className:"eps-app e-onboarding"},!n.isLibraryConnected&&l.default.createElement(p.default,{buttonRef:f.elRef}),l.default.createElement(c.default,{title:o("Getting Started","elementor"),buttons:r}),l.default.createElement("div",{className:"eps-app__main e-onboarding__page-"+e.pageId},l.default.createElement(d.default,{className:"e-onboarding__content"},l.default.createElement(u.default,null),e.children))))}Layout.propTypes={pageId:a.string.isRequired,nextStep:a.string,className:a.string,children:a.any.isRequired}},17271:(e,t,n)=>{var o=n(62688),a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=SkipButton;var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=n(79871),s=n(83040),c=a(n(79997));function SkipButton(e){var t=e.button,n=e.className,o=(0,i.useContext)(l.OnboardingContext),a=o.state,r=o.updateState,u=(0,s.useNavigate)(),d=t.action||function skipStep(){var e=JSON.parse(JSON.stringify(a));e.steps[a.currentStep]="skipped",r(e),a.nextStep&&u("onboarding/"+a.nextStep)};return delete t.action,t.onClick=function(){elementorCommon.events.dispatchEvent({event:"skip",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:a.currentStep}}),t.href||d()},i.default.createElement(c.default,{buttonSettings:t,className:n,type:"skip"})}SkipButton.propTypes={button:o.object.isRequired,className:o.string}},19197:(e,t,n)=>{var o=n(62688),a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=FooterButtons;var r=a(n(41594)),i=a(n(3416)),l=a(n(79997)),s=a(n(17271));function FooterButtons(e){var t=e.actionButton,n=e.skipButton,o=e.className,a="e-onboarding__footer";return o&&(a+=" "+o),r.default.createElement(i.default,{container:!0,alignItems:"center",justify:"space-between",className:a},t&&r.default.createElement(l.default,{buttonSettings:t,type:"action"}),n&&r.default.createElement(s.default,{button:n}))}FooterButtons.propTypes={actionButton:o.object,skipButton:o.object,className:o.string}},19634:(e,t,n)=>{var o=n(12470).__,a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function ProgressBar(){var e=(0,i.useContext)(s.OnboardingContext).state,t=(0,c.useNavigate)(),n=[{id:"account",title:o("Elementor Account","elementor"),route:"account"}];elementorAppConfig.onboarding.helloActivated||n.push({id:"hello",title:o("Hello Biz Theme","elementor"),route:"hello"});elementorAppConfig.onboarding.experiment?n.push({id:"chooseFeatures",title:o("Choose Features","elementor"),route:"chooseFeatures"}):n.push({id:"siteName",title:o("Site Name","elementor"),route:"site-name"},{id:"siteLogo",title:o("Site Logo","elementor"),route:"site-logo"});n.push({id:"goodToGo",title:o("Good to Go","elementor"),route:"good-to-go"});var a=n.map(function(n,o){return n.index=o,e.steps[n.id]&&(n.onClick=function(){elementorCommon.events.dispatchEvent({event:"step click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,next_step:n.id}}),t("/onboarding/"+n.id)}),i.default.createElement(u.default,(0,l.default)({key:n.id},n))});return i.default.createElement("div",{className:"e-onboarding__progress-bar"},a)};var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=a(n(78304)),s=n(79871),c=n(83040),u=a(n(71248))},20948:(e,t,n)=>{var o=n(62688);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Connect;var a=n(41594),r=n(79871);function Connect(e){var t=(0,a.useContext)(r.OnboardingContext),n=t.state,o=t.updateState,i=t.getStateObjectToUpdate;return(0,a.useEffect)(function(){jQuery(e.buttonRef.current).elementorConnect({success:function success(t){return e.successCallback?e.successCallback(t):function connectSuccessCallback(e){var t=i(n,"steps","account","completed");elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=e.kits_access_level||e.access_level||0,elementorCommon.config.library_connect.current_access_tier=e.access_tier,t.isLibraryConnected=!0,o(t)}(t)},error:function error(){e.errorCallback&&e.errorCallback()},popup:{width:726,height:534}})},[]),null}Connect.propTypes={buttonRef:o.object.isRequired,successCallback:o.func,errorCallback:o.func}},28931:(e,t,n)=>{var o=n(12470).__,a=n(62688),r=n(96784),i=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Header;var l=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,r,l={__proto__:null,default:e};if(null===e||"object"!=i(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((r=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(r.get||r.set)?a(l,s,r):l[s]=e[s]);return l}(e,t)}(n(41594)),s=n(79871),c=r(n(3416)),u=r(n(61553)),d=r(n(6056)),p=r(n(80791));function Header(e){(0,p.default)({title:e.title});var t=(0,l.useContext)(s.OnboardingContext).state;return l.default.createElement(c.default,{container:!0,alignItems:"center",justify:"space-between",className:"eps-app__header e-onboarding__header"},l.default.createElement("div",{className:"eps-app__logo-title-wrapper e-onboarding__header-logo"},l.default.createElement("i",{className:"eps-app__logo eicon-elementor"}),l.default.createElement("img",{src:elementorCommon.config.urls.assets+"images/logo-platform.svg",alt:o("Elementor Logo","elementor")})),l.default.createElement(d.default,{buttons:e.buttons,onClose:function onClose(){elementorCommon.events.dispatchEvent({event:"close modal",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep}}),window.top.location=elementorAppConfig.admin_url}}),!t.hasPro&&l.default.createElement(u.default,{buttonsConfig:e.buttons}))}Header.propTypes={title:a.string,buttons:a.arrayOf(a.object)},Header.defaultProps={buttons:[]}},31634:(e,t,n)=>{var o=n(12470).__,a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function SiteName(){var e,t=(0,i.useContext)(s.OnboardingContext),n=t.state,a=t.updateState,r=t.getStateObjectToUpdate,f=(0,u.default)(),m=f.ajaxState,g=f.setAjax,v=(0,i.useState)(null),b=(0,l.default)(v,2),_=b[0],h=b[1],y=(0,i.useState)(n.siteName),C=(0,l.default)(y,2),E=C[0],k=C[1],P="siteName",w="siteLogo",O=(0,c.useNavigate)(),S=(0,i.useRef)(),j={text:o("Next","elementor"),onClick:function onClick(){if(elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),S.current.value!==n.siteName&&""!==S.current.value)g({data:{action:"elementor_update_site_name",data:JSON.stringify({siteName:S.current.value})}});else if(S.current.value===n.siteName){var e=r(n,"steps",P,"completed");a(e),O("onboarding/"+w)}else{var t=r(n,"steps",P,"skipped");a(t),O("onboarding/"+w)}}};"completed"!==n.steps[P]&&(e={text:o("Skip","elementor")});E||(j.className="e-onboarding__button--disabled");return(0,i.useEffect)(function(){var e;if("initial"!==m.status)if("success"===m.status&&null!==(e=m.response)&&void 0!==e&&e.siteNameUpdated){var t=r(n,"steps",P,"completed");t.siteName=S.current.value,a(t),O("onboarding/"+w)}else"error"===m.status&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"site name update"}}),h({type:"error",icon:"eicon-warning",message:o("Sorry, the name wasn't saved. Try again, or skip for now.","elementor")}))},[m.status]),i.default.createElement(d.default,{pageId:P,nextStep:w},i.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Setup.svg",title:o("Now, let's give your site a name.","elementor"),actionButton:j,skipButton:e,noticeState:_},i.default.createElement("p",null,o("This is what your site is called on the WP dashboard, and can be changed later from the general settings - it's not your website's URL.","elementor")),i.default.createElement("input",{className:"e-onboarding__text-input e-onboarding__site-name-input",type:"text",placeholder:"e.g. Eric's Space Shuttles",defaultValue:n.siteName||"",ref:S,onChange:function onChange(e){return k(e.target.value)}})))};var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=a(n(18821)),s=n(79871),c=n(83040),u=a(n(73921)),d=a(n(12720)),p=a(n(42036))},32746:(e,t,n)=>{var o=n(12470).__,a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function UploadAndInstallPro(){(0,c.default)({title:o("Upload and Install Elementor Pro","elementor")});var e=(0,i.useContext)(f.OnboardingContext).state,t=(0,s.default)(),n=t.ajaxState,a=t.setAjax,r=(0,i.useState)(null),g=(0,l.default)(r,2),v=g[0],b=g[1],_=(0,i.useState)(!1),h=(0,l.default)(_,2),y=h[0],C=h[1],E=(0,i.useState)(),k=(0,l.default)(E,2),P=k[0],w=k[1],O=(0,i.useCallback)(function(e){C(!0),a({data:{action:"elementor_upload_and_install_pro",fileToUpload:e}})},[]),S=function setErrorNotice(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"upload",o=(null==t?void 0:t.message)||"That didn't work. Try uploading your file again.";elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,action_state:"failure",action:n+" pro",source:P}}),b({type:"error",icon:"eicon-warning",message:o})};(0,i.useEffect)(function(){var t;"initial"!==n.status&&(C(!1),"success"===n.status&&null!==(t=n.response)&&void 0!==t&&t.elementorProInstalled?(elementorCommon.events.dispatchEvent({event:"pro uploaded",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,source:P}}),opener&&opener!==window&&(opener.jQuery("body").trigger("elementor/upload-and-install-pro/success"),window.close(),opener.focus())):"error"===n.status&&S("install"))},[n.status]);if(y)return i.default.createElement(m.default,{loadingText:o("Uploading","elementor")});return i.default.createElement("div",{className:"eps-app e-onboarding__upload-pro"},i.default.createElement(u.default,null,i.default.createElement(d.default,{className:"e-onboarding__upload-pro-drop-zone",onFileSelect:function onFileSelect(e,t,n){w(n),O(e)},onError:function onError(e){return S(e,"upload")},filetypes:["zip"],buttonColor:"cta",buttonVariant:"contained",heading:o("Import your Elementor Pro plugin file","elementor"),text:o("Drag & Drop your .zip file here","elementor"),secondaryText:o("or","elementor"),buttonText:o("Browse","elementor")}),v&&i.default.createElement(p.default,{noticeState:v}),i.default.createElement("div",{className:"e-onboarding__upload-pro-get-file"},o("Don't know where to get the file from?","elementor")+" ",i.default.createElement("a",{onClick:function onClick(){return function onProUploadHelpLinkClick(){elementorCommon.events.dispatchEvent({event:"pro plugin upload help",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep}})}()},href:"https://my.elementor.com/subscriptions/"+elementorAppConfig.onboarding.utms.downloadPro,target:"_blank"},o("Click here","elementor")))))};var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=a(n(18821)),s=a(n(73921)),c=a(n(80791)),u=a(n(25368)),d=a(n(39970)),p=a(n(45485)),f=n(79871),m=a(n(38832))},34744:(e,t,n)=>{var o=n(62688),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PopoverDialog;var r=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var r,i,l={__proto__:null,default:e};if(null===e||"object"!=a(e)&&"function"!=typeof e)return l;if(r=t?o:n){if(r.has(e))return r.get(e);r.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(r=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?r(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594));function PopoverDialog(e){var t=e.targetRef,n=e.offsetTop,o=e.offsetLeft,a=e.wrapperClass,i=e.trigger,l=e.hideAfter,s=(0,r.useCallback)(function(e){var a=null==t?void 0:t.current;if(a&&e){var r=function showPopover(){e.style.display="block",e.setAttribute("aria-expanded",!0);var t=a.getBoundingClientRect(),r=e.getBoundingClientRect(),i=r.width-t.width;e.style.top=t.bottom+n+"px",e.style.left=t.left-i/2-o+"px",e.style.setProperty("--popover-arrow-offset-end",(r.width-16)/2+"px")},s=function hidePopover(){e.style.display="none",e.setAttribute("aria-expanded",!1)};"hover"===i?function handlePopoverHover(){var t=!0,n=null;a.addEventListener("mouseover",function(){t=!0,r()}),a.addEventListener("mouseleave",function(){n=setTimeout(function(){t&&"block"===e.style.display&&s()},l)}),e.addEventListener("mouseover",function(){t=!1,n&&(clearTimeout(n),n=null)}),e.addEventListener("mouseleave",function(){n=setTimeout(function(){t&&"block"===e.style.display&&s()},l),t=!0})}():"click"===i&&function handlePopoverClick(){var t=!1;a.addEventListener("click",function(e){e.preventDefault(),e.stopPropagation(),t?(s(),t=!1):(r(),t=!0)}),e.addEventListener("click",function(e){e.stopPropagation()}),document.body.addEventListener("click",function(){t&&(s(),t=!1)})}()}},[t]),c="e-app__popover";return a&&(c+=" "+a),r.default.createElement("div",{className:c,ref:s},e.children)}PopoverDialog.propTypes={targetRef:o.oneOfType([o.func,o.shape({current:o.any})]).isRequired,trigger:o.string,direction:o.string,offsetTop:o.oneOfType([o.string,o.number]),offsetLeft:o.oneOfType([o.string,o.number]),wrapperClass:o.string,children:o.any,hideAfter:o.number},PopoverDialog.defaultProps={direction:"bottom",trigger:"hover",offsetTop:10,offsetLeft:0,hideAfter:300}},41157:(e,t,n)=>{var o=n(62688),a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ChecklistItem;var r=a(n(41594));function ChecklistItem(e){return r.default.createElement("li",{className:"e-onboarding__checklist-item"},r.default.createElement("i",{className:"eicon-check-circle"}),e.children)}ChecklistItem.propTypes={children:o.string}},42036:(e,t,n)=>{var o=n(62688),a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PageContentLayout;var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=n(79871),s=a(n(3416)),c=a(n(45485)),u=a(n(19197));function PageContentLayout(e){var t=(0,i.useContext)(l.OnboardingContext).state;return i.default.createElement(i.default.Fragment,null,i.default.createElement(s.default,{container:!0,alignItems:"center",justify:"space-between",className:"e-onboarding__page-content"},i.default.createElement("div",{className:"e-onboarding__page-content-start"},i.default.createElement("h1",{className:"e-onboarding__page-content-section-title"},e.title,e.secondLineTitle&&i.default.createElement(i.default.Fragment,null,i.default.createElement("br",null),e.secondLineTitle)),i.default.createElement("div",{className:"e-onboarding__page-content-section-text"},e.children)),i.default.createElement("div",{className:"e-onboarding__page-content-end"},i.default.createElement("img",{src:e.image,alt:"Information"}))),e.noticeState&&i.default.createElement("div",{className:"e-onboarding__notice-container"},e.noticeState||t.proNotice?function printNotices(){return i.default.createElement(i.default.Fragment,null,e.noticeState&&i.default.createElement(c.default,{noticeState:e.noticeState}),t.proNotice&&i.default.createElement(c.default,{noticeState:t.proNotice}))}():i.default.createElement("div",{className:"e-onboarding__notice-empty-spacer"})),i.default.createElement(u.default,{actionButton:e.actionButton,skipButton:e.skipButton}))}PageContentLayout.propTypes={title:o.string,secondLineTitle:o.string,children:o.any,image:o.string,actionButton:o.object,skipButton:o.object,noticeState:o.any}},45485:(e,t,n)=>{var o=n(62688),a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Notice;var r=a(n(41594));function Notice(e){return r.default.createElement("div",{className:"e-onboarding__notice e-onboarding__notice--".concat(e.noticeState.type)},r.default.createElement("i",{className:e.noticeState.icon}),r.default.createElement("span",{className:"e-onboarding__notice-text"},e.noticeState.message))}Notice.propTypes={noticeState:o.object}},46908:(e,t,n)=>{var o=n(12470).__,a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function Account(){var e,t=(0,i.useContext)(c.OnboardingContext),n=t.state,a=t.updateState,r=t.getStateObjectToUpdate,f=(0,i.useState)(null),m=(0,l.default)(f,2),g=m[0],v=m[1],b=function getNextStep(){if(!n.isHelloThemeActivated)return"hello";return elementorAppConfig.onboarding.experiment?"chooseFeatures":"siteName"}(),_=(0,s.useNavigate)(),h="account",y=(0,i.useRef)(),C=(0,i.useRef)();"completed"!==n.steps[h]&&(e={text:o("Skip","elementor")});var E={};E=n.isLibraryConnected?{firstLine:i.default.createElement(i.default.Fragment,null,o("To get the most out of Elementor, we'll help you take your","elementor")," ",i.default.createElement("br",null)," ",o("first steps:","elementor")),listItems:elementorAppConfig.onboarding.experiment?[o("Set your site's theme","elementor"),o("Choose additional features","elementor"),o("Choose how to start creating","elementor")]:[o("Set your site's theme","elementor"),o("Give your site a name & logo","elementor"),o("Choose how to start creating","elementor")]}:elementorAppConfig.onboarding.experiment?{firstLine:o("Once you connect your Elementor account, you can choose from dozens of professional templates and manage your site with the My Elementor dashboard.","elementor"),listItems:[]}:{firstLine:o("To get the most out of Elementor, we’ll connect your account.","elementor")+" "+o("Then you can:","elementor"),listItems:[o("Choose from countless professional templates","elementor"),o("Manage your site with our handy dashboard","elementor"),o("Take part in the community forum, share & grow together","elementor")]};var k={role:"button"};n.isLibraryConnected?(k.text=o("Let’s do it","elementor"),k.onClick=function(){elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),a(r(n,"steps",h,"completed")),_("onboarding/"+b)}):(k.text=o("Create my account","elementor"),k.href=elementorAppConfig.onboarding.urls.signUp+elementorAppConfig.onboarding.utms.connectCta,k.ref=y,k.onClick=function(){elementorCommon.events.dispatchEvent({event:"create account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:"cta"}})});var P=function connectSuccessCallback(e){var t=r(n,"steps",h,"completed");t.isLibraryConnected=!0,elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=e.kits_access_level||e.access_level||0,elementorCommon.config.library_connect.current_access_tier=e.access_tier,a(t),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"success",action:"connect account"}}),v({type:"success",icon:"eicon-check-circle-o",message:"Alrighty - your account is connected."}),_("onboarding/"+b)};var w=function connectFailureCallback(){elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"connect account"}}),v({type:"error",icon:"eicon-warning",message:o("Oops, the connection failed. Try again.","elementor")}),_("onboarding/"+b)};return i.default.createElement(d.default,{pageId:h,nextStep:b},i.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Account.svg",title:elementorAppConfig.onboarding.experiment?o("You're here!","elementor"):o("You're here! Let's set things up.","elementor"),secondLineTitle:elementorAppConfig.onboarding.experiment?o(" Let's get connected.","elementor"):"",actionButton:k,skipButton:e,noticeState:g},k.ref&&!n.isLibraryConnected&&i.default.createElement(u.default,{buttonRef:k.ref,successCallback:function successCallback(e){return P(e)},errorCallback:w}),i.default.createElement("span",null,E.firstLine),i.default.createElement("ul",null,E.listItems.map(function(e,t){return i.default.createElement("li",{key:"listItem"+t},e)}))),!n.isLibraryConnected&&i.default.createElement("div",{className:"e-onboarding__footnote"},i.default.createElement("p",null,o("Already have one?","elementor")+" ",i.default.createElement("a",{ref:C,href:elementorAppConfig.onboarding.urls.connect+elementorAppConfig.onboarding.utms.connectCtaLink,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"connect account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement}})}},o("Connect your account","elementor"))),i.default.createElement(u.default,{buttonRef:C,successCallback:P,errorCallback:w})))};var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=a(n(18821)),s=n(83040),c=n(79871),u=a(n(20948)),d=a(n(12720)),p=a(n(42036))},51463:(e,t,n)=>{var o=n(12470).__,a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function HelloTheme(){var e=(0,i.useContext)(s.OnboardingContext),t=e.state,n=e.updateState,a=e.getStateObjectToUpdate,r=(0,u.default)(),f=r.ajaxState,m=r.setAjax,g=(0,i.useState)(!1),v=(0,l.default)(g,2),b=v[0],_=v[1],h=(0,i.useState)(!1),y=(0,l.default)(h,2),C=y[0],E=y[1],k={type:"success",icon:"eicon-check-circle-o",message:o("Your site’s got Hello theme. High-five!","elementor")},P=(0,i.useState)(t.isHelloThemeActivated?k:null),w=(0,l.default)(P,2),O=w[0],S=w[1],j=(0,i.useState)([]),x=(0,l.default)(j,2),N=x[0],A=x[1],T=t.isHelloThemeActivated?o("Next","elementor"):o("Continue with Hello Biz Theme","elementor"),M=(0,i.useState)(T),W=(0,l.default)(M,2),R=W[0],L=W[1],B=(0,c.useNavigate)(),q="hello",I=elementorAppConfig.onboarding.experiment?"chooseFeatures":"siteName",F=function goToNextScreen(){return B("onboarding/"+I)};(0,i.useEffect)(function(){if(!b&&t.isHelloThemeActivated){var e=a(t,"steps",q,"completed");n(e),F()}},[]);var D=function resetScreenContent(){N.forEach(function(e){return clearTimeout(e)}),A([]),E(!1),L(T)},H=(0,i.useCallback)(function(){E(!1),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"success",action:"hello theme activation"}}),S(k),L(o("Next","elementor"));var e=a(t,"steps",q,"completed");e.isHelloThemeActivated=!0,n(e),_(!0),F()},[]),U=function activateHelloTheme(){E(!0),n({isHelloThemeInstalled:!0}),m({data:{action:"elementor_activate_hello_theme"}})},G=function installHelloTheme(){C||E(!0),wp.updates.ajax("install-theme",{slug:"hello-biz",success:function success(){return U()},error:function error(){return function onErrorInstallHelloTheme(){elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"failure",action:"hello theme install"}}),S({type:"error",icon:"eicon-warning",message:o("There was a problem installing Hello Biz Theme.","elementor")}),D()}()}})},z=function sendNextButtonEvent(){elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep}})},J={text:R,role:"button"};C&&(J.className="e-onboarding__button--processing");t.isHelloThemeActivated?J.onClick=function(){z(),F()}:J.onClick=function(){z(),t.isHelloThemeInstalled&&!t.isHelloThemeActivated?U():t.isHelloThemeInstalled?F():G()};var K={};C&&(K.className="e-onboarding__button-skip--disabled");"completed"!==t.steps[q]&&(K.text=o("Skip","elementor"));return(0,i.useEffect)(function(){C&&L(i.default.createElement(i.default.Fragment,null,i.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"})));var e=[],t=setTimeout(function(){C&&L(i.default.createElement(i.default.Fragment,null,i.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"}),i.default.createElement("span",{className:"e-onboarding__action-button-text"},o("Hold on, this can take a minute...","elementor"))))},4e3);e.push(t);var n=setTimeout(function(){C&&L(i.default.createElement(i.default.Fragment,null,i.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"}),i.default.createElement("span",{className:"e-onboarding__action-button-text"},o("Okay, now we're really close...","elementor"))))},3e4);e.push(n),A(e)},[C]),(0,i.useEffect)(function(){var e;"initial"!==f.status&&("success"===f.status&&null!==(e=f.response)&&void 0!==e&&e.helloThemeActivated?H():"error"===f.status&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"failure",action:"hello theme activation"}}),S({type:"error",icon:"eicon-warning",message:o("There was a problem activating Hello Biz Theme.","elementor")}),D()))},[f.status]),i.default.createElement(d.default,{pageId:q,nextStep:I},i.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Hello_Biz.svg",title:o("Every site starts with a theme.","elementor"),actionButton:J,skipButton:K,noticeState:O},i.default.createElement("p",null,o("Hello Biz by Elementor helps you launch your professional business website - fast.","elementor")),!elementorAppConfig.onboarding.experiment&&i.default.createElement("p",null,o("Here's why:","elementor")),i.default.createElement("ul",{className:"e-onboarding__feature-list"},i.default.createElement("li",null,o("Get online faster","elementor")),i.default.createElement("li",null,o("Lightweight and fast loading","elementor")),i.default.createElement("li",null,o("Great for SEO","elementor")))),i.default.createElement("div",{className:"e-onboarding__footnote"},"* "+o("You can switch your theme later on","elementor")))};var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=a(n(18821)),s=n(79871),c=n(83040),u=a(n(73921)),d=a(n(12720)),p=a(n(42036))},55723:(e,t,n)=>{var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function App(){return(0,r.useEffect)(function(){var e="eps-theme-dark",t=document.body.classList.contains(e);if(t&&document.body.classList.remove(e),!elementorAppConfig.onboarding.onboardingAlreadyRan){var n=new FormData;n.append("_nonce",elementorCommon.config.ajax.nonce),n.append("action","elementor_update_onboarding_option"),fetch(elementorCommon.config.ajax.url,{method:"POST",body:n})}return elementorAppConfig.return_url=elementorAppConfig.admin_url,function(){t&&document.body.classList.add(e)}},[]),r.default.createElement(s.ContextProvider,null,r.default.createElement(i.LocationProvider,{history:l.default.appHistory},r.default.createElement(i.Router,null,r.default.createElement(c.default,{default:!0}),r.default.createElement(u.default,{path:"hello"}),r.default.createElement(g.default,{path:"chooseFeatures"}),r.default.createElement(d.default,{path:"siteName"}),r.default.createElement(p.default,{path:"siteLogo"}),r.default.createElement(f.default,{path:"goodToGo"}),r.default.createElement(m.default,{path:"uploadAndInstallPro"}))))};var r=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var r,i,l={__proto__:null,default:e};if(null===e||"object"!=a(e)&&"function"!=typeof e)return l;if(r=t?o:n){if(r.has(e))return r.get(e);r.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(r=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?r(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),i=n(83040),l=o(n(47485)),s=n(79871),c=o(n(46908)),u=o(n(51463)),d=o(n(31634)),p=o(n(2078)),f=o(n(98253)),m=o(n(32746)),g=o(n(76024))},61553:(e,t,n)=>{var o=n(12470).__,a=n(62688),r=n(96784),i=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=GoProPopover;var l=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,r,l={__proto__:null,default:e};if(null===e||"object"!=i(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((r=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(r.get||r.set)?a(l,s,r):l[s]=e[s]);return l}(e,t)}(n(41594)),s=n(79871),c=r(n(34744)),u=r(n(64801)),d=r(n(41157)),p=r(n(79997));function GoProPopover(e){var t=(0,l.useContext)(s.OnboardingContext),n=t.state,a=t.updateState,r=(0,l.useCallback)(function(e){e&&e.addEventListener("click",function(t){t.preventDefault(),elementorCommon.events.dispatchEvent({event:"already have pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),window.open(e.href+"&mode=popup","elementorUploadPro","toolbar=no, menubar=no, width=728, height=531, top=100, left=100"),elementorCommon.elements.$body.on("elementor/upload-and-install-pro/success",function(){a({hasPro:!0,proNotice:{type:"success",icon:"eicon-check-circle-o",message:o("Elementor Pro has been successfully installed.","elementor")}})})})},[]),i=e.buttonsConfig.find(function(e){return"go-pro"===e.id}),f={text:elementorAppConfig.onboarding.experiment?o("Upgrade now","elementor"):o("Upgrade Now","elementor"),className:"e-onboarding__go-pro-cta",target:"_blank",href:"https://elementor.com/pro/?utm_source=onboarding-wizard&utm_campaign=gopro&utm_medium=wp-dash&utm_content=top-bar-dropdown&utm_term="+elementorAppConfig.onboarding.onboardingVersion,tabIndex:0,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"get elementor pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})}};return l.default.createElement(c.default,{targetRef:i.elRef,wrapperClass:"e-onboarding__go-pro"},l.default.createElement("div",{className:"e-onboarding__go-pro-content"},l.default.createElement("h2",{className:"e-onboarding__go-pro-title"},o("Ready to Get Elementor Pro?","elementor")),l.default.createElement(u.default,null,l.default.createElement(d.default,null,o("90+ Basic & Pro widgets","elementor")),l.default.createElement(d.default,null,o("300+ Basic & Pro templates","elementor")),l.default.createElement(d.default,null,o("Premium Support","elementor"))),l.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},o("And so much more!","elementor")),l.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},l.default.createElement(p.default,{buttonSettings:f})),l.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},l.default.createElement("a",{tabIndex:"0",className:"e-onboarding__go-pro-already-have",ref:r,href:elementorAppConfig.onboarding.urls.uploadPro,rel:"opener"},o("Already have Elementor Pro?","elementor")))))}GoProPopover.propTypes={buttonsConfig:a.array.isRequired}},63747:(e,t,n)=>{var o=n(12470).__,a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.setSelectedFeatureList=t.options=void 0;var r=a(n(10906)),i=a(n(85707));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){(0,i.default)(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}t.options=[{plan:"essential",text:o("Templates & Theme Builder","elementor")},{plan:"advanced",text:o("WooCommerce Builder","elementor")},{plan:"essential",text:o("Lead Collection & Form Builder","elementor")},{plan:"essential",text:o("Dynamic Content","elementor")},{plan:"advanced",text:o("Popup Builder","elementor")},{plan:"advanced",text:o("Custom Code & CSS","elementor")},{plan:"essential",text:o("Motion Effects & Animations","elementor")},{plan:"advanced",text:o("Notes & Collaboration","elementor")}],t.setSelectedFeatureList=function setSelectedFeatureList(e){var t=e.checked,n=e.id,o=e.text,a=e.selectedFeatures,l=e.setSelectedFeatures,s=n.split("-")[0];l(_objectSpread(_objectSpread({},a),{},t?(0,i.default)({},s,[].concat((0,r.default)(a[s]),[o])):(0,i.default)({},s,a[s].filter(function(e){return e!==o}))))}},64801:(e,t,n)=>{var o=n(62688),a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Checklist;var r=a(n(41594));function Checklist(e){return r.default.createElement("ul",{className:"e-onboarding__checklist"},e.children)}Checklist.propTypes={children:o.any.isRequired}},67634:(e,t,n)=>{var o=n(12470).__,a=n(62688),r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Message;var i=r(n(41594)),l=r(n(18821));function Message(e){var t=e.tier,n=o("Based on the features you chose, we recommend the %s plan, or higher","elementor").split("%s"),a=(0,l.default)(n,2),r=a[0],s=a[1];return i.default.createElement(i.default.Fragment,null,r,i.default.createElement("strong",null,t),s)}Message.propTypes={tier:a.string.isRequired}},70129:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useButtonAction(e,t){var n=(0,o.useContext)(a.OnboardingContext),i=n.state,l=n.updateState,s=n.getStateObjectToUpdate,c=(0,r.useNavigate)();return{state:i,handleAction:function handleAction(n){var o=s(i,"steps",e,n);l(o),c("onboarding/"+t)}}};var o=n(41594),a=n(79871),r=n(83040)},71248:(e,t,n)=>{var o=n(62688),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ProgressBarItem;var r=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var r,i,l={__proto__:null,default:e};if(null===e||"object"!=a(e)&&"function"!=typeof e)return l;if(r=t?o:n){if(r.has(e))return r.get(e);r.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(r=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?r(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),i=n(79871);function ProgressBarItem(e){var t=(0,r.useContext)(i.OnboardingContext).state,n="completed"===t.steps[e.id],o="skipped"===t.steps[e.id],a="e-onboarding__progress-bar-item";return e.id===t.currentStep?a+=" e-onboarding__progress-bar-item--active":n?a+=" e-onboarding__progress-bar-item--completed":o&&(a+=" e-onboarding__progress-bar-item--skipped"),r.default.createElement("div",{onClick:e.onClick,className:a},r.default.createElement("div",{className:"e-onboarding__progress-bar-item-icon"},n?r.default.createElement("i",{className:"eicon-check"}):e.index+1),e.title)}ProgressBarItem.propTypes={index:o.number.isRequired,id:o.string.isRequired,title:o.string.isRequired,route:o.string,onClick:o.func}},76024:(e,t,n)=>{var o=n(12470).__,a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function ChooseFeatures(){var e,t=(0,s.default)().setAjax,n={advanced:o("Advanced","elementor"),essential:o("Essential","elementor")},a=(0,i.useState)({essential:[],advanced:[]}),r=(0,l.default)(a,2),m=r[0],g=r[1],v=(0,i.useState)(n.essential),b=(0,l.default)(v,2),_=b[0],h=b[1],y="chooseFeatures",C="goodToGo",E=(0,f.default)(y,C),k=E.state,P=E.handleAction,w={text:o("Upgrade Now","elementor"),href:elementorAppConfig.onboarding.urls.upgrade,target:"_blank",onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:k.currentStep}}),t({data:{action:"elementor_save_onboarding_features",data:JSON.stringify({features:m})}}),P("completed")}};"completed"!==k.steps[y]&&(e={text:o("Skip","elementor"),action:function action(){t({data:{action:"elementor_save_onboarding_features",data:JSON.stringify({features:m})}}),P("skipped")}});isFeatureSelected(m)||(w.className="e-onboarding__button--disabled");function isFeatureSelected(e){return!!e.advanced.length||!!e.essential.length}return(0,i.useEffect)(function(){m.advanced.length>0?h(n.advanced):h(n.essential)},[m]),i.default.createElement(d.default,{pageId:y,nextStep:C},i.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Setup.svg",title:o("Elevate your website with additional Pro features.","elementor"),actionButton:w,skipButton:e},i.default.createElement("p",null,o("Which Elementor Pro features do you need to bring your creative vision to life?","elementor")),i.default.createElement("form",{className:"e-onboarding__choose-features-section"},u.options.map(function(e,t){var n="".concat(e.plan,"-").concat(t);return i.default.createElement("label",{key:n,className:"e-onboarding__choose-features-section__label",htmlFor:n},i.default.createElement("input",{className:"e-onboarding__choose-features-section__checkbox",type:"checkbox",onChange:function onChange(t){return(0,u.setSelectedFeatureList)({checked:t.currentTarget.checked,id:t.target.value,text:e.text,selectedFeatures:m,setSelectedFeatures:g})},id:n,value:n}),e.text)})),i.default.createElement("p",{className:"e-onboarding__choose-features-section__message"},isFeatureSelected(m)&&i.default.createElement(c.default,{tier:_}))))};var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=a(n(18821)),s=a(n(73921)),c=a(n(67634)),u=n(63747),d=a(n(12720)),p=a(n(42036)),f=a(n(70129))},79871:(e,t,n)=>{var o=n(62688),a=n(96784),r=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.ContextProvider=ContextProvider,t.OnboardingContext=void 0;var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return l;if(a=t?o:n){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(n(41594)),l=a(n(85707)),s=a(n(18821));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){(0,l.default)(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var c=t.OnboardingContext=(0,i.createContext)({});function ContextProvider(e){var t=elementorAppConfig.onboarding,n={hasPro:elementorAppConfig.hasPro,isLibraryConnected:t.isLibraryConnected,isHelloThemeInstalled:t.helloInstalled,isHelloThemeActivated:t.helloActivated,siteName:t.siteName,siteLogo:t.siteLogo,proNotice:"",currentStep:"",nextStep:"",steps:{account:!1,hello:!1,chooseFeatures:!1,siteName:!1,siteLogo:!1,goodToGo:!1}},o=(0,i.useState)(n),a=(0,s.default)(o,2),r=a[0],l=a[1],u=(0,i.useCallback)(function(e){l(function(t){return _objectSpread(_objectSpread({},t),e)})},[l]);return i.default.createElement(c.Provider,{value:{state:r,setState:l,updateState:u,getStateObjectToUpdate:function getStateObjectToUpdate(e,t,n,o){var a=JSON.parse(JSON.stringify(e));return a[t][n]=o,a}}},e.children)}ContextProvider.propTypes={children:o.any}},79997:(e,t,n)=>{var o=n(62688),a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Button;var r=a(n(41594));function Button(e){var t=e.buttonSettings,n=e.type,o="e-onboarding__button";return n&&(o+=" e-onboarding__button-".concat(n)),t.className?t.className+=" "+o:t.className=o,t.href?r.default.createElement("a",t,t.text):r.default.createElement("div",t,t.text)}Button.propTypes={buttonSettings:o.object.isRequired,type:o.string}},92071:(e,t,n)=>{var o=n(62688),a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Card;var r=a(n(41594));function Card(e){var t=e.image,n=e.imageAlt,o=e.text,a=e.link,i=e.name,l=e.clickAction,s=e.target,c=void 0===s?"_self":s;return r.default.createElement("a",{target:c,className:"e-onboarding__card",href:a,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"starting canvas click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,selection:i}}),l&&l()}},r.default.createElement("img",{className:"e-onboarding__card-image",src:t,alt:n}),r.default.createElement("div",{className:"e-onboarding__card-text"},o))}Card.propTypes={image:o.string.isRequired,imageAlt:o.string.isRequired,text:o.string.isRequired,link:o.string.isRequired,name:o.string.isRequired,clickAction:o.func,target:o.string}},98253:(e,t,n)=>{var o=n(12470).__,a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function GoodToGo(){var e={text:o("Skip","elementor"),href:elementorAppConfig.onboarding.urls.createNewPage},t=elementorAppConfig.onboarding.urls.kitLibrary+"&referrer=onboarding";return r.default.createElement(s.default,{pageId:"goodToGo"},r.default.createElement("h1",{className:"e-onboarding__page-content-section-title"},elementorAppConfig.onboarding.experiment?o("Welcome aboard! What's next?","elementor"):o("That's a wrap! What's next?","elementor")),r.default.createElement("div",{className:"e-onboarding__page-content-section-text"},o("There are three ways to get started with Elementor:","elementor")),r.default.createElement(l.default,{container:!0,alignItems:"center",justify:"space-between",className:"e-onboarding__cards-grid e-onboarding__page-content"},r.default.createElement(c.default,{name:"blank",image:elementorCommon.config.urls.assets+"images/app/onboarding/Blank_Canvas.svg",imageAlt:o("Click here to create a new page and open it in Elementor Editor","elementor"),text:o("Edit a blank canvas with the Elementor Editor","elementor"),link:elementorAppConfig.onboarding.urls.createNewPage}),r.default.createElement(c.default,{name:"template",image:elementorCommon.config.urls.assets+"images/app/onboarding/Library.svg",imageAlt:o("Click here to go to Elementor's Website Templates","elementor"),text:o("Choose a professionally-designed template or import your own","elementor"),link:t,clickAction:function clickAction(){location.href=t,location.reload()}}),r.default.createElement(c.default,{name:"site-planner",image:elementorCommon.config.urls.assets+"images/app/onboarding/Site_Planner.svg",imageAlt:o("Click here to go to Elementor's Site Planner","elementor"),text:o("Create a professional site in minutes using AI","elementor"),link:elementorAppConfig.onboarding.urls.sitePlanner,target:"_blank"})),r.default.createElement(u.default,{skipButton:_objectSpread(_objectSpread({},e),{},{target:"_self"}),className:"e-onboarding__good-to-go-footer"}))};var r=a(n(41594)),i=a(n(85707)),l=a(n(3416)),s=a(n(12720)),c=a(n(92071)),u=a(n(19197));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){(0,i.default)(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}}}]);