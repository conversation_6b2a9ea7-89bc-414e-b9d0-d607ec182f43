__( 'Unexpected response from server', 'elementor' );
__( 'Unexpected response from server', 'elementor' );
__( 'Give your variable a name.', 'elementor' );
__( 'Add a value to complete your variable.', 'elementor' );
__( 'Use letters, numbers, dashes (-), or underscores (_) for the name.', 'elementor' );
__( 'Names have to include at least one non-special character.', 'elementor' );
__( 'Keep names up to 50 characters.', 'elementor' );
__( 'This variable name already exists. Please choose a unique name.', 'elementor' );
__( 'There was a glitch. Try saving your variable again.', 'elementor' );
__( 'Missing variable', 'elementor' );
__( 'Variables', 'elementor' );
__( 'Variables', 'elementor' );
__( 'Variables', 'elementor' );
__( 'Search', 'elementor' );
__( 'Create your first font variable', 'elementor' );
__( 'Restore variable', 'elementor' );
__( 'Restore', 'elementor' );
__( 'Delete', 'elementor' );
__( 'Go Back', 'elementor' );
__( 'Edit variable', 'elementor' );
__( 'Save', 'elementor' );
__( 'Go Back', 'elementor' );
__( 'Create variable', 'elementor' );
__( 'Create', 'elementor' );
__( 'Variables', 'elementor' );
__( 'Search', 'elementor' );
__( 'Create your first color variable', 'elementor' );
__( 'Restore variable', 'elementor' );
__( 'Restore', 'elementor' );
__( 'Delete', 'elementor' );
__( 'Edit variable', 'elementor' );
__( 'Go Back', 'elementor' );
__( 'Save', 'elementor' );
__( 'Go Back', 'elementor' );
__( 'Create variable', 'elementor' );
__( 'Create', 'elementor' );
__( 'Create your first variable', 'elementor' );
__(
							'Variables are saved attributes that you can apply anywhere on your site.',
							'elementor'
						);
__( 'Create a variable', 'elementor' );
__( 'There are no variables', 'elementor' );
__( 'With your current role, you can only connect and detach variables.', 'elementor' );
__( 'Sorry, nothing matched', 'elementor' );
__( 'Try something else.', 'elementor' );
__( 'Clear & try again', 'elementor' );
__( 'Clear', 'elementor' );
__( 'This variable is missing', 'elementor' );
__(
					'It may have been deleted. Try clearing this field and select a different value or variable.',
					'elementor'
				);
__( 'Edit', 'elementor' );
__( 'Unlink', 'elementor' );
__( 'Restore', 'elementor' );
__( 'Deleted variable', 'elementor' );
__( 'The variable', 'elementor' );
__(
					'has been deleted, but it is still referenced in this location. You may restore the variable or unlink it to assign a different value.',
					'elementor'
				);
__( 'Delete this variable?', 'elementor' );
__( 'All elements using', 'elementor' );
__( 'will keep their current values, but the variable itself will be removed.', 'elementor' );
__( 'Not now', 'elementor' );
__( 'Delete', 'elementor' );
__( 'Name', 'elementor' );
__( 'Value', 'elementor' );
__( 'Value', 'elementor' );
__( 'deleted', 'elementor' );
__( 'Unlink', 'elementor' );
__( 'Missing variable', 'elementor' );