/*! elementor - v3.31.0 - 09-09-2025 */
"use strict";(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[5352],{347:(t,r,o)=>{var a=o(96784),l=o(10564);Object.defineProperty(r,"__esModule",{value:!0}),r.useSettings=r.SettingsProvider=void 0;var i=function _interopRequireWildcard(t,r){if("function"==typeof WeakMap)var o=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;var i,u,c={__proto__:null,default:t};if(null===t||"object"!=l(t)&&"function"!=typeof t)return c;if(i=r?a:o){if(i.has(t))return i.get(t);i.set(t,c)}for(var d in t)"default"!==d&&{}.hasOwnProperty.call(t,d)&&((u=(i=Object.defineProperty)&&Object.getOwnPropertyDescriptor(t,d))&&(u.get||u.set)?i(c,d,u):c[d]=t[d]);return c}(t,r)}(o(41594)),u=a(o(78304)),c=a(o(10906)),d=a(o(85707)),p=a(o(18821)),y=a(o(83515));function ownKeys(t,r){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),o.push.apply(o,a)}return o}function _objectSpread(t){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(o),!0).forEach(function(r){(0,d.default)(t,r,o[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(o,r))})}return t}function _createForOfIteratorHelper(t,r){var o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=function _unsupportedIterableToArray(t,r){if(t){if("string"==typeof t)return _arrayLikeToArray(t,r);var o={}.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_arrayLikeToArray(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){o&&(t=o);var a=0,l=function F(){};return{s:l,n:function n(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function e(t){throw t},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,u=!0,c=!1;return{s:function s(){o=o.call(t)},n:function n(){var t=o.next();return u=t.done,t},e:function e(t){c=!0,i=t},f:function f(){try{u||null==o.return||o.return()}finally{if(c)throw i}}}}function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,a=Array(r);o<r;o++)a[o]=t[o];return a}var v=(0,i.createContext)(null);r.useSettings=function useSettings(){return(0,i.useContext)(v)},r.SettingsProvider=function SettingsProvider(t){var r=(0,i.useState)("idle"),o=(0,p.default)(r,2),a=o[0],l=o[1],d=(0,i.useState)(new Map),m=(0,p.default)(d,2),g=m[0],b=m[1],_=(0,i.useRef)(g),h=function setSettings(t){_.current=t,b(t)};(0,i.useEffect)(function(){l("loaded")},[g]);var w=(0,i.useCallback)(function(t){switch(t.detail.command){case"document/elements/settings":x(t.detail.args);break;case"document/repeater/insert":O(t.detail.args);break;case"document/repeater/remove":P(t.detail.args)}},[]),x=(0,y.default)(function(t){var r,o=t.container.model.attributes.name,a=new Map(_.current),l=_createForOfIteratorHelper(a.entries());try{for(l.s();!(r=l.n()).done;){var i=(0,p.default)(r.value,2),u=i[0],c=i[1];if(c.has(o))if(Array.isArray(c.get(o))){var d=c.get(o).findIndex(function(r){return r._id===t.container.id});if(-1===d)return;a.get(u).get(o)[d]=_objectSpread(_objectSpread({},c.get(o)[d]),t.settings)}else a.get(u).set(o,t.settings)}}catch(t){l.e(t)}finally{l.f()}h(a)},100),O=function onInsert(t){var r,o=t.name,a=new Map(_.current),l=_createForOfIteratorHelper(a.entries());try{for(l.s();!(r=l.n()).done;){var i,u=(0,p.default)(r.value,2),d=u[0],y=u[1];if(y.has(o)){var v=(0,c.default)(y.get(o)),m=void 0===(null===(i=t.options)||void 0===i?void 0:i.at)?v.length:t.options.at;a.get(d).set(o,[].concat((0,c.default)(v.slice(0,m)),[t.model],(0,c.default)(v.slice(m))))}}}catch(t){l.e(t)}finally{l.f()}h(a)},P=function onRemove(t){var r,o=t.name,a=new Map(_.current),l=_createForOfIteratorHelper(a.entries());try{for(l.s();!(r=l.n()).done;){var i=(0,p.default)(r.value,2),u=i[0],d=i[1];if(d.has(o)){var y=(0,c.default)(d.get(o));a.get(u).set(o,y.filter(function(r,o){return o!==t.index}))}}}catch(t){l.e(t)}finally{l.f()}h(a)};(0,i.useEffect)(function(){return function getInitialSettings(){l("loading");var t=elementor.documents.getCurrent().config.settings.settings,r=new Map([["colors",new Map([["system_colors",t.system_colors],["custom_colors",t.custom_colors]])],["fonts",new Map([["system_typography",t.system_typography],["custom_typography",t.custom_typography],["fallback_font",t.default_generic_fonts]])],["config",new Map([["is_debug",elementorCommon.config.isElementorDebug]])]]);h(r)}(),window.top.addEventListener("elementor/commands/run/after",w,{passive:!0}),function(){window.top.removeEventListener("elementor/commands/run/after",w)}},[]);var j={settings:g,isReady:"loaded"===a};return i.default.createElement(v.Provider,(0,u.default)({value:j},t))}},11096:(t,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.default=function useIntersectionObserver(t){var r,o=[];(0,a.useEffect)(function(){return r=new IntersectionObserver(function(r){var o=r.find(function(t){return t.isIntersecting});o&&t(o)},{}),function(){r.disconnect()}},[]);return{setObservedElements:function setObservedElements(t){!function unobserve(){0!==o.length&&o.forEach(function(t){t&&r.unobserve(t)})}(),o=t,function observe(){0!==o.length&&o.forEach(function(t){t&&r.observe(t)})}()}}};var a=o(41594)},12095:(t,r,o)=>{var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function Loader(){return l.default.createElement("div",{className:"e-styleguide-loader"},l.default.createElement("i",{className:"eicon-loading eicon-animation-spin"}))};var l=a(o(41594))},13979:(t,r,o)=>{var a=o(62688),l=o(96784),i=o(10564);Object.defineProperty(r,"__esModule",{value:!0}),r.default=Section;var u,c,d,p=l(o(98832)),y=l(o(41594)),v=function _interopRequireWildcard(t,r){if("function"==typeof WeakMap)var o=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;var l,u,c={__proto__:null,default:t};if(null===t||"object"!=i(t)&&"function"!=typeof t)return c;if(l=r?a:o){if(l.has(t))return l.get(t);l.set(t,c)}for(var d in t)"default"!==d&&{}.hasOwnProperty.call(t,d)&&((u=(l=Object.defineProperty)&&Object.getOwnPropertyDescriptor(t,d))&&(u.get||u.set)?l(c,d,u):c[d]=t[d]);return c}(t,r)}(o(15142)),m=l(o(92552)),g=l(o(21395)),b=l(o(73099));var _=(0,v.default)(g.default)(u||(u=(0,p.default)(["\n\tmargin-top: 55px;\n"]))),h=(0,v.default)(g.default)(c||(c=(0,p.default)(["\n\tdisplay: flex;\n\twidth: 100%;\n\n\t",";\n"])),function(t){var r=t.flex;return r&&(0,v.css)(d||(d=(0,p.default)(["\n\t\tflex-direction: ",";\n\t\tflex-wrap: ",";\n\t"])),"column"===r?"column":"row","column"===r?"nowrap":"wrap")});function Section(t){var r=t.title,o=t.items,a=t.columns,l=t.component,i=t.type,u=t.flex,c=void 0===u?"row":u;return y.default.createElement(_,null,y.default.createElement(m.default,null,r),y.default.createElement(b.default,null,y.default.createElement(h,{flex:c},o.map(function(t){return y.default.createElement(l,{key:t._id,item:t,type:i||null,columns:a})}))))}Section.propTypes={title:a.string.isRequired,items:a.array.isRequired,columns:a.shape({desktop:a.number,mobile:a.number}),component:a.func.isRequired,type:a.string,flex:a.oneOf(["row","column"])}},17278:(t,r,o)=>{var a=o(96784),l=o(10564);Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.ActiveContext=void 0,r.useActiveContext=function useActiveContext(){return(0,d.useContext)(v)};var i=a(o(78304)),u=a(o(85707)),c=a(o(18821)),d=function _interopRequireWildcard(t,r){if("function"==typeof WeakMap)var o=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;var i,u,c={__proto__:null,default:t};if(null===t||"object"!=l(t)&&"function"!=typeof t)return c;if(i=r?a:o){if(i.has(t))return i.get(t);i.set(t,c)}for(var d in t)"default"!==d&&{}.hasOwnProperty.call(t,d)&&((u=(i=Object.defineProperty)&&Object.getOwnPropertyDescriptor(t,d))&&(u.get||u.set)?i(c,d,u):c[d]=t[d]);return c}(t,r)}(o(41594)),p=o(347),y=a(o(11096));function ownKeys(t,r){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),o.push.apply(o,a)}return o}function _objectSpread(t){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(o),!0).forEach(function(r){(0,u.default)(t,r,o[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(o,r))})}return t}var v=r.ActiveContext=(0,d.createContext)(null);r.default=function ActiveProvider(t){var r=(0,d.useState)({element:"",area:""}),o=(0,c.default)(r,2),a=o[0],l=o[1],u=(0,d.useRef)(null),m=(0,d.useRef)(null),g=(0,p.useSettings)().isReady,b=(0,y.default)(function(t){u.current!==t.target?m.current===t.target&&_("fonts",{scroll:!1}):_("colors",{scroll:!1})}).setObservedElements,_=function activateArea(t){var r=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).scroll;(void 0===r||r)&&h(t),l(function(r){return _objectSpread(_objectSpread({},r),{},{area:t})})},h=function scrollToArea(t){("colors"===t?u:m).current.scrollIntoView({behavior:"smooth",block:"start",inline:"start"})};(0,d.useEffect)(function(){window.top.$e.routes.is("panel/global/global-colors")&&h("colors"),window.top.$e.routes.is("panel/global/global-typography")&&h("fonts")},[]),(0,d.useEffect)(function(){g&&(b([u.current,m.current]),window.top.$e.routes.on("run:after",function(t,r,o){"panel/global/global-typography"===r&&l(function(){return{area:"fonts",element:o.activeControl}}),"panel/global/global-colors"===r&&l(function(){return{area:"colors",element:o.activeControl}})}))},[g]);var w={activeElement:a.element,activeArea:a.area,activateElement:function activateElement(t,r,o){"color"===r&&window.top.$e.route("panel/global/global-colors",{activeControl:"".concat(t,"/").concat(o,"/color")},{history:!1}),"typography"===r&&window.top.$e.route("panel/global/global-typography",{activeControl:"".concat(t,"/").concat(o,"/typography_typography")},{history:!1})},activateArea:_,colorsAreaRef:u,fontsAreaRef:m,getElementControl:function getElementControl(t,r,o){return"color"===r?"".concat(t,"/").concat(o,"/color"):"typography"===r?"".concat(t,"/").concat(o,"/typography_typography"):void 0}};return d.default.createElement(v.Provider,(0,i.default)({value:w},t))}},20300:(t,r,o)=>{var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function App(){return u.default.createElement(d.SettingsProvider,null,u.default.createElement(g.default,null,u.default.createElement(p.default,null,u.default.createElement(y.default,null),u.default.createElement(b,null,u.default.createElement(v.default,null),u.default.createElement(m.default,null)))))};var l,i=a(o(98832)),u=a(o(41594)),c=a(o(15142)),d=o(347),p=a(o(17278)),y=a(o(35927)),v=a(o(58087)),m=a(o(99361)),g=a(o(68709)),b=c.default.div(l||(l=(0,i.default)(["\n\tpadding: 48px 0;\n"])))},21395:(t,r,o)=>{var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=a(o(98832)),u=a(o(15142)).default.div(l||(l=(0,i.default)(["\n\tbox-sizing: border-box;\n\tposition: relative;\n"])));r.default=u},30235:(t,r,o)=>{var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=a(o(98832)),u=a(o(15142)).default.h2(l||(l=(0,i.default)(["\n\tcolor: var(--e-a-color-txt);\n\tfont-family: Roboto, sans-serif;\n\tfont-size: 30px;\n\tfont-weight: 400;\n\ttext-transform: capitalize;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\ttext-align: center;\n\tpadding: 0;\n\tmargin: 0 0 48px 0;\n"])));r.default=u},35927:(t,r,o)=>{var a=o(12470).__,l=o(62688),i=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function Header(){return v.default.createElement(x,null,v.default.createElement(_.default,null,v.default.createElement(P,null,a("Show global settings","elementor")),v.default.createElement(O,null,v.default.createElement(w,{area:"colors"},a("Colors","elementor")),v.default.createElement(w,{area:"fonts"},a("Fonts","elementor")))))};var u,c,d,p,y=i(o(98832)),v=i(o(41594)),m=i(o(15142)),g=o(17278),b=i(o(21395)),_=i(o(73099)),h=m.default.button.attrs(function(t){return{"data-e-active":!!t.isActive||null}})(u||(u=(0,y.default)(["\n\tfont-size: 16px;\n\theight: 100%;\n\tfont-weight: 500;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.5em;\n\tletter-spacing: 0;\n\tcolor: var(--e-a-color-txt);\n\tborder: none;\n\tbackground: none;\n\ttext-transform: capitalize;\n\tfont-family: Roboto, sans-serif;\n\tpadding: 0;\n\n\t&:hover, &[data-e-active='true'], &:focus {\n\t\toutline: none;\n\t\tbackground: none;\n\t\tcolor: var(--e-a-color-txt-accent);\n\t}\n"]))),w=function AreaButton(t){var r=(0,g.useActiveContext)(),o=r.activeArea,a=r.activateArea,l=t.area,i=t.children;return v.default.createElement(h,{variant:"transparent",size:"s",onClick:function onClick(){a(l)},isActive:l===o},i)},x=(0,m.default)(b.default)(c||(c=(0,y.default)(["\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 48px;\n\tdisplay: flex;\n\tbackground: var(--e-a-bg-default);\n\tborder-bottom: 1px solid var(--e-a-border-color-bold);\n\tz-index: 1;\n"]))),O=(0,m.default)(b.default)(d||(d=(0,y.default)(["\n\tdisplay: flex;\n\tjustify-content: flex-end;\n\tflex-grow: 1;\n\tgap: 20px;\n"]))),P=m.default.h2(p||(p=(0,y.default)(["\n\tcolor: var(--e-a-color-txt-accent);\n\tfont-family: Roboto, sans-serif;\n\tfont-size: 16px;\n\tfont-weight: 600;\n\ttext-transform: capitalize;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.2em;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\tmargin: 0;\n"])));w.propTypes={area:l.string.isRequired,children:l.node.isRequired}},58087:(t,r,o)=>{var a=o(12470).__,l=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function ColorsArea(){var t=(0,u.useActiveContext)().colorsAreaRef,r={title:a("Global Colors","elementor"),type:"colors",component:d.default,sections:[{type:"system_colors",title:a("System Colors","elementor"),columns:{desktop:4,mobile:2}},{type:"custom_colors",title:a("Custom Colors","elementor"),columns:{desktop:6,mobile:2}}]};return i.default.createElement(c.default,{ref:t,config:r})};var i=l(o(41594)),u=o(17278),c=l(o(64072)),d=l(o(72981))},64072:(t,r,o)=>{var a=o(62688),l=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var i,u=l(o(98832)),c=l(o(41594)),d=l(o(15142)),p=o(347),y=l(o(12095)),v=l(o(21395)),m=l(o(30235)),g=l(o(13979)),b=(0,d.default)(v.default)(i||(i=(0,u.default)(["\n\twidth: 100%;\n  \tpadding-top: 96px;\n\tmin-height: 100px;\n\n\t@media (max-width: 1024px) {\n      \tpadding-top: 50px;\n\t}\n"]))),_=c.default.forwardRef(function(t,r){var o=t.config,a=(0,p.useSettings)(),l=a.settings,i=a.isReady;return c.default.createElement(b,{ref:r},c.default.createElement(m.default,{name:o.type},o.title),i?c.default.createElement(c.default.Fragment,null,o.sections.map(function(t){var r=l.get(o.type).get(t.type);return r.length?c.default.createElement(g.default,{key:t.type,title:t.title,items:r,columns:t.columns,component:o.component,type:t.type}):null})):c.default.createElement(y.default,null))});_.propTypes={config:a.shape({type:a.string.isRequired,title:a.string.isRequired,sections:a.arrayOf(a.shape({type:a.string.isRequired,title:a.string.isRequired,columns:a.object})).isRequired,component:a.func.isRequired}).isRequired};r.default=_},68709:(t,r,o)=>{var a=o(62688),l=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=AppWrapper;var i=l(o(41594)),u=o(347),c=l(o(12095));function AppWrapper(t){var r=(0,u.useSettings)(),o=r.settings;if(!r.isReady)return i.default.createElement(c.default,null);var a=o.get("config").get("is_debug")?i.default.StrictMode:i.default.Fragment;return i.default.createElement(a,null,t.children)}AppWrapper.propTypes={children:a.oneOfType([a.node,a.arrayOf(a.node)]).isRequired}},72981:(t,r,o)=>{var a=o(62688),l=o(96784),i=o(10564);Object.defineProperty(r,"__esModule",{value:!0}),r.default=Color;var u,c,d=l(o(98832)),p=function _interopRequireWildcard(t,r){if("function"==typeof WeakMap)var o=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;var l,u,c={__proto__:null,default:t};if(null===t||"object"!=i(t)&&"function"!=typeof t)return c;if(l=r?a:o){if(l.has(t))return l.get(t);l.set(t,c)}for(var d in t)"default"!==d&&{}.hasOwnProperty.call(t,d)&&((u=(l=Object.defineProperty)&&Object.getOwnPropertyDescriptor(t,d))&&(u.get||u.set)?l(c,d,u):c[d]=t[d]);return c}(t,r)}(o(41594)),y=l(o(15142)),v=l(o(21395)),m=l(o(83713)),g=l(o(86464)),b=o(17278);var _=(0,y.default)(v.default)(u||(u=(0,d.default)(["\n\tdisplay: flex;\n\twidth: 100%;\n\theight: 100px;\n\tbackground-color: ",";\n\tborder: 1px solid var(--e-a-border-color-focus);\n\tborder-radius: 3px;\n\talign-items: end;\n"])),function(t){return t.hex}),h=y.default.p(c||(c=(0,d.default)(["\n\tcolor: var(--e-a-color-txt-invert);\n\tfont-family: Roboto, sans-serif;\n\theight: 12px;\n\tfont-size: 12px;\n\tfont-weight: 500;\n\ttext-transform: uppercase;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.1em;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\tmargin: 12px;\n"])));function Color(t){var r=(0,b.useActiveContext)(),o=r.activeElement,a=r.activateElement,l=r.getElementControl,i=t.item,u=t.type,c="color",d=i._id,y=i.title,v=i.color,w=l(u,c,d),x=(0,p.useRef)(null);(0,p.useEffect)(function(){w===o&&x.current.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})},[o]);return p.default.createElement(g.default,{columns:t.columns,ref:x,isActive:w===o,onClick:function onClick(){a(u,c,d)}},p.default.createElement(m.default,null,y),p.default.createElement(_,{hex:v},p.default.createElement(h,null,v)))}Color.propTypes={item:a.shape({_id:a.string.isRequired,title:a.string.isRequired,color:a.string}).isRequired,type:a.string.isRequired,columns:a.shape({desktop:a.number,mobile:a.number})}},73099:(t,r,o)=>{var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=a(o(98832)),u=a(o(15142)),c=a(o(21395)),d=(0,u.default)(c.default)(l||(l=(0,i.default)(["\n\tdisplay: flex;\n\talign-items: center;\n\twidth: 100%;\n\tmax-width: 1140px;\n\tmargin: auto;\n\tflex-wrap: wrap;\n\tflex-direction: ",";\n\n\t@media (max-width: 1140px) {\n\t\tpadding: 0 15px;\n\t}\n\n\t@media (max-width: 767px) {\n\t\tpadding: 0 13px;\n\t}\n"])),function(t){var r;return null!==(r=t.flexDirection)&&void 0!==r?r:"row"});r.default=d},83515:(t,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.default=function useDebouncedCallback(t,r){var o=(0,a.useRef)();return(0,a.useCallback)(function(){for(var a=arguments.length,l=new Array(a),i=0;i<a;i++)l[i]=arguments[i];clearTimeout(o.current),o.current=setTimeout(function later(){clearTimeout(o.current),t.apply(void 0,l)},r)},[t,r])};var a=o(41594)},83713:(t,r,o)=>{var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=a(o(98832)),u=a(o(15142)).default.p(l||(l=(0,i.default)(["\n\tcolor: var(--e-a-color-txt);\n\tfont-family: Roboto, sans-serif;\n\tfont-size: 12px;\n\tfont-weight: 500;\n\ttext-transform: capitalize;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.1em;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\tpadding: 0;\n\tmargin: 0;\n"])));r.default=u},86464:(t,r,o)=>{var a=o(62688),l=o(96784),i=o(10564);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u,c,d,p=l(o(78304)),y=l(o(98832)),v=l(o(41594)),m=function _interopRequireWildcard(t,r){if("function"==typeof WeakMap)var o=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;var l,u,c={__proto__:null,default:t};if(null===t||"object"!=i(t)&&"function"!=typeof t)return c;if(l=r?a:o){if(l.has(t))return l.get(t);l.set(t,c)}for(var d in t)"default"!==d&&{}.hasOwnProperty.call(t,d)&&((u=(l=Object.defineProperty)&&Object.getOwnPropertyDescriptor(t,d))&&(u.get||u.set)?l(c,d,u):c[d]=t[d]);return c}(t,r)}(o(15142)),g=l(o(21395));var b=(0,m.default)(g.default)(u||(u=(0,y.default)(["\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 12px;\n\talign-items: flex-start;\n\tborder: 1px solid transparent;\n\tborder-radius: 3px;\n\tpadding: 12px;\n\tcursor: pointer;\n\t","\n\n\t&:hover:not(.active) {\n\t\tbackground-color: var(--e-a-bg-hover);\n\t\tborder-color: var(--e-a-border-color-bold);\n\t}\n\n\t&.active {\n\t\tbackground-color: var(--e-a-bg-active);\n\t\tborder-color: var(--e-a-border-color-accent);\n\t}\n\n\t@media (max-width: 767px) {\n\t\t","\n\t}\n"])),function(t){var r,o=100/(null!==(r=t.columns.desktop)&&void 0!==r?r:1);return(0,m.css)(c||(c=(0,y.default)(["\n\t\t\tflex: 0 0 ","%;\n\t\t"])),o)},function(t){var r,o=100/(null!==(r=t.columns.mobile)&&void 0!==r?r:1);return(0,m.css)(d||(d=(0,y.default)(["\n\t\t\t\tflex: 0 0 ","%;\n\t\t\t"])),o)}),_=v.default.forwardRef(function(t,r){var o=t.isActive,a=t.children;return v.default.createElement(b,(0,p.default)({},t,{ref:r,className:o?"active":""}),a)});r.default=_;_.propTypes={isActive:a.bool,children:a.oneOfType([a.node,a.arrayOf(a.node)])}},92552:(t,r,o)=>{var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=a(o(98832)),u=a(o(15142)).default.h3(l||(l=(0,i.default)(["\n\tpadding: 16px 12px;\n\tborder-style: solid;\n\tborder-width: 0 0 1px 0;\n\tborder-color: var(--e-a-border-color-bold);\n\tcolor: var(--e-a-color-txt);\n\tfont-family: Roboto, sans-serif;\n\tfont-size: 16px;\n\tfont-weight: 500;\n\ttext-transform: capitalize;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.5em;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\tmargin: 0 auto 25px;\n\twidth: 100%;\n\tmax-width: 1140px;\n"])));r.default=u},97741:(t,r,o)=>{var a=o(12470).__,l=o(62688),i=o(96784),u=o(10564);Object.defineProperty(r,"__esModule",{value:!0}),r.default=Font;var c,d,p=i(o(98832)),y=function _interopRequireWildcard(t,r){if("function"==typeof WeakMap)var o=new WeakMap,a=new WeakMap;return function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;var l,i,c={__proto__:null,default:t};if(null===t||"object"!=u(t)&&"function"!=typeof t)return c;if(l=r?a:o){if(l.has(t))return l.get(t);l.set(t,c)}for(var d in t)"default"!==d&&{}.hasOwnProperty.call(t,d)&&((i=(l=Object.defineProperty)&&Object.getOwnPropertyDescriptor(t,d))&&(i.get||i.set)?l(c,d,i):c[d]=t[d]);return c}(t,r)}(o(41594)),v=i(o(15142)),m=o(17278),g=o(347),b=i(o(86464)),_=i(o(83713));var h=(0,v.default)(_.default)(c||(c=(0,p.default)(["\n\tfont-size: 18px;\n"]))),w=v.default.p.withConfig({shouldForwardProp:function shouldForwardProp(t){return"style"!==t}})(d||(d=(0,p.default)(["\n\t",";\n"])),function(t){var r=t.style,o=function styleObjectToString(t){return Object.keys(t).reduce(function(r,o){return r+"".concat(o,": ").concat(t[o],";")},"")};return"\n\t\t\t".concat(o(r.style),"\n\n\t\t\t@media (max-width: 1024px) {\n\t\t\t\t").concat(o(r.tablet),"\n\t\t\t}\n\n\t\t\t@media (max-width: 767px) {\n\t\t\t\t").concat(o(r.mobile),"\n\t\t\t}\n\t\t")}),x=function parseFontToStyle(t,r){var o=function defaultKeyParser(t){return t.replace("typography_","").replace("_","-")},a=r.toLowerCase(),l=function sizeParser(t){return t&&t.size?"".concat(t.size).concat(t.unit):""},i=function defaultParser(t){return t},u={typography_font_family:{valueParser:function familyParser(t){return t?t+", ".concat(a):a},keyParser:o},typography_font_size:{valueParser:l,keyParser:o},typography_letter_spacing:{valueParser:l,keyParser:o},typography_line_height:{valueParser:l,keyParser:o},typography_word_spacing:{valueParser:l,keyParser:o},typography_font_style:{valueParser:i,keyParser:o},typography_font_weight:{valueParser:i,keyParser:o},typography_text_transform:{valueParser:i,keyParser:o},typography_text_decoration:{valueParser:i,keyParser:o}},c=["typography_font_size","typography_letter_spacing","typography_line_height","typography_word_spacing"],d=function reducer(r,o,a){var l=u[o],i=l.keyParser(o),c=o+(a?"_"+a:""),d=l.valueParser(t[c]);return d&&(r[i]=d),r};return{style:Object.keys(u).reduce(function(t,r){return d(t,r,"")},{}),tablet:c.reduce(function(t,r){return d(t,r,"tablet")},{}),mobile:c.reduce(function(t,r){return d(t,r,"mobile")},{})}};function Font(t){var r=(0,m.useActiveContext)(),o=r.activeElement,l=r.activateElement,i=r.getElementControl,u=t.item,c=t.type,d="typography",p=u._id,v=u.title,_=i(c,d,p),O=(0,y.useRef)(null),P=(0,g.useSettings)(),j=P.settings,k=P.isReady,E=(0,y.useMemo)(function(){return k?x(u,j.get("fonts").get("fallback_font")):""},[u,j]);return(0,y.useEffect)(function(){_===o&&O.current.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})},[o]),y.default.createElement(b.default,{columns:t.columns,ref:O,isActive:_===o,onClick:function onClick(){l(c,d,p)}},y.default.createElement(h,null,v),y.default.createElement(w,{style:E},a("The five boxing wizards jump quickly.","elementor")))}Font.propTypes={item:l.shape({_id:l.string.isRequired,title:l.string.isRequired,color:l.string}).isRequired,type:l.string.isRequired,columns:l.shape({desktop:l.number,mobile:l.number})}},99361:(t,r,o)=>{var a=o(12470).__,l=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function FontsArea(){var t=(0,u.useActiveContext)().fontsAreaRef,r={title:a("Global Fonts","elementor"),type:"fonts",component:d.default,sections:[{type:"system_typography",title:a("System Fonts","elementor"),flex:"column",columns:{desktop:1,mobile:1}},{type:"custom_typography",title:a("Custom Fonts","elementor"),flex:"column",columns:{desktop:1,mobile:1}}]};return i.default.createElement(c.default,{ref:t,config:r})};var i=l(o(41594)),u=o(17278),c=l(o(64072)),d=l(o(97741))}}]);