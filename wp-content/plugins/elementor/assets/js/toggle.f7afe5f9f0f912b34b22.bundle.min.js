/*! elementor - v3.31.0 - 09-09-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[775],{3049:(e,t,a)=>{var i=a(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=i(a(5249));class Toggle extends s.default{getDefaultSettings(){return{...super.getDefaultSettings(),showTabFn:"slideDown",hideTabFn:"slideUp",hidePrevious:!1,autoExpand:"editor"}}}t.default=Toggle},5249:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a(6281),a(4846),a(7458),a(6211);class baseTabs extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{tablist:'[role="tablist"]',tabTitle:".elementor-tab-title",tabContent:".elementor-tab-content"},classes:{active:"elementor-active"},showTabFn:"show",hideTabFn:"hide",toggleSelf:!0,hidePrevious:!0,autoExpand:!0,keyDirection:{ArrowLeft:elementorFrontendConfig.is_rtl?1:-1,ArrowUp:-1,ArrowRight:elementorFrontendConfig.is_rtl?-1:1,ArrowDown:1}}}getDefaultElements(){const e=this.getSettings("selectors");return{$tabTitles:this.findElement(e.tabTitle),$tabContents:this.findElement(e.tabContent)}}activateDefaultTab(){const e=this.getSettings();if(!e.autoExpand||"editor"===e.autoExpand&&!this.isEdit)return;const t=this.getEditSettings("activeItemIndex")||1,a={showTabFn:e.showTabFn,hideTabFn:e.hideTabFn};this.setSettings({showTabFn:"show",hideTabFn:"hide"}),this.changeActiveTab(t),this.setSettings(a)}handleKeyboardNavigation(e){const t=e.currentTarget,a=jQuery(t.closest(this.getSettings("selectors").tablist)),i=a.find(this.getSettings("selectors").tabTitle),s="vertical"===a.attr("aria-orientation");switch(e.key){case"ArrowLeft":case"ArrowRight":if(s)return;break;case"ArrowUp":case"ArrowDown":if(!s)return;e.preventDefault();break;case"Home":return e.preventDefault(),void i.first().trigger("focus");case"End":return e.preventDefault(),void i.last().trigger("focus");default:return}const n=t.getAttribute("data-tab")-1,r=this.getSettings("keyDirection")[e.key],o=i[n+r];o?o.focus():-1===n+r?i.last().trigger("focus"):i.first().trigger("focus")}deactivateActiveTab(e){const t=this.getSettings(),a=t.classes.active,i=e?'[data-tab="'+e+'"]':"."+a,s=this.elements.$tabTitles.filter(i),n=this.elements.$tabContents.filter(i);s.add(n).removeClass(a),s.attr({tabindex:"-1","aria-selected":"false","aria-expanded":"false"}),n[t.hideTabFn](),n.attr("hidden","hidden")}activateTab(e){const t=this.getSettings(),a=t.classes.active,i=this.elements.$tabTitles.filter('[data-tab="'+e+'"]'),s=this.elements.$tabContents.filter('[data-tab="'+e+'"]'),n="show"===t.showTabFn?0:400;i.add(s).addClass(a),i.attr({tabindex:"0","aria-selected":"true","aria-expanded":"true"}),s[t.showTabFn](n,()=>elementorFrontend.elements.$window.trigger("elementor-pro/motion-fx/recalc")),s.removeAttr("hidden")}isActiveTab(e){return this.elements.$tabTitles.filter('[data-tab="'+e+'"]').hasClass(this.getSettings("classes.active"))}bindEvents(){this.elements.$tabTitles.on({keydown:e=>{jQuery(e.target).is("a")&&"Enter"===e.key&&e.preventDefault(),["End","Home","ArrowUp","ArrowDown"].includes(e.key)&&this.handleKeyboardNavigation(e)},keyup:e=>{switch(e.code){case"ArrowLeft":case"ArrowRight":this.handleKeyboardNavigation(e);break;case"Enter":case"Space":e.preventDefault(),this.changeActiveTab(e.currentTarget.getAttribute("data-tab"))}},click:e=>{e.preventDefault(),this.changeActiveTab(e.currentTarget.getAttribute("data-tab"))}})}onInit(){super.onInit(...arguments),this.activateDefaultTab()}onEditSettingsChange(e){"activeItemIndex"===e&&this.activateDefaultTab()}changeActiveTab(e){const t=this.isActiveTab(e),a=this.getSettings();!a.toggleSelf&&t||!a.hidePrevious||this.deactivateActiveTab(),!a.hidePrevious&&t&&this.deactivateActiveTab(e),t||this.activateTab(e)}}t.default=baseTabs}}]);