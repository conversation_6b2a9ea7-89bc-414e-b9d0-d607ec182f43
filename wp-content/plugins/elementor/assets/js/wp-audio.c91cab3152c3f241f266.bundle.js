/*! elementor - v3.31.0 - 09-09-2025 */
"use strict";
(self["webpackChunkelementorFrontend"] = self["webpackChunkelementorFrontend"] || []).push([["wp-audio"],{

/***/ "../assets/dev/js/frontend/handlers/wp-audio.js":
/*!******************************************************!*\
  !*** ../assets/dev/js/frontend/handlers/wp-audio.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
class WpAudio extends elementorModules.frontend.handlers.Base {
  onInit() {
    super.onInit();
    window.wp.mediaelement.initialize();
  }
}
exports["default"] = WpAudio;

/***/ })

}]);
//# sourceMappingURL=wp-audio.c91cab3152c3f241f266.bundle.js.map