@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");
body {
  background-color: #f6f7fe;
}

#adminmenuwrap {
  z-index: 9990 !important;
}

#adminmenuback {
  z-index: 9980 !important;
}

.eb-settings-container {
  font-family: "DM Sans", sans-serif;
  margin-right: 30px;
}
.eb-settings-container a {
  color: #2673ff;
}
.eb-settings-container a:hover {
  color: #211c70;
}
.eb-settings-container h1,
.eb-settings-container h2,
.eb-settings-container h3,
.eb-settings-container h4,
.eb-settings-container h5,
.eb-settings-container h6,
.eb-settings-container p {
  margin: 0;
  padding: 0;
}

.eb-admin-block {
  background: #ffffff;
  border-radius: 5px;
  box-shadow: 0px 1px 2px rgba(23, 57, 97, 0.1);
  padding: 30px;
}
.eb-admin-block.block-flex {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
}
.eb-admin-block.block-flex img {
  margin-right: 20px;
}
.eb-admin-block h2 {
  font-style: normal;
  font-weight: 700;
  font-size: 21px;
  line-height: 27px;
  color: #211c70;
}
.eb-admin-block .eb-admin-block__title {
  font-weight: 500;
  font-size: 16px;
  line-height: 1.2rem;
  color: #211c70;
  margin-bottom: 15px;
  display: flex;
}
.eb-admin-block .eb-admin-block__title img {
  margin-right: 10px;
}
.eb-admin-block .eb-admin-block__text {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.2rem;
  color: #6a72a5;
  margin-bottom: 15px;
}
.eb-admin-block .eb-admin-block__link {
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: #2673ff;
  text-decoration: none;
}

.p0 {
  padding: 0 !important;
}

.mb30 {
  margin-bottom: 30px;
}

.mb20 {
  margin-bottom: 20px !important;
}

.mt10 {
  margin-top: 10px;
}

.eb-admin-block-title {
  font-weight: 700;
  font-size: 21px;
  line-height: 27px;
  margin: 0 0 16px 0 !important;
  padding: 0;
  color: #211c70;
}

.eb-admin-checkboxes-group-wrapper .eb-group-title-wrapper {
  margin: 30px 0;
}
.eb-admin-checkboxes-group-wrapper .eb-group-title-wrapper .eb-block-group-title {
  font-weight: 500;
  font-size: 20px;
  line-height: 26px;
  margin: 0 0 15px 0;
  padding: 0;
  color: #211c70;
  border-bottom: 1px solid rgba(23, 57, 97, 0.1);
  padding-bottom: 10px;
  text-transform: capitalize;
}
.eb-admin-checkboxes-group-wrapper .eb-admin-checkboxes-wrapper {
  margin-bottom: 60px;
}

.eb-admin-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 30px;
  margin-bottom: 30px;
}
.eb-admin-grid .eb-admin-inner-grid {
  background-color: #F5F7F9;
  padding: 24px;
  border-radius: 4px;
}
@media all and (max-width: 767px) {
  .eb-admin-grid {
    display: block;
  }
}

@media (min-width: 768px) {
  .eb-admin-grid .eb-col-12 {
    grid-column: span 12;
  }
  .eb-admin-grid .eb-col-8 {
    grid-column: span 8;
  }
  .eb-admin-grid .eb-col-7 {
    grid-column: span 7;
  }
  .eb-admin-grid .eb-col-6 {
    grid-column: span 6;
  }
  .eb-admin-grid .eb-col-5 {
    grid-column: span 5;
  }
  .eb-admin-grid .eb-col-4 {
    grid-column: span 4;
  }
  .eb-admin-grid .eb-col-3 {
    grid-column: span 3;
  }
}
.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}
.embed-responsive::before {
  display: block;
  content: "";
}

.embed-responsive-16by9::before {
  padding-top: 56.25%;
}

.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.eb-btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  padding: 11px 20px;
  font-size: 1rem;
  line-height: 1.5;
  text-decoration: none;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.eb-btn-primary {
  color: #fff !important;
  background-color: #007bff;
  border-color: #007bff;
}
.eb-btn-link {
  font-weight: 400;
  color: #007bff;
  background-color: transparent;
}
.eb-btn-border {
  border: 1px solid #2673ff;
  border-radius: 5px;
  color: #2673ff;
  background-color: transparent;
}
.eb-btn-border:hover {
  color: #fff !important;
  background-color: #2673ff;
  border-color: #2673ff;
}
.eb-btn-sm {
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  padding: 9px 14px;
}
.eb-btn-md {
  font-weight: 500;
  font-size: 15px;
  line-height: 1.2em;
  padding: 9px 14px;
}
.eb-btn-center {
  display: block;
  width: -moz-max-content;
  width: max-content;
  margin: 0 auto !important;
}
.eb-btn-reset {
  margin: 0 15px;
  color: #e41c1c;
  background-color: #fdf2f2;
}
.eb-btn .eb-install-loader {
  width: 18px;
  margin-right: 5px;
}

.eb-form-control {
  margin-bottom: 20px;
}
.eb-form-control label {
  display: inline-block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  /* identical to box height, or 171% */
  color: #211c70;
}
.eb-form-control .eb-input-control {
  display: block;
  width: 100%;
  padding: 11px 15px;
  font-size: 1rem;
  line-height: 1.5;
  color: #6a72a5;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #cfdcf1;
  border-radius: 3px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.eb-form-control .eb-input-control::-moz-placeholder {
  font-weight: 400;
  font-size: 12px;
  line-height: 24px;
  color: #6a72a5;
}
.eb-form-control .eb-input-control::placeholder {
  font-weight: 400;
  font-size: 12px;
  line-height: 24px;
  color: #6a72a5;
}
.eb-form-control .eb-form-radio-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  font-size: 0.85rem;
  line-height: 1.5;
  color: #6a72a5;
}

.mp0 {
  padding: 0;
  margin: 0;
}

.eb-alert-error {
  font-size: 12px;
  line-height: 1.2em;
  color: #ff0000;
  margin-top: 5px;
  display: inline-block;
}

.eb-alert {
  padding: 0.5rem 0.7rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
}
.eb-alert.eb-alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc;
  font-size: 14px;
  line-height: 1.2rem;
}
.eb-alert .eb-alert-warning {
  color: #ff0000;
  font-size: 12px;
  line-height: 1.2rem;
  font-weight: 400;
  text-transform: capitalize;
  display: inline-block;
  margin-top: 5px;
}

@media all and (max-width: 1024px) {
  .eb-block-md {
    display: block;
  }
  .eb-block-md {
    display: block;
  }
  .eb-col-4-md {
    grid-column: span 4 !important;
  }
}
@media all and (max-width: 767px) {
  .eb-block-xs {
    display: block !important;
  }
}
.eb-header-block {
  padding: 20px 30px;
  display: flex;
  align-items: center;
}
@media all and (max-width: 1024px) {
  .eb-header-block {
    flex-wrap: wrap;
  }
}
.eb-header-block .eb-admin-logo-inline {
  position: relative;
  padding-right: 25px;
  margin-right: 20px;
}
@media all and (max-width: 1024px) {
  .eb-header-block .eb-admin-logo-inline {
    order: 0;
    flex-basis: 44%;
    padding-right: 3%;
    margin-right: 3%;
    text-align: right;
  }
}
.eb-header-block .eb-admin-logo-inline::before {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  width: 1px;
  height: 20px;
  background-color: #82aeff;
  transform: translateY(-50%);
}
.eb-header-block .eb-admin-logo-inline img {
  width: 38px;
}
.eb-header-block .eb-header-nav {
  display: flex;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
  gap: 10px;
}
@media all and (max-width: 1024px) {
  .eb-header-block .eb-header-nav {
    order: 2;
    flex-basis: 100%;
    justify-content: center;
    border-top: 1px dashed #d6d8e4;
    padding-top: 15px;
    margin-top: 10px;
  }
}
.eb-header-block .eb-header-nav li {
  margin: 0;
  padding: 0;
}
.eb-header-block .eb-header-nav li:first-child a:hover path,
.eb-header-block .eb-header-nav li:first-child a.eb-nav-active path, .eb-header-block .eb-header-nav li:nth-child(3) a:hover path,
.eb-header-block .eb-header-nav li:nth-child(3) a.eb-nav-active path {
  stroke: #2673ff;
  fill: transparent;
}
.eb-header-block .eb-header-nav a {
  align-items: center;
  border-radius: 10px;
  box-shadow: none;
  box-sizing: border-box;
  color: #6a72a5;
  display: inline-flex;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 21px;
  justify-content: center;
  margin-bottom: 0;
  margin-left: 0px;
  margin-right: 0px;
  min-height: 40px;
  padding: 1px 17px;
  text-align: center;
  text-decoration: none;
  text-transform: capitalize;
  transition: all 0.15s ease-in-out;
}
.eb-header-block .eb-header-nav a:hover, .eb-header-block .eb-header-nav a.eb-nav-active {
  background: #eff4fc;
  color: #2673ff;
  cursor: pointer;
}
.eb-header-block .eb-header-nav a:hover path, .eb-header-block .eb-header-nav a.eb-nav-active path {
  fill: #2673ff;
}
.eb-header-block .eb-header-nav svg {
  margin-right: 8px;
}
.eb-header-block .eb-version {
  margin-left: auto;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  color: #6a72a5;
  display: flex;
  flex-flow: column;
  text-align: right;
  gap: 5px;
}
.eb-header-block .eb-version span {
  background-color: #eff4fc;
  color: #2673ff;
  padding: 1px 5px;
  margin-left: 3px;
  border-radius: 3px;
}
@media all and (max-width: 1024px) {
  .eb-header-block .eb-version {
    order: 1;
    flex-basis: 50%;
  }
}

.eb-templates-intro-block .temlately-logo {
  width: 145px;
  margin-bottom: 15px;
}
.eb-templates-intro-block h2 {
  margin: 0;
  padding: 0;
  font-weight: 700;
  font-size: 30px;
  line-height: 45px;
  color: #211c70;
}
.eb-templates-intro-block h2 span {
  background: linear-gradient(91deg, #f34f8c 50%, #ffb45a 40%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.eb-templates-intro-block ul {
  margin: 15px 0 0 0;
}
.eb-templates-intro-block ul li {
  margin-bottom: 6px;
  display: flex;
  gap: 10px;
  align-items: center;
  margin: 0 0 15px 0;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  /* or 171% */
  color: #211c70;
}
.eb-templates-intro-block ul li svg,
.eb-templates-intro-block ul li img {
  width: 20px;
}
.eb-templates-intro-block .eb-btn {
  margin: 30px 0 0 30px;
}
.eb-templates-intro-block .eb-admin-grid {
  margin-bottom: 0;
}
@media all and (max-width: 767px) {
  .eb-templates-intro-block .eb-admin-video-block {
    margin-top: 30px;
  }
}

.templates-heading-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.templates-heading-wrapper h3 {
  font-weight: 700;
  font-size: 24px;
  line-height: 31px;
  color: #211c70;
  margin: 0 !important;
}

.eb-templates-wrapper {
  grid-template-columns: repeat(4, 1fr);
}
@media all and (max-width: 1024px) {
  .eb-templates-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}

.eb-templates-block {
  background: #ffffff;
  box-shadow: 0px 15px 25px -10px rgba(32, 31, 80, 0.15);
  border-radius: 5px;
  display: block;
  text-align: left;
  transition: all 0.15s ease-in-out;
  position: relative;
}
@media all and (max-width: 767px) {
  .eb-templates-block {
    margin-top: 30px;
  }
}
.eb-templates-block:hover {
  box-shadow: 0px 65px 75px -10px rgba(32, 31, 80, 0.25);
}
.eb-templates-block img {
  width: 100%;
  border-radius: 5px 5px 0 0;
}
.eb-templates-block a {
  text-decoration: none;
}
.eb-templates-block h4 {
  font-weight: 700;
  font-size: 16px;
  line-height: 22px;
  text-decoration: none;
  display: block;
  margin: 15px 15px 0;
  word-break: break-word;
  text-align: left;
  color: #262234;
}
.eb-templates-block .label {
  position: absolute;
  top: 30px;
  right: 0;
  height: 29px;
  padding: 0 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  transition: padding 0.3s ease-in-out 0s;
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  line-height: 1em;
}
.eb-templates-block .label.starter {
  background-color: #5453f4;
}
.eb-templates-block .label.pro {
  background-color: #fa7d8e;
}
.eb-templates-block .eb-templates-meta {
  display: flex;
  justify-content: left;
  padding: 10px 15px 15px;
  gap: 8px;
  flex-wrap: wrap;
}
.eb-templates-block .eb-templates-meta div {
  padding: 6px 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #d5ddec;
  border-radius: 4px;
  color: #262234;
  font-size: 12px;
  font-weight: 500;
  line-height: 1em;
}
.eb-templates-block .eb-templates-meta div svg {
  margin-right: 3px;
}
.eb-templates-block .eb-templates-meta .template-download {
  background-color: #e5eaf9;
  color: #5453f4;
}

.eb-templates-intro-block .temlately-logo {
  width: 145px;
  margin-bottom: 15px;
}
.eb-templates-intro-block h2 {
  margin: 0;
  padding: 0;
  font-weight: 700;
  font-size: 30px;
  line-height: 45px;
  color: #211c70;
}
.eb-templates-intro-block h2 span {
  background: linear-gradient(269deg, #ffb45a 45.3%, #f34f8c 95.28%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.eb-templates-intro-block ul {
  margin: 15px 0 0 0;
}
.eb-templates-intro-block ul li {
  margin-bottom: 6px;
  display: flex;
  gap: 10px;
  align-items: center;
  margin: 0 0 15px 0;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  /* or 171% */
  color: #211c70;
}
.eb-templates-intro-block ul li svg,
.eb-templates-intro-block ul li img {
  width: 20px;
}
.eb-templates-intro-block a {
  margin: 15px 0 0 30px;
}

.templates-heading-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.templates-heading-wrapper h3 {
  font-weight: 700;
  font-size: 24px;
  line-height: 31px;
  color: #211c70;
  margin: 0 0 20px 0;
}

.eb-block-box {
  background: #ffffff;
  box-shadow: 0px 1px 2px rgba(23, 57, 97, 0.1);
  border-radius: 5px;
  padding: 18px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
@media all and (max-width: 767px) {
  .eb-block-box {
    margin-bottom: 20px !important;
  }
}
.eb-block-box .block-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.eb-block-box .block-title .block-icon {
  margin-right: 10px;
  width: 20px;
}
.eb-block-box h4 {
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: #211c70;
  padding: 0;
  margin: 0;
}
.eb-block-box .block-content {
  display: flex;
  align-items: center;
  gap: 5px;
}
.eb-block-box .block-content a {
  background: #f0f2f5;
  border-radius: 5px;
  width: 22px;
  height: 22px;
  text-align: center;
  position: relative;
  transition: all 0.3s ease-in-out 0s;
}
.eb-block-box .block-content a svg {
  width: 14px;
  top: 50%;
  position: relative;
  transform: translateY(-50%);
}
.eb-block-box .block-content a:hover .tooltip-text {
  opacity: 1;
}
.eb-block-box .block-content a .tooltip-text {
  position: absolute;
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
  background: #5e2eff;
  border-radius: 5px;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: #ffffff;
  padding: 3px 8px;
  transition: opacity 0.3s ease;
  opacity: 0;
  z-index: 1;
  width: -moz-max-content;
  width: max-content;
}
.eb-block-box .block-content a .tooltip-text::before {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top: 5px solid #5e2eff;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  content: "";
}
.eb-block-box.eb-block-label {
  position: relative;
  border: 0;
  margin: 0;
  box-shadow: unset;
  padding: 18px 15px;
}
.eb-block-box.eb-block-label.popular::after {
  content: "Popular";
  background: #0064ff;
}
.eb-block-box.eb-block-label.new::after {
  content: "New";
  background: #5e2eff;
}
.eb-block-box.eb-block-label.updated::after {
  content: "Updated";
  background: #828196;
}
.eb-block-box.eb-block-label::after {
  position: absolute;
  top: -12px;
  font-size: 12px;
  color: white;
  left: 15px;
  font-weight: 600;
  border-radius: 4px;
  width: 65px;
  text-align: center;
  height: 22px;
  line-height: 22px;
}
.eb-block-box.pro .eb-pro {
  position: absolute;
  top: -12px;
  font-size: 12px;
  color: white;
  right: 15px;
  font-weight: 600;
  border-radius: 4px;
  width: 41px;
  text-align: center;
  height: 22px;
  line-height: 22px;
  background-color: #f2a80b;
  color: #ffffff;
}

.eb-admin-checkbox-label {
  padding: 0;
  margin-left: 5px;
}
.eb-admin-checkbox-label .rc-switch {
  width: 45px;
  height: 22px;
  line-height: 22px;
  border-radius: 50px;
  background-color: #cfdcf1;
  border-color: #cfdcf1;
}
.eb-admin-checkbox-label .rc-switch.rc-switch-checked {
  background-color: #2673ff;
  border-color: #2673ff;
}
.eb-admin-checkbox-label .rc-switch.rc-switch-checked::after {
  left: 25px;
}
.eb-admin-checkbox-label .rc-switch::after {
  width: 16px;
  height: 16px;
  top: 2px;
  left: 2px;
  box-shadow: 0px 7px 7px rgba(15, 16, 45, 0.15);
}
.eb-admin-checkbox-label .rc-switch:focus {
  box-shadow: none;
}
.eb-admin-checkbox-label .rc-switch .rc-switch-inner {
  display: none;
}

.eb-global-controls-block {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media all and (max-width: 767px) {
  .eb-global-controls-block {
    text-align: center;
  }
}
.eb-global-controls-block h4 {
  font-style: normal;
  font-weight: 500;
  font-size: 22px;
  line-height: 29px;
  margin: 0;
  /* identical to box height */
  color: #211c70;
}
.eb-global-controls-block p {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  margin: 0;
  color: #6a72a5;
}
.eb-global-controls-block .controls {
  display: flex;
  align-items: center;
  gap: 10px;
}
.eb-global-controls-block .controls .switch-status {
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: #a3a9ca;
}

.eb-admin-checkboxes-wrapper,
.eb-admin-checkbox {
  margin: 0;
}

#eb-save-admin-options {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px dashed #c0cbdc;
  text-align: right;
}

@media all and (max-width: 767px) {
  .eb-global-btn-wrapper {
    margin-top: 20px;
  }
}
.eb-global-btn-wrapper button {
  margin-left: 10px;
}
.eb-global-btn-wrapper button.eb-btn-primary:hover {
  border: 1px solid #2673ff;
  border-radius: 5px;
  color: #2673ff !important;
  background-color: transparent;
}

.eb_pro_modal {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.eb_pro_modal .eb_pro_modal_content {
  padding: 40px;
  background-color: #fff;
  text-align: center;
  border-radius: 8px;
  width: 570px;
  position: relative;
}
@media all and (max-width: 767px) {
  .eb_pro_modal .eb_pro_modal_content {
    width: 60%;
  }
}
.eb_pro_modal .eb_pro_modal_content .eb_pro_modal_close {
  position: absolute;
  top: 14px;
  right: 14px;
}
.eb_pro_modal .eb_pro_modal_content .eb_pro_modal_close:hover {
  cursor: pointer;
}
.eb_pro_modal .eb_pro_modal_content .eb_pro_modal_icon {
  width: 100px;
  height: 100px;
  background: #fffaeb;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px;
}
.eb_pro_modal .eb_pro_modal_content h4 {
  color: #211c70;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.5rem;
  margin: 0 0 10px 0;
}
.eb_pro_modal .eb_pro_modal_content p {
  color: #6a72a5;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5rem;
}
.eb_pro_modal .eb_pro_modal_content .eb-btn {
  margin-top: 20px;
}

.eb-option-block {
  display: grid;
}
.eb-option-block .option-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.eb-option-block .option-block-header img {
  height: 32px;
  width: auto;
}
.eb-option-block .option-block-header .block-content {
  display: flex;
  align-items: center;
  gap: 5px;
}
.eb-option-block .option-block-header .block-content a {
  background: #f0f2f5;
  border-radius: 5px;
  width: 30px;
  height: 30px;
  text-align: center;
  position: relative;
  transition: all 0.3s ease-in-out 0s;
  cursor: pointer;
}
.eb-option-block .option-block-header .block-content a svg {
  width: 20px !important;
  top: 50%;
  position: relative;
  transform: translateY(-50%);
}
.eb-option-block .option-block-header .block-content a:hover .tooltip-text {
  opacity: 1;
}
.eb-option-block .option-block-header .block-content a .tooltip-text {
  position: absolute;
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
  background: #5e2eff;
  border-radius: 8px;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: #ffffff;
  padding: 3px 8px;
  transition: opacity 0.3s ease;
  opacity: 0;
  z-index: 1;
  width: -moz-max-content;
  width: max-content;
}
.eb-option-block .option-block-header .block-content a .tooltip-text::before {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top: 5px solid #5e2eff;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  content: "";
}

.regenerate-asset-block {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media all and (max-width: 767px) {
  .regenerate-asset-block .eb-btn {
    margin-top: 20px;
  }
}
.regenerate-asset-block .eb-admin-block__title {
  font-weight: 500;
  font-size: 18px;
  line-height: 30px;
  /* identical to box height, or 167% */
  color: #211c70;
}
.regenerate-asset-block p {
  font-weight: 500;
  font-size: 16px;
  line-height: 30px;
  /* identical to box height, or 188% */
  color: #6a72a5;
  margin: 5px 20px 0 0;
  padding: 0;
}

.option-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 160000;
  display: flex;
  justify-content: center;
  align-items: center;
}
.option-modal .option-modal__inner {
  overflow: hidden;
  position: relative;
  top: 25px;
  left: 0;
  width: 40%;
  padding: 30px;
  background-color: #fff;
  box-shadow: 0px 45px 95px rgba(7, 8, 41, 0.14);
  border-radius: 3px;
}
.option-modal .close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  background: 0 0;
  color: #646970;
  z-index: 1000;
  cursor: pointer;
  outline: 0;
  transition: color 0.1s ease-in-out, background 0.1s ease-in-out;
}
.option-modal .option-modal__title {
  font-weight: 700;
  font-size: 26px;
  line-height: 34px;
  /* identical to box height */
  color: #211c70;
  padding: 0;
  margin: 0 0 5px 0;
}
.option-modal .option-modal__content {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  /* or 171% */
  color: #6a72a5;
  padding: 0;
  margin: 0 0 25px 0;
}

.eb-responsive-form-wrapper .eb-form-control {
  display: flex;
  gap: 10px;
  align-items: center;
}
.eb-responsive-form-wrapper .eb-form-control label {
  min-width: 160px;
  margin-bottom: 0;
}

.eb-integration-block-wrapper {
  height: 100%;
}

.eb-integration-block {
  background: #ffffff;
  box-shadow: 0px 1px 1px rgba(51, 62, 119, 0.12);
  border-radius: 5px;
  text-align: center;
  padding: 30px 20px;
  transition: box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 60px;
}
@media all and (max-width: 767px) {
  .eb-integration-block {
    margin-top: 100px;
  }
}
.eb-integration-block .icon {
  height: 120px;
  width: 120px;
  background: #ffffff;
  box-shadow: 0px -1px 1px rgba(51, 62, 119, 0.06);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin: 0 auto 20px;
  margin-top: -90px;
  overflow: hidden;
}
.eb-integration-block .icon img {
  max-width: 60%;
  max-height: 60%;
}
.eb-integration-block .eb-integration-block__title {
  font-weight: 500;
  font-size: 20px;
  line-height: 1.2rem;
  color: #211c70;
  margin: 0 0 30px 0;
}
.eb-integration-block .eb-integration-block__title img {
  margin-right: 10px;
}
.eb-integration-block .eb-integration-block__text {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.2rem;
  color: #6a72a5;
  margin: 0 0 30px 0;
}
.eb-integration-block .eb-btn-border {
  border-color: #c8d3e9;
  color: #000 !important;
}
.eb-integration-block .eb-btn-border:hover {
  background: transparent;
  color: #000 !important;
}
.eb-integration-block button:disabled {
  cursor: not-allowed;
  pointer-events: all !important;
}

.eb-intro-block h2 {
  font-weight: 700;
  font-size: 21px;
  line-height: 1.2em;
  color: #211c70;
  margin-bottom: 20px;
}

.eb-admin-video-block {
  height: 453px;
}
.eb-admin-video-block .react-player__preview {
  border-radius: 5px;
}
.eb-admin-video-block .react-player__preview img {
  background: #ffffff;
  border-radius: 50px;
  width: 24px;
  height: 24px;
  padding: 20px;
}
.eb-admin-video-block iframe {
  border-radius: 5px;
}

.changelog-block .changelog-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  font-size: 20px;
  line-height: 29px;
  /* identical to box height */
  color: #211c70;
  cursor: pointer;
  display: flex;
  flex-direction: row;
}
.changelog-block .changelog-header h5 {
  font-size: 20px;
  font-weight: 500;
}
.changelog-block .changelog-header .eb-btn-border {
  background: #eff4fc;
  padding: 5px;
}
.changelog-block .changelog-details {
  transition: all 0.5s ease;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}
.changelog-block .changelog-details .changelog-wrapper {
  width: calc(50% - 10px);
  min-width: 350px;
}
.changelog-block .changelog-details .eb_all_changelog_btn {
  width: 100%;
  text-align: center;
}
.changelog-block .changelog-title {
  color: #313131;
  font-size: 16px;
  line-height: 28px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  margin-top: 15px;
  margin-bottom: 10px;
}
.changelog-block .changelog-date {
  margin-left: 10px;
  background: #656363;
  color: #fff;
  padding: 2px 10px;
  line-height: 1;
  font-size: 14px;
  border-radius: 3px;
}
.changelog-block .changelog-content {
  list-style: disc;
  margin-left: 15px;
  color: #888;
  font-size: 15px;
  line-height: 26px;
  font-weight: 400;
  margin-top: 0;
  margin-bottom: 30px;
}
.changelog-block .changelog-content li:not(:last-child) {
  margin-bottom: 12px;
}

.eb-install-button {
  display: flex;
  align-items: center;
  justify-content: center;
}
.eb-install-button .eb-install-loader {
  height: 25px;
  margin-right: 10px;
}

.pro-teaser-block {
  gap: 20px;
  position: relative;
}
.pro-teaser-block .pro-teaser-icon {
  width: 71px;
  height: 71px;
  background: #fffaeb;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 0 0 71px;
}
.pro-teaser-block .pro-teaser-content {
  width: 80%;
}
.pro-teaser-block .pro-teaser-title {
  font-weight: 700;
  font-size: 24px;
  line-height: 1.5rem;
  color: #211c70;
  margin-bottom: 8px;
}
.pro-teaser-block .pro-teaser-description {
  line-height: 1.5rem;
  color: #6a72a5;
  font-weight: 400;
  font-size: 16px;
  margin-bottom: 20px;
  padding-right: 20%;
}
@media all and (max-width: 767px) {
  .pro-teaser-block .pro-teaser-description {
    padding-right: 0;
  }
}
.pro-teaser-block .teaser-box {
  display: flex;
}
@media all and (max-width: 767px) {
  .pro-teaser-block .teaser-box {
    flex-direction: column;
    gap: 20px;
  }
}
.pro-teaser-block .teaser-box img {
  max-width: 100%;
  margin: 0;
}
.pro-teaser-block .teaser-cta {
  display: inline-grid;
  position: absolute;
  bottom: 38px;
  right: 38px;
}
@media all and (max-width: 767px) {
  .pro-teaser-block .teaser-cta {
    position: relative;
    right: 0;
    bottom: 0;
  }
  .pro-teaser-block .teaser-cta img {
    display: none;
  }
}
.pro-teaser-block .teaser-cta img {
  margin: 0 auto 30px 10px;
}

.eb-setting-saved {
  display: flex;
  height: 100vh;
  width: 100vw;
  background: rgba(255, 255, 255, 0.7);
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 1111;
  align-items: center;
  justify-content: center;
}
.eb-setting-saved .eb-message {
  font-size: 16px;
  text-transform: capitalize;
  font-weight: 700;
  color: #2673ff;
}

.eb-write-with-ai .eb-admin-grid {
  margin-bottom: 0;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid > * {
  grid-column: initial;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid input,
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid select {
  border: 1px solid #fff;
  width: 100%;
  max-width: 100%;
  padding: 18px 15px;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid h2 {
  margin: 0 0 10px;
  padding: 0;
  font-weight: 600;
  font-size: 22px;
  line-height: 120%;
  color: #211C70;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid p {
  color: #6A72A5;
  font-size: 14px;
  line-height: 150%;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid .eb-post-types-select {
  width: 100%;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid .eb-post-types-select .eb-select__control {
  border: 0px;
  min-height: 55px;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid .eb-openai-api-key {
  flex-flow: column;
  /* API Key Error Styling */
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid .eb-openai-api-key .eb-input-error {
  border-color: #e74c3c !important;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-inner-grid .eb-openai-api-key .eb-api-key-error {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
  padding: 5px;
  background-color: rgba(231, 76, 60, 0.1);
  border-radius: 3px;
  word-break: break-all;
  width: 95%;
  margin-bottom: 5px;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-action-grid .eb-save-message.success {
  color: #008000;
}
.eb-write-with-ai .eb-admin-grid .eb-admin-action-grid .eb-save-message.error {
  color: #ff0000;
}
.eb-write-with-ai .eb-admin-checkbox-wrapper {
  background: #ffffff;
  box-shadow: 0px 1px 2px rgba(23, 57, 97, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.eb-write-with-ai .eb-admin-checkbox-wrapper .eb-block-box {
  background: transparent;
  box-shadow: none;
  border-radius: 0px;
  border: 0;
}
.eb-write-with-ai .eb-admin-checkbox-wrapper .eb-admin-checkbox-items-wrapper {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}/*# sourceMappingURL=index.css.map */