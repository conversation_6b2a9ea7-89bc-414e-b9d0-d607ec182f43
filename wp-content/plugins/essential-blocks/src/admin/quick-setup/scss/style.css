@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
body {
  background-color: #f6f7fe;
}

#adminmenuwrap {
  z-index: 9990 !important;
}

#adminmenuback {
  z-index: 9980 !important;
}

.eb-admin-checkboxes-group-wrapper .eb-group-title-wrapper {
  margin: 30px 0;
}
.eb-admin-checkboxes-group-wrapper .eb-group-title-wrapper .eb-block-group-title {
  font-weight: 500;
  font-size: 20px;
  line-height: 26px;
  margin: 0 0 15px 0;
  padding: 0;
  color: #211c70;
  border-bottom: 1px solid rgba(23, 57, 97, 0.1);
  padding-bottom: 10px;
  text-transform: capitalize;
}
.eb-admin-checkboxes-group-wrapper .eb-admin-checkboxes-wrapper {
  margin-bottom: 60px;
}
@media all and (max-width: 767px) {
  .eb-admin-checkboxes-group-wrapper .eb-admin-checkboxes-wrapper {
    margin-bottom: 0;
  }
}

.eb-admin-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 30px;
  margin-bottom: 30px;
}
@media all and (max-width: 767px) {
  .eb-admin-grid {
    display: block;
  }
}

@media (min-width: 768px) {
  .eb-admin-grid .eb-col-12 {
    grid-column: span 12;
  }
  .eb-admin-grid .eb-col-8 {
    grid-column: span 8;
  }
  .eb-admin-grid .eb-col-7 {
    grid-column: span 7;
  }
  .eb-admin-grid .eb-col-6 {
    grid-column: span 6;
  }
  .eb-admin-grid .eb-col-5 {
    grid-column: span 5;
  }
  .eb-admin-grid .eb-col-4 {
    grid-column: span 4;
  }
  .eb-admin-grid .eb-col-3 {
    grid-column: span 3;
  }
}
.eb-setup-settings-container {
  font-family: "Inter", sans-serif;
}
.eb-setup-settings-container a {
  color: #2673ff;
}
.eb-setup-settings-container a:hover {
  color: #211c70;
}
.eb-setup-settings-container h1,
.eb-setup-settings-container h2,
.eb-setup-settings-container h3,
.eb-setup-settings-container h4,
.eb-setup-settings-container h5,
.eb-setup-settings-container h6,
.eb-setup-settings-container p {
  margin: 0;
  padding: 0;
}

.eb-flex-row-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.eb-flex-row-between {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.eb-flex-row-end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.eb-flex-column-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.eb-setup-settings-container {
  max-width: 1170px;
  margin: 0 auto;
  padding: 80px 20px 0 0;
}
@media all and (max-width: 1024px) {
  .eb-setup-settings-container {
    padding: 20px 20px 0 0;
  }
}
@media all and (max-width: 768px) {
  .eb-setup-settings-container {
    padding: 20px 10px 0 0;
  }
}

.eb-quick-setup-content {
  box-sizing: border-box;
  background: #FFFFFF;
  border: 1px solid #F0F2F5;
  box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.35);
  border-radius: 8px;
  text-align: center;
  padding: 25px;
}
.eb-quick-setup-content.eb-text-left {
  text-align: left;
}
.eb-quick-setup-content h3 {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.5rem;
  color: #15253E;
}
.eb-quick-setup-content p {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.4rem;
  color: #4C5C7A;
}

.eb-setup-btn {
  padding: 12px 20px;
  gap: 8px;
  border: none;
  border-radius: 4px;
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 120%;
  cursor: pointer;
}
.eb-setup-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.eb-setup-btn svg {
  width: 20px;
  height: 20px;
}
.eb-setup-btn .eb-install-loader {
  width: 16px;
}
.eb-setup-btn.eb-setup-btn-next {
  background: #6C3BFF;
  color: #fff;
}
.eb-setup-btn.eb-setup-btn-next svg g {
  stroke: #fff;
}
.eb-setup-btn.eb-pro-upgrade {
  background: #6C3BFF;
  color: #fff;
  display: inline-block;
  margin-top: 20px;
  text-decoration: none;
}
.eb-setup-btn.eb-pro-upgrade:hover {
  color: #fff;
}
.eb-setup-btn.eb-setup-btn-previous, .eb-setup-btn.eb-setup-btn-secondry {
  background: #FFFFFF;
  border: 1px solid #E0E7F1;
  color: #6C3BFF;
}
.eb-setup-btn.eb-setup-btn-link {
  color: #6C3BFF;
  text-decoration: none;
  padding: 0;
  border-radius: 0;
}

.skip-setup-btn {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.2rem;
  text-decoration-line: underline;
  color: #707E95;
  background: none;
  border: none;
  padding: 0;
  outline: inherit;
  cursor: pointer;
}
.skip-setup-btn.no-underline {
  text-decoration: none;
}

.setup-cta-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  gap: 20px;
  background: linear-gradient(77.15deg, #FDFCFF 30.96%, #EEE1FF 218.05%);
  border: 1px solid #EBE0F8;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 25px;
}
@media all and (max-width: 768px) {
  .setup-cta-section {
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }
  .setup-cta-section .eb-setup-btn,
  .setup-cta-section .eb-setup-cta-btn {
    margin-left: 0 !important;
  }
}
.setup-cta-section img {
  width: 30px;
  padding: 10px;
  background: #FFFFFF;
  border: 0.518868px solid #FFFFFF;
  box-shadow: 0px 0px 1px rgba(31, 38, 50, 0.35);
  border-radius: 8px;
}
.setup-cta-section h3 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 550;
  font-size: 24px;
  line-height: 100%;
  color: #1F242C;
  margin-bottom: 10px;
}
.setup-cta-section h3 span {
  color: #6C3BFF;
  position: relative;
}
.setup-cta-section p {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.2rem;
  color: #4C5C7A;
}
.setup-cta-section .eb-setup-btn,
.setup-cta-section .eb-setup-cta-btn {
  margin-left: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
}

.eb-setup-option-card-wrap {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}
@media all and (max-width: 768px) {
  .eb-setup-option-card-wrap {
    grid-template-columns: repeat(1, 1fr);
  }
}
.eb-setup-option-card-wrap .eb-setup-option-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  background: #FFFFFF;
  border: 1px solid #DCE0E7;
  border-radius: 8px;
}
.eb-setup-option-card-wrap .eb-setup-option-card .option-block-header {
  padding: 21px 24px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid #DCE0E7;
  width: 100%;
  box-sizing: border-box;
  background-color: #F9FBFF;
  border-radius: 8px 8px 0 0;
}
.eb-setup-option-card-wrap .eb-setup-option-card .option-block-header img {
  width: 30px;
  height: 30px;
}
.eb-setup-option-card-wrap .eb-setup-option-card .option-block-header h5 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 110%;
  color: #1D2939;
}
.eb-setup-option-card-wrap .eb-setup-option-card .option-block-content {
  padding: 24px;
}
.eb-setup-option-card-wrap .eb-setup-option-card .option-block-footer {
  border-top: 1px solid #DCE0E7;
  display: flex;
  gap: 10px;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  width: -webkit-fill-available;
  width: -moz-available;
  margin: auto 25px 25px;
  padding-top: 15px;
}
.eb-setup-option-card-wrap .eb-setup-option-card .option-block-footer h5 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 120%;
  color: #1D2939;
}

.option-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 160000;
  display: flex;
  justify-content: center;
  align-items: center;
}
.option-modal.setup-complete-modal .option-modal__inner {
  text-align: center;
  width: 650px;
  padding: 70px 30px;
  box-sizing: border-box;
}
@media all and (max-width: 767px) {
  .option-modal.setup-complete-modal .option-modal__inner {
    padding: 20px;
  }
}
.option-modal.setup-complete-modal .option-modal__inner .option-modal-content svg {
  width: 220px;
  height: 210px;
}
@media all and (max-width: 767px) {
  .option-modal.setup-complete-modal .option-modal__inner .option-modal-content svg {
    width: 120px;
    height: 110px;
  }
}
.option-modal.setup-complete-modal .option-modal__title {
  font-size: 40px;
  line-height: 2rem;
  color: #1D2939;
  margin-top: 20px;
}
@media all and (max-width: 767px) {
  .option-modal.setup-complete-modal .option-modal__title {
    font-size: 30px;
    margin-top: 0;
  }
}
.option-modal.setup-complete-modal .option-modal__content {
  font-size: 16px;
  line-height: 1.5rem;
  color: #475467;
  margin: 20px 0 0 0;
}
.option-modal .option-modal__inner {
  overflow: hidden;
  position: relative;
  top: 25px;
  left: 0;
  width: 40%;
  padding: 30px;
  background-color: #fff;
  box-shadow: 0px 45px 95px rgba(7, 8, 41, 0.14);
  border-radius: 16px;
}
@media all and (max-width: 767px) {
  .option-modal .option-modal__inner {
    width: 70%;
  }
}
.option-modal .close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  background: 0 0;
  color: #646970;
  z-index: 1000;
  cursor: pointer;
  outline: 0;
  transition: color 0.1s ease-in-out, background 0.1s ease-in-out;
}
.option-modal .close-btn svg {
  width: 24px;
  height: 24px;
}
.option-modal .option-modal__title {
  font-weight: 700;
  font-size: 26px;
  line-height: 34px;
  /* identical to box height */
  color: #211c70;
  padding: 0;
  margin: 0 0 5px 0;
}
.option-modal .option-modal__content {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  /* or 171% */
  color: #6a72a5;
  padding: 0;
  margin: 0 0 25px 0;
}

.eb-setup-nav-wrapper {
  padding: 20px 30px;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  background: #FFFFFF;
  border: 1px solid #F0F2F5;
  box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.35);
  border-radius: 8px;
}
@media all and (max-width: 1024px) {
  .eb-setup-nav-wrapper {
    flex-wrap: wrap;
  }
}
.eb-setup-nav-wrapper .eb-setup-nav {
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}
@media all and (max-width: 1024px) {
  .eb-setup-nav-wrapper .eb-setup-nav {
    justify-content: center;
  }
}
.eb-setup-nav-wrapper .eb-setup-nav li {
  margin: 0;
  padding: 0;
  gap: 8px;
}
.eb-setup-nav-wrapper .eb-setup-nav li.active .eb-setup-count {
  color: #15253E;
  border-color: #6C3BFF;
  box-shadow: 0px 0px 0px 3px rgba(108, 59, 255, 0.3);
}
.eb-setup-nav-wrapper .eb-setup-nav li.active .eb-setup-title {
  color: #15253E;
}
.eb-setup-nav-wrapper .eb-setup-nav li.complete .eb-setup-count {
  color: #15253E;
  font-size: 0;
  border-color: #6C3BFF;
  background-color: #6C3BFF;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9IkZyYW1lIiBjbGlwLXBhdGg9InVybCgjY2xpcDBfMjA2MV8xMjc1MikiPgo8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMy43NSA5TDcuNSAxMi43NUwxNSA1LjI1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIuNzUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L2c+CjxkZWZzPgo8Y2xpcFBhdGggaWQ9ImNsaXAwXzIwNjFfMTI3NTIiPgo8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIGZpbGw9IndoaXRlIi8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg==);
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 16px;
}
.eb-setup-nav-wrapper .eb-setup-nav li.complete .eb-setup-title {
  color: #15253E;
}
.eb-setup-nav-wrapper .eb-setup-nav .eb-setup-count {
  border: 1.5px solid #CBD1DA;
  color: #7C8798;
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 28px;
  width: 28px;
  height: 28px;
  display: inline-block;
  border-radius: 50%;
  text-align: center;
}
.eb-setup-nav-wrapper .eb-setup-nav .eb-setup-title {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 16px;
  color: #707E95;
}

.eb-setup-templates .eb-templates-intro-block {
  margin: 45px;
  gap: 30px;
  flex-wrap: wrap;
}
@media all and (max-width: 1024px) {
  .eb-setup-templates .eb-templates-intro-block {
    margin: 0px;
  }
}
@media all and (max-width: 768) {
  .eb-setup-templates .eb-templates-intro-block {
    flex-direction: column;
  }
}
.eb-setup-templates .eb-templates-intro-block > div {
  flex: 0 0 46%;
}
.eb-setup-templates .eb-templates-intro-block h3 {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 32px;
  line-height: 2.5rem;
  color: #1D2939;
  margin-bottom: 15px;
}
.eb-setup-templates .eb-templates-intro-block h3 span {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 32px;
  line-height: 130%;
  color: #6C3BFF;
  position: relative;
}
.eb-setup-templates .eb-templates-intro-block h3 span::after {
  position: absolute;
  bottom: -4px;
  left: 0;
  content: url("data:image/svg+xml,%3Csvg width='108' height='6' viewBox='0 0 108 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 2.01621L107 1C98.767 1 78.1845 3 62.7476 5' stroke='%236C3BFF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.eb-setup-templates .eb-templates-intro-block p {
  font-size: 16px;
  line-height: 1.5rem;
  color: #475467;
  margin-bottom: 25px;
}
.eb-setup-templates .eb-templates-intro-block ul {
  margin: 15px 0 0 0;
}
.eb-setup-templates .eb-templates-intro-block ul li {
  display: flex;
  gap: 10px;
  align-items: center;
  margin: 0;
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 40px;
  color: #1D2939;
}
.eb-setup-templates .eb-templates-intro-block ul li img {
  width: 20px;
}
.eb-setup-templates .eb-templates-intro-block .eb-templates-frame {
  background: linear-gradient(124.05deg, rgba(249, 248, 255, 0.4) 0%, rgba(255, 246, 241, 0.4) 5%, rgba(249, 248, 255, 0.4) 100%);
  border: 1px solid #EBE0F8;
  -webkit-backdrop-filter: blur(49.1192px);
          backdrop-filter: blur(49.1192px);
  border-radius: 10px;
  padding: 13px;
  box-sizing: border-box;
}
.eb-setup-templates .eb-templates-intro-block .eb-templates-frame img {
  width: 100%;
}
.eb-setup-templates .skip-setup-btn {
  margin: 0;
  color: #1F242C;
}

.eb-block-box {
  background: #ffffff;
  box-shadow: 0px 1px 2px rgba(23, 57, 97, 0.1);
  border-radius: 5px;
  padding: 18px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
@media all and (max-width: 767px) {
  .eb-block-box {
    margin-bottom: 20px !important;
  }
}
.eb-block-box .block-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.eb-block-box .block-title .block-icon {
  margin-right: 10px;
  width: 20px;
}
.eb-block-box h4 {
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: #211c70;
  padding: 0;
  margin: 0;
}
.eb-block-box .block-content {
  display: flex;
  align-items: center;
  gap: 5px;
}
.eb-block-box .block-content a {
  background: #f0f2f5;
  border-radius: 5px;
  width: 22px;
  height: 22px;
  text-align: center;
  position: relative;
  transition: all 0.3s ease-in-out 0s;
}
.eb-block-box .block-content a svg {
  width: 14px;
  top: 50%;
  position: relative;
  transform: translateY(-50%);
}
.eb-block-box .block-content a:hover .tooltip-text {
  opacity: 1;
}
.eb-block-box .block-content a .tooltip-text {
  position: absolute;
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
  background: #5e2eff;
  border-radius: 5px;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: #ffffff;
  padding: 3px 8px;
  transition: opacity 0.3s ease;
  opacity: 0;
  z-index: 1;
  width: -moz-max-content;
  width: max-content;
}
.eb-block-box .block-content a .tooltip-text::before {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top: 5px solid #5e2eff;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  content: "";
}
.eb-block-box.eb-block-label {
  position: relative;
  border: 0;
  margin: 0;
  box-shadow: unset;
  padding: 18px 15px;
}
.eb-block-box.eb-block-label.popular::after {
  content: "Popular";
  background: #0064ff;
}
.eb-block-box.eb-block-label.new::after {
  content: "New";
  background: #5e2eff;
}
.eb-block-box.eb-block-label.updated::after {
  content: "Updated";
  background: #828196;
}
.eb-block-box.eb-block-label::after {
  position: absolute;
  top: -12px;
  font-size: 12px;
  color: white;
  left: 15px;
  font-weight: 600;
  border-radius: 4px;
  width: 65px;
  text-align: center;
  height: 22px;
  line-height: 22px;
}
.eb-block-box.pro .eb-pro {
  position: absolute;
  top: -12px;
  font-size: 12px;
  color: white;
  left: 15px;
  font-weight: 600;
  border-radius: 4px;
  width: 41px;
  text-align: center;
  height: 22px;
  line-height: 22px;
  background-color: #f2a80b;
  color: #ffffff;
}

.eb-admin-checkbox-label {
  padding: 0;
  margin-left: 5px;
}
.eb-admin-checkbox-label .rc-switch {
  width: 45px;
  height: 22px;
  line-height: 22px;
  border-radius: 50px;
  background-color: #cfdcf1;
  border-color: #cfdcf1;
}
.eb-admin-checkbox-label .rc-switch.rc-switch-checked {
  background-color: #2673ff;
  border-color: #2673ff;
}
.eb-admin-checkbox-label .rc-switch.rc-switch-checked::after {
  left: 25px;
}
.eb-admin-checkbox-label .rc-switch::after {
  width: 16px;
  height: 16px;
  top: 2px;
  left: 2px;
  box-shadow: 0px 7px 7px rgba(15, 16, 45, 0.15);
}
.eb-admin-checkbox-label .rc-switch:focus {
  box-shadow: none;
}
.eb-admin-checkbox-label .rc-switch .rc-switch-inner {
  display: none;
}

.eb-admin-checkboxes-wrapper,
.eb-admin-checkbox {
  margin: 0;
}

#eb-save-admin-options {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px dashed #c0cbdc;
  text-align: right;
}

.eb_pro_modal {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.eb_pro_modal .eb_pro_modal_content {
  padding: 40px;
  background-color: #fff;
  text-align: center;
  border-radius: 8px;
  width: 570px;
  position: relative;
}
@media all and (max-width: 767px) {
  .eb_pro_modal .eb_pro_modal_content {
    width: 60%;
  }
}
.eb_pro_modal .eb_pro_modal_content .eb_pro_modal_close {
  position: absolute;
  top: 14px;
  right: 14px;
}
.eb_pro_modal .eb_pro_modal_content .eb_pro_modal_close:hover {
  cursor: pointer;
}
.eb_pro_modal .eb_pro_modal_content .eb_pro_modal_icon {
  width: 100px;
  height: 100px;
  background: #fffaeb;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px;
}
.eb_pro_modal .eb_pro_modal_content h4 {
  color: #211c70;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.5rem;
  margin: 0 0 10px 0;
}
.eb_pro_modal .eb_pro_modal_content p {
  color: #6a72a5;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5rem;
}
.eb_pro_modal .eb_pro_modal_content .eb-btn {
  margin-top: 20px;
}

.eb-blocks-content-wrap {
  max-height: 450px;
  overflow-y: scroll;
}
.eb-blocks-content-wrap .eb-admin-grid:first-child {
  display: none;
}
.eb-blocks-content-wrap .eb-group-title-wrapper {
  margin-top: 0;
}
.eb-blocks-content-wrap .eb-block-box {
  background: #FFFFFF;
  border: 1px solid #F0F2F5;
  border-radius: 4px;
}
.eb-blocks-content-wrap .eb-block-box h4 {
  color: #211C70;
}

.eb-setup-optimaization .eb-setup-cta-btn {
  gap: 14px;
  font-weight: 500;
  font-size: 14px;
  line-height: 120%;
  color: #475467;
}

.eb-optimaization-content-wrap > h4 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 550;
  font-size: 20px;
  line-height: 100%;
  color: #1F242C;
  margin-bottom: 15px;
}
.eb-optimaization-content-wrap .eb-setup-option-card-wrap {
  margin-bottom: 30px;
}

.eb-setup-integrations .setup-cta-section h3 {
  font-size: 21px;
}
.eb-setup-integrations .setup-cta-section h3 span::after {
  position: absolute;
  bottom: -5px;
  left: 6px;
  content: url("data:image/svg+xml,%3Csvg width='115' height='6' viewBox='0 0 115 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 2.01562L114 1C106 1 77 2.99941 62 4.99941' stroke='%236C3BFF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.eb-setup-integrations .eb-setup-cta-btn {
  gap: 14px;
  font-weight: 500;
  font-size: 14px;
  line-height: 120%;
  color: #475467;
}
.eb-setup-integrations .eb-setup-btn.eb-setup-btn-next {
  padding: 14px 35px;
}
.eb-setup-integrations .option-block-footer {
  border-top: 1px solid #DCE0E7;
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  gap: 10px;
  box-sizing: border-box;
  width: 100%;
  margin: auto 25px 25px;
  padding-top: 15px;
}
.eb-setup-integrations .option-block-footer .option-block-footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.eb-setup-integrations .option-block-footer .integration-error {
  color: red;
  font-size: 14px;
}
.eb-setup-integrations .rc-switch:not(.eb-custom-disabled).rc-switch-disabled::after {
  background: #fff;
}
.eb-setup-integrations .eb-custom-disabled {
  cursor: pointer;
}
.eb-setup-integrations .eb-custom-disabled.rc-switch-checked {
  background-color: #2673ff !important;
  border-color: #2673ff;
}
.eb-setup-integrations .eb-custom-disabled::after {
  cursor: pointer;
  background: #fff;
}

.eb-integrations-content-wrap {
  max-height: 450px;
  overflow-y: scroll;
}

.get-started-intro-content {
  gap: 25px;
  margin: 60px 20% 40px;
}
@media all and (max-width: 767px) {
  .get-started-intro-content {
    margin: 25px 10% 30px;
  }
}
.get-started-intro-content img {
  width: 240px;
}

p.get-started-footer {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.2rem;
  color: #4C5C7A;
  gap: 5px;
  margin-top: 60px;
}
@media all and (max-width: 767px) {
  p.get-started-footer {
    margin-top: 30px;
    flex-direction: column;
  }
}
p.get-started-footer svg {
  width: 20px;
  height: 20px;
}
p.get-started-footer .get-started-collect-info {
  text-decoration-line: underline;
  color: #707E95;
  cursor: pointer;
}

.collect-modal .option-modal__inner {
  width: 35%;
  border-radius: 16px;
  padding: 0;
}
@media all and (max-width: 767px) {
  .collect-modal .option-modal__inner {
    width: 70%;
  }
}
.collect-modal .close-btn {
  border: none;
  top: 30px;
  right: 30px;
}
.collect-modal .option-modal__title {
  padding: 30px;
  border-bottom: 1px solid #DCE0E7;
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  line-height: 1.6rem;
  color: #15253E;
}
.collect-modal .option-modal__content {
  padding: 30px;
  margin: 0;
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px;
  line-height: 1.5rem;
  color: #4C5C7A;
}

.eb-setting-saved {
  display: flex;
  height: 100vh;
  width: 100vw;
  background: rgba(255, 255, 255, 0.7);
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 1111;
  align-items: center;
  justify-content: center;
}
.eb-setting-saved .eb-message {
  font-size: 16px;
  text-transform: capitalize;
  font-weight: 700;
  color: #2673ff;
}

.configuration-intro-content {
  gap: 25px;
  margin: 60px 20% 40px;
}
@media all and (max-width: 768px) {
  .configuration-intro-content {
    margin: 15px 5% 40px;
  }
}
.configuration-intro-content img {
  width: 30px;
  padding: 10px;
  background: #FFFFFF;
  border: 0.518868px solid #FFFFFF;
  box-shadow: 0px 0px 1px rgba(31, 38, 50, 0.35);
  border-radius: 8px;
}

.eb-configuration-content-wrap {
  gap: 30px;
  width: 80%;
  margin: 0 auto 80px;
}
@media all and (max-width: 768px) {
  .eb-configuration-content-wrap {
    flex-direction: column;
    margin-bottom: 0;
  }
}
.eb-configuration-content-wrap .eb-configuration-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.eb-configuration-content-wrap .eb-configuration-content label {
  background: linear-gradient(309.52deg, #F6F3FF 0%, #FFFEFF 46.38%);
  border: 1px solid #EBE0F8;
  border-radius: 8px;
  text-align: left;
  cursor: pointer;
}
.eb-configuration-content-wrap .eb-configuration-content label.selected {
  border-color: #2673FF;
}
.eb-configuration-content-wrap .eb-configuration-content .eb-configuration-content-title {
  padding: 24px;
  border-bottom: 1px solid #EBE0F8;
}
.eb-configuration-content-wrap .eb-configuration-content .eb-configuration-content-title h4 {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 110%;
  color: #1D2939;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}
.eb-configuration-content-wrap .eb-configuration-content .eb-configuration-content-title h4 span {
  color: #D047DF;
  background: #FDF5FF;
  border: 1px solid #F4E8F7;
  border-radius: 12px;
  padding: 4px 8px;
  font-family: "Inter";
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 110%;
}
.eb-configuration-content-wrap .eb-configuration-content p {
  padding: 24px 24px 50px;
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5rem;
  color: #4C5C7A;
}
@media all and (max-width: 1024px) {
  .eb-configuration-content-wrap .eb-configuration-content p {
    padding: 20px;
  }
}

.step-wrapper {
  margin-top: 20px;
  gap: 20px;
}

.eb-setup-pro .setup-cta-section h3 span::after {
  position: absolute;
  bottom: -5px;
  right: 9px;
  content: url("data:image/svg+xml,%3Csvg width='105' height='6' viewBox='0 0 105 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 2.01621L104 1C96 1 76 3 61 5' stroke='%236C3BFF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.eb-setup-pro .btn-upgrade-pro {
  text-decoration: none;
  display: inline-flex;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.5rem;
  color: #fff;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  padding: 10px 22px;
  background: #FF9437;
  border-radius: 8px;
  border-bottom: 1px solid #ED7206;
  transition: all 0.35s ease-in-out;
  cursor: pointer;
}
.eb-setup-pro .btn-upgrade-pro:hover {
  color: #fff;
}
.eb-setup-pro .eb-pro-content-wrap {
  gap: 10%;
}
@media all and (max-width: 768px) {
  .eb-setup-pro .eb-pro-content-wrap {
    flex-direction: column-reverse;
    gap: 30px;
  }
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-content h3 {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 2.5rem;
  color: #1D2939;
  margin-bottom: 15px;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-content h3 span {
  color: #6C3BFF;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-content p {
  font-size: 16px;
  line-height: 1.5rem;
  color: #475467;
  margin-bottom: 25px;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-content ul {
  margin: 15px 0 0 0;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-content ul li {
  margin: 0;
  font-family: "Inter";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 40px;
  color: #344054;
  position: relative;
  padding: 0 0 0 25px;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-content ul li::before {
  position: absolute;
  left: 0;
  top: 1px;
  content: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 7L6.33333 8.33333L9 5.66667M1 7C1 7.78793 1.15519 8.56815 1.45672 9.2961C1.75825 10.0241 2.20021 10.6855 2.75736 11.2426C3.31451 11.7998 3.97595 12.2417 4.7039 12.5433C5.43185 12.8448 6.21207 13 7 13C7.78793 13 8.56815 12.8448 9.2961 12.5433C10.0241 12.2417 10.6855 11.7998 11.2426 11.2426C11.7998 10.6855 12.2417 10.0241 12.5433 9.2961C12.8448 8.56815 13 7.78793 13 7C13 6.21207 12.8448 5.43185 12.5433 4.7039C12.2417 3.97595 11.7998 3.31451 11.2426 2.75736C10.6855 2.20021 10.0241 1.75825 9.2961 1.45672C8.56815 1.15519 7.78793 1 7 1C6.21207 1 5.43185 1.15519 4.7039 1.45672C3.97595 1.75825 3.31451 2.20021 2.75736 2.75736C2.20021 3.31451 1.75825 3.97595 1.45672 4.7039C1.15519 5.43185 1 6.21207 1 7Z' stroke='%232673FF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-content .eb-setup-btn-link {
  margin-top: 40px;
  font-size: 16px;
  width: -moz-max-content;
  width: max-content;
  border-bottom: 1px solid #6C3BFF;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-content .eb-setup-btn-link:hover {
  color: #000;
  border-bottom-color: #000;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-blocks-wrap {
  background: linear-gradient(124.05deg, #F9F8FF 0%, #FFF6F1 25.45%, #F9F8FF 100%);
  border: 1px solid #F3ECFA;
  border-radius: 9px;
  padding: 40px 25px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 5px;
}
@media all and (max-width: 768px) {
  .eb-setup-pro .eb-pro-content-wrap .eb-pro-blocks-wrap {
    width: 100%;
    box-sizing: border-box;
  }
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-blocks-wrap .eb-pro-block {
  text-decoration: none;
  position: relative;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 15px;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-blocks-wrap .eb-pro-block:hover .tooltip-text {
  opacity: 1;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-blocks-wrap .eb-pro-block img {
  display: block;
  width: 38px;
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-blocks-wrap .eb-pro-block .tooltip-text {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 3px;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: #000;
  padding: 8px;
  transition: opacity 0.3s ease;
  opacity: 0;
  z-index: 1;
  width: -moz-max-content;
  width: max-content;
  box-shadow: 0px 8px 16px -2px rgba(27, 33, 44, 0.1215686275);
}
.eb-setup-pro .eb-pro-content-wrap .eb-pro-blocks-wrap .eb-pro-block .tooltip-text::before {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top: 5px solid #fff;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  content: "";
}/*# sourceMappingURL=style.css.map */