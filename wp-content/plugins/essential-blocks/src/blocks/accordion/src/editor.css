.eb-accordion-container.eb-accordion-type-horizontal .eb-accordion-inner {
  display: block !important;
}
.eb-accordion-container.eb-accordion-type-horizontal .block-editor-block-list__layout {
  position: relative;
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
}
.eb-accordion-container.eb-accordion-type-horizontal .block-editor-block-list__layout .eb-accordion-item {
  position: relative;
  display: flex;
  width: -moz-max-content;
  width: max-content;
}
.eb-accordion-container.eb-accordion-type-horizontal .block-editor-block-list__layout .eb-accordion-item.is-selected, .eb-accordion-container.eb-accordion-type-horizontal .block-editor-block-list__layout .eb-accordion-item.has-child-selected {
  flex-grow: 1;
}
.eb-accordion-container.eb-accordion-type-horizontal .block-editor-block-list__layout .eb-accordion-item .eb-accordion-title-wrapper {
  height: 100%;
  align-items: center;
  justify-content: space-between;
}
.eb-accordion-container.eb-accordion-type-horizontal .block-editor-block-list__layout .eb-accordion-item .eb-accordion-title-content-wrap {
  width: 100%;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  transform-origin: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.eb-accordion-container.eb-accordion-type-horizontal .block-editor-block-list__layout .eb-accordion-item .eb-accordion-title-content-wrap .eb-accordion-title {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
}
.eb-accordion-container.eb-accordion-type-horizontal .eb-accordion-content .block-editor-block-list__layout {
  display: block !important;
}
.eb-accordion-container.eb-accordion-type-horizontal .eb-accordion-wrapper {
  width: 100% !important;
  overflow: hidden;
  transition: flex 0.3s ease;
}
.eb-accordion-container.eb-accordion-type-horizontal .eb-accordion-wrapper.editor-expanded {
  max-width: 100%;
}
.eb-accordion-container.eb-accordion-type-horizontal .eb-accordion-wrapper.editor-expanded .eb-accordion-content-wrapper {
  width: 100% !important;
  display: block !important;
  opacity: 1 !important;
}
.eb-accordion-container.eb-accordion-type-horizontal .eb-accordion-wrapper:not(.editor-expanded) .eb-accordion-content-wrapper {
  display: none !important;
  opacity: 0 !important;
}
.eb-accordion-container.eb-accordion-type-horizontal .eb-accordion-content-wrapper {
  display: block;
  overflow: scroll;
  max-height: auto !important;
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: translateY(10px);
  width: 0;
}
.eb-accordion-container.eb-accordion-type-horizontal .eb-accordion-wrapper.editor-expanded .eb-accordion-content-wrapper {
  opacity: 1;
  transform: translateY(0);
  width: 100%;
}

.eb-accordion-image-editor {
  position: relative;
  display: inline-block;
}

.eb-accordion-image-editor img {
  display: block;
  width: 100%;
}

.eb-replace-img-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #cad2df;
  color: #000000;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.eb-accordion-image-editor:hover .eb-replace-img-button {
  opacity: 1;
  color: #000000;
}

.eb-accordion-add-button {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 60px;
  background-color: #F5F8FF;
  border: 1px dashed #BFC9D7;
  border-radius: 6px;
  padding: 40px 0 !important;
  margin-top: 10px;
  width: 100%;
  outline: none;
}

.eb-accordion-add-button-label {
  font-size: 16px;
  color: #444F62;
  margin-left: 10px;
}

.eb-accordion-add-button .dashicon::before {
  color: #444F62;
  background-color: #DAE0EE;
  padding: 5px;
  border-radius: 8px;
}

.eb-panel-control .eb-accordion-add-button {
  margin-top: 0px;
}

.eb-accordion-sortable-base {
  margin-bottom: 0px !important;
}

.eb-sortable-accordion-list {
  margin-bottom: 0px;
}
.eb-sortable-accordion-list li:last-child {
  margin-bottom: 0px;
}

.eb-accordion-expanded {
  overflow: visible;
  opacity: 1;
}

.eb-accordion-upload-button {
  height: 100px;
  width: 100%;
  background: #e3e3e3;
  color: #666666;
  display: inline-block;
}/*# sourceMappingURL=editor.css.map */