.eb-add-to-cart-wrapper.layout-stacked .quantity,
.eb-add-to-cart-wrapper.layout-stacked .eb-quantity {
  display: block !important;
  margin-bottom: 10px !important;
}
.eb-add-to-cart-wrapper .cart.variations_form table.variations th.label,
.eb-add-to-cart-wrapper .cart.variations_form table.variations td.value {
  display: block;
  text-align: left;
  padding-left: 0;
}
.eb-add-to-cart-wrapper .eb-cart-form table {
  margin-bottom: 1em;
  border: 0;
}
.eb-add-to-cart-wrapper .eb-cart-form table del {
  margin-right: 5px;
}
.eb-add-to-cart-wrapper .eb-quantity {
  display: inline-block;
  margin-right: 15px;
}
.eb-add-to-cart-wrapper .eb-quantity input {
  width: 60px;
  text-align: center;
}/*# sourceMappingURL=style.css.map */