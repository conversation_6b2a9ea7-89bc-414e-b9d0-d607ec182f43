/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 */
.eb-advanced-video-wrapper {
  position: relative;
  transition: 0.5s;
  overflow: hidden;
  display: block;
  align-items: center;
  justify-content: center;
}
.eb-advanced-video-wrapper .plyr__controls-false .plyr__controls {
  display: none;
}
.eb-advanced-video-wrapper .player-placeholder {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  box-sizing: border-box;
}
.eb-advanced-video-wrapper .eb-player-option.lightbox {
  position: relative;
  padding-top: 56.25%;
  /* 720 / 1280 = 0.5625 */
}

.eb-player-wrapper {
  position: relative;
  padding-top: 56.25%;
  /* 720 / 1280 = 0.5625 */
}

.eb-react-player {
  position: absolute;
  top: 0;
  left: 0;
}
.eb-react-player iframe {
  box-sizing: border-box;
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes fade-out-down {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(100%);
  }
}
.eb-player-option.eb-sticky.stuck-out {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 260px;
  height: 145px;
  transform: translateY(100%);
  z-index: 1;
  animation: fade-out-down 0.75s ease forwards;
}
.eb-player-option.eb-sticky.stuck-out.left {
  left: 20px;
}
.eb-player-option.eb-sticky.stuck-out.right {
  right: 20px;
}

.eb-player-option.eb-sticky.stuck {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 260px;
  height: 145px;
  transform: translateY(100%);
  animation: fade-in-up 0.75s ease forwards;
  z-index: 10;
}
.eb-player-option.eb-sticky.stuck.left {
  left: 20px;
}
.eb-player-option.eb-sticky.stuck.right {
  right: 20px;
}

.eb-sticky-video-close {
  font-size: 16px;
  line-height: 1.2rem;
  font-weight: bold;
  position: absolute;
  right: -11px;
  top: -20px;
  color: white;
  cursor: pointer;
  background: black;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  text-align: center;
}

.video_wrapper {
  position: relative;
}

.video-overlay {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 4;
  cursor: pointer;
  background-size: cover;
  background-position: 50%;
  text-align: center;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.poster-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #fff;
  transform: translate(-50%, -50%);
}
.poster-btn img {
  width: 100px;
}

/* The Modal (background) */
.eb-modal-player {
  display: none;
  /* Hidden by default */
  position: fixed;
  /* Stay in place */
  z-index: 99;
  /* Sit on top */
  padding-top: 100px;
  /* Location of the box */
  left: 0;
  top: 0;
  width: 100%;
  /* Full width */
  height: 100%;
  /* Full height */
  overflow: auto;
  /* Enable scroll if needed */
  background-color: rgb(0, 0, 0);
  /* Fallback color */
  background-color: rgba(0, 0, 0, 0.4);
  /* Black w/ opacity */
}
.eb-modal-player .eb-player-wrapper {
  margin: auto;
  padding: 20px;
  width: 80%;
}
.eb-modal-player .eb-player-option.lightbox {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 300px;
  height: 250px;
}
.eb-modal-player .eb-modal-close {
  line-height: 30px;
  font-weight: bold;
  position: absolute;
  right: 0;
  top: 0;
  color: #000;
  cursor: pointer;
  border-radius: 50%;
  z-index: 10;
  width: 30px;
  height: 30px;
  text-align: center;
}/*# sourceMappingURL=style.css.map */