.eb-cia-wrapper {
  display: flex;
  align-items: center;
  text-align: center;
  color: #ffffff;
  padding: 75px;
  border-radius: 5px;
  box-shadow: 0 25px 35px 0 rgba(0, 9, 78, 0.18);
}

.eb-cia-upload-button {
  height: 100px;
  width: 100%;
  background: #e3e3e3;
  color: #666666;
  display: inline-block;
}

.eb-cia-wrapper .eb-cia-button-wrapper {
  z-index: 999;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.eb-cia-wrapper .eb-cia-button-wrapper a {
  line-height: inherit;
  text-decoration: none !important;
}
.eb-cia-wrapper .eb-cia-button-wrapper a:focus {
  outline: 0 !important;
  background: transparent !important;
}
.eb-cia-wrapper .eb-cia-button-wrapper .eb-cia-button {
  padding: 13px 20px;
  border-radius: 5px;
}

.eb-cia-description {
  word-break: initial;
}

.eb-cia-text-wrapper {
  flex: 4 1 0%;
  width: 100%;
  word-break: break-word;
  padding: 5px;
  z-index: 999;
}
.eb-cia-text-wrapper .eb-cia-title {
  color: #fff;
}
.eb-cia-text-wrapper .eb-cia-description {
  color: #fff;
}
.eb-cia-text-wrapper .eb-cia-subtitle {
  color: #fff;
}
.eb-cia-text-wrapper .eb-cia-icon {
  display: block;
  flex: 1 1 0%;
  font-size: 70px;
  color: #fff;
  width: unset;
  height: unset;
}/*# sourceMappingURL=style.css.map */