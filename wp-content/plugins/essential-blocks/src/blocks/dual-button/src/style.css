.eb-button-group-wrapper {
  position: relative;
}
.eb-button-group__midldeInner {
  align-self: center;
  width: 0;
  position: relative;
}
.eb-button-group__midldeInner span {
  position: absolute;
  background-color: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 9999;
}

.eb-button-group {
  transition: all 0.3s ease-in-out;
}
.eb-button-group-one, .eb-button-group-two {
  text-decoration: none;
}

.eb-typography-base div {
  display: flex;
  justify-content: space-between;
}

.eb-inspector-btn-group {
  display: flex;
  padding-bottom: 5px;
}

.eb-inspector-btn-group button {
  flex: 1;
  padding-left: 40px !important;
}

.eb-button-group-wrapper .eb-button-parent,
.eb-button-group-wrapper .eb-button-parent:focus,
.eb-button-group-wrapper .eb-button-anchor,
.eb-button-group-wrapper .eb-button-anchor:focus {
  text-decoration: none !important;
}

.eb-button-group-wrapper .eb-button-parent .eb-button-text,
.eb-button-group-wrapper .eb-button-anchor .eb-button-text {
  line-height: 1.8;
}

.eb-button-group-wrapper.preset-4 {
  flex-direction: column !important;
}
.eb-button-group-wrapper.preset-4 .eb-button-parent.eb-button-one,
.eb-button-group-wrapper.preset-4 .eb-button-anchor.eb-button-one {
  margin-top: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.eb-button-group-wrapper.preset-4 .eb-button-parent.eb-button-two,
.eb-button-group-wrapper.preset-4 .eb-button-anchor.eb-button-two {
  margin-left: 0 !important;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}

.eb-button-group-wrapper:not(.preset-4) .eb-button-parent.eb-button-one,
.eb-button-group-wrapper:not(.preset-4) .eb-button-anchor.eb-button-one {
  margin-top: 0 !important;
  margin-left: 0 !important;
  margin-bottom: 0 !important;
}

.eb-button-group-wrapper:not(.preset-4) .eb-button-parent.eb-button-two,
.eb-button-group-wrapper:not(.preset-4) .eb-button-anchor.eb-button-two {
  margin-top: 0 !important;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}/*# sourceMappingURL=style.css.map */