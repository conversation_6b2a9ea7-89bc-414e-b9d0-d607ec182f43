@charset "UTF-8";
/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 */
.eb-form-wrapper {
  position: relative;
  padding: 0px 15px 15px;
  box-sizing: border-box;
}
.eb-form-wrapper * {
  box-sizing: border-box;
}
.eb-form-wrapper .eb-form.form-layout-inline {
  display: flex;
}
.eb-form-wrapper .eb-form.form-layout-inline > div {
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: 50%;
}
.eb-form-wrapper .eb-form.form-layout-inline .eb-field-wrapper {
  position: relative;
}
.eb-form-wrapper .eb-form.form-layout-inline .eb-field-wrapper .eb-form-validation {
  position: absolute;
  margin-top: 5px;
}
.eb-form-wrapper .eb-form.form-layout-inline .eb-form-submit {
  margin-top: 0;
}
.eb-form-wrapper .eb-form.form-layout-inline ~ .eb_form_submit_response {
  margin-top: 40px;
}
.eb-form-wrapper .eb-form.form-style-modern input:not([type=radio]):not([type=checkbox]) {
  outline: none;
}
.eb-form-wrapper .eb-form.form-style-modern input:not([type=radio]):not([type=checkbox]) ~ label {
  position: absolute;
  top: 50%;
  padding: 0;
  margin: 0;
  pointer-events: none;
  transform: translateY(-50%);
  transition: ease-in-out 0.2s;
}
.eb-form-wrapper .eb-form.form-style-modern input:not([type=radio]):not([type=checkbox]):focus ~ label, .eb-form-wrapper .eb-form.form-style-modern input:not([type=radio]):not([type=checkbox]) ~ label.active {
  top: 15px;
  font-size: 12px;
}
.eb-form-wrapper .eb-form.form-style-modern input:not([type=radio]):not([type=checkbox])::-moz-placeholder {
  color: transparent !important;
  opacity: 0 !important;
}
.eb-form-wrapper .eb-form.form-style-modern input:not([type=radio]):not([type=checkbox])::placeholder {
  color: transparent !important;
  opacity: 0 !important;
}
.eb-form-wrapper .eb-form.form-style-modern textarea {
  outline: none;
}
.eb-form-wrapper .eb-form.form-style-modern textarea ~ label {
  position: absolute;
  top: 50%;
  padding: 0;
  margin: 0;
  pointer-events: none;
  transform: translateY(-50%);
  transition: 0.5s;
}
.eb-form-wrapper .eb-form.form-style-modern textarea:focus ~ label, .eb-form-wrapper .eb-form.form-style-modern textarea ~ label.active {
  top: 15px !important;
  font-size: 12px;
}
.eb-form-wrapper .eb-form.form-style-modern textarea::-moz-placeholder {
  color: transparent !important;
  opacity: 0 !important;
}
.eb-form-wrapper .eb-form.form-style-modern textarea::placeholder {
  color: transparent !important;
  opacity: 0 !important;
}
.eb-form-wrapper .eb-form.form-style-modern .eb-select-field-wrapper .eb-field-input-wrap div select {
  outline: none;
}
.eb-form-wrapper .eb-form.form-style-modern .eb-select-field-wrapper .eb-field-input-wrap div select::-moz-placeholder {
  color: transparent !important;
  opacity: 0 !important;
}
.eb-form-wrapper .eb-form.form-style-modern .eb-select-field-wrapper .eb-field-input-wrap div select::placeholder {
  color: transparent !important;
  opacity: 0 !important;
}
.eb-form-wrapper .eb-form.form-style-modern .eb-select-field-wrapper .eb-field-input-wrap div ~ label {
  position: absolute;
  top: 50%;
  padding: 0;
  margin: 0;
  pointer-events: none;
  transform: translateY(-50%);
  transition: 0.5s;
}
.eb-form-wrapper .eb-form.form-style-modern .eb-select-field-wrapper .eb-field-input-wrap div ~ label.active {
  top: 15px;
  font-size: 12px;
}
.eb-form-wrapper .eb-form.form-style-modern .eb-select-field-wrapper .eb-field-input-wrap div:focus ~ label, .eb-form-wrapper .eb-form.form-style-modern .eb-select-field-wrapper .eb-field-input-wrap div ~ label.active {
  top: 15px;
}
.eb-form-wrapper .eb-form.form-style-modern select {
  outline: none;
}
.eb-form-wrapper .eb-form.form-style-modern select ~ label {
  position: absolute;
  top: 15px;
  padding: 0;
  margin: 0;
  pointer-events: none;
  transform: translateY(-50%);
  transition: 0.5s;
  font-size: 12px;
}
.eb-form-wrapper .eb-form.form-style-modern select::-moz-placeholder {
  color: transparent !important;
  opacity: 0 !important;
}
.eb-form-wrapper .eb-form.form-style-modern select::placeholder {
  color: transparent !important;
  opacity: 0 !important;
}
.eb-form-wrapper .eb-form.form-style-modern .eb-datetime-field-wrapper .eb-field-input-wrap label {
  position: absolute;
  top: 50%;
  padding: 0;
  margin: 0;
  pointer-events: none;
  transform: translateY(-50%);
  transition: ease-in-out 0.2s;
}
.eb-form-wrapper .eb-form.form-style-modern .eb-datetime-field-wrapper .eb-field-input-wrap label.active {
  top: 15px;
  font-size: 12px;
}
.eb-form-wrapper .eb-form-fields {
  display: flex;
  flex-direction: column;
}
.eb-form-wrapper .eb-form-fields .alignfull {
  margin-left: auto;
  margin-right: auto;
  padding: 0;
  width: 100%;
}
.eb-form-wrapper .eb-form-fields .eb-form-field {
  display: flex;
  flex-direction: column;
  margin: 0;
}
.eb-form-wrapper .eb-field-wrapper .eb-field-input {
  font-family: inherit;
}
.eb-form-wrapper .eb-field-wrapper .eb-field-input:hover, .eb-form-wrapper .eb-field-wrapper .eb-field-input:focus-visible {
  border-color: #000;
}
.eb-form-wrapper .eb-field-wrapper .eb-field-input ::-moz-placeholder {
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
}
.eb-form-wrapper .eb-field-wrapper .eb-field-input ::placeholder {
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
}
.eb-form-wrapper .eb-field-wrapper label {
  line-height: 1.2em;
  display: block;
  margin-bottom: 5px;
}
.eb-form-wrapper .eb-field-wrapper input:not([type=radio]):not([type=checkbox]) {
  line-height: 1.2em;
  min-height: 40px;
  height: auto;
  display: block;
  max-width: 100%;
}
.eb-form-wrapper .eb-field-wrapper textarea {
  line-height: 1.2rem;
  min-height: 40px;
  padding: 15px;
  border-radius: 4px;
  resize: vertical;
}
.eb-form-wrapper .eb-field-wrapper input:not([type=radio]):not([type=checkbox]),
.eb-form-wrapper .eb-field-wrapper select,
.eb-form-wrapper .eb-field-wrapper textarea {
  box-shadow: 0 0 transparent;
  min-height: 40px;
}
.eb-form-wrapper .eb-field-wrapper input:not([type=radio]):not([type=checkbox]):hover, .eb-form-wrapper .eb-field-wrapper input:not([type=radio]):not([type=checkbox]):focus-visible,
.eb-form-wrapper .eb-field-wrapper select:hover,
.eb-form-wrapper .eb-field-wrapper select:focus-visible,
.eb-form-wrapper .eb-field-wrapper textarea:hover,
.eb-form-wrapper .eb-field-wrapper textarea:focus-visible {
  outline: none;
}
.eb-form-wrapper .eb-field-wrapper input[type=checkbox],
.eb-form-wrapper .eb-field-wrapper input[type=radio] {
  box-sizing: border-box;
  padding: 0;
  border: 1px solid #8c8f94;
  background: transparent;
  color: #50575e;
  clear: none;
  cursor: pointer;
  display: inline-block;
  min-width: auto;
  height: 1rem;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: 0.05s border-color ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.eb-form-wrapper .eb-field-wrapper input[type=checkbox]:checked::before,
.eb-form-wrapper .eb-field-wrapper input[type=radio]:checked::before {
  display: inline-block;
  vertical-align: middle;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.eb-form-wrapper .eb-field-wrapper input[type=radio] {
  border-radius: 50%;
  position: relative;
}
.eb-form-wrapper .eb-field-wrapper input[type=radio]::before {
  content: "";
  border-radius: 50%;
  width: 0.5rem;
  height: 0.5rem;
  background-color: #3582c4;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: 0.5ms all ease-in-out;
}
.eb-form-wrapper .eb-field-wrapper input[type=radio]:checked::before {
  transform: translate(-50%, -50%) scale(1);
  margin: 0;
}
.eb-form-wrapper .eb-field-wrapper .eb-radio-inputarea {
  margin-bottom: 5px;
}
.eb-form-wrapper .eb-field-wrapper .eb-radio-inputarea label {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  width: auto;
}
.eb-form-wrapper .eb-field-wrapper input[type=checkbox] {
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}
.eb-form-wrapper .eb-field-wrapper input[type=checkbox]::before {
  position: absolute;
  display: block;
  color: #fff;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: red;
  font-size: 12px;
  margin: 0;
  height: auto;
  width: auto;
}
.eb-form-wrapper .eb-field-wrapper input[type=checkbox]:checked::before {
  content: "\f00c";
  font-weight: 700;
  font-family: "Font Awesome 5 Free";
}
.eb-form-wrapper .eb-field-wrapper .eb-checkbox-inputarea {
  margin-bottom: 5px;
}
.eb-form-wrapper .eb-field-wrapper .eb-checkbox-inputarea label {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  width: auto;
  margin-bottom: 0;
}
.eb-form-wrapper .eb-field-wrapper select {
  line-height: 1.5em;
  width: 100%;
  min-height: 40px;
  max-width: 100%;
  display: block;
  background-color: transparent;
  background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3C!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0) --%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' width='512px' height='512px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Cpath fill='%23000' d='M293.751,455.868c-20.181,20.179-53.165,19.913-73.673-0.595l0,0c-20.508-20.508-20.773-53.493-0.594-73.672 l189.999-190c20.178-20.178,53.164-19.913,73.672,0.595l0,0c20.508,20.509,20.772,53.492,0.595,73.671L293.751,455.868z'/%3E%3Cpath fill='%23000' d='M220.249,455.868c20.18,20.179,53.164,19.913,73.672-0.595l0,0c20.509-20.508,20.774-53.493,0.596-73.672 l-190-190c-20.178-20.178-53.164-19.913-73.671,0.595l0,0c-20.508,20.509-20.772,53.492-0.595,73.671L220.249,455.868z'/%3E%3C/svg%3E");
  background-position: right 13px center;
  background-repeat: no-repeat;
  background-size: auto 13px;
  color: #404246;
  padding: 13px 30px 13px 15px;
  outline: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}
.eb-form-wrapper .eb-field-wrapper select::-ms-expand {
  display: none;
}
@-moz-document url-prefix() {
  .eb-form-wrapper .eb-field-wrapper select {
    color: rgba(0, 0, 0, 0);
    text-shadow: 0 0 0 #ffffff;
  }
}
.eb-form-wrapper .eb-field-wrapper.eb-validation-error .eb-field-input {
  border-color: #dc072f;
}
.eb-form-wrapper .eb-field-wrapper.eb-validation-error .eb-form-validation {
  display: block;
  color: #ca3521;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin-top: 10px;
  padding: 0;
}
.eb-form-wrapper .eb-form-submit {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.eb-form-wrapper .eb-form-submit .eb-form-submit-button {
  font-size: 1em;
  padding: 15px 30px;
  transition: ease-in-out 0.3s;
  cursor: pointer;
  line-height: normal;
}
.eb-form-wrapper .eb-form-submit .eb-form-submit-button .eb-form-submit-loader {
  display: none;
  height: 1.4em;
  width: auto;
  margin-left: 15px;
}
.eb-form-wrapper .eb-form-submit .eb-form-submit-button.loading .eb-form-submit-loader {
  display: inline-block;
}
.eb-form-wrapper .eb-form-submit .eb-button-icon {
  display: inline;
}
.eb-form-wrapper .block-list-appender {
  width: 100%;
}
.eb-form-wrapper .block-list-appender button {
  box-shadow: none;
  border: 1px solid rgb(195, 195, 195);
  background: rgba(204, 204, 204, 0.2784313725);
  border-radius: 5px;
  /* color: white; */
}
.eb-form-wrapper .block-list-appender button:hover {
  border-color: #000;
  color: #000;
}

.eb_form_submit_response {
  font-size: 1em;
  padding: 8px 15px;
  margin-top: 25px;
  border-radius: 4px;
}

.wp-admin .eb-form-wrapper .eb-multistep-form .eb-form-fields > .block-editor-inner-blocks > .block-editor-block-list__layout {
  gap: 0px !important;
}

.eb-multistep-form .step-bar-wrapper {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 20px;
}
.eb-multistep-form .step-bar-wrapper .step-item {
  padding: 10px;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  align-items: center;
  position: relative;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid #eee;
}
.eb-multistep-form .step-bar-wrapper .step-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
.eb-multistep-form .step-bar-wrapper .step-item:focus {
  outline: 2px solid #007cba;
  outline-offset: 2px;
}
.eb-multistep-form .step-bar-wrapper .step-item.active {
  color: #673AB7;
  border-bottom-color: #673AB7;
  font-weight: bold;
}
.eb-multistep-form .step-bar-wrapper .step-item .step-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}
.eb-multistep-form .step-bar-wrapper.step-style-numbered .step-item {
  counter-increment: step-counter;
}
.eb-multistep-form .step-bar-wrapper.step-style-numbered .step-item::before {
  content: counter(step-counter);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}
.eb-multistep-form .step-bar-wrapper.step-style-numbered .step-item.active::before {
  background: #673AB7;
  color: white;
}
.eb-multistep-form .step-bar-wrapper.step-style-dots .step-item::before {
  content: "";
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #eee;
  display: block;
  margin-right: 8px;
}
.eb-multistep-form .step-bar-wrapper.step-style-dots .step-item.active::before {
  background: #673AB7;
}
.eb-multistep-form .block-editor-block-list__layout .wp-block-essential-blocks-pro-form-multistep-wrapper {
  border: 2px dashed #eee;
}
.eb-multistep-form .step-navigation-wrapper {
  margin: 0 10px 30px;
}
.eb-multistep-form .step-navigation-wrapper .step-content {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 7px;
}
.eb-multistep-form .step-navigation-progress-bar .step-progress-bar {
  position: relative;
  width: 100%;
  height: 8px;
  background-color: #EFF0F6;
  border-radius: 4px;
  overflow: visible;
  margin-bottom: 15px;
}
.eb-multistep-form .step-navigation-progress-bar .step-progress-bar .step-progress-bar-inner {
  height: 100%;
  background-color: #3C3C3C;
  transition: width 0.3s ease;
  border-radius: 4px;
}
.eb-multistep-form .step-navigation-progress-bar .step-progress-bar .step-progress-markers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.eb-multistep-form .step-navigation-progress-bar .step-marker {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background-color: #CCCCCC;
  border-radius: 50%;
  z-index: 2;
}
.eb-multistep-form .step-navigation-progress-bar .step-marker.active {
  background-color: #3C3C3C !important;
}
.eb-multistep-form .step-navigation-progress-bar .step-marker:first-child {
  left: 0 !important;
  transform: translate(0%, -50%);
}
.eb-multistep-form .step-navigation-progress-bar .step-marker:last-child {
  left: 100% !important;
  transform: translate(-100%, -50%);
}
.eb-multistep-form .step-navigation-progress-bar .step-progress-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: 10px;
}
.eb-multistep-form .step-navigation-breadcrumb .step-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 98%;
  padding: 0;
  margin: 0;
  list-style: none;
}
.eb-multistep-form .step-navigation-breadcrumb .step-nav-item {
  position: relative;
  background-color: #f5f5f5;
  color: #333;
  padding: 10px 15px;
  font-weight: 500;
  clip-path: polygon(90% 0%, 100% 50%, 90% 100%, 0% 100%, 10% 50%, 0% 0%);
  margin-right: -20px;
  flex: 1;
  text-align: center;
  min-width: 120px;
}
.eb-multistep-form .step-navigation-breadcrumb .step-nav-item:first-child {
  clip-path: polygon(90% 0%, 100% 50%, 90% 100%, 0% 100%, 0% 0%);
}
.eb-multistep-form .step-navigation-breadcrumb .step-nav-item.active {
  background-color: #333;
  color: white;
  z-index: 2;
}
.eb-multistep-form .step-navigation-step-titles .step-step-titles {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.eb-multistep-form .step-navigation-step-titles .step-nav-item {
  padding: 8px 16px;
  color: #777;
  flex: 1;
}
.eb-multistep-form .step-navigation-step-titles .step-nav-item.active {
  color: #673AB7;
  border-bottom: 2px solid #673AB7;
}
.eb-multistep-form .step-navigation-step-titles .step-nav-item .step-count {
  background: #EBEEF2;
  color: #333;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}
.eb-multistep-form .step-navigation-dots {
  padding: 0 10px;
}
.eb-multistep-form .step-navigation-dots .step-dots {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.eb-multistep-form .step-navigation-dots .step-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  text-align: center;
  position: relative;
  flex: 1;
}
.eb-multistep-form .step-navigation-dots .step-nav-item:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 10px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: #ddd;
  z-index: 1;
}
.eb-multistep-form .step-navigation-dots .step-nav-item:not(:first-child)::before {
  content: "";
  position: absolute;
  top: 10px;
  left: -50%;
  width: 100%;
  height: 2px;
  background-color: #ddd;
  z-index: 0;
}
.eb-multistep-form .step-navigation-dots .step-nav-item.active:not(:first-child)::before {
  background-color: #333;
}
.eb-multistep-form .step-navigation-dots .step-nav-item .step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #E7E7E7;
  border: 2px solid #fff;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  position: relative;
}
.eb-multistep-form .step-navigation-dots .step-nav-item .step-dot::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #A2A2A2;
  z-index: 3;
}
.eb-multistep-form .step-navigation-dots .step-nav-item .step-title {
  font-size: 14px;
  color: #777;
  margin-top: 5px;
  font-weight: normal;
}
.eb-multistep-form .step-navigation-dots .step-nav-item .step-subtitle {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-top: 2px;
}
.eb-multistep-form .step-navigation-dots .step-nav-item.active .step-dot {
  background-color: #333;
  border-color: #fff;
}
.eb-multistep-form .step-navigation-dots .step-nav-item.active .step-dot::after {
  content: none;
}
.eb-multistep-form .step-navigation-dots .step-nav-item.active .step-dot::before {
  content: "\f00c";
  font-weight: 700;
  font-family: "Font Awesome 5 Free";
  color: #fff;
  font-size: 12px;
}
.eb-multistep-form .step-navigation-dots .step-nav-item.active .step-title {
  color: #333;
  font-weight: 500;
}
.eb-multistep-form .step-navigation-dots .step-nav-item.completed .step-dot {
  background-color: #333;
}
.eb-multistep-form .step-navigation-dots .step-nav-item.completed .step-dot::after {
  content: "✓";
  color: #fff;
  font-size: 12px;
}
.eb-multistep-form .step-navigation-step-title-2 .step-step-title-2 {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  gap: 10px;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item .label {
  font-size: 14px;
  color: #7a7a7a;
  margin-bottom: 8px;
  text-align: center;
  font-weight: 600;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item .step-count {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #c3c7cf;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #7a7a7a;
  position: relative;
  z-index: 1;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item .step-line {
  position: absolute;
  top: auto;
  bottom: 14px;
  left: 50%;
  width: 100%;
  height: 2px;
  background-color: #c3c7cf;
  z-index: 0;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item.completed .circle {
  background-color: #333;
  color: #fff;
  border-color: #333;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item.completed .line {
  background-color: #333;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item.completed .label {
  color: #000;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item.active .step-count {
  border: 2px solid #333;
  background-color: #fff;
  color: #333;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item.active .step-count::before {
  transform: translate(-50%, -50%) scale(1);
  border-color: #333;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item.active .label {
  color: #333;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item.active .step-line {
  background-color: #c3c7cf;
}
.eb-multistep-form .step-navigation-step-title-2 .step-nav-item:last-child .step-line {
  display: none;
}
.eb-multistep-form .step-navigation-step-title-3 .step-step-title-3 {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex: 1;
  border-right: 1px solid #eee;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item:last-child {
  border-right: none;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item .step-icon {
  width: 48px;
  height: 48px;
  background-color: #f7f7f7;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item .step-icon img {
  width: 24px;
  height: 24px;
  opacity: 0.3;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item .text {
  display: none;
  margin-left: 10px;
  flex-direction: column;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item .text .step-label {
  font-size: 12px;
  color: #9ca3af;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item .text .step-subtitle {
  font-weight: 600;
  font-size: 14px;
  color: #111827;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item.active {
  flex-direction: row;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item.active .step-icon {
  background-color: #e5e7eb;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item.active .step-icon img {
  opacity: 1;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item.active .text {
  display: flex;
}
.eb-multistep-form .step-navigation-step-title-3 .step-nav-item .divider {
  height: 48px;
  width: 1px;
  background-color: #eee;
}
.eb-multistep-form .eb-form-submit {
  display: flex;
  justify-content: flex-end !important;
  align-items: center;
}
.eb-multistep-form .eb-form-submit #ebFormPrevBtn {
  margin-right: auto;
}
.eb-multistep-form .eb-form-submit #ebFormNextBtn .eb-form-next-loader {
  display: none;
  height: 1em;
  width: auto;
  margin-left: 10px;
}
.eb-multistep-form .eb-form-submit #ebFormNextBtn.loading .eb-form-next-loader {
  display: inline-block;
}
.eb-multistep-form .eb-form-submit #ebFormNextBtn,
.eb-multistep-form .eb-form-submit .eb-form-submit-button {
  margin-left: 10px;
}
.eb-multistep-form .eb-form-submit button {
  font-size: 1em;
  padding: 15px 30px;
  transition: ease-in-out 0.3s;
  cursor: pointer;
  line-height: normal;
  box-shadow: none;
  border: 1px solid rgb(195, 195, 195);
  background: rgba(204, 204, 204, 0.2784313725);
  border-radius: 5px;
  width: auto !important;
}
.eb-multistep-form .eb-form-submit button:hover {
  color: #000;
}

.eb-form-add-step-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 20px;
  padding: 25px !important;
  font-size: 16px;
  background-color: #f7f7f7;
  border: 1px dashed #ccc;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}
.eb-form-add-step-button:hover {
  background-color: #e9e9e9;
  border-color: #999;
}
.eb-form-add-step-button .eb-form-add-step-button-label {
  margin-left: 8px;
}

.eb-form-template-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  width: 100%;
  background-color: #f8f9fa;
  border-radius: 4px;
}
.eb-form-template-loading img {
  width: 45px;
  margin: 0 0 10px;
}
.eb-form-template-loading p {
  margin: 0;
  color: #757575;
  font-size: 16px;
  font-weight: 500;
}/*# sourceMappingURL=style.css.map */