.eb-icon-wrapper .eb-icon-container {
  display: inline-block;
  line-height: 1;
  transition: all 0.3s;
  color: #333333;
  text-align: center;
}
.eb-icon-wrapper .eb-icon-container i {
  width: 1em;
  height: 1em;
  position: relative;
  display: block;
}
.eb-icon-wrapper .eb-icon-container i:before {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.eb-icon-wrapper.eb-icon-shape-circle .eb-icon-container {
  border-radius: 50%;
}
.eb-icon-wrapper.eb-icon-view-framed .eb-icon-container {
  padding: 0.5em;
  border: 3px solid #333333;
  background-color: transparent;
}
.eb-icon-wrapper.eb-icon-view-stacked .eb-icon-container {
  padding: 0.5em;
  background-color: #333333;
  color: #fff;
  fill: #fff;
}
.eb-icon-wrapper .dashicons,
.eb-icon-wrapper .dashicons-before:before {
  width: unset;
  height: unset;
  font-size: unset;
}/*# sourceMappingURL=style.css.map */