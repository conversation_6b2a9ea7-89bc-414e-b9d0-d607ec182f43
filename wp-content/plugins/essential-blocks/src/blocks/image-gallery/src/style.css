.eb-gallery-img-wrapper {
  overflow: hidden;
}
.eb-gallery-img-wrapper ~ .eb-img-gallery-loadmore-container .eb-img-gallery-loadmore {
  display: block;
  width: auto;
  margin: 20px auto 0;
  outline: 1px solid #000 0;
  text-shadow: none;
  white-space: nowrap;
  -webkit-appearance: none;
  box-sizing: border-box;
  cursor: pointer;
  transition: box-shadow 0.1s linear;
  border: 0;
  border-radius: 0;
}
.eb-gallery-img-wrapper ~ .eb-img-gallery-loadmore-container .eb-img-gallery-loadmore.loadmore-disable {
  cursor: none;
}
.eb-gallery-img-wrapper ~ .eb-img-gallery-loadmore-container .eb-img-gallery-loadmore.loadmore-disable img {
  width: 16px;
  margin-right: 5px;
  display: inline-block;
  position: relative;
  top: 2px;
}
.eb-gallery-img-wrapper .eb-gallery-img-content {
  color: transparent;
}
.eb-gallery-img-wrapper .eb-gallery-link-wrapper {
  overflow: hidden;
  display: block;
  position: relative;
  height: 100%;
  width: 100%;
}
.eb-gallery-img-wrapper.grid {
  display: grid;
  grid-template-columns: repeat(3, auto);
  grid-auto-rows: 1fr;
  grid-auto-flow: dense;
}
.eb-gallery-img-wrapper.grid.enable-isotope.show-loadmore, .eb-gallery-img-wrapper.grid.eb-filterable-img-gallery.show-loadmore {
  height: 0;
}
.eb-gallery-img-wrapper.grid .eb-gallery-img-content img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  box-sizing: border-box;
}
.eb-gallery-img-wrapper.masonry {
  box-sizing: border-box;
}
.eb-gallery-img-wrapper.masonry:after {
  content: "";
  display: block;
  clear: both;
}
.eb-gallery-img-wrapper.masonry .grid-sizer,
.eb-gallery-img-wrapper.masonry .eb-gallery-img-content {
  width: 33.3333333333%;
}
.eb-gallery-img-wrapper.masonry .eb-gallery-img-content {
  display: block;
  float: left;
  box-sizing: border-box;
  min-width: 1px;
}
.eb-gallery-img-wrapper.masonry .eb-gallery-img-content.wide {
  width: 66.6666666667%;
}
.eb-gallery-img-wrapper.masonry .eb-gallery-img-content img {
  display: block;
  height: auto;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}
.eb-gallery-img-wrapper.eb-filterable-img-gallery.masonry {
  -moz-columns: unset;
       columns: unset;
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content {
  position: relative;
  display: block;
  overflow: hidden;
  line-height: 0;
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption {
  position: absolute;
  width: auto;
  transform: translate(0%, 0%);
  line-height: 1em;
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.left.top, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.left.top {
  left: 0px;
  top: 0px;
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.left.middle, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.left.middle {
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.left.bottom, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.left.bottom {
  left: 0px;
  bottom: 0px;
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.center.top, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.center.top {
  left: 50%;
  top: 0px;
  transform: translateX(-50%);
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.center.middle, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.center.middle {
  left: 50%;
  top: 50%;
  bottom: auto !important;
  transform: translate(-50%, -50%);
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.center.bottom, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.center.bottom {
  left: 50%;
  bottom: 0px;
  transform: translateX(-50%);
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.right.top, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.right.top {
  right: 0px;
  top: 0px;
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.right.middle, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.right.middle {
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-gallery-img-wrapper:not(.v2) .eb-gallery-img-content .eb-gallery-img-caption.right.bottom, .eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption.right.bottom {
  right: 0px;
  bottom: 0px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption {
  opacity: 0;
  transition: all 0.4s ease-in-out;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.left.top {
  left: -10px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.left.middle {
  left: -10px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.left.bottom {
  left: -10px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.center.top {
  top: -10px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.center.middle {
  bottom: 0;
  transform: translate(-50%, 20px);
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.center.bottom {
  bottom: -10px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.right.top {
  right: -10px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.right.middle {
  right: -10px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content .eb-gallery-img-caption.right.bottom {
  right: -10px;
}
.eb-gallery-img-wrapper:not(.v2).caption-on-hover .eb-gallery-img-content:hover .eb-gallery-img-caption {
  opacity: 1;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-1 {
  overflow: hidden;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-1 .eb-gallery-img-caption {
  opacity: 0;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-1 .eb-gallery-img {
  transition: all 0.25s ease-in-out;
  filter: grayscale(100%) !important;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-1 .eb-gallery-img-content:hover .eb-gallery-img {
  filter: grayscale(0%) !important;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-1 .eb-gallery-img-content:hover .eb-gallery-img-caption {
  opacity: 1;
  transition: all 0.3s ease-in-out;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2 .eb-gallery-link-wrapper:after {
  content: "";
  position: absolute;
  margin: 0px !important;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease-in-out;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2 .eb-gallery-link-wrapper .eb-gallery-img-caption {
  z-index: 99;
  transition: all 0.4s ease-in-out;
  visibility: hidden;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2 .eb-gallery-link-wrapper:hover .eb-gallery-img-caption {
  visibility: visible;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2 .eb-gallery-img {
  transition: all 0.25s ease-in-out;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.overlay-top .eb-gallery-link-wrapper:after {
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.overlay-top .eb-gallery-link-wrapper:hover:after {
  height: 100%;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.overlay-bottom .eb-gallery-link-wrapper:after {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.overlay-bottom .eb-gallery-link-wrapper:hover:after {
  height: 100%;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.overlay-left .eb-gallery-link-wrapper:after {
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.overlay-left .eb-gallery-link-wrapper:hover:after {
  width: 100%;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.overlay-right .eb-gallery-link-wrapper:after {
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.overlay-right .eb-gallery-link-wrapper:hover:after {
  width: 100%;
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.zoom .eb-gallery-link-wrapper:after {
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: scale(0);
}
.eb-gallery-img-wrapper:not(.v2).caption-style-2.zoom .eb-gallery-link-wrapper:hover:after {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  transform: scale(1);
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content {
  position: relative;
  display: block;
  overflow: hidden;
  line-height: 0;
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content {
  position: absolute;
  transform: translate(0%, 0%);
  line-height: 1em;
  display: flex;
  flex-direction: column;
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content .eb-gallery-img-caption {
  position: relative;
  box-sizing: border-box;
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.left.top, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.left.top {
  left: 0px;
  top: 0px;
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.left.middle, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.left.middle {
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.left.bottom, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.left.bottom {
  left: 0px;
  bottom: 0px;
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.center.top, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.center.top {
  left: 50%;
  top: 0px;
  transform: translateX(-50%);
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.center.middle, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.center.middle {
  left: 50%;
  top: 50%;
  bottom: auto !important;
  transform: translate(-50%, -50%);
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.center.bottom, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.center.bottom {
  left: 50%;
  bottom: 0px;
  transform: translateX(-50%);
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.right.top, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.right.top {
  right: 0px;
  top: 0px;
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.right.middle, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.right.middle {
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-gallery-img-wrapper.v2.default .eb-gallery-img-content .eb-img-gallery-content.right.bottom, .eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content.right.bottom {
  right: 0px;
  bottom: 0px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content {
  opacity: 0;
  transition: all 0.4s ease-in-out;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.left.top {
  left: -10px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.left.middle {
  left: -10px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.left.bottom {
  left: -10px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.center.top {
  top: -10px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.center.middle {
  bottom: 0;
  transform: translate(-50%, 20px);
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.center.bottom {
  bottom: -10px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.right.top {
  right: -10px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.right.middle {
  right: -10px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content .eb-img-gallery-content.right.bottom {
  right: -10px;
}
.eb-gallery-img-wrapper.v2.default.caption-on-hover .eb-gallery-img-content:hover .eb-img-gallery-content {
  opacity: 1;
}
.eb-gallery-img-wrapper.v2.default.caption-style-1 {
  overflow: hidden;
}
.eb-gallery-img-wrapper.v2.default.caption-style-1 .eb-img-gallery-content {
  opacity: 0;
}
.eb-gallery-img-wrapper.v2.default.caption-style-1 .eb-gallery-img {
  transition: all 0.25s ease-in-out;
  filter: grayscale(100%) !important;
}
.eb-gallery-img-wrapper.v2.default.caption-style-1 .eb-gallery-img-content:hover .eb-gallery-img {
  filter: grayscale(0%) !important;
}
.eb-gallery-img-wrapper.v2.default.caption-style-1 .eb-gallery-img-content:hover .eb-img-gallery-content {
  opacity: 1;
  transition: all 0.3s ease-in-out;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2 .eb-gallery-link-wrapper:after {
  content: "";
  position: absolute;
  margin: 0px !important;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease-in-out;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2 .eb-gallery-link-wrapper .eb-img-gallery-content {
  z-index: 99;
  transition: all 0.4s ease-in-out;
  visibility: hidden;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2 .eb-gallery-link-wrapper:hover .eb-img-gallery-content {
  visibility: visible;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2 .eb-gallery-img {
  transition: all 0.25s ease-in-out;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.overlay-top .eb-gallery-link-wrapper:after {
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.overlay-top .eb-gallery-link-wrapper:hover:after {
  height: 100%;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.overlay-bottom .eb-gallery-link-wrapper:after {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.overlay-bottom .eb-gallery-link-wrapper:hover:after {
  height: 100%;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.overlay-left .eb-gallery-link-wrapper:after {
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.overlay-left .eb-gallery-link-wrapper:hover:after {
  width: 100%;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.overlay-right .eb-gallery-link-wrapper:after {
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.overlay-right .eb-gallery-link-wrapper:hover:after {
  width: 100%;
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.zoom .eb-gallery-link-wrapper:after {
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: scale(0);
}
.eb-gallery-img-wrapper.v2.default.caption-style-2.zoom .eb-gallery-link-wrapper:hover:after {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  transform: scale(1);
}
.eb-gallery-img-wrapper .hidden {
  display: none !important;
}
.eb-gallery-img-wrapper:not(.default) .eb-gallery-img-content {
  box-sizing: border-box;
  position: relative;
  display: block;
  overflow: hidden;
  line-height: 0;
}
.eb-gallery-img-wrapper:not(.default) .eb-img-gallery-overlay {
  position: absolute;
  width: 100%;
  display: flex;
  box-sizing: border-box;
}
.eb-gallery-img-wrapper:not(.default) .eb-img-gallery-content {
  width: 100%;
  box-sizing: border-box;
}
.eb-gallery-img-wrapper:not(.default) .eb-gallery-img-caption {
  position: relative;
  font-size: 18px;
  line-height: 1.2em;
  text-align: unset;
  word-wrap: break-word;
}
.eb-gallery-img-wrapper:not(.default) .eb-gallery-img-description {
  font-size: 16px;
  line-height: 1.2em;
  text-align: unset;
  word-wrap: break-word;
}
.eb-gallery-img-wrapper:not(.default) .eb-img-gallery-actions {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}
.eb-gallery-img-wrapper:not(.default) .eb-img-gallery-actions .eb-img-gallery-action {
  text-align: center;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
}
.eb-gallery-img-wrapper.preset-5 .eb-gallery-img-content.grid__item--current {
  opacity: 0 !important;
}
.eb-gallery-img-wrapper.preset-5 .eb-gallery-link-wrapper {
  transition: all 0.4s ease;
  transform: scale(1, 1);
  border-radius: 10px;
}
.eb-gallery-img-wrapper.preset-5 .eb-gallery-link-wrapper img {
  transition: all 0.4s ease !important;
  transform: scale(1, 1);
}
.eb-gallery-img-wrapper.preset-5 .eb-gallery-link-wrapper:hover {
  transform: scale(0.95, 0.95);
}
.eb-gallery-img-wrapper.preset-5 .eb-gallery-link-wrapper:hover img {
  transform: scale(1.4, 1.4);
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper {
  position: relative;
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay .eb-img-gallery-content .eb-gallery-img-caption,
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay .eb-img-gallery-content .eb-gallery-img-description {
  transform: translateY(0);
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay .eb-img-gallery-actions .eb-img-gallery-action {
  opacity: 1;
  transform: translateX(0) !important;
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay .eb-img-gallery-content {
  opacity: 1;
  transform: translateY(0);
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper .eb-img-gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-actions {
  align-self: end;
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-actions .eb-img-gallery-action {
  transition: all 0.4s ease;
  opacity: 0;
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-actions .eb-img-gallery-action:first-child {
  transform: translateX(-30px);
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-actions .eb-img-gallery-action:last-child {
  transform: translateX(30px);
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-content {
  align-self: self-start;
  transition: all 0.4s ease;
  transform: translateY(10px);
  opacity: 0;
  width: 100%;
  padding: 10px;
  overflow: hidden;
}
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-content .eb-gallery-img-caption,
.eb-gallery-img-wrapper.preset-4 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-content .eb-gallery-img-description {
  will-change: transform;
  transform: translateY(100%);
  transition: all 1s cubic-bezier(0.37, 0.31, 0.2, 0.85);
}
.eb-gallery-img-wrapper.preset-3 {
  overflow: visible;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-img-content {
  overflow: visible;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper {
  position: relative;
  overflow: visible;
  perspective: 1700px;
  perspective-origin: 0 50%;
  transform-style: preserve-3d;
  transition: box-shadow 350ms cubic-bezier(0.24, 0.8, 0.58, 1), transform 350ms cubic-bezier(0.37, 0.9, 0.58, 1);
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper:hover::before {
  height: 70%;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper:hover .eb-gallery-img-container img {
  transform: translateX(25%);
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay {
  opacity: 1;
  transform: rotateY(0);
  transition: transform 0.4s, opacity 0.1s;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-gallery-img-container {
  position: relative;
  overflow: hidden;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-gallery-img-container img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.4s ease !important;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-img-gallery-actions {
  position: absolute;
  top: auto;
  bottom: 0;
  right: 0;
  background: white;
  border-radius: 10px 0 10px 0;
  padding: 10px;
  left: auto;
  height: -moz-max-content;
  height: max-content;
  z-index: 0;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-img-gallery-actions::after, .eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-img-gallery-actions::before {
  content: "";
  position: absolute;
  background-color: transparent;
  z-index: -1;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-img-gallery-actions::after {
  bottom: 0;
  left: -50px;
  height: 25px;
  width: 50px;
  border-bottom-right-radius: 25px;
  box-shadow: 20px 0px 0 0 white;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-img-gallery-actions::before {
  top: -50px;
  right: 0;
  height: 50px;
  width: 25px;
  border-bottom-right-radius: 25px;
  box-shadow: 0px 25px 0 0 white;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-img-gallery-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
  opacity: 0;
  transform: rotateY(-90deg);
  transform-origin: 0 0;
  transition: transform 0.4s, opacity 0.1s 0.3s;
  backface-visibility: hidden;
  width: 50%;
}
.eb-gallery-img-wrapper.preset-3 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-content {
  overflow: hidden;
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper {
  position: relative;
  transition: all 0.4s ease;
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper:hover::before {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay .eb-img-gallery-actions {
  opacity: 1;
  visibility: visible;
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay {
  transform: translateY(0);
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay .eb-img-gallery-content {
  transform: translateY(0);
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper:hover .eb-img-gallery-overlay .eb-img-gallery-content .eb-gallery-img-description {
  display: block;
  opacity: 1;
  transform: translateY(0);
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper::before {
  content: "";
  display: block;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  transition: all 0.4s ease-out;
  background: #525A66;
  z-index: 1;
  transform: translateY(50%);
  opacity: 0;
  visibility: hidden;
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper .eb-img-gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  transition: all 0.4s ease;
  padding: 0;
  flex-direction: column-reverse;
  transform: translateY(100%);
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-actions {
  margin: 20px;
  transition: all 0.4s ease;
  opacity: 0;
  visibility: hidden;
  transform-origin: bottom;
  transform-style: flat;
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-content {
  padding: 20px;
  align-self: self-start;
  transition: all 0.4s ease;
  transform: translateY(-100%);
}
.eb-gallery-img-wrapper.preset-2 .eb-gallery-link-wrapper .eb-img-gallery-overlay .eb-img-gallery-content .eb-gallery-img-description {
  opacity: 0;
  transform: translateY(-50%);
  display: none;
}

#eb-img-gallery-not-found {
  text-align: center;
  opacity: 0;
  height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
#eb-img-gallery-not-found.show {
  opacity: 1;
  height: auto;
  min-height: 200px;
}

.eb-gallery-upload-button {
  display: flex;
  flex-direction: column;
  justify-content: center !important;
  height: 110px;
  width: 100%;
  background: #F5F8FF;
  color: #444F62;
  border: #BFC9D7 1px dashed;
  font-size: 16px !important;
}
.eb-gallery-upload-button .addIcon {
  background: #DAE0EE;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.eb-gallery-img-link {
  position: relative;
  cursor: pointer;
}

.eb-img-gallery-filter-wrapper {
  margin: 0px auto 20px auto;
  padding: 0px;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: center;
  gap: 5px;
}
.eb-img-gallery-filter-wrapper li {
  line-height: 1.8;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 6px 15px;
  cursor: pointer;
  margin: 0 5px;
  font-weight: normal;
  list-style: none;
  text-align: center;
}
.eb-img-gallery-filter-wrapper.preset-4 .is-checked {
  position: relative;
}
.eb-img-gallery-filter-wrapper.preset-4 .is-checked::after {
  position: absolute;
  bottom: -5px;
  left: 0;
  content: "";
  width: 100%;
  height: 1px;
  background: 2D323A;
}

.fslightbox-container.fslightbox-fade-in-strong {
  animation: fslightbox-fade-in-complete 0.25s forwards;
}/*# sourceMappingURL=style.css.map */