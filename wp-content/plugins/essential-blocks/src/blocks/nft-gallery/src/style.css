.eb-nft-gallery-wrapper {
  overflow: hidden;
  position: relative;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item {
  box-shadow: rgba(0, 0, 0, 0.09) 0px 4px 15px;
  padding: 15px;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  transition: 0.3s ease-in-out;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item:hover {
  box-shadow: rgb(214, 214, 214) 0px 4px 15px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_thumbnail {
  line-height: 0;
  text-align: center;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_thumbnail img {
  width: auto;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_creator {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_creator a {
  text-decoration: none;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_price {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 5px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_price .ebnft_currency {
  line-height: 1;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_price .ebnft_currency svg {
  height: 15px !important;
  width: auto !important;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_price .ebnft_price {
  line-height: 1;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_button {
  transition: 0.3s ease-in-out;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_button button {
  display: block;
  padding: 0;
  border: 0;
  box-shadow: unset;
  background-color: transparent;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap .eb_nft_item .eb_nft_button button a {
  text-decoration: none;
  display: block;
  border-radius: 4px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid {
  grid-template-columns: repeat(3, 1fr);
  display: grid;
  grid-column-gap: 15px;
  grid-row-gap: 15px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-1 .eb_nft_item .eb_nft_thumbnail {
  overflow: hidden;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-1 .eb_nft_item .eb_nft_thumbnail img {
  transition-duration: 0.4s;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-1 .eb_nft_item .eb_nft_button {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  transform: translate(0px, 30px);
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-1 .eb_nft_item .eb_nft_button button {
  width: 100%;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-1 .eb_nft_item .eb_nft_button button a {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-1 .eb_nft_item:hover .eb_nft_thumbnail img {
  transform: scale(1.1);
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-1 .eb_nft_item:hover .eb_nft_button {
  opacity: 1;
  visibility: visible;
  transform: translate(0px, 0px);
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-2 .eb_nft_item .eb_nft_price_wrapper {
  margin-bottom: 15px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-2 .eb_nft_item .eb_nft_thumbnail img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-3 .eb_nft_item {
  padding: 0 !important;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-3 .eb_nft_item .eb_nft_thumbnail img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  margin-bottom: 0 !important;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-3 .eb_nft_item .eb_nft_content {
  padding: 15px;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s ease-in-out;
  transform: translate(0px, 100%);
  z-index: 999;
  background-color: rgba(237, 236, 246, 0.9);
  box-sizing: border-box;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-3 .eb_nft_item .eb_nft_price_wrapper {
  margin-bottom: 15px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_grid.preset-3 .eb_nft_item:hover .eb_nft_content {
  opacity: 1;
  visibility: visible;
  transform: translate(0px, 0px);
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_list {
  grid-template-columns: repeat(1, 1fr);
  display: grid;
  grid-column-gap: 15px;
  grid-row-gap: 15px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_list .eb_nft_item {
  display: flex;
  gap: 15px;
  align-items: flex-start;
  justify-content: flex-start;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_list .eb_nft_price_wrapper {
  margin-bottom: 15px;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_list .eb_nft_thumbnail img {
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.eb_nft_list.preset-1.eb_nft_item .eb_nft_thumbnail img {
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-nft-gallery-wrapper .eb_nft_content_wrap.nft_collections.preset-1 .eb_nft_content {
  margin-bottom: 35px;
}/*# sourceMappingURL=style.css.map */