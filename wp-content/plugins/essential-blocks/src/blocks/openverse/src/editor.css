.eb-openverse-grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  align-items: stretch;
  --size: calc(50vw / 4);
  --gap: 10px;
  gap: var(--gap);
}
.eb-openverse-grid .eb-openverse-grid-item {
  cursor: pointer;
  border: 3px solid transparent;
}
.eb-openverse-grid .eb-openverse-grid-item.selected {
  border-color: #007cba;
}
.eb-openverse-grid .eb_openverse_item_thumbnail {
  height: calc(var(--size) - var(--gap));
}
.eb-openverse-grid .eb_openverse_item_thumbnail img {
  max-width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.search-key {
  margin: 0 0 10px;
}

.editor-styles-wrapper .search-filter h4,
.search-filter h4 {
  font-size: 16px;
  line-height: 1.3;
  font-weight: 600;
  font-family: sans-serif;
}

.editor-styles-wrapper .filter-item h5,
.filter-item h5 {
  font-size: 13px;
  line-height: 1.3;
  font-weight: 600;
  margin: 10px 0;
  font-family: sans-serif;
}

.filter-item-inner .form-check-label {
  font-size: 13px;
  line-height: 1.3;
  font-weight: 400;
  top: -2px;
  position: relative;
}
.filter-item-inner .form-check-label svg {
  width: 14px;
  margin-right: 3px;
  top: 2px;
  position: relative;
}
.filter-item-inner .form-check-label svg:last-child {
  margin-right: 6px;
}
.filter-item-inner .form-check-input {
  position: relative;
  height: 18px;
  width: 18px;
  flex-shrink: 0;
  -webkit-appearance: none !important;
     -moz-appearance: none !important;
          appearance: none !important;
  border-radius: 2px;
  border-width: 1px;
  border-color: #8c8f94;
  margin-inline-end: 0.75rem;
}

.openverse-search-section {
  display: flex;
  flex: auto;
  flex-wrap: nowrap;
  max-width: 100%;
}

.search-result-count {
  position: absolute;
  right: 126px;
  font-size: 12px;
  top: 29px;
  color: #797979;
}

.openverse-search-input:focus {
  box-shadow: inset 0px 0px 0 1px #2673ff !important;
}

.openverse-search-input {
  background-image: url("data:image/svg+xml; base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0xNS44NTMgMTYuNTZjLTEuNjgzIDEuNTE3LTMuOTExIDIuNDQtNi4zNTMgMi40NC01LjI0MyAwLTkuNS00LjI1Ny05LjUtOS41czQuMjU3LTkuNSA5LjUtOS41IDkuNSA0LjI1NyA5LjUgOS41YzAgMi40NDItLjkyMyA0LjY3LTIuNDQgNi4zNTNsNy40NCA3LjQ0LS43MDcuNzA3LTcuNDQtNy40NHptLTYuMzUzLTE1LjU2YzQuNjkxIDAgOC41IDMuODA5IDguNSA4LjVzLTMuODA5IDguNS04LjUgOC41LTguNS0zLjgwOS04LjUtOC41IDMuODA5LTguNSA4LjUtOC41eiIvPjwvc3ZnPg==");
  background-color: #fff;
  background-size: 16px 16px;
  background-position: left 10px center;
  background-repeat: no-repeat;
  padding: 10px 1em !important;
  padding-left: 2em !important;
  border: 1px solid #2673ff !important;
  border-radius: 0 !important;
  color: #000;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  -webkit-appearance: textfield;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  font-family: sans-serif;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  min-width: 170px;
  flex-grow: 1;
  height: 50px;
}

.search-section .openverse-search-input {
  padding-right: 130px !important;
}

.openverse-placheholderbox {
  padding: 20px;
  border: 1px solid;
}
.openverse-placheholderbox__label {
  font-size: 25px;
  font-weight: 400;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.openverse-placheholderbox__label svg {
  margin-right: 10px;
}
.openverse-placheholderbox__description {
  font-size: 15px;
  font-weight: 400;
  padding: 0;
  margin-bottom: 10px;
}
.openverse-placheholderbox__note {
  font-size: 13px;
}

.openverse-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 160000;
  display: flex;
  justify-content: center;
  align-items: center;
}
.openverse-modal__inner {
  overflow: hidden;
  position: relative;
  top: 25px;
  left: -60px;
  width: 72%;
  height: 70vh;
  background-color: #fff;
}
.openverse-modal__header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  z-index: 200;
  padding: 15px;
  box-sizing: border-box;
}
.openverse-modal__header .title-section {
  padding: 0;
  margin: 0;
  font-size: 19px;
  font-weight: 500;
  line-height: 1.2rem;
  display: inline-block;
}
.openverse-modal__header .close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  background: none;
  color: #646970;
  z-index: 1000;
  outline: 0;
  transition: color 0.1s ease-in-out, background 0.1s ease-in-out;
}
.openverse-modal__content {
  position: absolute;
  top: 61px;
  left: 0;
  right: 0;
  bottom: 61px;
  height: auto;
  width: auto;
  margin: 0;
  overflow: auto;
  background: #fff;
  border-top: 1px solid #dcdcde;
}
.openverse-modal__footer {
  top: auto;
  bottom: 0;
  height: auto;
  overflow: visible;
  border-top: 1px solid #dcdcde;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 100;
  height: 61px;
  padding: 15px;
  box-sizing: border-box;
  text-align: right;
}
.openverse-modal__footer button {
  font-size: 13px !important;
  background: #2673ff;
  color: #fff;
  outline: none;
  text-decoration: none;
  text-shadow: none;
  white-space: nowrap;
  align-items: center;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: 0;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  font-family: inherit;
  font-weight: 400 !important;
  text-align: center;
  height: 35px;
  margin: 0;
  padding: 3px 20px;
  transition: box-shadow 0.1s linear;
}
.openverse-modal__footer button:disabled {
  color: #a7aaad !important;
  background: #f6f7f7 !important;
  border: 1px solid #dcdcde !important;
  box-shadow: none !important;
  text-shadow: none !important;
  cursor: default;
}

.search-section {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 75px;
  padding: 15px;
  box-sizing: border-box;
  border: 0 solid #dcdcde;
  overflow: hidden;
  border-bottom: 1px solid #dcdcde;
  display: flex;
}
.search-section .openverse-search-input,
.search-section .openverse-search-btn {
  height: 45px;
}

.search-result-section {
  position: absolute;
  top: 75px;
  left: 0;
  bottom: 0;
  overflow: auto;
  outline: 0;
  width: 100%;
  display: grid;
  grid-template-columns: 75% 25%;
}
.search-result-section .search-content {
  padding: 15px;
}
.search-result-section .search-filter {
  background-color: #f3f2f2;
  border-left: 1px solid #dcdcde;
  padding: 15px;
}

.openverse-search-btn {
  min-width: 100px;
  margin: 0 auto;
  height: 50px;
  display: block;
  border-radius: 0;
  background-color: #2673ff;
  padding: 6px 12px;
  color: #fff;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  text-shadow: none;
  white-space: nowrap;
  align-items: center;
  -webkit-appearance: none;
  border: 0;
  box-sizing: border-box;
  cursor: pointer;
  font-family: inherit;
  font-size: 16px !important;
  font-weight: 400 !important;
  text-decoration: none !important;
  transition: box-shadow 0.1s linear;
}

.eb-openverse-form-wrapper {
  position: relative;
  min-height: 150px;
}
.eb-openverse-form-wrapper.loading-circle {
  position: relative;
}
.eb-openverse-form-wrapper.loading-circle::after {
  content: "";
  background-color: black;
  opacity: 0.3;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
}
.eb-openverse-form-wrapper.loading-circle::before {
  content: "";
  background-image: url("./template-components/icons/loading.gif");
  background-size: 40px 40px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 40px;
  height: 40px;
}

.eb-openverse-no-data {
  text-align: center;
}
.eb-openverse-no-data p {
  margin: 0 0 20px 0;
  padding: 0;
  font-size: 16px;
  font-weight: 700;
  text-transform: capitalize;
  color: black;
  display: block;
}
.eb-openverse-no-data span {
  font-size: 13px;
  font-weight: 400;
  text-transform: capitalize;
  color: black;
  display: block;
}

.api-info .openverse-input {
  background-color: #fff;
  background-size: 16px 16px;
  background-position: left 10px center;
  background-repeat: no-repeat;
  padding: 10px 2em !important;
  border: 1px solid #2673ff !important;
  border-radius: 0 !important;
  color: #000;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  -webkit-appearance: textfield;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  font-family: sans-serif;
  font-size: 100%;
  line-height: 1.15;
  margin: 0 0 10px 0;
  width: 100%;
  height: 50px;
}
.api-info .openverse-api-btn {
  min-width: 150px;
  margin: 0 auto;
  height: 50px;
  display: block;
  border-radius: 0;
  background-color: #2673ff;
  padding: 6px 12px;
  color: #fff;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  text-decoration: none;
  text-shadow: none;
  white-space: nowrap;
  align-items: center;
  -webkit-appearance: none;
  border: 0;
  box-sizing: border-box;
  cursor: pointer;
  font-family: inherit;
  font-size: 16px;
  font-weight: 400;
  text-decoration: none;
  transition: box-shadow 0.1s linear;
}
.api-info .openverse-api-btn:disabled {
  background-color: #ccc;
}

.eb-alert {
  padding: 0.5rem 0.7rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
}
.eb-alert.eb-alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc;
  font-size: 14px;
  line-height: 1.2rem;
}
.eb-alert .eb-alert-warning {
  color: #ff0000;
  font-size: 12px;
  line-height: 1.2rem;
  font-weight: 400;
  text-transform: capitalize;
  display: inline-block;
  margin-top: 5px;
}

.eb-alert-error {
  font-size: 13px;
  font-weight: 400;
  text-transform: capitalize;
  color: #ff0000;
  display: block;
  margin-bottom: 20px;
}

@media (max-width: 767px) {
  .eb-openverse-grid {
    --size: calc(70vw / 4) !important;
  }
  .openverse-search-section {
    display: block;
  }
  .openverse-search-input {
    margin-bottom: 15px;
  }
  .openverse-search-input,
  .openverse-search-btn {
    width: 100%;
  }
  .openverse-modal__inner {
    left: 0 !important;
    width: 90% !important;
  }
  .search-result-section {
    display: block !important;
  }
  .search-filter,
  .search-result-count {
    display: none;
  }
  .search-section .openverse-search-input,
  .search-section .openverse-search-btn {
    width: auto;
  }
  .search-section .openverse-search-input {
    padding-right: 10px !important;
  }
}/*# sourceMappingURL=editor.css.map */