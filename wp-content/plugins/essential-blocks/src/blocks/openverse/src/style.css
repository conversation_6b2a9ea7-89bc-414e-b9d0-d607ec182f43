/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 */
.eb-openverse-wrapper {
  margin: 0 0 1em 0;
  /* Zoom In #2 */
}
.eb-openverse-wrapper.no-image {
  border-radius: 0 !important;
  border: none !important;
  padding: 0 !important;
}
.eb-openverse-wrapper.img-style-triangle .image-wrapper {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}
.eb-openverse-wrapper.img-style-rhombus .image-wrapper {
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}
.eb-openverse-wrapper.img-style-octagon .image-wrapper {
  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
}
.eb-openverse-wrapper.attribution-style-1 {
  overflow: visible;
}
.eb-openverse-wrapper.attribution-style-1 .image-attribution span {
  display: block;
  margin-bottom: 10px;
}
.eb-openverse-wrapper.attribution-style-2 {
  display: flex;
  flex-direction: column;
}
.eb-openverse-wrapper.attribution-style-2.top {
  flex-direction: column-reverse;
}
.eb-openverse-wrapper .image-wrapper {
  overflow: hidden;
  position: relative;
}
.eb-openverse-wrapper img {
  display: block;
  width: 100%;
  height: 100%;
  max-width: 100%;
  margin: 0 !important;
}
.eb-openverse-wrapper .image-attribution {
  line-height: 1;
}
.eb-openverse-wrapper .image-attribution a {
  color: inherit;
  text-decoration: none;
}
.eb-openverse-wrapper .image-attribution .licensed-wrap {
  text-transform: uppercase;
}
.eb-openverse-wrapper.zoom-in img {
  transform: scale(1);
}
.eb-openverse-wrapper.zoom-in .image-wrapper:hover img {
  transform: scale(1.3);
}
.eb-openverse-wrapper.zoom-out img {
  transform: scale(1.5);
}
.eb-openverse-wrapper.zoom-out .image-wrapper:hover img {
  transform: scale(1);
}
.eb-openverse-wrapper.slide img {
  margin-left: 30px !important;
  transform: scale(1.3);
  transition: 0.3s ease-in-out !important;
}
.eb-openverse-wrapper.slide .image-wrapper:hover img {
  margin-left: 0 !important;
}
.eb-openverse-wrapper.blur img {
  filter: blur(3px);
}
.eb-openverse-wrapper.blur .image-wrapper:hover img {
  filter: blur(0);
}

.loadmore-btn {
  padding: 6px 12px;
  background: #2673ff;
  color: #fff;
  outline: 1px solid #000 0;
  text-decoration: none;
  text-shadow: none;
  white-space: nowrap;
  align-items: center;
  -webkit-appearance: none;
  border: 0;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  font-family: inherit;
  font-size: 16px;
  font-weight: 500;
  height: 45px;
  width: 100%;
  margin: 20px auto 0;
  display: block;
  transition: box-shadow 0.1s linear;
}/*# sourceMappingURL=style.css.map */