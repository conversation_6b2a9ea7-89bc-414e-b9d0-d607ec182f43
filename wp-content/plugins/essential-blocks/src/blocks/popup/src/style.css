.eb-popup-container .eb-popup-button a.eb-popup-button-anchor {
  padding: 15px 30px;
  background-color: rgb(121, 103, 255);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  text-decoration: none;
  text-align: center;
  font-weight: normal;
  line-height: 1.3;
  border-radius: 5px;
  cursor: pointer;
}
.eb-popup-container .eb-popup-button a.eb-popup-button-anchor p {
  padding: 0;
  margin: 0;
}
.eb-popup-container .eb-popup-button a.eb-popup-button-anchor .eb-popup-button-icon-left {
  margin-right: 8px;
}
.eb-popup-container .eb-popup-button a.eb-popup-button-anchor .eb-popup-button-icon-right {
  margin-left: 8px;
}
.eb-popup-container .eb-popup-button.alignment-left {
  text-align: left;
}
.eb-popup-container .eb-popup-button.alignment-right {
  text-align: right;
}
.eb-popup-container .eb-popup-button.alignment-center {
  text-align: center;
}

.eb-popup-overlay {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
  /* display: none; */
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 999;
}

.eb-popup-overlay.active {
  visibility: visible;
  opacity: 1;
}

.eb-popup-overlay.inactive {
  visibility: hidden;
  opacity: 0;
}

.modal-main-wrap,
.modal-main-wrap * {
  box-sizing: border-box;
}

.modal-main-wrap {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
  /* display: none; */
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  overflow: auto;
}
.modal-main-wrap * {
  visibility: hidden !important;
  transition: none !important;
}

.modal-main-wrap.active {
  visibility: visible;
  opacity: 1;
}
.modal-main-wrap.active * {
  visibility: visible !important;
  transition: all 0.3s ease !important;
}

.modal-main-wrap.inactive {
  visibility: hidden;
  opacity: 0;
}
.modal-main-wrap.inactive * {
  visibility: hidden;
  transition: none !important;
}

.eb-modal-container {
  min-height: 100vh;
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
  padding: 50px;
  display: flex;
  flex-wrap: wrap;
}
.eb-modal-container.eb_popup_top_left {
  align-items: flex-start;
  justify-content: flex-start;
}
.eb-modal-container.eb_popup_top_center {
  align-items: flex-start;
  justify-content: center;
}
.eb-modal-container.eb_popup_top_right {
  align-items: flex-start;
  justify-content: flex-end;
}
.eb-modal-container.eb_popup_middle_left {
  align-items: center;
  justify-content: flex-start;
}
.eb-modal-container.eb_popup_middle_center {
  align-items: center;
  justify-content: center;
}
.eb-modal-container.eb_popup_middle_right {
  align-items: center;
  justify-content: flex-end;
}
.eb-modal-container.eb_popup_bottom_left {
  align-items: flex-end;
  justify-content: flex-start;
}
.eb-modal-container.eb_popup_bottom_center {
  align-items: flex-end;
  justify-content: center;
}
.eb-modal-container.eb_popup_bottom_right {
  align-items: flex-end;
  justify-content: flex-end;
}

.eb-popup-content-editor {
  margin: 0 auto;
}

.eb-popup-content,
.eb-popup-content-editor {
  width: auto;
  height: auto;
  background-color: #ffffff;
  box-shadow: 0 10px 30px 0 rgba(0, 0, 0, 0.2);
  position: relative;
  padding: 10px 15px;
  overflow: auto;
}

.eb-popup-close-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 16px;
  line-height: 1.3;
  font-weight: 600;
  color: #000000;
  cursor: pointer;
  z-index: 999;
}

.eb-popup-active {
  display: block;
}

.eb-popup-inactive {
  display: none;
}

.eb-popup-before-content {
  text-align: center;
}

.eb-popup-block-overflow {
  overflow: hidden;
}/*# sourceMappingURL=style.css.map */