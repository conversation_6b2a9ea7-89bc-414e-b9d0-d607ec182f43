.wp-block-essential-blocks-post-carousel {
  width: 100%;
  min-width: 0;
}

.eb-post-carousel-wrapper {
  z-index: 1;
  margin-bottom: 30px;
}
.eb-post-carousel-wrapper.slick-arrows {
  margin-left: 30px;
  margin-right: 30px;
}
.eb-post-carousel-wrapper a,
.eb-post-carousel-wrapper a.ebpg-carousel-post-link,
.eb-post-carousel-wrapper .ebpg-posted-by a {
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}
.eb-post-carousel-wrapper .ebpg-carousel-post-holder a.ebpg-post-link-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
}
.eb-post-carousel-wrapper .ebpg-entry-header.swt-transparent-header {
  position: relative;
}
.eb-post-carousel-wrapper .ebpg-entry-thumbnail {
  position: relative;
  line-height: 0;
}
.eb-post-carousel-wrapper .ebpg-entry-thumbnail img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-post-carousel-wrapper .ebpg-entry-thumbnail:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s ease-in-out;
}
.eb-post-carousel-wrapper .ebpg-entry-thumbnail:hover:after {
  visibility: visible;
  opacity: 1;
}
.eb-post-carousel-wrapper .ebpg-entry-meta {
  display: flex;
  align-items: center;
}
.eb-post-carousel-wrapper .ebpg-entry-meta .ebpg-entry-meta-items {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.eb-post-carousel-wrapper .ebpg-author-avatar a {
  display: block;
}
.eb-post-carousel-wrapper .ebpg-author-avatar a img {
  height: 32px;
  width: 32px;
  max-height: 32px;
  max-width: 32px;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-post-carousel-wrapper .ebpg-meta a {
  padding-left: 5px;
  border-radius: 3px;
  margin-right: 5px;
  margin-bottom: 5px;
  display: inline-block;
  position: relative;
}
.eb-post-carousel-wrapper .ebpg-meta a::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  height: 80%;
  width: 1px;
  background: #9e9e9e;
  transform: translateY(-50%);
}
.eb-post-carousel-wrapper .ebpg-meta a:first-child {
  padding-left: 0;
}
.eb-post-carousel-wrapper .ebpg-meta a:first-child::before {
  content: none;
}
.eb-post-carousel-wrapper .ebpg-post-carousel-column {
  overflow: hidden;
  z-index: 99;
}
.eb-post-carousel-wrapper .ebpg-readmore-btn a {
  display: inline-block;
}
.eb-post-carousel-wrapper.style-1 .ebpg-carousel-post-holder {
  padding: 0 15px 0 15px;
  position: relative;
  z-index: 1;
}
.eb-post-carousel-wrapper.style-1 .ebpg-carousel-post-holder::after {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 70%;
  content: "";
  background: #f8f8fe;
  z-index: -1;
  border-radius: 5px;
  transition: all 300ms;
}
.eb-post-carousel-wrapper.style-1 .ebpg-entry-wrapper {
  padding-bottom: 10px;
}
.eb-post-carousel-wrapper.style-2 .ebpg-carousel-post-holder {
  background-color: #f8f8fe;
  border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-radius: 8px 8px 8px 8px;
}
.eb-post-carousel-wrapper.style-2 .ebpg-entry-wrapper {
  padding: 15px 25px 25px;
}
.eb-post-carousel-wrapper.style-3 .ebpg-carousel-post-holder {
  display: flex;
  gap: 10px;
}
.eb-post-carousel-wrapper.style-3 .ebpg-carousel-post-holder .ebpg-entry-media {
  width: 40%;
}
.eb-post-carousel-wrapper.style-3 .ebpg-carousel-post-holder .ebpg-entry-wrapper {
  width: 60%;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder {
  position: relative;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder .ebpg-entry-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column;
  box-sizing: border-box;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder .ebpg-entry-wrapper .ebpg-entry-meta {
  z-index: 999;
  line-height: 1;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder .ebpg-entry-thumbnail:after {
  visibility: visible;
  opacity: 1;
  border-width: 0px !important;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder .ebpg-author-avatar img {
  width: 24px;
  height: 24px;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder:hover .ebpg-entry-thumbnail:after {
  visibility: visible;
  opacity: 1;
  border-radius: inherit;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder:hover .ebpg-entry-title a {
  text-decoration: underline;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder .ebpg-entry-wrapper > * {
  transform: translate(0px, 0px);
  transition: ease-in 200ms;
}
.eb-post-carousel-wrapper.style-4 .ebpg-carousel-post-holder:hover .ebpg-entry-wrapper > * {
  transform: translate(0px, -10px);
}
.eb-post-carousel-wrapper .slick-prev,
.eb-post-carousel-wrapper .slick-next {
  z-index: 99;
  width: auto;
}
.eb-post-carousel-wrapper .slick-prev::before,
.eb-post-carousel-wrapper .slick-next::before {
  content: "" !important;
  display: none;
}
.eb-post-carousel-wrapper.dot-style-2 .slick-dots li button:before {
  font-size: 0;
}
.eb-post-carousel-wrapper.dot-style-3 .slick-dots li button:before {
  border-radius: 50%;
}
.eb-post-carousel-wrapper.dot-style-3 .slick-dots li.slick-active button:before {
  border-radius: 10px;
}
.eb-post-carousel-wrapper.dot-style-4 .slick-dots li button:before {
  border-radius: 0px;
  height: 5px;
}
.eb-post-carousel-wrapper.equal-height .slick-track {
  display: flex !important;
}
.eb-post-carousel-wrapper.equal-height .slick-slide {
  height: auto !important;
}
.eb-post-carousel-wrapper.equal-height .ebpg-carousel-post-holder {
  height: 100%;
}

.wp-admin .eb-post-carousel-wrapper.equal-height .slick-slide {
  height: auto !important;
}
.wp-admin .eb-post-carousel-wrapper.equal-height .slick-slide > div {
  height: 100%;
}
.wp-admin .eb-post-carousel-wrapper.equal-height .slick-slide > div > article {
  height: 100%;
}/*# sourceMappingURL=style.css.map */