.eb-post-meta-wrapper {
  box-sizing: border-box;
}
.eb-post-meta-wrapper .eb-post-metadata {
  display: flex;
  align-items: center;
}
.eb-post-meta-wrapper .eb-post-metadata.eb-post-meta-stacked {
  flex-direction: column;
}
.eb-post-meta-wrapper .eb-post-metadata.eb-post-meta-inline {
  flex-direction: row;
}
.eb-post-meta-wrapper .eb-post-metadata .eb-post-metadata-item a {
  text-decoration: none;
  line-height: 0;
}
.eb-post-meta-wrapper .eb-post-metadata-label {
  font-weight: bold;
  color: #000000;
}
.eb-post-meta-wrapper .eb-post-metadata-value {
  color: #000000;
}
.eb-post-meta-wrapper .eb-post-meta-stacked .eb-post-metadata-item {
  display: block;
}
.eb-post-meta-wrapper .eb-post-meta-inline .eb-post-metadata-item {
  display: inline-block;
}
.eb-post-meta-wrapper .eb-author-picture {
  display: inline-block;
  vertical-align: middle;
}
.eb-post-meta-wrapper .eb-author-avatar {
  display: block;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-post-meta-wrapper .eb-author-stacked-layout {
  display: flex;
  align-items: center;
  gap: 10px;
}
.eb-post-meta-wrapper .eb-author-stacked-layout .eb-author-picture {
  flex-shrink: 0;
}
.eb-post-meta-wrapper .eb-author-stacked-layout .eb-author-meta-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}
.eb-post-meta-wrapper .eb-author-stacked-layout .eb-author-meta-content .eb-author-info,
.eb-post-meta-wrapper .eb-author-stacked-layout .eb-author-meta-content .eb-date-info {
  display: flex;
  align-items: center;
  gap: 5px;
}
.eb-post-meta-wrapper .eb-author-inline-layout {
  display: flex;
  align-items: center;
  gap: 5px;
}
.eb-post-meta-wrapper .eb-author-inline-layout .eb-author-picture {
  display: inline-flex;
  align-items: center;
}
.eb-post-meta-wrapper .eb-author-inline-layout .eb-post-metadata-label,
.eb-post-meta-wrapper .eb-author-inline-layout .eb-post-metadata-value,
.eb-post-meta-wrapper .eb-author-inline-layout .eb-post-metadata-icon {
  display: inline-flex;
  align-items: center;
}
.eb-post-meta-wrapper .eb-post-metadata-author {
  display: flex;
  align-items: center;
  gap: 10px;
}
.eb-post-meta-wrapper .eb-author-meta-content {
  display: flex;
  align-items: center;
  gap: 5px;
}/*# sourceMappingURL=style.css.map */