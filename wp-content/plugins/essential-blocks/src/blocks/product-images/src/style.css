.eb-product-image_slider {
  width: 100%;
  gap: 12px;
}
.eb-product-image_slider img {
  width: 100%;
  display: inline-block;
  box-sizing: border-box;
}
.eb-product-image_slider.eb-product-gallery-top {
  display: flex;
  flex-direction: column-reverse;
  overflow: hidden;
  max-width: 100%;
  position: relative;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-body {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  overflow: hidden;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-body .slick-list {
  width: 100%;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-body .slick-list .slick-slide {
  max-width: 100%;
  height: 100%;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer {
  max-height: 130px;
  max-width: 100%;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer .eb-product-image_slider-footer-item {
  outline: none;
  padding: 0 5px;
  box-sizing: border-box;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer .slick-list.draggable {
  width: 100%;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer .slick-list.draggable .slick-slide {
  height: 100%;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer .slick-list.draggable .slick-slide div {
  line-height: 0;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer .slick-list.draggable .slick-slide img {
  width: 100%;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer button.slick-prev {
  position: absolute;
  z-index: 2;
  border: none;
  color: black;
  background-color: rgba(255, 255, 255, 0.5450980392);
  cursor: pointer;
  left: 0px;
  bottom: 0px;
  width: 40px;
  height: 100%;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer button.slick-prev i {
  transform: rotate(270deg);
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer button.slick-next {
  position: absolute;
  z-index: 2;
  border: none;
  color: black;
  background-color: rgba(255, 255, 255, 0.5450980392);
  cursor: pointer;
  right: 0px;
  bottom: 0;
  width: 40px;
  height: 100%;
}
.eb-product-image_slider.eb-product-gallery-top .eb-product-image_slider-footer button.slick-next i {
  transform: rotate(270deg);
}
.eb-product-image_slider.eb-product-gallery-bottom {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 100%;
  position: relative;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-body {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  overflow: hidden;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-body .slick-list.draggable {
  width: 100%;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-body .slick-list.draggable .slick-slide {
  max-width: 100%;
  height: 100%;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer {
  max-height: 130px;
  max-width: 100%;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer .eb-product-image_slider-footer-item {
  outline: none;
  padding: 0 5px;
  box-sizing: border-box;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer .slick-list.draggable {
  width: 100%;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer .slick-list.draggable .slick-slide {
  height: 100%;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer .slick-list.draggable .slick-slide div {
  line-height: 0;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer .slick-list.draggable .slick-slide img {
  width: 100%;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer button.slick-prev {
  position: absolute;
  z-index: 2;
  border: none;
  color: black;
  background-color: rgba(255, 255, 255, 0.5450980392);
  cursor: pointer;
  left: 0px;
  bottom: 0px;
  width: 40px;
  height: 100%;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer button.slick-prev i {
  transform: rotate(270deg);
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer button.slick-next {
  position: absolute;
  z-index: 2;
  border: none;
  color: black;
  background-color: rgba(255, 255, 255, 0.5450980392);
  cursor: pointer;
  right: 0px;
  bottom: 0;
  width: 40px;
  height: 100%;
}
.eb-product-image_slider.eb-product-gallery-bottom .eb-product-image_slider-footer button.slick-next i {
  transform: rotate(270deg);
}
.eb-product-image_slider.eb-product-gallery-left {
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
}
.eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-body {
  max-width: calc(100% - 117px);
  margin: 0;
}
@media (max-width: 600px) {
  .eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-body {
    max-width: 70%;
    margin: 0;
  }
}
.eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer {
  display: flex;
  align-items: center;
  max-width: 105px;
  position: relative;
  overflow: hidden;
  line-height: 0;
}
.eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer .eb-product-image_slider-footer-item {
  outline: none;
  max-height: 100px;
  padding: 10px 0;
}
.eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer .slick-list.draggable {
  overflow: hidden;
}
@media (max-width: 1200px) {
  .eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer .slick-list.draggable {
    height: 62vw !important;
    overflow: hidden;
  }
}
.eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer button.slick-prev {
  position: absolute;
  top: 4%;
  left: 0;
  z-index: 2;
  height: 40px;
  width: 100%;
  border: none;
  color: black;
  background-color: rgba(255, 255, 255, 0.5450980392);
  cursor: pointer;
}
.eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer button.slick-prev::before {
  display: inline-block;
  transform: rotate(90deg) !important;
}
.eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer button.slick-next {
  position: absolute;
  z-index: 2;
  height: 40px;
  width: 100%;
  border: none;
  top: 96%;
  left: 0;
  right: 0;
  color: black;
  background-color: rgba(255, 255, 255, 0.5450980392);
  cursor: pointer;
}
.eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer button.slick-next::before {
  display: inline-block;
  transform: rotate(90deg) !important;
}
@media (max-width: 600px) {
  .eb-product-image_slider.eb-product-gallery-left .eb-product-image_slider-footer {
    flex: 1;
  }
}
.eb-product-image_slider.eb-product-gallery-right {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
}
.eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-body {
  max-width: calc(100% - 117px);
  display: inline-block;
}
@media (max-width: 600px) {
  .eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-body {
    max-width: 70%;
    margin: 0;
  }
}
.eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer {
  max-width: 105px;
  display: inline-block;
  position: relative;
  overflow: hidden;
  line-height: 0;
}
.eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer .eb-product-image_slider-footer-item {
  outline: none;
  max-height: 100px;
  padding: 10px 0;
}
.eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer .slick-list.draggable {
  height: shrink !important;
  overflow: hidden;
  line-height: 0;
}
@media (max-width: 1200px) {
  .eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer .slick-list.draggable {
    height: 62vw !important;
    overflow: hidden;
  }
}
.eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer button.slick-prev {
  position: absolute;
  left: 0;
  top: 0;
  bottom: auto;
  z-index: 2;
  height: 60px;
  width: 100%;
  border: none;
  transform: unset;
  color: #000000;
  background-color: rgba(255, 255, 255, 0.5450980392);
  cursor: pointer;
}
.eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer button.slick-prev::before {
  display: inline-block;
  transform: rotate(90deg) !important;
}
.eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer button.slick-next {
  position: absolute;
  z-index: 2;
  height: 60px;
  width: 100%;
  border: none;
  top: auto;
  bottom: 0;
  left: 0;
  right: 0;
  transform: unset;
  color: black;
  background-color: rgba(255, 255, 255, 0.5450980392);
  cursor: pointer;
}
.eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer button.slick-next::before {
  display: inline-block;
  transform: rotate(90deg) !important;
}
@media (max-width: 600px) {
  .eb-product-image_slider.eb-product-gallery-right .eb-product-image_slider-footer {
    flex: 1;
  }
}
.eb-product-image_slider .eb-product-image_slider-body-item {
  display: flex !important;
  align-items: center;
}

.eb-product-images-disable-nav .eb-product-image_slider .slick-list {
  padding: 0 !important;
}/*# sourceMappingURL=style.css.map */