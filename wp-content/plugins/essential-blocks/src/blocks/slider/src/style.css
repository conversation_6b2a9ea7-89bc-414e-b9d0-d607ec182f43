.wp-block-essential-blocks-slider {
  min-width: 0;
}

.eb-slider-wrapper .slick-slide {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  pointer-events: none;
}
.eb-slider-wrapper .slick-slide.slick-active {
  pointer-events: auto;
}
.eb-slider-wrapper .eb-slider-type-image:not(.slick-initialized):not(.slick-vertical) {
  display: flex;
}
.eb-slider-wrapper .eb-slider-type-image .eb-slider-item img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-slider-wrapper .eb-slider-second-button {
  background-color: #fff;
  color: #000;
}
.eb-slider-wrapper .eb-slider-type-content {
  position: relative;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item {
  position: relative;
  overflow: hidden;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item .eb-slider-button-wrapper {
  display: block;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item .eb-slider-button,
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item .eb-slider-second-button {
  text-decoration: none;
  display: inline-block;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-1 .eb-slider-content {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  padding: 20px 30px;
  display: flex;
  flex-flow: column;
  box-sizing: border-box;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-1 .eb-slider-content.align-left .eb-slider-button, .eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-1 .eb-slider-content.align-justify .eb-slider-button {
  align-self: flex-start;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-1 .eb-slider-content.align-center .eb-slider-button {
  align-self: center;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-1 .eb-slider-content.align-right .eb-slider-button {
  align-self: flex-end;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-3 {
  display: flex !important;
  justify-content: flex-start;
  flex-direction: row;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-3 div {
  flex: 1 1 100%;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-3 .eb-slider-content {
  padding: 0px 15px;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-4 {
  display: flex !important;
  justify-content: space-between;
  flex-direction: row-reverse;
  flex-wrap: nowrap;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-4 div {
  flex: 1 1 100%;
}
.eb-slider-wrapper .eb-slider-type-content .eb-slider-item.content-4 .eb-slider-content {
  padding: 0px 15px;
}
.eb-slider-wrapper .slick-prev,
.eb-slider-wrapper .slick-next {
  z-index: 99;
  text-align: center;
}
.eb-slider-wrapper .slick-prev::before,
.eb-slider-wrapper .slick-next::before {
  content: none;
}
.eb-slider-wrapper .slick-prev i,
.eb-slider-wrapper .slick-next i {
  font-size: 20px;
  color: #000;
}
.eb-slider-wrapper ul.slick-dots {
  margin: 0;
}
.eb-slider-wrapper .eb-slider-init.slick-initialized::before {
  display: none;
}
.eb-slider-wrapper .eb-slider-init.slick-initialized .eb-slider-item {
  visibility: visible;
}
.eb-slider-wrapper .eb-slider-init::before {
  background-image: url(../../../assets/images/ajax-loader.gif);
  background-size: cover;
  content: "";
  display: block;
  height: 50px;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
}
.eb-slider-wrapper .eb-slider-init .eb-slider-item {
  visibility: hidden;
}

[dir=rtl] .eb-slider-wrapper .slick-prev::before,
[dir=rtl] .eb-slider-wrapper .slick-next::before {
  content: none;
}
[dir=rtl] .eb-slider-wrapper .slick-prev {
  left: auto;
}
[dir=rtl] .eb-slider-wrapper .slick-prev .fa-arrow-alt-circle-left {
  transform: rotate(180deg);
}
[dir=rtl] .eb-slider-wrapper .slick-next {
  right: auto;
}
[dir=rtl] .eb-slider-wrapper .slick-next .fa-arrow-alt-circle-right {
  transform: rotate(180deg);
}
[dir=rtl] .eb-slider-wrapper .eb-slider-type-content .eb-slider-item .eb-slider-content {
  text-align: right;
  direction: rtl;
}/*# sourceMappingURL=style.css.map */