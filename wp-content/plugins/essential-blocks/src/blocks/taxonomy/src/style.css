.eb-taxonomies-wrapper.display-inline {
  display: flex;
}
.eb-taxonomies-wrapper.display-block {
  display: block;
}
.eb-taxonomies-wrapper.display-block .eb-tax-wrap {
  flex-direction: column;
}
.eb-taxonomies-wrapper .eb-tax-wrap {
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
}
.eb-taxonomies-wrapper .eb-tax-wrap span {
  padding: 0;
  margin: 0;
}
.eb-taxonomies-wrapper .eb-tax-wrap a {
  text-decoration: none;
  display: inline-block;
}
.eb-taxonomies-wrapper .eb-tax-separator:last-child {
  display: none;
}/*# sourceMappingURL=style.css.map */