.eb-team-wrapper .socials.socials-title li a {
  height: auto !important;
  width: auto !important;
  display: flex;
  gap: 5px;
}
.eb-team-wrapper.preset3.content-overlay:hover .eb-team-member-contents {
  opacity: 1;
  visibility: visible;
}
.eb-team-wrapper.preset3.content-overlay .eb-team-inner {
  position: relative;
}
.eb-team-wrapper.preset3.content-overlay .eb-team-member-contents {
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
}
.eb-team-wrapper.new-preset1, .eb-team-wrapper.new-preset2, .eb-team-wrapper.new-preset3 {
  line-height: 0;
}
.eb-team-wrapper.new-preset1 .eb-team-member-contents, .eb-team-wrapper.new-preset2 .eb-team-member-contents, .eb-team-wrapper.new-preset3 .eb-team-member-contents {
  position: absolute;
  left: 0;
  top: auto !important;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.eb-team-wrapper.new-preset1 .eb-team-member-contents .eb-team-member-job-title,
.eb-team-wrapper.new-preset1 .eb-team-member-contents .eb-team-member-name, .eb-team-wrapper.new-preset2 .eb-team-member-contents .eb-team-member-job-title,
.eb-team-wrapper.new-preset2 .eb-team-member-contents .eb-team-member-name, .eb-team-wrapper.new-preset3 .eb-team-member-contents .eb-team-member-job-title,
.eb-team-wrapper.new-preset3 .eb-team-member-contents .eb-team-member-name {
  line-height: 1em;
}
.eb-team-wrapper.new-preset1 .eb-team-member-contents .eb-team-member-description, .eb-team-wrapper.new-preset2 .eb-team-member-contents .eb-team-member-description, .eb-team-wrapper.new-preset3 .eb-team-member-contents .eb-team-member-description {
  line-height: 1.4em;
}
.eb-team-wrapper.new-preset1 .eb-team-member-contents .eb-team-member-social-separator, .eb-team-wrapper.new-preset2 .eb-team-member-contents .eb-team-member-social-separator, .eb-team-wrapper.new-preset3 .eb-team-member-contents .eb-team-member-social-separator {
  background: transparent;
}
.eb-team-wrapper.new-preset1:hover .eb-team-member-contents {
  bottom: 0;
  top: auto !important;
}
.eb-team-wrapper.new-preset1 .eb-team-member-contents {
  bottom: -100%;
  transition: 0.5s;
  height: auto !important;
}
.eb-team-wrapper.new-preset1 .eb-team-member-job-title {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  position: absolute;
  top: 20px;
  right: 30px;
  transform: rotate(-180deg);
}
.eb-team-wrapper.new-preset2:hover .eb-team-member-contents {
  transform: translate(0px, -10px) rotate(0deg);
}
.eb-team-wrapper.new-preset2 .eb-team-inner {
  padding-bottom: 60px;
}
.eb-team-wrapper.new-preset2 .eb-team-member-contents {
  bottom: -15px;
  height: auto !important;
  transform: translate(490px, 145px) rotate(45deg);
  transition: all 0.5s ease-in-out;
}
.eb-team-wrapper.new-preset3 {
  overflow: unset !important;
}
.eb-team-wrapper.new-preset3.hover-left:hover .eb-team-member-image {
  transform: rotate3d(0, 1, 0, -180deg);
}
.eb-team-wrapper.new-preset3.hover-right:hover .eb-team-member-image {
  transform: rotate3d(0, 1, 0, 180deg);
  z-index: 12;
}
.eb-team-wrapper.new-preset3.hover-right .eb-team-member-image {
  transform-origin: 100% 50%;
}
.eb-team-wrapper.new-preset3 .eb-team-member-image {
  transform-origin: 0% 50%;
  z-index: 11;
  transition: all 0.5s ease-in-out;
  position: relative;
}
.eb-team-wrapper.new-preset3 .eb-team-member-contents {
  top: 0 !important;
  height: 100% !important;
}
.eb-team-wrapper.new-preset3 .eb-team-member-contents .eb-team-member-contents-inner {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.eb-team-wrapper.new-preset3 .eb-team-member-contents .eb-team-member-contents-inner .eb-team-member-social-separator {
  flex-grow: 1;
}
@media (max-width: 767px) {
  .eb-team-wrapper.new-preset3.hover-left:hover .eb-team-member-image, .eb-team-wrapper.new-preset3.hover-right:hover .eb-team-member-image {
    transform: rotate3d(1, 0, 0, 180deg);
  }
  .eb-team-wrapper.new-preset3.hover-left .eb-team-member-image, .eb-team-wrapper.new-preset3.hover-right .eb-team-member-image {
    transform-origin: 50% 0%;
  }
}/*# sourceMappingURL=style.css.map */