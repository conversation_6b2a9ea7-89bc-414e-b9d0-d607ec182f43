.eb-toggle-wrapper > * {
  position: relative;
}

.eb-toggle-switch {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 60px;
  height: 34px;
  margin: 0 10px;
}

.eb-toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.eb-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.eb-toggle-controller {
  cursor: pointer;
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
}

.toggle-s {
  width: 48px;
  height: 26px;
}
.toggle-s .eb-toggle-controller {
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
}

.toggle-l {
  width: 72px;
  height: 36px;
}
.toggle-l .eb-toggle-controller {
  height: 28px;
  width: 28px;
  left: 4px;
  bottom: 4px;
}

.toggle-xl {
  width: 84px;
  height: 42px;
}
.toggle-xl .eb-toggle-controller {
  height: 32px;
  width: 32px;
  left: 5px;
  bottom: 5px;
}

.eb-toggle-diamond {
  vertical-align: middle;
  width: 20px;
  height: 20px;
  background: #3074ff;
  transform: rotate(-45deg);
}

.eb-toggle-circle {
  vertical-align: middle;
  width: 20px;
  height: 20px;
  background: #3074ff;
  border-radius: 50%;
  transform: rotate(0deg);
}

.eb-toggle-primary-label,
.eb-toggle-secondary-label {
  cursor: pointer;
}

.eb-text-switch-content {
  display: flex;
  text-align: center;
  width: 30%;
}

.eb-text-switch-label,
.eb-text-switch-toggle {
  height: 2.6em;
  border-radius: 100px;
}

.eb-text-switch-label {
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 100px;
  position: relative;
  cursor: pointer;
}

.eb-text-switch-toggle {
  position: absolute;
  height: 100%;
  width: 50%;
  background-color: #fff;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.eb-switch-names {
  height: 100%;
  width: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-around;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.eb-switch-names span {
  flex: 1;
}

.eb-switch-names .eb-toggle-secondary-label {
  opacity: 0.5;
}

/* -------- Switch Styles ------------*/
.eb-toggle-switch input[type=checkbox] {
  display: none;
}

[type=checkbox]:checked + .eb-text-switch-wrapper .eb-text-switch-toggle {
  transform: translateX(100%);
}

[type=checkbox]:checked + .eb-text-switch-wrapper .eb-toggle-secondary-label {
  opacity: 1;
}

[type=checkbox]:checked + .eb-text-switch-wrapper .eb-toggle-primary-label {
  opacity: 0.5;
}

.eb-toggle-content > .wp-block-essential-blocks-wrapper,
.eb-toggle-content > .wp-block-essential-blocks-row,
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-wrapper,
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-row {
  position: relative;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.4s ease-in-out;
}
.eb-toggle-content > .wp-block-essential-blocks-wrapper.active, .eb-toggle-content > .wp-block-essential-blocks-wrapper[data-active-tab=active],
.eb-toggle-content > .wp-block-essential-blocks-row.active,
.eb-toggle-content > .wp-block-essential-blocks-row[data-active-tab=active],
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-wrapper.active,
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-wrapper[data-active-tab=active],
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-row.active,
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-row[data-active-tab=active] {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  height: auto;
  z-index: 11;
}
.eb-toggle-content > .wp-block-essential-blocks-wrapper.inactive, .eb-toggle-content > .wp-block-essential-blocks-wrapper[data-active-tab=inactive],
.eb-toggle-content > .wp-block-essential-blocks-row.inactive,
.eb-toggle-content > .wp-block-essential-blocks-row[data-active-tab=inactive],
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-wrapper.inactive,
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-wrapper[data-active-tab=inactive],
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-row.inactive,
.eb-toggle-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .wp-block-essential-blocks-row[data-active-tab=inactive] {
  height: 0;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
}
.eb-toggle-content .wp-block-essential-blocks-image-gallery {
  z-index: 999;
}/*# sourceMappingURL=style.css.map */