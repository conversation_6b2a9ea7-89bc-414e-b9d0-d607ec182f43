.eb-wpforms-custom-radio-checkbox input[type=checkbox],
.eb-wpforms-custom-radio-checkbox input[type=radio] {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-style: solid;
  border-width: 0;
  outline: none;
  min-width: 1px;
  width: 15px;
  height: 15px;
  background: #ddd;
  padding: 3px;
}

.eb-wpforms-custom-radio-checkbox input[type=checkbox]:before,
.eb-wpforms-custom-radio-checkbox input[type=radio]:before {
  content: "";
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  display: block;
}

.eb-wpforms-custom-radio-checkbox input[type=checkbox]:checked:before,
.eb-wpforms-custom-radio-checkbox input[type=radio]:checked:before {
  background: #999;
  transition: all 0.25s linear 0s;
}

.eb-wpforms-custom-radio-checkbox input[type=radio] {
  border-radius: 50%;
}

.eb-wpforms-custom-radio-checkbox input[type=radio]:before {
  border-radius: 50%;
}

.eb-wpforms-wrapper .wpforms-container .wpforms-form input[type=checkbox],
.eb-wpforms-wrapper .wpforms-container .wpforms-form input[type=radio] {
  padding: 3px;
}/*# sourceMappingURL=style.css.map */