.eb-block-default-popup {
  left: 0 !important;
  top: 0 !important;
  height: 100% !important;
  width: 100% !important;
  background-color: rgba(0, 0, 0, 0.4588235294);
  transform: unset !important;
  transform-origin: unset !important;
}
.eb-block-default-popup .components-popover__content {
  position: relative;
  max-height: 80% !important;
  min-height: 70%;
  height: auto;
  width: auto;
  max-width: 80%;
  top: 10%;
  left: 50%;
  transform: translate(-50%, 0%) !important;
  overflow: visible !important;
  display: flex;
  justify-content: center;
}
.eb-block-default-popup .components-popover__content .eb-block-default {
  overflow: scroll;
  max-height: 80vh;
  min-width: 300px;
  width: 300px;
  box-sizing: border-box;
  background-color: #f6f6f6;
}
.eb-block-default-popup .components-popover__content .block-editor-block-preview__container {
  box-sizing: border-box;
  overflow: unset;
}
.eb-block-default-popup .components-popover__content .btn-block-default {
  position: absolute;
  z-index: 999;
}
.eb-block-default-popup .components-popover__content .btn-block-default.btn-block-default-close {
  top: -15px;
  right: -15px;
  background-color: white;
  color: #000;
  font-size: 14px;
  line-height: 1;
  font-weight: 700;
  height: 30px;
  width: 30px;
  border-radius: 50%;
  border: 1px solid #c4c4c4;
  padding: 0;
  text-align: center;
  display: block;
}
.eb-block-default-popup .components-popover__content .btn-block-default.btn-block-default-close:focus {
  outline: none;
  box-shadow: none;
}
.eb-block-default-popup .components-popover__content .btn-block-default.btn-block-default-save {
  bottom: 10px;
  right: 12px;
  background-color: #871fe3;
  color: #fff;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 4px;
}
.eb-block-default-popup .block-editor-block-preview__container .block-editor-block-preview__content iframe {
  width: auto !important;
}

.components-popover[data-x-axis=right].eb-block-default-popup .components-popover__content {
  position: relative !important;
  left: 50% !important;
}
.components-popover[data-x-axis=right].eb-block-default-popup .components-popover__content > div {
  display: flex;
  justify-content: center;
  width: 100%;
}
.components-popover[data-x-axis=right].eb-block-default-popup .components-popover__content > div .block-editor-block-preview__content iframe {
  min-height: 150px;
}

.components-menu-group .components-menu-item__button svg {
  max-width: 24px;
  max-height: 24px;
}

.eb-panel-control .eb-tab-control-item > div {
  margin-bottom: 20px;
}
.eb-panel-control .eb-tab-control-item > div:last-child {
  margin-bottom: 0;
}
.eb-panel-control .eb-block-button {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}
.eb-panel-control .eb-block-button .eb-global-icon {
  width: 20px;
  height: auto;
  margin-right: 10px;
}
.eb-panel-control .eb-block-button .eb-block-default-button {
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: unset !important;
  color: #1D2939 !important;
  display: flex;
  flex-wrap: wrap;
  height: auto;
  justify-content: flex-start;
  max-width: 200px;
  outline: 0 !important;
  width: 100%;
  padding: 10px 15px;
  transition: 0.3s;
}
.eb-panel-control .eb-block-button .eb-block-default-button:hover {
  border-color: #5632d1 !important;
  box-shadow: unset !important;
  color: #5632d1 !important;
}
.eb-panel-control .eb-block-button .eb-block-default-button .active .dashicon {
  font-size: 14px;
  color: #e39eff;
  line-height: 21px;
}
.eb-panel-control .eb-global-typography button:focus {
  outline: 0;
  box-shadow: unset;
}
.eb-panel-control .eb-global-typography .components-navigator-back-button {
  padding-left: 0;
  margin-bottom: 10px;
}
.eb-panel-control .eb-global-typography .components-navigator-back-button .dashicon {
  margin-right: 10px;
}
.eb-panel-control .eb-global-typography .note {
  font-size: 0.95em;
  font-style: italic;
}
.eb-panel-control .eb-global-typography .eb-typography-control-wrapper {
  border-top: 1px solid #cbc8c8;
  padding-top: 10px;
}
.eb-panel-control .global-controls-save {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}
.eb-panel-control .global-controls-save button {
  background-color: #2271b1;
  color: #fff;
  gap: 8px;
  box-shadow: unset !important;
}
.eb-panel-control .global-controls-save button.is-secondary {
  background-color: #ebebeb;
  color: #6b6a6a;
}
.eb-panel-control .global-controls-save button.is-secondary:hover {
  background-color: #ddd5d5;
  box-shadow: unset !important;
  color: #222;
}

.eb-block-default .eb-panel-control .components-panel__body-title {
  background-color: #e9e9e9;
}
.eb-block-default .eb-panel-control .components-panel__body.is-opened {
  background-color: #f6f6f6;
}
.eb-block-default .eb-panel-control hr.components-divider {
  border-top: none;
  border-bottom: 1px solid #f0f0f0;
  margin: 1.5em 0;
}

.eb-block-default-preveiw-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}
.eb-block-default-preveiw-wrapper .eb-block-default-heading {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #211c70;
  padding: 10px;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.15);
}
.eb-block-default-preveiw-wrapper .block-editor-block-preview__container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  max-width: 100%;
  height: 60%;
  display: flex;
  justify-content: left;
  align-items: flex-start;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}
.eb-block-default-preveiw-wrapper .block-default-popup-footer {
  position: absolute;
  z-index: 999;
  width: 100%;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  box-shadow: -2px 0px 5px rgba(0, 0, 0, 0.15);
}
.eb-block-default-preveiw-wrapper .block-default-popup-footer .btn-block-default-save {
  background: #3b82f4;
  border-radius: 5px;
  color: #fff;
  font-weight: 500;
  font-size: 12px;
  line-height: 24px;
  padding: 4px 15px;
  height: 30px;
}
.eb-block-default-preveiw-wrapper .block-default-popup-footer .btn-block-default-link {
  color: #6a72a5;
  font-weight: 500;
  font-size: 12px;
  line-height: 24px;
}
.eb-block-default-preveiw-wrapper .block-default-popup-footer .btn-block-default-link:hover {
  color: #3b82f4;
}
.eb-block-default-preveiw-wrapper .block-default-popup-footer .btn-block-default-reset {
  margin-right: auto;
}

.preview-not-available {
  font-size: 16px;
  position: absolute;
  left: 50%;
  top: 43%;
  transform: translate(-50%, -50%);
  padding: 0;
}

.media-modal {
  z-index: 999999999 !important;
}

.eb-global-controls .eb-color-panel {
  border-top: 0 !important;
}
.eb-global-controls .eb-add-btn {
  text-align: center;
  margin-top: 15px;
}
.eb-global-controls .eb-add-btn .eb-add-btn__button {
  text-decoration: none !important;
  border: 1px solid #037cba;
  color: #037cba;
}
.eb-global-controls .eb-add-btn .eb-add-btn__button .dashicon {
  margin-left: 5px;
}
.eb-global-controls .eb-custom-panel .eb-global-typography-element-content {
  overflow: hidden;
}
.eb-global-controls .eb-custom-panel .eb-global-typography-element-content .font_preview_box {
  width: 100%;
  height: 70px;
  text-align: center;
  background-color: #e2e2e2;
  align-items: center;
  justify-content: center;
  display: flex;
  margin-bottom: 10px;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item,
.eb-global-controls .eb-custom-panel .eb-custom-element {
  position: relative;
  display: flex;
  gap: 10px;
  align-items: center;
  border: 1px solid #ddd;
  border-bottom: 0px;
  padding: 5px 15px;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item:first-child,
.eb-global-controls .eb-custom-panel .eb-custom-element:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item:last-child,
.eb-global-controls .eb-custom-panel .eb-custom-element:last-child {
  border-bottom: 1px solid #ddd;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .eb-custom-element__edit-input,
.eb-global-controls .eb-custom-panel .eb-custom-element .eb-custom-element__edit-input {
  margin: 0 !important;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .eb-custom-element__edit-input > div,
.eb-global-controls .eb-custom-panel .eb-custom-element .eb-custom-element__edit-input > div {
  margin: 0 !important;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .eb-custom-element__edit-input input,
.eb-global-controls .eb-custom-panel .eb-custom-element .eb-custom-element__edit-input input {
  background-color: transparent;
  border: 0 !important;
  outline: 0;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .components-navigator-button,
.eb-global-controls .eb-custom-panel .eb-custom-element .components-navigator-button {
  padding: 0;
  width: 100%;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .components-navigator-button .font_preview,
.eb-global-controls .eb-custom-panel .eb-custom-element .components-navigator-button .font_preview {
  margin-right: 10px;
  padding: 4px;
  background-color: #e2e2e2;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .components-navigator-button .edit_indecator,
.eb-global-controls .eb-custom-panel .eb-custom-element .components-navigator-button .edit_indecator {
  border: 3px solid #037cba;
  border-radius: 50%;
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -2px;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .eb-delete-item,
.eb-global-controls .eb-custom-panel .eb-custom-element .eb-delete-item {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(0px, -50%);
  border: 0px;
  background-color: #f54242;
  color: #fff;
  padding: 0;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  text-align: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .eb-delete-item:hover,
.eb-global-controls .eb-custom-panel .eb-custom-element .eb-delete-item:hover {
  cursor: pointer;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item .eb-delete-item .dashicons,
.eb-global-controls .eb-custom-panel .eb-custom-element .eb-delete-item .dashicons {
  font-size: 16px;
  line-height: 1em;
  padding-top: 2px;
}
.eb-global-controls .eb-custom-panel .components-tools-panel-item:hover .eb-delete-item,
.eb-global-controls .eb-custom-panel .eb-custom-element:hover .eb-delete-item {
  visibility: visible;
  opacity: 1;
  transform: translate(-10px, -50%);
}
.eb-global-controls .eb-global-color-tab-panel .components-tab-panel__tabs {
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
}
.eb-global-controls .eb-global-color-tab-panel .components-tab-panel__tabs .components-tab-panel__tabs-item {
  padding: 0;
  margin-right: 15px;
  min-width: 50px;
  border-bottom: 2px solid transparent;
}
.eb-global-controls .eb-global-color-tab-panel .components-tab-panel__tabs .components-tab-panel__tabs-item.active-tab {
  border-bottom: 2px solid #000;
}
.eb-global-controls .eb-global-color-tab-panel .eb-color-panel {
  padding: 0;
}
.eb-global-controls .eb-global-color-tab-panel .eb-custom-gradient-color-panel .item-content {
  padding: 0px !important;
}
.eb-global-controls .eb-global-color-tab-panel .eb-gradient-color-label {
  color: #1e1e1e !important;
  font-size: 13px !important;
  margin-bottom: 15px !important;
}
.eb-global-controls .eb-global-color-tab-panel .eb-gradient-color-list {
  display: flex;
  flex-flow: column;
}
.eb-global-controls .eb-global-color-tab-panel .eb-gradient-color-list .eb-custom-color-item:hover {
  cursor: pointer;
}
.eb-global-controls .eb-global-color-tab-panel .eb-gradient-color-list .eb-custom-color-item:hover .actions {
  transform: translate(0px, 0px);
  opacity: 1;
  visibility: visible;
}
.eb-global-controls .eb-global-color-tab-panel .eb-gradient-color-list .eb-custom-color-item .item-content {
  padding: 10px 0px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}
.eb-global-controls .eb-global-color-tab-panel .eb-gradient-color-list .eb-custom-color-item .actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 5px;
  transform: translate(0px, -15px);
  opacity: 0;
  visibility: hidden;
  width: 0;
}
.eb-global-controls .eb-global-color-tab-panel .eb-gradient-color-list .eb-custom-color-item .actions .dashicon {
  font-size: 14px;
}
.eb-global-controls .eb-global-color-tab-panel .eb-gradient-color-list .eb-custom-color-item .actions .eb-delete {
  background-color: #f54242;
  color: #fff;
  padding: 0;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  padding-top: 3px;
  text-align: center;
  margin-top: -5px;
}

.eb-gradient-color-popup-content {
  padding: 15px;
}
.eb-gradient-color-popup-content .components-circular-option-picker__swatches {
  display: none !important;
}
.eb-gradient-color-popup-content .components-circular-option-picker__custom-clear-wrapper {
  margin: 0px !important;
}/*# sourceMappingURL=style.css.map */