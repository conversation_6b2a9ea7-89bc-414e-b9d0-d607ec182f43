.eb-write-ai-button-wrapper {
  min-width: 140px;
}

.eb-write-ai-button {
  background-color: #ebe4ff;
  color: #6c3bff;
  border: 1px solid #ebe4ff;
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  text-transform: capitalize;
  border-radius: 2px;
  padding: 5px 10px;
  transition: ease-in 0.3s;
}
.eb-write-ai-button:hover {
  cursor: pointer;
  color: #6c3bff;
  border: 1px solid #6c3bff;
}
.eb-write-ai-button:focus {
  outline: 0;
  box-shadow: unset !important;
}
.eb-write-ai-button img {
  height: 18px;
  margin-right: 5px;
}

.eb-write-ai-popover {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  padding: 30px;
  transform: unset !important;
  height: 100vh !important;
  width: 100% !important;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999999 !important;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.eb-write-ai-popover .components-popover__content {
  width: 80%;
  min-width: 500px;
  max-width: 650px;
  z-index: 1;
  left: 50%;
  top: 50px;
  position: relative;
  transform: translateX(-50%);
  box-shadow: none;
  border-radius: 0;
  padding: 0;
  background: transparent;
  overflow: hidden !important;
}

.is-sidebar-opened .eb-write-ai-popover {
  width: calc(100% - 280px) !important;
}

.eb-write-ai-popover-content {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #eaecf0;
  box-shadow: 0px 15px 25px -10px rgba(32, 31, 80, 0.15);
  width: 100%;
}
.eb-write-ai-popover-content .eb-write-ai-header {
  padding: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #eaeaea;
}
.eb-write-ai-popover-content .eb-write-ai-header .eb-write-ai-heading {
  color: #333;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 1.4em;
  margin: 0px 0px 20px;
  display: flex;
  gap: 10px;
  align-items: center;
}
.eb-write-ai-popover-content .eb-write-ai-header .eb-write-ai-heading svg {
  background: #6c3bff;
  border-radius: 50%;
  height: 50px;
  width: 50px;
  padding: 10px;
}
.eb-write-ai-popover-content .eb-write-ai-header p {
  color: #667085;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.4em;
  margin: 0px;
}
.eb-write-ai-popover-content .eb-write-ai-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 30px;
  overflow: scroll;
  height: auto;
  max-height: calc(90vh - 200px);
}
.eb-write-ai-popover-content .eb-write-ai-form label {
  font-weight: 600 !important;
  margin-bottom: 5px;
  display: block !important;
  font-size: 14px !important;
  line-height: 15px !important;
  text-transform: capitalize !important;
}
.eb-write-ai-popover-content .eb-write-ai-form input,
.eb-write-ai-popover-content .eb-write-ai-form textarea,
.eb-write-ai-popover-content .eb-write-ai-form select {
  width: 100% !important;
  height: auto !important;
  padding: 10px !important;
  box-sizing: border-box !important;
  border: 1px solid #D0D5DD !important;
  border-radius: 4px;
  color: #667085 !important;
  font-weight: 400;
  font-size: 13px !important;
}
.eb-write-ai-popover-content .eb-write-ai-form .eb-write-ai-note p {
  margin: 0 0 10px;
}
.eb-write-ai-popover-content .eb-write-ai-form .eb-write-ai-generate-button-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.eb-write-ai-popover-content .eb-write-ai-form .eb-write-ai-generate-button-wrapper button {
  background: #6c3bff;
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.4em;
  padding: 12px 15px;
  text-decoration: none;
  display: flex;
  gap: 5px;
  height: auto;
  border-radius: 4px;
}
.eb-write-ai-popover-content .eb-write-ai-form .eb-write-ai-generate-button-wrapper button:disabled {
  background-color: #9c7aff;
}
.eb-write-ai-popover-content .eb-write-ai-form .eb-write-ai-generate-button-wrapper button svg {
  fill: transparent;
  height: 20px;
}
.eb-write-ai-popover-content .eb-write-ai-form .eb-write-ai-generate-button-wrapper button img.eb-install-loader {
  height: 20px;
}
.eb-write-ai-popover-content .eb-write-ai-form .components-input-control__backdrop {
  border: 0px transparent !important;
}
.eb-write-ai-popover-content .eb-write-ai-form .eb-write-ai-api-key-warning {
  background-color: #fbebed;
  padding: 12px;
  border-radius: 5px;
  font-size: 14px;
  line-height: 1.4em;
  border-left: 4px solid #d63638;
}
.eb-write-ai-popover-content .eb-button {
  background: #6c3bff;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.4em;
  padding: 15px;
  text-decoration: none;
  display: flex;
  gap: 10px;
  height: auto;
}
.eb-write-ai-popover-content .eb-button.eb-button-error {
  background-color: #e01d1d;
}
.eb-write-ai-popover-content .eb-button img {
  height: 24px;
}
.eb-write-ai-popover-content .eb-hide-button {
  margin-top: 15px;
  padding: 0;
  height: auto;
  font-size: 13px;
  text-decoration: underline;
  text-transform: capitalize;
  font-style: italic;
  color: #475467;
}
.eb-write-ai-popover-content .pattern-content {
  padding: 20px 0 20px 20px;
}
.eb-write-ai-popover-content .pattern-img {
  text-align: right;
  padding: 5px;
  padding-left: 15px;
}
.eb-write-ai-popover-content .pattern-img img {
  height: 100%;
  max-width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 10px;
  margin: 0;
  padding: 0;
}

.eb-write-ai-close-btn {
  border-radius: 8px;
  border: 1px solid #eaecf0;
  background: #f4f4f4;
  width: 32px;
  height: 32px;
  position: absolute;
  right: 30px;
  top: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (max-width: 1024px) {
  .eb-write-ai-popover {
    width: 100% !important;
  }
  .eb-write-ai-popover .components-popover__content {
    min-width: 500px;
  }
  .eb-write-ai-popover .components-popover__content .eb-write-ai-popover-content {
    display: flex;
    flex-flow: column-reverse;
  }
  .eb-write-ai-popover .components-popover__content .pattern-img {
    text-align: center;
    padding: 10px;
  }
}/*# sourceMappingURL=style.css.map */