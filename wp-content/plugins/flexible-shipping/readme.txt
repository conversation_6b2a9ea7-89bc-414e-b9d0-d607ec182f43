=== Table Rate Shipping Method for WooCommerce by Flexible Shipping ===
Contributors: octolize,grola,sebastianpisula
Donate link: https://octol.io/fs-repo-up
Tags: woocommerce shipping, table rate shipping, woocommerce table rate shipping, advanced shipping, flexible shipping woocommerce
Requires at least: 5.8
Tested up to: 6.8
Stable tag: 6.4.0
Requires PHP: 7.4
License: GPLv3 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Table Rate shipping plugin for WooCommerce. Easily define shipping calculation rules based on weight or cart total. Improve conversion - try it now!

== Description ==

= Table Rate Shipping Killer =

Flexible Shipping is the most advanced shipping plugin for WooCommerce stores, allowing you to calculate the shipping costs based on weight and/or cart total. Combine it with the PRO version and it will become the only WooCommerce shipping plugin you'll ever need.

[youtube https://www.youtube.com/watch?v=UPumLCbqjZA]

> **Upgrade to Flexible Shipping PRO**<br />
> Upgrade to [Flexible Shipping PRO now](https://octol.io/fs-repo-up) to get the priority e-mail support and gain an access to all the PRO features!

= Possible Shipping Scenarios =

* Shipping costs based on cart weight
* Shipping costs based on cart total
* Adding handling fee or an insurance cost after reaching a certain order value
* Creating COD (Cash On Delivery) shipping method with additional costs
* Different shipping costs for different shipping classes, products or product categories (PRO)
* Disabling/hiding the shipping method if the configured rule has been matched in the cart (PRO)
* Additional cost added to whole order and/or per each one product in the cart (PRO)
* Enabling/disabling the shipping method based on the Time of the Day and Day of the week (PRO)
* Hiding the shipping method for certain products

These are only a few examples of the Flexible Shipping usage, however, sky is the limit. We have described the most popular use cases in the comprehensive and detailed plugin documentation and [Ready to use scenarios &rarr;](https://octol.io/fs-repo-docs).

= Features =

* Unlimited shipping methods and costs calculation rules
* Possibility of adding the titles and **descriptions** to your shipping methods
* Shipping cost based on cart total and/or weight
* Minimum and maximum values for cart total and/or weight
* Summing up the costs of e.g. two different rules at the same time e.g. one based on cart total and the second based on weight
* Free shipping over amount threshold
* Option to display the selected shipping methods only for logged-in users
* Further shipping companies integrations (see the info below for more details)
* WPML and Polylang compatibility
* Built-in ready to use scenarios
* Automatic notification about shipping zone configuration conflict
* Cart calculation settings (cart or package value)
* Built-in tutorial with step-by-step guide

= PRO Features =

* All free features
* **Shipping classes support**
* Shipping costs based on products' quantity and/or cart line item count
* Shipping cost based on the product’s length, width, height and/or maximal dimension
* Shipping cost based on the volume of the products in the cart
* Shipping cost based on dimensional weight (with custom DIM Factor)
* Shipping cost based on products (products, product categories, product tags)
* Shipping cost based on user role
* Enabling/disabling the shipping method based on the Time of the Day and Day of the week
* Additional costs based on price, weight, dimensional weight, item quantity, cart line item, volume
* Stopping a rule (if the rule is matched the following rules will not be calculated)
* Hiding a shipping method (if the rule is matched, the related shipping method will remain hidden and will not be displayed in the cart and checkout)
* Conditional logic for conditions with selection (e.g. shipping class) - matches any/all/none
* Conditional logic for conditions with ranges (e.g. weight) - is/is not
* Additional calculation methods (sum, lowest cost, highest cost)
* Maximum shipping cost per shipping method
* Free shipping coupons support

[Upgrade to PRO Now &rarr;](https://octol.io/fs-repo-up)

= Table rate available for all shipping methods =

We’ve added integration with any shipping method in WooCommerce. The new feature allows the use of shipping cost calculation rules for all shipping methods available in WooCommerce, including Flat Rate and those added by other plugins. This provides greater control over delivery costs and allows you to take advantage of the existing features of other methods. We encourage you to give it a try with one of our live rates plugins listed below in Useful free WooCommerce shipping plugins from Octolize section.

= Flexible Shipping Box Packing WooCommerce =

Flexible Shipping Box Packing WooCommerce introduces the advanced box packing algorithm allowing to automatically fit the ordered products into your shipping boxes the most optimal way. Give it a try and configure the shipping cost calculation rules based on the type and number of the used shipping boxes. It works with both the free and PRO versions, so you can buy it separately if you don't need the PRO features.

[Buy Flexible Shipping Box Packing WooCommerce now &rarr;](https://octol.io/fs-repo-cross-bp)

= Distance Based Shipping Rates for WooCommerce =

Distance Based Shipping Rates for WooCommerce extends the Flexible Shipping plugin functionalities by adding the rules based on distance and delivery duration. It works with both the free and PRO versions, so you can buy it separately if you don't need the PRO features.

[Buy Distance Based Shipping Rates for WooCommerce now &rarr;](https://octol.io/fs-repo-cross-dbsr)

= WooCommerce Delivery Date Picker =

WooCommerce Delivery Date Picker extends the default features of Flexible Shipping plugin, allows you to choose a convenient delivery date for your ordered products and makes the shipping cost dependent on the selected date. It works with both the free and PRO versions, so you can buy it separately if you don't need the PRO features.

[Buy WooCommerce Delivery Date Picker now &rarr;](https://octol.io/fs-repo-cross-ddp)

= Flexible Shipping Locations Add-On =

Flexible Shipping Locations Add-On extends the default Flexible Shipping for WooCommerce functionalities and adds the possiblity to create the additional rules based on locations (WooCommerce and custom ones). It works with both, free and PRO versions, so you can buy it separately if you do not need the PRO features.

[Buy Flexible Shipping Locations Add-On now &rarr;](https://octol.io/fs-repo-cross-locations)

= Flexible Shipping Import Export Add-On =

Flexible Shipping Import Export Add-On allows you to easily import and export Flexible Shipping methods. This way you can easily move and update shipping methods. Plugin supports CSV format. It works with both, free and PRO versions, so you can buy it separately if you do not need the PRO features.

[Buy Flexible Shipping Import Export Add-On now &rarr;](https://octol.io/fs-repo-cross-fsie)

= Multi Vendor Shipping for WooCommerce Add-On =

Multi Vendor Shipping for WooCommerce Add-on extends the Flexible Shipping plugin by adding rules based on Product Author (Vendor). This allows you to assign shipping methods to vendors or set additional shipping costs to them. It works with both, free and PRO versions so you can buy it separately if you do not need the PRO features.

[Buy Multi Vendor Shipping for WooCommerce Add-On now &rarr;](https://octol.io/fs-repo-cross-mvs)

You might also be interested in other...

= Useful free WooCommerce shipping plugins from Octolize =

* [Flexible Shipping for UPS and WooCommerce](https://octol.io/ups-repo) - the most powerful UPS WooCommerce integration
* [Flexible Shipping for FedEx and WooCommerce](https://octol.io/fedex-repo) - first free plugin to display FedEx Live Rates
* [Live rates for USPS and WooCommerce](https://octol.io/usps-repo) - the best free plugin to display the USPS Live Rates
* [Live rates for DHL Express and WooCommerce](https://octol.io/dhlexpress-repo) - automatic international shipping costs calculation and displaying DHL Express live rates
* [Shipping Live Rates for Australia Post for WooCommerce](https://octol.io/ap-repo) – Australia Post WooCommerce shipping methods with real-time calculated shipping rates
* [Shipping Live Rates for Canada Post for WooCommerce](https://octol.io/cp-repo) - Canada Post WooCommerce shipping methods with real-time calculated shipping rates
* [Shipping Live Rates for Royal Mail for WooCommerce](https://octol.io/rm-repo) - Royal Mail WooCommerce shipping methods with real-time calculated shipping rates
* [Shipping Notices](https://octol.io/notices-repo) - your own custom WooCommerce shipping notices instead of the default "No shipping options were found" info
* [Shipping Cost on Product Page](https://octol.io/scopp-repo) - displaying the shipping cost calculator to your customers directly on the product page, before reaching the cart or checkout

= Docs =

View the dedicated [Flexible Shipping Documentation &rarr;](https://octol.io/fs-repo-docs)

= Support Policy =

We provide a limited support for the free version of our Flexible Shipping plugin on the [dedicated plugin Support Forum](https://wordpress.org/support/plugin/flexible-shipping/). Please upgrade to PRO version to get the priority e-mail support as well as all PRO features. [Upgrade Now &rarr;](https://octol.io/fs-repo-up)

= Further Integrations =

**United Kingdom**

We have released a DPD UK & Local WooCommerce integrations for Flexible Shipping covering the whole UK territory. Check our plugins - [offer your customers the DPD UK services in your shop](https://octol.io/fs-repo-cross-dpd-uk) and [show them the DPD UK Pickup Points map](https://octol.io/fs-repo-cross-dpd-uk-pp) to choose their preferred one to collect their orders from.

**Poland**

There have also develop more further Flexible Shipping integrations for Polish carriers and shipping companies:

* DPD - WooCommerce
* DHL - WooCommerce
* Paczkomaty InPost - WooCommerce
* UPS - WooCommerce
* eNadawca Poczta Polska - WooCommerce
* Orlen Paczka - WooCommerce

= Compatible WooCommerce Plugins =

We have verified and tested the Flexible Shipping compatibility with the following popular WooCommerce plugins:

* [WPML](https://wpml.org/)
* [Germanized](https://wordpress.org/plugins/woocommerce-germanized/)

= Supported Currency Switchers =

* [Aelia Currency Switcher](https://aelia.co/shop/currency-switcher-woocommerce/)
* [WooCommerce Currency Switcher](https://wordpress.org/plugins/woocommerce-currency-switcher/)
* [Currency Switcher for WooCommerce](https://wordpress.org/plugins/currency-switcher-woocommerce/)
* [Multi Currency for WooCommerce](https://wordpress.org/plugins/woo-multi-currency/)
* [WPML](https://wpml.org/)

= Translations =

* English - default
* Polish
* German by [jensratzel](https://profiles.wordpress.org/jensratzel/)
* Spanish by [Jose Luis](https://profiles.wordpress.org/jose64/), [Javier Esteban](https://profiles.wordpress.org/nobnob/), [lacasitadecadera](https://profiles.wordpress.org/lacasitadecera/)
* Dutch by [Vernum](https://profiles.wordpress.org/vernum/), [Peter Smits](https://profiles.wordpress.org/psmits1567/), [Pjeterjan Deneys](https://profiles.wordpress.org/nekojonez/)

= Interested in plugin translations? =

We are actively looking for contributors to translate this and [other Octolize plugins](https://profiles.wordpress.org/octolize/#content-plugins). Each supported language tremendously help store owners to conveniently manage shipping operations.

Your translations contribute to the WordPress community at large. Moreover, we're glad to offer you discounts for our PRO plugins and establish long-term collaboration. If you have any translation related questions, please email us at [<EMAIL>](mailto:<EMAIL>).

Head over here and help us to translate this plugin:
[https://translate.wordpress.org/projects/wp-plugins/flexible-shipping](https://translate.wordpress.org/projects/wp-plugins/flexible-shipping)

= Flexible Shipping in a nutshell =

Key features:

* improved shipping-related user experience,
* custom shipping rules,
* free shipping based on the price or products' in the cart quantity,
* cost based shipping cost
* weight based shipping cost
* total order based shipping cost,
* item count based shipping cost,
* shipping class based shipping cost,
* WooCommerce shipping cost rules,
* WooCommerce shipping plugin,
* WooCommerce table rate shipping.

Give it a try and see for yourself that our Flexible Shipping is the only one Table Rate Shipping plugin you need!

== Installation	 ==

This integration can be easily installed like any other WordPress plugin by following the steps below:

1. Download and unzip the latest zip file release.
2. Upload the entire plugin directory to your **/wp-content/plugins/** path.
3. Activate the plugin using the **Plugins** menu in WordPress sidebar menu.

Optionally you can also try to upload the plugin zip file using **Plugins &rarr; Add New &rarr; Upload Plugin** option from the WordPress sidebar menu. Then go directly to point 3.

== Frequently Asked Questions ==

= How to configure the plugin? =

To make it clear and as easy as possible we have prepared the detailed step-by-step guides in our [Flexible Shipping Docs here](https://octol.io/fs-repo-docs). You can also use the built-in tutorial that will guide you throught the whole process.

= Do you offer support? =

We provide a limited support for the free version of our Flexible Shipping plugin on the [dedicated plugin Support Forum](https://wordpress.org/support/plugin/flexible-shipping/). Please upgrade to PRO version to get the priority e-mail support as well as all PRO features. [Upgrade Now &rarr;](https://octol.io/fs-repo-up)

== Screenshots ==

1. Flexible Shipping shipping method configuration screen
2. Adding a new Flexible Shipping shipping method
3. Flexible Shipping shipping methods added within a shipping zone
4. Flexible Shipping cost calculation rules table
5. Flexible Shipping shipping methods in the cart


== Upgrade Notice ==

If you are upgrading from the old Flexible Shipping version (1.3.2, woo-flexible-shipping) make sure to completely delete the old version first. If you install the new version without deleting the old one it may break your WordPress installation.

== Changelog ==

= 6.4.0 - 2025-09-08 =
* Added newsletter subscription in the Flexible Shipping Info tab
* Added WC_Shipping_Method class existence checking

= 6.3.0 - 2025-08-18 =
* Added a new AI chat version

= 6.2.4 - 2025-08-11 =
* Added support for WooCommerce 10.2

= 6.2.3 - 2025-08-04 =
* Added support for WooCommerce 10.1

= 6.2.2 - 2025-07-28 =
* Fixed rule duplication error
* The rule-sorting functionality has been modified

= 6.2.1 - 2025-06-24 =
* Added summer promo coupon, available in the Shipping Extensions tab
* Added support for WooCommerce 10.0

= 6.2.0 - 2025-06-16 =
* AI functionality improvements

= 6.1.2 - 2025-06-09 =
* Added CSAT after an AI scenario used
* Fixed fatal in third party shipping methods integration
* Fixed fatal in free shipping notice on some configurations

= 6.1.1 - 2025-05-15 =
* Added support for WooCommerce 9.9

= 6.1.0 - 2025-05-05 =
* Added support for WordPress 6.8
* Fixed debug mode messages

= 6.0.0 - 2025-03-27 =
* Added the ability to paste JSON with rules table configuration in the shipping method settings
* Added an AI assistant for rules table configuration: https://octol.io/fs-rules-table-ai
* Changed the button layout in the rules table settings

= 5.3.5 - 2025-03-18 =
* Fixed warning in free shipping notice generator
* Fixed additional costs settings not saving with some shipping methods
* Fixed fatal error with Product Feed PRO plugin

= 5.3.4 - 2025-03-10 =
* Added support for WooCommerce 9.8

= 5.3.3 - 2025-02-27 =
* Added shipping method description in block checkout

= 5.3.2 - 2025-02-27 =
* Fixed prefixing Psr\Log library with Delivery Date Picker extension

= 5.3.1 - 2025-02-25 =
* Fixed prefixing Psr\Log library

= 5.3.0 - 2025-02-25 =
* Prefixed Psr\Log library
* Fixed conflicts with plugins and themes using non prefixed Psr\Log library
* Fixed rules table crash with Flexible Shipping Locations Add-On

= 5.2.0 - 2025-01-30 =
* Improved rules table settings REACT component
* Fixed fatal Unsupported operand types: string + float with some shipping methods integration.

= 5.1.4 - 2025-01-22 =
* Added support for WooCommerce 9.7

= 5.1.3 - 2025-01-20 =
* Added support for WooCommerce 9.6

= 5.1.2 - 2024-12-17 =
* Fixed calculation method handling with PRO version

= 5.1.1 - 2024-12-12 =
* Fixed fatal error when shipping cost are calculated as string in some cases

= 5.1.0 - 2024-12-09 =
* Fixed translations loading

= 5.0.9 - 2024-11-20 =
* Fixed fatal error on order meta data

= 5.0.8 - 2024-11-19 =
* Fixed upgrade info popup

= 5.0.7 - 2024-11-19 =
* Fixed support for WooCommerce 9.5

= 5.0.6 - 2024-11-18 =
* Added support for WooCommerce 9.5

= 5.0.5 - 2024-11-13 =
* Added rules table information in modal shipping method settings

= 5.0.4 - 2024-11-07 =
* Fixed tax calculation for shipping methods with rules table

= 5.0.3 - 2024-10-30 =
* Fixed compatibility with Mondial Relay – InPost Official (mondialrelay_official_shipping) by removing rules table from the shipping method settings

= 5.0.2 - 2024-10-28 =
* Fixed rules table settings fatal error after PRO version deactivation
* Fixed compatibility with Box Now Delivery Plugin by removing rules table from the shipping method settings

= 5.0.1 - 2024-10-24 =
* Fixed PHP notices in shipping method settings
* Fixed PHP notices on PHP 8

= 5.0.0 - 2024-10-22 =
* Added ability to use Rules Table in other shipping methods
* Changed plugin video
