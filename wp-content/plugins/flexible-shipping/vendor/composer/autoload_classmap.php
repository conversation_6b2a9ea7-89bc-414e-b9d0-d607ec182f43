<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'FSVendor\\Monolog\\Attribute\\AsMonologProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Attribute/AsMonologProcessor.php',
    'FSVendor\\Monolog\\DateTimeImmutable' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/DateTimeImmutable.php',
    'FSVendor\\Monolog\\ErrorHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/ErrorHandler.php',
    'FSVendor\\Monolog\\Formatter\\ChromePHPFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ChromePHPFormatter.php',
    'FSVendor\\Monolog\\Formatter\\ElasticaFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ElasticaFormatter.php',
    'FSVendor\\Monolog\\Formatter\\ElasticsearchFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ElasticsearchFormatter.php',
    'FSVendor\\Monolog\\Formatter\\FlowdockFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FlowdockFormatter.php',
    'FSVendor\\Monolog\\Formatter\\FluentdFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FluentdFormatter.php',
    'FSVendor\\Monolog\\Formatter\\FormatterInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FormatterInterface.php',
    'FSVendor\\Monolog\\Formatter\\GelfMessageFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/GelfMessageFormatter.php',
    'FSVendor\\Monolog\\Formatter\\GoogleCloudLoggingFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/GoogleCloudLoggingFormatter.php',
    'FSVendor\\Monolog\\Formatter\\HtmlFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/HtmlFormatter.php',
    'FSVendor\\Monolog\\Formatter\\JsonFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/JsonFormatter.php',
    'FSVendor\\Monolog\\Formatter\\LineFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LineFormatter.php',
    'FSVendor\\Monolog\\Formatter\\LogglyFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogglyFormatter.php',
    'FSVendor\\Monolog\\Formatter\\LogmaticFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogmaticFormatter.php',
    'FSVendor\\Monolog\\Formatter\\LogstashFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogstashFormatter.php',
    'FSVendor\\Monolog\\Formatter\\MongoDBFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/MongoDBFormatter.php',
    'FSVendor\\Monolog\\Formatter\\NormalizerFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/NormalizerFormatter.php',
    'FSVendor\\Monolog\\Formatter\\ScalarFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ScalarFormatter.php',
    'FSVendor\\Monolog\\Formatter\\WildfireFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/WildfireFormatter.php',
    'FSVendor\\Monolog\\Handler\\AbstractHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractHandler.php',
    'FSVendor\\Monolog\\Handler\\AbstractProcessingHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php',
    'FSVendor\\Monolog\\Handler\\AbstractSyslogHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractSyslogHandler.php',
    'FSVendor\\Monolog\\Handler\\AmqpHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AmqpHandler.php',
    'FSVendor\\Monolog\\Handler\\BrowserConsoleHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/BrowserConsoleHandler.php',
    'FSVendor\\Monolog\\Handler\\BufferHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/BufferHandler.php',
    'FSVendor\\Monolog\\Handler\\ChromePHPHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ChromePHPHandler.php',
    'FSVendor\\Monolog\\Handler\\CouchDBHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/CouchDBHandler.php',
    'FSVendor\\Monolog\\Handler\\CubeHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/CubeHandler.php',
    'FSVendor\\Monolog\\Handler\\Curl\\Util' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Curl/Util.php',
    'FSVendor\\Monolog\\Handler\\DeduplicationHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DeduplicationHandler.php',
    'FSVendor\\Monolog\\Handler\\DoctrineCouchDBHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DoctrineCouchDBHandler.php',
    'FSVendor\\Monolog\\Handler\\DynamoDbHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DynamoDbHandler.php',
    'FSVendor\\Monolog\\Handler\\ElasticaHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ElasticaHandler.php',
    'FSVendor\\Monolog\\Handler\\ElasticsearchHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ElasticsearchHandler.php',
    'FSVendor\\Monolog\\Handler\\ErrorLogHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ErrorLogHandler.php',
    'FSVendor\\Monolog\\Handler\\FallbackGroupHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FallbackGroupHandler.php',
    'FSVendor\\Monolog\\Handler\\FilterHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FilterHandler.php',
    'FSVendor\\Monolog\\Handler\\FingersCrossedHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossedHandler.php',
    'FSVendor\\Monolog\\Handler\\FingersCrossed\\ActivationStrategyInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ActivationStrategyInterface.php',
    'FSVendor\\Monolog\\Handler\\FingersCrossed\\ChannelLevelActivationStrategy' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ChannelLevelActivationStrategy.php',
    'FSVendor\\Monolog\\Handler\\FingersCrossed\\ErrorLevelActivationStrategy' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ErrorLevelActivationStrategy.php',
    'FSVendor\\Monolog\\Handler\\FirePHPHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FirePHPHandler.php',
    'FSVendor\\Monolog\\Handler\\FleepHookHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FleepHookHandler.php',
    'FSVendor\\Monolog\\Handler\\FlowdockHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FlowdockHandler.php',
    'FSVendor\\Monolog\\Handler\\FormattableHandlerInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FormattableHandlerInterface.php',
    'FSVendor\\Monolog\\Handler\\FormattableHandlerTrait' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FormattableHandlerTrait.php',
    'FSVendor\\Monolog\\Handler\\GelfHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/GelfHandler.php',
    'FSVendor\\Monolog\\Handler\\GroupHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/GroupHandler.php',
    'FSVendor\\Monolog\\Handler\\Handler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Handler.php',
    'FSVendor\\Monolog\\Handler\\HandlerInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/HandlerInterface.php',
    'FSVendor\\Monolog\\Handler\\HandlerWrapper' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/HandlerWrapper.php',
    'FSVendor\\Monolog\\Handler\\IFTTTHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/IFTTTHandler.php',
    'FSVendor\\Monolog\\Handler\\InsightOpsHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/InsightOpsHandler.php',
    'FSVendor\\Monolog\\Handler\\LogEntriesHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogEntriesHandler.php',
    'FSVendor\\Monolog\\Handler\\LogglyHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogglyHandler.php',
    'FSVendor\\Monolog\\Handler\\LogmaticHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogmaticHandler.php',
    'FSVendor\\Monolog\\Handler\\MailHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MailHandler.php',
    'FSVendor\\Monolog\\Handler\\MandrillHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MandrillHandler.php',
    'FSVendor\\Monolog\\Handler\\MissingExtensionException' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MissingExtensionException.php',
    'FSVendor\\Monolog\\Handler\\MongoDBHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MongoDBHandler.php',
    'FSVendor\\Monolog\\Handler\\NativeMailerHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NativeMailerHandler.php',
    'FSVendor\\Monolog\\Handler\\NewRelicHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NewRelicHandler.php',
    'FSVendor\\Monolog\\Handler\\NoopHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NoopHandler.php',
    'FSVendor\\Monolog\\Handler\\NullHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NullHandler.php',
    'FSVendor\\Monolog\\Handler\\OverflowHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/OverflowHandler.php',
    'FSVendor\\Monolog\\Handler\\PHPConsoleHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PHPConsoleHandler.php',
    'FSVendor\\Monolog\\Handler\\ProcessHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessHandler.php',
    'FSVendor\\Monolog\\Handler\\ProcessableHandlerInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessableHandlerInterface.php',
    'FSVendor\\Monolog\\Handler\\ProcessableHandlerTrait' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessableHandlerTrait.php',
    'FSVendor\\Monolog\\Handler\\PsrHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PsrHandler.php',
    'FSVendor\\Monolog\\Handler\\PushoverHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PushoverHandler.php',
    'FSVendor\\Monolog\\Handler\\RedisHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RedisHandler.php',
    'FSVendor\\Monolog\\Handler\\RedisPubSubHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RedisPubSubHandler.php',
    'FSVendor\\Monolog\\Handler\\RollbarHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RollbarHandler.php',
    'FSVendor\\Monolog\\Handler\\RotatingFileHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RotatingFileHandler.php',
    'FSVendor\\Monolog\\Handler\\SamplingHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SamplingHandler.php',
    'FSVendor\\Monolog\\Handler\\SendGridHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SendGridHandler.php',
    'FSVendor\\Monolog\\Handler\\SlackHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SlackHandler.php',
    'FSVendor\\Monolog\\Handler\\SlackWebhookHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SlackWebhookHandler.php',
    'FSVendor\\Monolog\\Handler\\Slack\\SlackRecord' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Slack/SlackRecord.php',
    'FSVendor\\Monolog\\Handler\\SocketHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SocketHandler.php',
    'FSVendor\\Monolog\\Handler\\SqsHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SqsHandler.php',
    'FSVendor\\Monolog\\Handler\\StreamHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/StreamHandler.php',
    'FSVendor\\Monolog\\Handler\\SwiftMailerHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SwiftMailerHandler.php',
    'FSVendor\\Monolog\\Handler\\SymfonyMailerHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SymfonyMailerHandler.php',
    'FSVendor\\Monolog\\Handler\\SyslogHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogHandler.php',
    'FSVendor\\Monolog\\Handler\\SyslogUdpHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogUdpHandler.php',
    'FSVendor\\Monolog\\Handler\\SyslogUdp\\UdpSocket' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogUdp/UdpSocket.php',
    'FSVendor\\Monolog\\Handler\\TelegramBotHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/TelegramBotHandler.php',
    'FSVendor\\Monolog\\Handler\\TestHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/TestHandler.php',
    'FSVendor\\Monolog\\Handler\\WebRequestRecognizerTrait' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/WebRequestRecognizerTrait.php',
    'FSVendor\\Monolog\\Handler\\WhatFailureGroupHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/WhatFailureGroupHandler.php',
    'FSVendor\\Monolog\\Handler\\ZendMonitorHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ZendMonitorHandler.php',
    'FSVendor\\Monolog\\LogRecord' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/LogRecord.php',
    'FSVendor\\Monolog\\Logger' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Logger.php',
    'FSVendor\\Monolog\\Processor\\HostnameProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/HostnameProcessor.php',
    'FSVendor\\Monolog\\Processor\\IntrospectionProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/IntrospectionProcessor.php',
    'FSVendor\\Monolog\\Processor\\MemoryPeakUsageProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryPeakUsageProcessor.php',
    'FSVendor\\Monolog\\Processor\\MemoryProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryProcessor.php',
    'FSVendor\\Monolog\\Processor\\MemoryUsageProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryUsageProcessor.php',
    'FSVendor\\Monolog\\Processor\\ProcessIdProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/ProcessIdProcessor.php',
    'FSVendor\\Monolog\\Processor\\ProcessorInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/ProcessorInterface.php',
    'FSVendor\\Monolog\\Processor\\PsrLogMessageProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/PsrLogMessageProcessor.php',
    'FSVendor\\Monolog\\Processor\\TagProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/TagProcessor.php',
    'FSVendor\\Monolog\\Processor\\UidProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/UidProcessor.php',
    'FSVendor\\Monolog\\Processor\\WebProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/WebProcessor.php',
    'FSVendor\\Monolog\\Registry' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Registry.php',
    'FSVendor\\Monolog\\ResettableInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/ResettableInterface.php',
    'FSVendor\\Monolog\\SignalHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/SignalHandler.php',
    'FSVendor\\Monolog\\Test\\TestCase' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Test/TestCase.php',
    'FSVendor\\Monolog\\Utils' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Utils.php',
    'FSVendor\\Octolize\\BetterDocs\\Beacon\\Beacon' => $baseDir . '/vendor_prefixed/octolize/wp-betterdocs-beacon/src/Beacon.php',
    'FSVendor\\Octolize\\BetterDocs\\Beacon\\BeaconOptions' => $baseDir . '/vendor_prefixed/octolize/wp-betterdocs-beacon/src/BeaconOptions.php',
    'FSVendor\\Octolize\\BetterDocs\\Beacon\\BeaconPro' => $baseDir . '/vendor_prefixed/octolize/wp-betterdocs-beacon/src/BeaconPro.php',
    'FSVendor\\Octolize\\Blocks\\CheckoutIntegration' => $baseDir . '/vendor_prefixed/octolize/octolize-checkout-block-integration/src/Blocks/CheckoutIntegration.php',
    'FSVendor\\Octolize\\Blocks\\IntegrationData' => $baseDir . '/vendor_prefixed/octolize/octolize-checkout-block-integration/src/Blocks/IntegrationData.php',
    'FSVendor\\Octolize\\Blocks\\Registrator' => $baseDir . '/vendor_prefixed/octolize/octolize-checkout-block-integration/src/Blocks/Registrator.php',
    'FSVendor\\Octolize\\Blocks\\StoreEndpoint' => $baseDir . '/vendor_prefixed/octolize/octolize-checkout-block-integration/src/Blocks/StoreEndpoint.php',
    'FSVendor\\Octolize\\Brand\\Assets\\AdminAssets' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-brand-assets/src/Brand/Assets/AdminAssets.php',
    'FSVendor\\Octolize\\Brand\\UpsellingBox\\ConstantShouldShowStrategy' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-brand-assets/src/Brand/UpsellingBox/ConstantShouldShowStrategy.php',
    'FSVendor\\Octolize\\Brand\\UpsellingBox\\SettingsSidebar' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-brand-assets/src/Brand/UpsellingBox/SettingsSidebar.php',
    'FSVendor\\Octolize\\Brand\\UpsellingBox\\ShippingMethodAndConstantDisplayStrategy' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-brand-assets/src/Brand/UpsellingBox/ShippingMethodAndConstantDisplayStrategy.php',
    'FSVendor\\Octolize\\Brand\\UpsellingBox\\ShippingMethodInstanceShouldShowStrategy' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-brand-assets/src/Brand/UpsellingBox/ShippingMethodInstanceShouldShowStrategy.php',
    'FSVendor\\Octolize\\Brand\\UpsellingBox\\ShippingMethodShouldShowStrategy' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-brand-assets/src/Brand/UpsellingBox/ShippingMethodShouldShowStrategy.php',
    'FSVendor\\Octolize\\Csat\\Csat' => $baseDir . '/vendor_prefixed/octolize/wp-csat-petition/src/Csat.php',
    'FSVendor\\Octolize\\Csat\\CsatCode' => $baseDir . '/vendor_prefixed/octolize/wp-csat-petition/src/CsatCode.php',
    'FSVendor\\Octolize\\Csat\\CsatCodeFromFile' => $baseDir . '/vendor_prefixed/octolize/wp-csat-petition/src/CsatCodeFromFile.php',
    'FSVendor\\Octolize\\Csat\\CsatCodeFromString' => $baseDir . '/vendor_prefixed/octolize/wp-csat-petition/src/CsatCodeFromString.php',
    'FSVendor\\Octolize\\Csat\\CsatOption' => $baseDir . '/vendor_prefixed/octolize/wp-csat-petition/src/CsatOption.php',
    'FSVendor\\Octolize\\Csat\\CsatOptionDependedOnShippingMethod' => $baseDir . '/vendor_prefixed/octolize/wp-csat-petition/src/CsatOptionDependedOnShippingMethod.php',
    'FSVendor\\Octolize\\Onboarding\\Field\\Html' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/Field/Html.php',
    'FSVendor\\Octolize\\Onboarding\\Onboarding' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/Onboarding.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingAjax' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingAjax.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingButton' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingButton.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingDeactivationData' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingDeactivationData.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingOption' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingOption.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingShouldShowAlwaysStrategy' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingShouldShowAlwaysStrategy.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingShouldShowGetParametersStrategy' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingShouldShowGetParametersStrategy.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingShouldShowNeverStrategy' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingShouldShowNeverStrategy.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingShouldShowStrategy' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingShouldShowStrategy.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingStep' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingStep.php',
    'FSVendor\\Octolize\\Onboarding\\OnboardingTrackerData' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/OnboardingTrackerData.php',
    'FSVendor\\Octolize\\Onboarding\\PluginUpgrade\\MessageFactory\\LiveRatesFsRulesTable' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/PluginUpgrade/MessageFactory/LiveRatesFsRulesTable.php',
    'FSVendor\\Octolize\\Onboarding\\PluginUpgrade\\PluginUpgradeAjax' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/PluginUpgrade/PluginUpgradeAjax.php',
    'FSVendor\\Octolize\\Onboarding\\PluginUpgrade\\PluginUpgradeMessage' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/PluginUpgrade/PluginUpgradeMessage.php',
    'FSVendor\\Octolize\\Onboarding\\PluginUpgrade\\PluginUpgradeOnboardingFactory' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/PluginUpgrade/PluginUpgradeOnboardingFactory.php',
    'FSVendor\\Octolize\\Onboarding\\PluginUpgrade\\PluginUpgradeWatcher' => $baseDir . '/vendor_prefixed/octolize/wp-onboarding/src/Onboarding/PluginUpgrade/PluginUpgradeWatcher.php',
    'FSVendor\\Octolize\\ShippingExtensions\\AdminPage' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/AdminPage.php',
    'FSVendor\\Octolize\\ShippingExtensions\\Assets' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/Assets.php',
    'FSVendor\\Octolize\\ShippingExtensions\\Page' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/Page.php',
    'FSVendor\\Octolize\\ShippingExtensions\\PageViewTracker' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/PageViewTracker.php',
    'FSVendor\\Octolize\\ShippingExtensions\\PluginLinks' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/PluginLinks.php',
    'FSVendor\\Octolize\\ShippingExtensions\\Plugin\\Plugin' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/Plugin/Plugin.php',
    'FSVendor\\Octolize\\ShippingExtensions\\Plugin\\PluginFactory' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/Plugin/PluginFactory.php',
    'FSVendor\\Octolize\\ShippingExtensions\\Plugin\\PluginSorter' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/Plugin/PluginSorter.php',
    'FSVendor\\Octolize\\ShippingExtensions\\ShippingExtensions' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/ShippingExtensions.php',
    'FSVendor\\Octolize\\ShippingExtensions\\Tracker\\DataProvider\\ShippingExtensionsDataProvider' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/Tracker/DataProvider/ShippingExtensionsDataProvider.php',
    'FSVendor\\Octolize\\ShippingExtensions\\Tracker\\Tracker' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/Tracker/Tracker.php',
    'FSVendor\\Octolize\\ShippingExtensions\\Tracker\\ViewPageTracker' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/Tracker/ViewPageTracker.php',
    'FSVendor\\Octolize\\ShippingExtensions\\WooCommerceSuggestions' => $baseDir . '/vendor_prefixed/octolize/wp-shipping-extensions/src/ShippingExtensions/WooCommerceSuggestions.php',
    'FSVendor\\Octolize\\Tracker\\DeactivationTracker\\OctolizeProReasonsFactory' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/DeactivationTracker/OctolizeProReasonsFactory.php',
    'FSVendor\\Octolize\\Tracker\\DeactivationTracker\\OctolizeReasonsFactory' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/DeactivationTracker/OctolizeReasonsFactory.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\OptInNotice' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/OptInNotice.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\ShouldDisplay' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/ShouldDisplay.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\ShouldDisplayAlways' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/ShouldDisplayAlways.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\ShouldDisplayAndConditions' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/ShouldDisplayAndConditions.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\ShouldDisplayGetParameterPresent' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/ShouldDisplayGetParameterPresent.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\ShouldDisplayGetParameterValue' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/ShouldDisplayGetParameterValue.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\ShouldDisplayNever' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/ShouldDisplayNever.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\ShouldDisplayOrConditions' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/ShouldDisplayOrConditions.php',
    'FSVendor\\Octolize\\Tracker\\OptInNotice\\ShouldDisplayShippingMethodInstanceSettings' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/OptInNotice/ShouldDisplayShippingMethodInstanceSettings.php',
    'FSVendor\\Octolize\\Tracker\\SenderRegistrator' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/SenderRegistrator.php',
    'FSVendor\\Octolize\\Tracker\\SenderToOctolize' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/SenderToOctolize.php',
    'FSVendor\\Octolize\\Tracker\\TrackerInitializer' => $baseDir . '/vendor_prefixed/octolize/wp-octolize-tracker/src/TrackerInitializer.php',
    'FSVendor\\Psr\\Log\\AbstractLogger' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/AbstractLogger.php',
    'FSVendor\\Psr\\Log\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/InvalidArgumentException.php',
    'FSVendor\\Psr\\Log\\LogLevel' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LogLevel.php',
    'FSVendor\\Psr\\Log\\LoggerAwareInterface' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerAwareInterface.php',
    'FSVendor\\Psr\\Log\\LoggerAwareTrait' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerAwareTrait.php',
    'FSVendor\\Psr\\Log\\LoggerInterface' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerInterface.php',
    'FSVendor\\Psr\\Log\\LoggerTrait' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerTrait.php',
    'FSVendor\\Psr\\Log\\NullLogger' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/NullLogger.php',
    'FSVendor\\Psr\\Log\\Test\\DummyTest' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/Test/DummyTest.php',
    'FSVendor\\Psr\\Log\\Test\\LoggerInterfaceTest' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
    'FSVendor\\Psr\\Log\\Test\\TestLogger' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/Test/TestLogger.php',
    'FSVendor\\WPDesk\\ABTesting\\ABTest' => $baseDir . '/vendor_prefixed/wpdesk/wp-abtesting/src/ABTest.php',
    'FSVendor\\WPDesk\\ABTesting\\ABTest\\EqualGroupsRandomABTest' => $baseDir . '/vendor_prefixed/wpdesk/wp-abtesting/src/ABTest/EqualGroupsRandomABTest.php',
    'FSVendor\\WPDesk\\ABTesting\\ABVariant' => $baseDir . '/vendor_prefixed/wpdesk/wp-abtesting/src/ABVariant.php',
    'FSVendor\\WPDesk\\ABTesting\\ABVariant\\BasicABVariant' => $baseDir . '/vendor_prefixed/wpdesk/wp-abtesting/src/ABVariant/BasicABVariant.php',
    'FSVendor\\WPDesk\\Beacon\\Beacon' => $baseDir . '/vendor_prefixed/wpdesk/wp-helpscout-beacon/src/Beacon/Beacon.php',
    'FSVendor\\WPDesk\\Beacon\\BeaconGetShouldShowStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-helpscout-beacon/src/Beacon/BeaconGetShouldShowStrategy.php',
    'FSVendor\\WPDesk\\Beacon\\BeaconPro' => $baseDir . '/vendor_prefixed/wpdesk/wp-helpscout-beacon/src/Beacon/BeaconPro.php',
    'FSVendor\\WPDesk\\Beacon\\BeaconShouldShowStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-helpscout-beacon/src/Beacon/BeaconShouldShowStrategy.php',
    'FSVendor\\WPDesk\\Beacon\\Beacon\\WooCommerceSettingsFieldsModifier' => $baseDir . '/vendor_prefixed/wpdesk/wp-helpscout-beacon/src/Beacon/WooCommerceSettingsFieldsModifier.php',
    'FSVendor\\WPDesk\\FS\\Compatibility\\BlockSettings' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-compatibility/src/BlockSettings.php',
    'FSVendor\\WPDesk\\FS\\Compatibility\\Notice' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-compatibility/src/Notice.php',
    'FSVendor\\WPDesk\\FS\\Compatibility\\PluginCompatibility' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-compatibility/src/PluginCompatibility.php',
    'FSVendor\\WPDesk\\FS\\Compatibility\\PluginCompatibilityChecker' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-compatibility/src/PluginCompatibilityChecker.php',
    'FSVendor\\WPDesk\\FS\\Compatibility\\PluginDetails' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-compatibility/src/PluginDetails.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Assets' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Assets.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Checkout\\ShipmentCreator' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Checkout/ShipmentCreator.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\CustomPostType' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/CustomPostType.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Exception\\UnableToCreateTmpFileException' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Exception/UnableToCreateTmpFileException.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Exception\\UnableToCreateTmpZipFileException' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Exception/UnableToCreateTmpZipFileException.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Label\\LabelsBulkActionHandler' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Label/LabelsBulkActionHandler.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Label\\LabelsFileCreator' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Label/LabelsFileCreator.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Label\\LabelsFileDispatcher' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Label/LabelsFileDispatcher.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Label\\SingleLabelFileDispatcher' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Label/SingleLabelFileDispatcher.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Manifest\\ManifestCustomPostType' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Manifest/CustomPostType.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Metabox\\Ajax' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Metabox/Ajax.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Order\\AddShippingMetabox' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Order/AddShippingMetabox.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\RestApi\\OrderDataProvider' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/RestApi/OrderDataProvider.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\RestApi\\OrderDataProviderDefault' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/RestApi/OrderDataProviderDefault.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\RestApi\\OrderDataProvidersCollection' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/RestApi/OrderDataProvidersCollection.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\RestApi\\OrderDataProvidersFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/RestApi/OrderDataProvidersFactory.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\RestApi\\OrderResponseDataAppender' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/RestApi/OrderResponseDataAppender.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\ShipmentFunctionality' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/ShipmentFunctionality.php',
    'FSVendor\\WPDesk\\FS\\Shipment\\Subscriptions\\SubscriptionsIntegration' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-shipment/src/WPDesk/FS/Shipment/Subscriptions/SubscriptionsIntegration.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\AbstractOptions' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/AbstractOptions.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\BasedOnOptions' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/BasedOnOptions.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\CalculationMethodOptions' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/CalculationMethodOptions.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Logger\\ArrayLogger' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Logger/ArrayLogger.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Logger\\Assets' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Logger/Assets.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Logger\\CanFormatForLog' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Logger/CanFormatForLog.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Logger\\NoticeLogger' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Logger/NoticeLogger.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Logger\\ShippingMethodLogger' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Logger/ShippingMethodLogger.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Settings\\CartCalculationOptions' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Settings/CartCalculationOptions.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Settings\\CheckboxValue' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Settings/CheckboxValue.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Settings\\IntegrationSettings' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Settings/IntegrationSettings.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Settings\\IntegrationSettingsFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Settings/IntegrationSettingsFactory.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Settings\\IntegrationSettingsImplementation' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Settings/IntegrationSettingsImplementation.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Settings\\MethodSettings' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Settings/MethodSettings.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Settings\\MethodSettingsFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Settings/MethodSettingsFactory.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Settings\\MethodSettingsImplementation' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Settings/MethodSettingsImplementation.php',
    'FSVendor\\WPDesk\\FS\\TableRate\\Weight\\Rounding' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-fs-table-rate/src/Weight/Rounding.php',
    'FSVendor\\WPDesk\\Forms\\ContainerForm' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/ContainerForm.php',
    'FSVendor\\WPDesk\\Forms\\Escaper' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Escaper.php',
    'FSVendor\\WPDesk\\Forms\\Field' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field.php',
    'FSVendor\\WPDesk\\Forms\\FieldProvider' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/FieldProvider.php',
    'FSVendor\\WPDesk\\Forms\\FieldRenderer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/FieldRenderer.php',
    'FSVendor\\WPDesk\\Forms\\Field\\BasicField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/BasicField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\ButtonField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/ButtonField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\CheckboxField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/CheckboxField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\DateField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/DateField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\DatePickerField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/DatePickerField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\Header' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/Header.php',
    'FSVendor\\WPDesk\\Forms\\Field\\HiddenField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/HiddenField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\ImageInputField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/ImageInputField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\InputNumberField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/InputNumberField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\InputTextField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/InputTextField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\MultipleInputTextField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/MultipleInputTextField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\NoOnceField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/NoOnceField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\NoValueField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/NoValueField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\Paragraph' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/Paragraph.php',
    'FSVendor\\WPDesk\\Forms\\Field\\ProductSelect' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/ProductSelect.php',
    'FSVendor\\WPDesk\\Forms\\Field\\RadioField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/RadioField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\SelectField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/SelectField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\SubmitField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/SubmitField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\TextAreaField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/TextAreaField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\TimepickerField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/TimepickerField.php',
    'FSVendor\\WPDesk\\Forms\\Field\\Traits\\HtmlAttributes' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/Traits/HtmlAttributes.php',
    'FSVendor\\WPDesk\\Forms\\Field\\WooSelect' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/WooSelect.php',
    'FSVendor\\WPDesk\\Forms\\Field\\WyswigField' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Field/WyswigField.php',
    'FSVendor\\WPDesk\\Forms\\FieldsDataReceiver' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/FieldsDataReceiver.php',
    'FSVendor\\WPDesk\\Forms\\Form' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Form.php',
    'FSVendor\\WPDesk\\Forms\\Form\\AbstractForm' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Form/AbstractForm.php',
    'FSVendor\\WPDesk\\Forms\\Form\\FormWithFields' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Form/FormWithFields.php',
    'FSVendor\\WPDesk\\Forms\\Form\\FormsCollection' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Form/FormsCollection.php',
    'FSVendor\\WPDesk\\Forms\\Persistence\\FieldPersistenceStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Persistence/FieldPersistenceStrategy.php',
    'FSVendor\\WPDesk\\Forms\\Renderer\\JsonNormalizedRenderer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Renderer/JsonNormalizedRenderer.php',
    'FSVendor\\WPDesk\\Forms\\Resolver\\DefaultFormFieldResolver' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Resolver/DefaultFormFieldResolver.php',
    'FSVendor\\WPDesk\\Forms\\Sanitizer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Sanitizer.php',
    'FSVendor\\WPDesk\\Forms\\Sanitizer\\CallableSanitizer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Sanitizer/CallableSanitizer.php',
    'FSVendor\\WPDesk\\Forms\\Sanitizer\\NoSanitize' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Sanitizer/NoSanitize.php',
    'FSVendor\\WPDesk\\Forms\\Sanitizer\\TextFieldSanitizer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Sanitizer/TextFieldSanitizer.php',
    'FSVendor\\WPDesk\\Forms\\Serializer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Serializer.php',
    'FSVendor\\WPDesk\\Forms\\Serializer\\JsonSerializer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Serializer/JsonSerializer.php',
    'FSVendor\\WPDesk\\Forms\\Serializer\\NoSerialize' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Serializer/NoSerialize.php',
    'FSVendor\\WPDesk\\Forms\\Serializer\\ProductSelectSerializer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Serializer/ProductSelectSerializer.php',
    'FSVendor\\WPDesk\\Forms\\Serializer\\SerializeSerializer' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Serializer/SerializeSerializer.php',
    'FSVendor\\WPDesk\\Forms\\Validator' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Validator.php',
    'FSVendor\\WPDesk\\Forms\\Validator\\ChainValidator' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Validator/ChainValidator.php',
    'FSVendor\\WPDesk\\Forms\\Validator\\NoValidateValidator' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Validator/NoValidateValidator.php',
    'FSVendor\\WPDesk\\Forms\\Validator\\NonceValidator' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Validator/NonceValidator.php',
    'FSVendor\\WPDesk\\Forms\\Validator\\RequiredValidator' => $baseDir . '/vendor_prefixed/wpdesk/wp-forms/src/Validator/RequiredValidator.php',
    'FSVendor\\WPDesk\\Logger\\BasicLoggerFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/BasicLoggerFactory.php',
    'FSVendor\\WPDesk\\Logger\\LoggerFacade' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/LoggerFacade.php',
    'FSVendor\\WPDesk\\Logger\\LoggerFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/LoggerFactory.php',
    'FSVendor\\WPDesk\\Logger\\Processor\\SensitiveDataProcessor' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/Processor/SensitiveDataProcessor.php',
    'FSVendor\\WPDesk\\Logger\\Settings' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/Settings.php',
    'FSVendor\\WPDesk\\Logger\\SimpleLoggerFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/SimpleLoggerFactory.php',
    'FSVendor\\WPDesk\\Logger\\WC\\Exception\\WCLoggerAlreadyCaptured' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/WC/Exception/WCLoggerAlreadyCaptured.php',
    'FSVendor\\WPDesk\\Logger\\WC\\WooCommerceCapture' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/WC/WooCommerceCapture.php',
    'FSVendor\\WPDesk\\Logger\\WC\\WooCommerceHandler' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/WC/WooCommerceHandler.php',
    'FSVendor\\WPDesk\\Logger\\WC\\WooCommerceMonologPlugin' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/WC/WooCommerceMonologPlugin.php',
    'FSVendor\\WPDesk\\Logger\\WPDeskLoggerFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/WPDeskLoggerFactory.php',
    'FSVendor\\WPDesk\\Logger\\WP\\WPCapture' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/WP/WPCapture.php',
    'FSVendor\\WPDesk\\Mutex\\Mutex' => $baseDir . '/vendor_prefixed/wpdesk/wp-mutex/src/WPDesk/Mutex/Mutex.php',
    'FSVendor\\WPDesk\\Mutex\\MutexNotFoundInStorage' => $baseDir . '/vendor_prefixed/wpdesk/wp-mutex/src/WPDesk/Mutex/MutexNotFoundInStorage.php',
    'FSVendor\\WPDesk\\Mutex\\MutexStorage' => $baseDir . '/vendor_prefixed/wpdesk/wp-mutex/src/WPDesk/Mutex/MutexStorage.php',
    'FSVendor\\WPDesk\\Mutex\\StaticMutexStorage' => $baseDir . '/vendor_prefixed/wpdesk/wp-mutex/src/WPDesk/Mutex/StaticMutexStorage.php',
    'FSVendor\\WPDesk\\Mutex\\WordpressMySQLLockMutex' => $baseDir . '/vendor_prefixed/wpdesk/wp-mutex/src/WPDesk/Mutex/WordpressMySQLLockMutex.php',
    'FSVendor\\WPDesk\\Mutex\\WordpressPostMutex' => $baseDir . '/vendor_prefixed/wpdesk/wp-mutex/src/WPDesk/Mutex/WordpressPostMutex.php',
    'FSVendor\\WPDesk\\Mutex\\WordpressWpdb' => $baseDir . '/vendor_prefixed/wpdesk/wp-mutex/src/WPDesk/Mutex/WordpressWpdb.php',
    'FSVendor\\WPDesk\\Notice\\AjaxHandler' => $baseDir . '/vendor_prefixed/wpdesk/wp-notice/src/WPDesk/Notice/AjaxHandler.php',
    'FSVendor\\WPDesk\\Notice\\Factory' => $baseDir . '/vendor_prefixed/wpdesk/wp-notice/src/WPDesk/Notice/Factory.php',
    'FSVendor\\WPDesk\\Notice\\Notice' => $baseDir . '/vendor_prefixed/wpdesk/wp-notice/src/WPDesk/Notice/Notice.php',
    'FSVendor\\WPDesk\\Notice\\PermanentDismissibleNotice' => $baseDir . '/vendor_prefixed/wpdesk/wp-notice/src/WPDesk/Notice/PermanentDismissibleNotice.php',
    'FSVendor\\WPDesk\\Persistence\\Adapter\\ArrayContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Adapter/ArrayContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Adapter\\ReferenceArrayContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Adapter/ReferenceArrayContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Adapter\\WooCommerce\\WooCommerceSettingsContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Adapter/WooCommerce/WooCommerceSettingsContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Adapter\\WooCommerce\\WooCommerceShippingInstanceContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Adapter/WooCommerce/WooCommerceShippingInstanceContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Adapter\\WordPress\\WordpressOptionsContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Adapter/WordPress/WordpressOptionsContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Adapter\\WordPress\\WordpressPostMetaContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Adapter/WordPress/WordpressPostMetaContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Adapter\\WordPress\\WordpressSerializedOptionsContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Adapter/WordPress/WordpressSerializedOptionsContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Adapter\\WordPress\\WordpressTransientContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Adapter/WordPress/WordpressTransientContainer.php',
    'FSVendor\\WPDesk\\Persistence\\AllDataAccessContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/AllDataAccessContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Decorator\\DelayPersistentContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Decorator/DelayPersistentContainer.php',
    'FSVendor\\WPDesk\\Persistence\\Decorator\\DelaySinglePersistentContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/Decorator/DelaySinglePersistentContainer.php',
    'FSVendor\\WPDesk\\Persistence\\DeferredPersistentContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/DeferredPersistentContainer.php',
    'FSVendor\\WPDesk\\Persistence\\ElementNotExistsException' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/ElementNotExistsException.php',
    'FSVendor\\WPDesk\\Persistence\\PersistentContainer' => $baseDir . '/vendor_prefixed/wpdesk/wp-persistence/src/PersistentContainer.php',
    'FSVendor\\WPDesk\\PluginBuilder\\BuildDirector\\LegacyBuildDirector' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/BuildDirector/LegacyBuildDirector.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Builder\\AbstractBuilder' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Builder/AbstractBuilder.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Builder\\InfoActivationBuilder' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Builder/InfoActivationBuilder.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Builder\\InfoBuilder' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Builder/InfoBuilder.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\AbstractPlugin' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/AbstractPlugin.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\Activateable' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/Activateable.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\ActivationAware' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/ActivationAware.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\ActivationTracker' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/ActivationTracker.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\Conditional' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/Conditional.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\Deactivateable' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/Deactivateable.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\Hookable' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/Hookable.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\HookableCollection' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/HookableCollection.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\HookableParent' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/HookableParent.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\HookablePluginDependant' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/HookablePluginDependant.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\PluginAccess' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/PluginAccess.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\SlimPlugin' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/SlimPlugin.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Plugin\\TemplateLoad' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/TemplateLoad.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Storage\\Exception\\ClassAlreadyExists' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Storage/Exception/ClassAlreadyExists.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Storage\\Exception\\ClassNotExists' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Storage/Exception/ClassNotExists.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Storage\\PluginStorage' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Storage/PluginStorage.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Storage\\StaticStorage' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Storage/StaticStorage.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Storage\\StorageFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Storage/StorageFactory.php',
    'FSVendor\\WPDesk\\PluginBuilder\\Storage\\WordpressFilterStorage' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Storage/WordpressFilterStorage.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\Initialization\\BuilderTrait' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/Initialization/BuilderTrait.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\Initialization\\InitializationFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/Initialization/InitializationFactory.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\Initialization\\InitializationStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/Initialization/InitializationStrategy.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\Initialization\\PluginDisablerByFileTrait' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/Initialization/PluginDisablerByFileTrait.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\Initialization\\Simple\\SimpleFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/Initialization/Simple/SimpleFactory.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\Initialization\\Simple\\SimpleFreeStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/Initialization/Simple/SimpleFreeStrategy.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\Initialization\\Simple\\SimplePaidStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/Initialization/Simple/SimplePaidStrategy.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\Initialization\\Simple\\TrackerInstanceAsFilterTrait' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/Initialization/TrackerInstanceAsFilterTrait.php',
    'FSVendor\\WPDesk\\Plugin\\Flow\\PluginBootstrap' => $baseDir . '/vendor_prefixed/wpdesk/wp-plugin-flow-common/src/PluginBootstrap.php',
    'FSVendor\\WPDesk\\Pointer\\PointerConditions' => $baseDir . '/vendor_prefixed/wpdesk/wp-pointer/src/WPDesk/Pointer/PointerConditions.php',
    'FSVendor\\WPDesk\\Pointer\\PointerMessage' => $baseDir . '/vendor_prefixed/wpdesk/wp-pointer/src/WPDesk/Pointer/PointerMessage.php',
    'FSVendor\\WPDesk\\Pointer\\PointerPosition' => $baseDir . '/vendor_prefixed/wpdesk/wp-pointer/src/WPDesk/Pointer/PointerPosition.php',
    'FSVendor\\WPDesk\\Pointer\\PointersScripts' => $baseDir . '/vendor_prefixed/wpdesk/wp-pointer/src/WPDesk/Pointer/PointersScripts.php',
    'FSVendor\\WPDesk\\RepositoryRating\\DisplayStrategy\\AlwaysDisplayDisplayDecision' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/DisplayStrategy/AlwaysDisplayDisplayDecision.php',
    'FSVendor\\WPDesk\\RepositoryRating\\DisplayStrategy\\DisplayDecision' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/DisplayStrategy/DisplayDecision.php',
    'FSVendor\\WPDesk\\RepositoryRating\\DisplayStrategy\\GetParametersDisplayDecision' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/DisplayStrategy/GetParametersDisplayDecision.php',
    'FSVendor\\WPDesk\\RepositoryRating\\DisplayStrategy\\ShippingMethodDisplayDecision' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/DisplayStrategy/ShippingMethodDisplayDecision.php',
    'FSVendor\\WPDesk\\RepositoryRating\\PetitionText' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/PetitionText.php',
    'FSVendor\\WPDesk\\RepositoryRating\\RatingPetitionNotice' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/RatingPetitionNotice.php',
    'FSVendor\\WPDesk\\RepositoryRating\\RepositoryRatingPetitionText' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/RepositoryRatingPetitionText.php',
    'FSVendor\\WPDesk\\RepositoryRating\\TextPetitionDisplayer' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/TextPetitionDisplayer.php',
    'FSVendor\\WPDesk\\RepositoryRating\\TimeWatcher' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/TimeWatcher.php',
    'FSVendor\\WPDesk\\RepositoryRating\\TimeWatcher\\ShippingMethodGlobalSettingsWatcher' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/TimeWatcher/ShippingMethodGlobalSettingsWatcher.php',
    'FSVendor\\WPDesk\\RepositoryRating\\TimeWatcher\\ShippingMethodInstanceWatcher' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-rating-petition/src/TimeWatcher/ShippingMethodInstanceWatcher.php',
    'FSVendor\\WPDesk\\Session\\Adapter\\WooCommerceSession' => $baseDir . '/vendor_prefixed/wpdesk/wpdesk-sessions/src/WPDesk/Sessions/Adapter/WooCommerceSession.php',
    'FSVendor\\WPDesk\\Session\\Session' => $baseDir . '/vendor_prefixed/wpdesk/wpdesk-sessions/src/WPDesk/Sessions/Session.php',
    'FSVendor\\WPDesk\\Session\\SessionFactory' => $baseDir . '/vendor_prefixed/wpdesk/wpdesk-sessions/src/WPDesk/Sessions/SessionFactory.php',
    'FSVendor\\WPDesk\\ShowDecision\\AndStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/AndStrategy.php',
    'FSVendor\\WPDesk\\ShowDecision\\ConstantDefinedStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/ConstantDefinedStrategy.php',
    'FSVendor\\WPDesk\\ShowDecision\\ConstantNotDefinedStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/ConstantNotDefinedStrategy.php',
    'FSVendor\\WPDesk\\ShowDecision\\GetStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/GetStrategy.php',
    'FSVendor\\WPDesk\\ShowDecision\\OrStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/OrStrategy.php',
    'FSVendor\\WPDesk\\ShowDecision\\PostTypeStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/PostTypeStrategy.php',
    'FSVendor\\WPDesk\\ShowDecision\\ShouldShowStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/ShouldShowStrategy.php',
    'FSVendor\\WPDesk\\ShowDecision\\WooCommerce\\ShippingMethodInstanceStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/WooCommerce/ShippingMethodInstanceStrategy.php',
    'FSVendor\\WPDesk\\ShowDecision\\WooCommerce\\ShippingMethodStrategy' => $baseDir . '/vendor_prefixed/wpdesk/wp-show-decision/src/WooCommerce/ShippingMethodStrategy.php',
    'FSVendor\\WPDesk\\Tracker\\Assets' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/PSR/WPDesk/Tracker/Assets.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\AjaxDeactivationDataHandler' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/AjaxDeactivationDataHandler.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\DeactivationContent' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/DeactivationContent.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\DefaultReasonsFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/DefaultReasonsFactory.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\PluginData' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/PluginData.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\Reason' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/Reason.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\ReasonsFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/ReasonsFactory.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\Scripts' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/Scripts.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\Thickbox' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/Thickbox.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\Tracker' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/Tracker.php',
    'FSVendor\\WPDesk\\Tracker\\Deactivation\\TrackerFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-deactivation/src/WPDesk/Tracker/Deactivation/TrackerFactory.php',
    'FSVendor\\WPDesk\\Tracker\\OptInOptOut' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/PSR/WPDesk/Tracker/OptInOptOut.php',
    'FSVendor\\WPDesk\\Tracker\\OptInPage' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/PSR/WPDesk/Tracker/OptInPage.php',
    'FSVendor\\WPDesk\\Tracker\\OptOut' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/PSR/WPDesk/Tracker/OptOut.php',
    'FSVendor\\WPDesk\\Tracker\\PluginActionLinks' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/PSR/WPDesk/Tracker/PluginActionLinks.php',
    'FSVendor\\WPDesk\\Tracker\\Sender\\NullSender' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/Sender/NullSender.php',
    'FSVendor\\WPDesk\\Tracker\\Shop' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/PSR/WPDesk/Tracker/Shop.php',
    'FSVendor\\WPDesk\\Tracker\\UserFeedback\\AjaxUserFeedbackDataHandler' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/UserFeedback/AjaxUserFeedbackDataHandler.php',
    'FSVendor\\WPDesk\\Tracker\\UserFeedback\\Scripts' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/UserFeedback/Scripts.php',
    'FSVendor\\WPDesk\\Tracker\\UserFeedback\\Thickbox' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/UserFeedback/Thickbox.php',
    'FSVendor\\WPDesk\\Tracker\\UserFeedback\\Tracker' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/UserFeedback/Tracker.php',
    'FSVendor\\WPDesk\\Tracker\\UserFeedback\\TrackerFactory' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/UserFeedback/TrackerFactory.php',
    'FSVendor\\WPDesk\\Tracker\\UserFeedback\\UserFeedbackContent' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/UserFeedback/UserFeedbackContent.php',
    'FSVendor\\WPDesk\\Tracker\\UserFeedback\\UserFeedbackData' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/UserFeedback/UserFeedbackData.php',
    'FSVendor\\WPDesk\\Tracker\\UserFeedback\\UserFeedbackOption' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker-user-feedback/src/WPDesk/Tracker/UserFeedback/UserFeedbackOption.php',
    'FSVendor\\WPDesk\\View\\PluginViewBuilder' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/PluginViewBuilder.php',
    'FSVendor\\WPDesk\\View\\Renderer\\LoadTemplatePlugin' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Renderer/LoadTemplatePlugin.php',
    'FSVendor\\WPDesk\\View\\Renderer\\Renderer' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Renderer/Renderer.php',
    'FSVendor\\WPDesk\\View\\Renderer\\SimplePhpRenderer' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Renderer/SimplePhpRenderer.php',
    'FSVendor\\WPDesk\\View\\Resolver\\ChainResolver' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Resolver/ChainResolver.php',
    'FSVendor\\WPDesk\\View\\Resolver\\DirResolver' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Resolver/DirResolver.php',
    'FSVendor\\WPDesk\\View\\Resolver\\Exception\\CanNotResolve' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Resolver/Exception/CanNotResolve.php',
    'FSVendor\\WPDesk\\View\\Resolver\\NullResolver' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Resolver/NullResolver.php',
    'FSVendor\\WPDesk\\View\\Resolver\\Resolver' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Resolver/Resolver.php',
    'FSVendor\\WPDesk\\View\\Resolver\\WPThemeResolver' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Resolver/WPThemeResolver.php',
    'FSVendor\\WPDesk\\View\\Resolver\\WooTemplateResolver' => $baseDir . '/vendor_prefixed/wpdesk/wp-view/src/Resolver/WooTemplateResolver.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\AbstractConverter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/AbstractConverter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\FilterConverter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/FilterConverter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\FilterConvertersFactory' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/FilterConvertersFactory.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\ShippingIntegrations' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/ShippingIntegrations.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\SwitcherConverter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/SwitcherConverter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\Switcher\\Aelia\\Converter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/Switcher/Aelia/Converter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\Switcher\\CurrencySwitcherWoocommerce\\Converter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/Switcher/CurrencySwitcherWoocommerce/Converter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\Switcher\\FoxCurrencySwitcher\\Converter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/Switcher/FoxCurrencySwitcher/Converter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\Switcher\\WCML\\Converter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/Switcher/WCML/Converter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\Switcher\\WMCS\\Converter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/Switcher/WMCS/Converter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\Switcher\\WooCommerceMultiCurrency\\Converter' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/Switcher/WooCommerceMultiCurrency/Converter.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\Switcher\\WooCommerceMultiCurrency\\ShippingMethodController' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/Switcher/WooCommerceMultiCurrency/ShippingMethodController.php',
    'FSVendor\\WPDesk\\WooCommerce\\CurrencySwitchers\\Switcher\\WooCommerceMultiCurrency\\ShippingMethodIntegration' => $baseDir . '/vendor_prefixed/wpdesk/wc-currency-switchers-integrations/src/Switcher/WooCommerceMultiCurrency/ShippingMethodIntegration.php',
    'FSVendor\\WPDesk_Basic_Requirement_Checker' => $baseDir . '/vendor_prefixed/wpdesk/wp-basic-requirements/src/Basic_Requirement_Checker.php',
    'FSVendor\\WPDesk_Basic_Requirement_Checker_Factory' => $baseDir . '/vendor_prefixed/wpdesk/wp-basic-requirements/src/Basic_Requirement_Checker_Factory.php',
    'FSVendor\\WPDesk_Basic_Requirement_Checker_With_Update_Disable' => $baseDir . '/vendor_prefixed/wpdesk/wp-basic-requirements/src/Basic_Requirement_Checker_With_Update_Disable.php',
    'FSVendor\\WPDesk_Buildable' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/WithoutNamespace/Buildable.php',
    'FSVendor\\WPDesk_Has_Plugin_Info' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/WithoutNamespace/Has_Plugin_Info.php',
    'FSVendor\\WPDesk_Logger' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/deprecated/wpdesk-logger.php',
    'FSVendor\\WPDesk_Logger_Factory' => $baseDir . '/vendor_prefixed/wpdesk/wp-logs/src/deprecated/wpdesk-logger-factory.php',
    'FSVendor\\WPDesk_Plugin_Info' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/WithoutNamespace/Plugin_Info.php',
    'FSVendor\\WPDesk_Requirement_Checker' => $baseDir . '/vendor_prefixed/wpdesk/wp-basic-requirements/src/Requirement_Checker.php',
    'FSVendor\\WPDesk_Requirement_Checker_Factory' => $baseDir . '/vendor_prefixed/wpdesk/wp-basic-requirements/src/Requirement_Checker_Factory.php',
    'FSVendor\\WPDesk_Tracker' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/class-wpdesk-tracker.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Gateways' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-gateways.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Identification' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-identification.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Identification_Gdpr' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-identification-gdpr.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Jetpack' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-jetpack.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_License_Emails' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-license-emails.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Orders' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-orders.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Orders_Country' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-orders-country.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Orders_Month' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-orders-month.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Plugins' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-plugins.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Products' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-products.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Products_Variations' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-products-variations.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Server' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-server.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Settings' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-settings.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Shipping_Classes' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-shipping-classes.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Shipping_Methods' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-shipping-methods.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Shipping_Methods_Zones' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-shipping-methods-zones.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Templates' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-templates.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Theme' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-theme.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_User_Agent' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-user-agent.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Users' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-users.php',
    'FSVendor\\WPDesk_Tracker_Data_Provider_Wordpress' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/data_provider/class-wpdesk-tracker-data-provider-wordpress.php',
    'FSVendor\\WPDesk_Tracker_Factory_Prefixed' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/class-wpdesk-tracker-factory-prefixed.php',
    'FSVendor\\WPDesk_Tracker_Interface' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/class-wpdesk-tracker-interface.php',
    'FSVendor\\WPDesk_Tracker_Persistence_Consent' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/persistence/class-wpdesk-tracker-persistence-consent.php',
    'FSVendor\\WPDesk_Tracker_Sender' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/sender/class-wpdesk-tracker-sender.php',
    'FSVendor\\WPDesk_Tracker_Sender_Exception_WpError' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/sender/Exception/class-wpdesk-tracker-sender-exception-wperror.php',
    'FSVendor\\WPDesk_Tracker_Sender_Logged' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/sender/class-wpdesk-tracker-sender-logged.php',
    'FSVendor\\WPDesk_Tracker_Sender_Wordpress_To_WPDesk' => $baseDir . '/vendor_prefixed/wpdesk/wp-wpdesk-tracker/src/sender/class-wpdesk-tracker-sender-wordpress-to-wpdesk.php',
    'FSVendor\\WPDesk_Translable' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/WithoutNamespace/Translable.php',
    'FSVendor\\WPDesk_Translatable' => $baseDir . '/vendor_prefixed/wpdesk/wp-builder/src/Plugin/WithoutNamespace/Translatable.php',
    'Flexible_Shipping_Contextual_Info' => $baseDir . '/classes/Flexible_Shipping_Contextual_Info.php',
    'Flexible_Shipping_Plugin' => $baseDir . '/classes/class-flexible-shipping-plugin.php',
    'WPDesk\\FS\\AdvertMetabox\\FsiePluginAdvertMetabox' => $baseDir . '/src/WPDesk/FS/AdvertMetabox/FsiePluginAdvertMetabox.php',
    'WPDesk\\FS\\AdvertMetabox\\ProPluginAdvertMetabox' => $baseDir . '/src/WPDesk/FS/AdvertMetabox/ProPluginAdvertMetabox.php',
    'WPDesk\\FS\\Blocks\\FreeShipping\\FreeShippingBlock' => $baseDir . '/src/WPDesk/FS/Blocks/FreeShipping/FreeShippingBlock.php',
    'WPDesk\\FS\\Blocks\\FreeShipping\\FreeShippingStoreEndpointData' => $baseDir . '/src/WPDesk/FS/Blocks/FreeShipping/FreeShippingStoreEndpointData.php',
    'WPDesk\\FS\\Csat\\CsatOptionDependedOnShippingMethodAndAiUsage' => $baseDir . '/src/WPDesk/FS/Csat/CsatOptionDependedOnShippingMethodAndAiUsage.php',
    'WPDesk\\FS\\Helpers\\FlexibleShippingMethodsChecker' => $baseDir . '/src/WPDesk/FS/Helpers/FlexibleShippingMethodsChecker.php',
    'WPDesk\\FS\\Helpers\\ShippingMethod' => $baseDir . '/src/WPDesk/FS/Helpers/ShippingMethod.php',
    'WPDesk\\FS\\Helpers\\WooSettingsPageChecker' => $baseDir . '/src/WPDesk/FS/Helpers/WooSettingsPageChecker.php',
    'WPDesk\\FS\\Info\\FSIE' => $baseDir . '/src/WPDesk/FS/Info/FSIE.php',
    'WPDesk\\FS\\Info\\FSPro' => $baseDir . '/src/WPDesk/FS/Info/FSPro.php',
    'WPDesk\\FS\\Info\\FSWalkthrough' => $baseDir . '/src/WPDesk/FS/Info/FSWalkthrough.php',
    'WPDesk\\FS\\Info\\FSWalkthroughPL' => $baseDir . '/src/WPDesk/FS/Info/FSWalkthroughPL.php',
    'WPDesk\\FS\\Info\\Metabox' => $baseDir . '/src/WPDesk/FS/Info/Metabox.php',
    'WPDesk\\FS\\Info\\Metabox\\Links' => $baseDir . '/src/WPDesk/FS/Info/Metabox/Links.php',
    'WPDesk\\FS\\Info\\Video' => $baseDir . '/src/WPDesk/FS/Info/Video.php',
    'WPDesk\\FS\\Info\\WooCommerceABC' => $baseDir . '/src/WPDesk/FS/Info/WooCommerceABC.php',
    'WPDesk\\FS\\Info\\WooCommerceABCPL' => $baseDir . '/src/WPDesk/FS/Info/WooCommerceABCPL.php',
    'WPDesk\\FS\\Integration\\ExternalPluginAccess' => $baseDir . '/src/WPDesk/FS/Integration/ExternalPluginAccess.php',
    'WPDesk\\FS\\Newsletter\\SubscriptionForm' => $baseDir . '/src/WPDesk/FS/Newsletter/SubscriptionForm.php',
    'WPDesk\\FS\\Onboarding\\TableRate\\FinishOption' => $baseDir . '/src/WPDesk/FS/Onboarding/TableRate/FinishOption.php',
    'WPDesk\\FS\\Onboarding\\TableRate\\Onboarding' => $baseDir . '/src/WPDesk/FS/Onboarding/TableRate/Onboarding.php',
    'WPDesk\\FS\\Onboarding\\TableRate\\OptionAjaxUpdater' => $baseDir . '/src/WPDesk/FS/Onboarding/TableRate/OptionAjaxUpdater.php',
    'WPDesk\\FS\\Onboarding\\TableRate\\PopupData' => $baseDir . '/src/WPDesk/FS/Onboarding/TableRate/PopupData.php',
    'WPDesk\\FS\\Onboarding\\TableRate\\Tracker' => $baseDir . '/src/WPDesk/FS/Onboarding/TableRate/Tracker.php',
    'WPDesk\\FS\\Plugin\\PluginActivation' => $baseDir . '/src/WPDesk/FS/Plugin/PluginActivation.php',
    'WPDesk\\FS\\Plugin\\PluginLinks' => $baseDir . '/src/WPDesk/FS/Plugin/PluginLinks.php',
    'WPDesk\\FS\\Plugin\\UpgradeOnboarding' => $baseDir . '/src/WPDesk/FS/Plugin/UpgradeOnboarding.php',
    'WPDesk\\FS\\ProFeatures\\Tracker\\AjaxTracker' => $baseDir . '/src/WPDesk/FS/ProFeatures/Tracker/AjaxTracker.php',
    'WPDesk\\FS\\ProFeatures\\Tracker\\Tracker' => $baseDir . '/src/WPDesk/FS/ProFeatures/Tracker/Tracker.php',
    'WPDesk\\FS\\ProFeatures\\Tracker\\TrackingData' => $baseDir . '/src/WPDesk/FS/ProFeatures/Tracker/TrackingData.php',
    'WPDesk\\FS\\ProVersion\\ProVersionUpdateReminder' => $baseDir . '/src/WPDesk/FS/ProVersion/ProVersionUpdateReminder.php',
    'WPDesk\\FS\\Rate\\RateNotice' => $baseDir . '/classes/notices/abstract-rate.php',
    'WPDesk\\FS\\Rate\\RateNoticeImplementation' => $baseDir . '/classes/notices/rate-notice-implementation.php',
    'WPDesk\\FS\\Rate\\RateNoticeInterface' => $baseDir . '/classes/notices/interface-rate.php',
    'WPDesk\\FS\\Rate\\WPDesk_Flexible_Shipping_Rate_Notice' => $baseDir . '/classes/notices/rate-notice.php',
    'WPDesk\\FS\\Shipment\\AdminNotices' => $baseDir . '/src/WPDesk/FS/Shipment/AdminNotices.php',
    'WPDesk\\FS\\Shipment\\BulkAction' => $baseDir . '/src/WPDesk/FS/Shipment/BulkAction.php',
    'WPDesk\\FS\\Shipment\\BulkAction\\HandleAction' => $baseDir . '/src/WPDesk/FS/Shipment/BulkAction/HandleAction.php',
    'WPDesk\\FS\\Shipment\\BulkAction\\HandleActionLabels' => $baseDir . '/src/WPDesk/FS/Shipment/BulkAction/HandleActionLabels.php',
    'WPDesk\\FS\\Shipment\\BulkAction\\HandleActionManifest' => $baseDir . '/src/WPDesk/FS/Shipment/BulkAction/HandleActionManifest.php',
    'WPDesk\\FS\\Shipment\\BulkAction\\HandleActionSend' => $baseDir . '/src/WPDesk/FS/Shipment/BulkAction/HandleActionSend.php',
    'WPDesk\\FS\\Shipment\\BulkAction\\HandleActionStrategy' => $baseDir . '/src/WPDesk/FS/Shipment/BulkAction/HandleActionStrategy.php',
    'WPDesk\\FS\\Shipment\\BulkAction\\HandleActionStrategyInterface' => $baseDir . '/src/WPDesk/FS/Shipment/BulkAction/HandleActionStrategyInterface.php',
    'WPDesk\\FS\\Shipment\\DispatchLabelFile' => $baseDir . '/src/WPDesk/FS/Shipment/DispatchLabelFile.php',
    'WPDesk\\FS\\Shipment\\FilterOrders' => $baseDir . '/src/WPDesk/FS/Shipment/FilterOrders.php',
    'WPDesk\\FS\\Shipment\\ModifyOrderTable' => $baseDir . '/src/WPDesk/FS/Shipment/ModifyOrderTable.php',
    'WPDesk\\FS\\Shipment\\ModifyStatuses' => $baseDir . '/src/WPDesk/FS/Shipment/ModifyStatuses.php',
    'WPDesk\\FS\\Shipment\\SubscriptionsIntegration' => $baseDir . '/src/WPDesk/FS/Shipment/SubscriptionsIntegration.php',
    'WPDesk\\FS\\TableRate\\AI\\TrackerData' => $baseDir . '/src/WPDesk/FS/TableRate/AI/TrackerData.php',
    'WPDesk\\FS\\TableRate\\AI\\TrackerDataOnShippingMethodSaver' => $baseDir . '/src/WPDesk/FS/TableRate/AI/TrackerDataOnShippingMethodSaver.php',
    'WPDesk\\FS\\TableRate\\Beacon' => $baseDir . '/src/WPDesk/FS/TableRate/Beacon.php',
    'WPDesk\\FS\\TableRate\\Beacon\\Beacon' => $baseDir . '/src/WPDesk/FS/TableRate/Beacon/Beacon.php',
    'WPDesk\\FS\\TableRate\\Beacon\\BeaconClickedAjax' => $baseDir . '/src/WPDesk/FS/TableRate/Beacon/BeaconClickedAjax.php',
    'WPDesk\\FS\\TableRate\\Beacon\\BeaconDeactivationTracker' => $baseDir . '/src/WPDesk/FS/TableRate/Beacon/BeaconDeactivationTracker.php',
    'WPDesk\\FS\\TableRate\\Beacon\\BeaconDisplayStrategy' => $baseDir . '/src/WPDesk/FS/TableRate/Beacon/BeaconDisplayStrategy.php',
    'WPDesk\\FS\\TableRate\\ContextualInfo\\Creator' => $baseDir . '/src/WPDesk/FS/TableRate/ContextualInfo/Creator.php',
    'WPDesk\\FS\\TableRate\\Debug\\DebugTracker' => $baseDir . '/src/WPDesk/FS/TableRate/Debug/DebugTracker.php',
    'WPDesk\\FS\\TableRate\\Debug\\MultipleShippingZonesMatchedSameTerritoryNotice' => $baseDir . '/src/WPDesk/FS/TableRate/Debug/MultipleShippingZonesMatchedSameTerritoryNotice.php',
    'WPDesk\\FS\\TableRate\\Debug\\MultipleShippingZonesMatchedSameTerritoryTracker' => $baseDir . '/src/WPDesk/FS/TableRate/Debug/MultipleShippingZonesMatchedSameTerritoryTracker.php',
    'WPDesk\\FS\\TableRate\\Debug\\NoShippingMethodsNotice' => $baseDir . '/src/WPDesk/FS/TableRate/Debug/NoShippingMethodsNotice.php',
    'WPDesk\\FS\\TableRate\\DefaultRulesSettings' => $baseDir . '/src/WPDesk/FS/TableRate/DefaultRulesSettings.php',
    'WPDesk\\FS\\TableRate\\Exception\\ConditionInvalidNumberValue' => $baseDir . '/src/WPDesk/FS/TableRate/Exception/ConditionInvalidNumberValue.php',
    'WPDesk\\FS\\TableRate\\Exception\\ConditionNotDefined' => $baseDir . '/src/WPDesk/FS/TableRate/Exception/ConditionNotDefined.php',
    'WPDesk\\FS\\TableRate\\Exception\\ConditionRequiredField' => $baseDir . '/src/WPDesk/FS/TableRate/Exception/ConditionRequiredField.php',
    'WPDesk\\FS\\TableRate\\Exception\\RuleRequired' => $baseDir . '/src/WPDesk/FS/TableRate/Exception/RuleRequired.php',
    'WPDesk\\FS\\TableRate\\FreeShipping\\Assets' => $baseDir . '/src/WPDesk/FS/TableRate/FreeShipping/Assets.php',
    'WPDesk\\FS\\TableRate\\FreeShipping\\FreeShippingNotice' => $baseDir . '/src/WPDesk/FS/TableRate/FreeShipping/FreeShippingNotice.php',
    'WPDesk\\FS\\TableRate\\FreeShipping\\FreeShippingNoticeData' => $baseDir . '/src/WPDesk/FS/TableRate/FreeShipping/FreeShippingNoticeData.php',
    'WPDesk\\FS\\TableRate\\FreeShipping\\FreeShippingNoticeGenerator' => $baseDir . '/src/WPDesk/FS/TableRate/FreeShipping/FreeShippingNoticeGenerator.php',
    'WPDesk\\FS\\TableRate\\FreeShipping\\FreeShippingNoticeRenderer' => $baseDir . '/src/WPDesk/FS/TableRate/FreeShipping/FreeShippingNoticeRenderer.php',
    'WPDesk\\FS\\TableRate\\FreeShipping\\NoticeTextSettings' => $baseDir . '/src/WPDesk/FS/TableRate/FreeShipping/NoticeTextSettings.php',
    'WPDesk\\FS\\TableRate\\FreeShipping\\ProgressBarSettings' => $baseDir . '/src/WPDesk/FS/TableRate/FreeShipping/ProgressBarSettings.php',
    'WPDesk\\FS\\TableRate\\FreeShipping\\Tracker' => $baseDir . '/src/WPDesk/FS/TableRate/FreeShipping/Tracker.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Exporter' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Exporter.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\ExporterData' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/ExporterData.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Exporter\\JSON' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Exporter/JSON.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\ImporterData' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/ImporterData.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Importer\\AbstractImporter' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Importer/AbstractImporter.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Importer\\CSV' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Importer/CSV.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Importer\\Exception\\FileNotExists' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Importer/Exception/FileNotExists.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Importer\\Exception\\ImportCSVException' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Importer/Exception/ImportCSVException.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Importer\\Exception\\InvalidFile' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Importer/Exception/InvalidFile.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Importer\\Importer' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Importer/Importer.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Importer\\ImporterFactory' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Importer/ImporterFactory.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\Importer\\JSON' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/Importer/JSON.php',
    'WPDesk\\FS\\TableRate\\ImporterExporter\\ShippingClassTrait' => $baseDir . '/src/WPDesk/FS/TableRate/ImporterExporter/ShippingClassTrait.php',
    'WPDesk\\FS\\TableRate\\MultiCurrency' => $baseDir . '/src/WPDesk/FS/TableRate/MultiCurrency.php',
    'WPDesk\\FS\\TableRate\\Order\\ItemMeta' => $baseDir . '/src/WPDesk/FS/TableRate/Order/ItemMeta.php',
    'WPDesk\\FS\\TableRate\\Rates\\FlexibleShippingRates' => $baseDir . '/src/WPDesk/FS/TableRate/Rates/FlexibleShippingRates.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\AbstractCondition' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/Condition/AbstractCondition.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Condition' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/Condition/Condition.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\ConditionsFactory' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/ConditionsFactory.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\None' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/Condition/None.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Price' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Price.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\CartLineItem' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/CartLineItem.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\DayOfTheWeek' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/DayOfTheWeek.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\DimensionalWeight' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/DimensionalWeight.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\Item' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/Item.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\MaxDimension' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/MaxDimension.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\Product' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/Product.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductCategory' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductCategory.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductDimensionHeight' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductDimensionHeight.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductDimensionLength' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductDimensionLength.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductDimensionWidth' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductDimensionWidth.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductFieldRange' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductFieldRange.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductFieldValue' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductFieldValue.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductStockQuantity' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductStockQuantity.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductStockStatus' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductStockStatus.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ProductTag' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ProductTag.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ShippingClass' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ShippingClass.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\ShippingCost' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/ShippingCost.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\TimeOfTheDay' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/TimeOfTheDay.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\TotalOverallDimensions' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/TotalOverallDimensions.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\UserRole' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/UserRole.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Pro\\Volume' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Pro/Volume.php',
    'WPDesk\\FS\\TableRate\\Rule\\Condition\\Weight' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Condition/Weight.php',
    'WPDesk\\FS\\TableRate\\Rule\\ContentsFilter' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/ContentsFilter.php',
    'WPDesk\\FS\\TableRate\\Rule\\Cost\\AbstractAdditionalCost' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/Cost/AbstractAdditionalCost.php',
    'WPDesk\\FS\\TableRate\\Rule\\Cost\\AdditionalCost' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/Cost/AdditionalCost.php',
    'WPDesk\\FS\\TableRate\\Rule\\Cost\\RuleAdditionalCostFactory' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Cost/RuleAdditionalCostFactory.php',
    'WPDesk\\FS\\TableRate\\Rule\\Cost\\RuleAdditionalCostFieldsFactory' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Cost/RuleAdditionalCostFieldsFactory.php',
    'WPDesk\\FS\\TableRate\\Rule\\Cost\\RuleCostFieldsFactory' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/Cost/RuleCostFieldsFactory.php',
    'WPDesk\\FS\\TableRate\\Rule\\CostsCalculator' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/CostsCalculator.php',
    'WPDesk\\FS\\TableRate\\Rule\\PreconfiguredScenarios\\PreconfiguredScenariosFactory' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/PreconfiguredScenarios/PreconfiguredScenariosFactory.php',
    'WPDesk\\FS\\TableRate\\Rule\\PreconfiguredScenarios\\PredefinedScenario' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/PreconfiguredScenarios/PredefinedScenario.php',
    'WPDesk\\FS\\TableRate\\Rule\\PreconfiguredScenarios\\Tracker\\AjaxTracker' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/PreconfiguredScenarios/Tracker/AjaxTracker.php',
    'WPDesk\\FS\\TableRate\\Rule\\PreconfiguredScenarios\\Tracker\\Tracker' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/PreconfiguredScenarios/Tracker/Tracker.php',
    'WPDesk\\FS\\TableRate\\Rule\\PreconfiguredScenarios\\Tracker\\TrackingData' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/PreconfiguredScenarios/Tracker/TrackingData.php',
    'WPDesk\\FS\\TableRate\\Rule\\RoundingPrecision' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/RoundingPrecision.php',
    'WPDesk\\FS\\TableRate\\Rule\\Rule' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/Rule.php',
    'WPDesk\\FS\\TableRate\\Rule\\Settings\\SettingsProcessor' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/Settings/SettingsProcessor.php',
    'WPDesk\\FS\\TableRate\\Rule\\ShippingContents\\DestinationAddress' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/ShippingContents/DestinationAddress.php',
    'WPDesk\\FS\\TableRate\\Rule\\ShippingContents\\DestinationAddressFactory' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/ShippingContents/DestinationAddressFactory.php',
    'WPDesk\\FS\\TableRate\\Rule\\ShippingContents\\ShippingContents' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/ShippingContents/ShippingContents.php',
    'WPDesk\\FS\\TableRate\\Rule\\ShippingContents\\ShippingContentsImplementation' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/ShippingContents/ShippingContentsImplementation.php',
    'WPDesk\\FS\\TableRate\\Rule\\ShippingContents\\ShippingContentsMeta' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/ShippingContents/ShippingContentsMeta.php',
    'WPDesk\\FS\\TableRate\\Rule\\SpecialAction\\AbstractSpecialAction' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/SpecialAction/AbstractSpecialAction.php',
    'WPDesk\\FS\\TableRate\\Rule\\SpecialAction\\None' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/SpecialAction/None.php',
    'WPDesk\\FS\\TableRate\\Rule\\SpecialAction\\SpecialAction' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/Rule/SpecialAction/SpecialAction.php',
    'WPDesk\\FS\\TableRate\\Rule\\SpecialAction\\SpecialActionFactory' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/SpecialAction/SpecialActionFactory.php',
    'WPDesk\\FS\\TableRate\\Rule\\SpecialAction\\SpecialActionFieldsFactory' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/SpecialAction/SpecialActionFieldsFactory.php',
    'WPDesk\\FS\\TableRate\\Rule\\TrackerData' => $baseDir . '/src/WPDesk/FS/TableRate/Rule/TrackerData.php',
    'WPDesk\\FS\\TableRate\\RulesSettingsFactory' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/RulesSettingsFactory.php',
    'WPDesk\\FS\\TableRate\\RulesSettingsField' => $baseDir . '/src/WPDesk/FS/TableRate/RulesSettingsField.php',
    'WPDesk\\FS\\TableRate\\RulesTableSettings' => $baseDir . '/src/WPDesk/FS/TableRate/RulesTableSettings.php',
    'WPDesk\\FS\\TableRate\\ShippingMethodSingle' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethodSingle.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\BlockEditing\\BlockEditing' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/BlockEditing/BlockEditing.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\CommonMethodSettings' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/CommonMethodSettings.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Convert\\ConvertAction' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Convert/ConvertAction.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Convert\\ConvertNotice' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Convert/ConvertNotice.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Convert\\ConvertTracker' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Convert/ConvertTracker.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Duplicate\\DuplicateAction' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Duplicate/DuplicateAction.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Duplicate\\DuplicateNotice' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Duplicate/DuplicateNotice.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Duplicate\\DuplicateScript' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Duplicate/DuplicateScript.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Duplicate\\DuplicateTracker' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Duplicate/DuplicateTracker.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Duplicate\\DuplicatorChecker' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Duplicate/DuplicatorChecker.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\FreeShippingCalculator' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/FreeShippingCalculator.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Management\\ShippingMethodManagement' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Management/ShippingMethodManagement.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\MethodDescription' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/MethodDescription.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\MethodSettings' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/MethodSettings.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\MethodTitle' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/MethodTitle.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\RateCalculator' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/RateCalculator.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\RateCalculatorFactory' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/RateCalculatorFactory.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\SettingsDisplayPreparer' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/SettingsDisplayPreparer.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\SettingsProcessor' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/SettingsProcessor.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\SingleMethodSettings' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/SingleMethodSettings.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Timestamps\\MethodTimestamps' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Timestamps/MethodTimestamps.php',
    'WPDesk\\FS\\TableRate\\ShippingMethod\\Timestamps\\TrackerData' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethod/Timestamps/TrackerData.php',
    'WPDesk\\FS\\TableRate\\ShippingMethodsIntegration\\Integration' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethodsIntegration/Integration.php',
    'WPDesk\\FS\\TableRate\\ShippingMethodsIntegration\\OrderMetaData' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethodsIntegration/OrderMetaData.php',
    'WPDesk\\FS\\TableRate\\ShippingMethodsIntegration\\SettingsFields' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethodsIntegration/SettingsFields.php',
    'WPDesk\\FS\\TableRate\\ShippingMethodsIntegration\\ShippingRate' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethodsIntegration/ShippingRate.php',
    'WPDesk\\FS\\TableRate\\ShippingMethodsIntegration\\Tracker' => $baseDir . '/src/WPDesk/FS/TableRate/ShippingMethodsIntegration/Tracker.php',
    'WPDesk\\FS\\TableRate\\SingleRuleSettings' => $baseDir . '/vendor_prefixed/octolize/flexible-shipping-rules/src/TableRate/SingleRuleSettings.php',
    'WPDesk\\FS\\TableRate\\Tax\\TaxCalculator' => $baseDir . '/src/WPDesk/FS/TableRate/Tax/TaxCalculator.php',
    'WPDesk\\FS\\TableRate\\Tax\\Tracker' => $baseDir . '/src/WPDesk/FS/TableRate/Tax/Tracker.php',
    'WPDesk\\FS\\TableRate\\UserFeedback' => $baseDir . '/src/WPDesk/FS/TableRate/UserFeedback.php',
    'WPDesk\\FS\\Tracker\\TrackerNotices' => $baseDir . '/src/WPDesk/FS/Tracker/TrackerNotices.php',
    'WPDesk\\Helper\\HelperAsLibrary' => $vendorDir . '/wpdesk/wp-wpdesk-helper-override/src/Helper/HelperAsLibrary.php',
    'WPDesk_Flexible_Shipping' => $baseDir . '/classes/table-rate/shipping-method.php',
    'WPDesk_Flexible_Shipping_Admin_Notices' => $baseDir . '/classes/notices/admin-notices.php',
    'WPDesk_Flexible_Shipping_Logger_Settings' => $baseDir . '/classes/table-rate/logger/class-logger-settings.php',
    'WPDesk_Flexible_Shipping_Method_Created_Tracker_Deactivation_Data' => $baseDir . '/classes/tracker/class-method-created-tracker-deactivation-data.php',
    'WPDesk_Flexible_Shipping_Multilingual' => $baseDir . '/classes/table-rate/class-wpdesk-flexible-shipping-multilingual.php',
    'WPDesk_Flexible_Shipping_Order_Item_Meta' => $baseDir . '/classes/table-rate/order-item-meta.php',
    'WPDesk_Flexible_Shipping_Settings' => $baseDir . '/classes/table-rate/flexible-shipping-settings.php',
    'WPDesk_Flexible_Shipping_Shorcode_Unit_Dimension' => $baseDir . '/classes/table-rate/class-shortcode-unit-dimension.php',
    'WPDesk_Flexible_Shipping_Shorcode_Unit_Weight' => $baseDir . '/classes/table-rate/class-shortcode-unit-weight.php',
    'WPDesk_Flexible_Shipping_Tracker' => $baseDir . '/classes/tracker/class-wpdesk-flexible-shipping-tracker.php',
    'WPDesk_Tracker_Data_Provider' => $vendorDir . '/wpdesk/wp-wpdesk-helper-override/src/Interop/Tracker/class-wpdesk-tracker-data-provider.php',
    'WPDesk_Tracker_Factory' => $vendorDir . '/wpdesk/wp-wpdesk-helper-override/src/Helper/TrackerFactory.php',
    'WPDesk_Tracker_Interface' => $vendorDir . '/wpdesk/wp-wpdesk-helper-override/src/Interop/Tracker/class-wpdesk-tracker-interface.php',
    'WPDesk_Tracker_Sender' => $vendorDir . '/wpdesk/wp-wpdesk-helper-override/src/Interop/Tracker/class-wpdesk-tracker-sender.php',
);
