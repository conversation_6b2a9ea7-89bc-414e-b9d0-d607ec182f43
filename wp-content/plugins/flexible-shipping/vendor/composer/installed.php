<?php return array(
    'root' => array(
        'name' => 'wpdesk/flexible-shipping',
        'pretty_version' => '6.4.0',
        'version' => '*******',
        'reference' => '413f6550cbc035633a96adc0b5d46f94d5b27314',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'wpdesk/flexible-shipping' => array(
            'pretty_version' => '6.4.0',
            'version' => '*******',
            'reference' => '413f6550cbc035633a96adc0b5d46f94d5b27314',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wpdesk/wp-wpdesk-fs-shipment-interfaces' => array(
            'pretty_version' => '1.1.0',
            'version' => '*******',
            'reference' => 'bdbe9239aa428cf7f8fd5b8c7dfa19b9d13ff7dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wpdesk/wp-wpdesk-fs-shipment-interfaces',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wpdesk/wp-wpdesk-helper-override' => array(
            'pretty_version' => '1.1.0',
            'version' => '*******',
            'reference' => '77844fc71da7ebb7645f10f0fc15f5294fed8542',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wpdesk/wp-wpdesk-helper-override',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
