===Floating Notification Bar, <PERSON><PERSON> Menu on Scroll, Announcement Banner, and <PERSON><PERSON> Header for Any Theme - My Sticky Bar (formerly myStickymenu)===
Contributors: tomeraharon, galdub, premio
Tags:notification bar, floating bar, sticky menu, sticky header, sticky bar
Requires at least: 3.5.1
Tested up to: 6.8
Stable tag: 2.8.3
License: GPLv3

🔔 Create a welcome notification bar for your website. Also, My Sticky Bar plugin can make your menu or header sticky to the top when scrolled 📌

== Description ==

<a href="https://premio.io/help/mystickymenu/?utm_source=wordpressorg" target="_blank" title="Support">Support</a> | <a href="https://demo.premio.io/mystickymenu/?utm_source=wordpressorg" target="_blank" title="Premio.io demo site">Demo</a> | <a href="https://premio.io/?utm_source=wordpressorg" target="_blank" title="WordPress plugins">Recommended Plugins</a> | <a href="https://premio.io/downloads/mystickymenu/?utm_source=wordpressorg" target="_blank" title="My Sticky Bar Pro"><strong>Upgrade to Pro ⭐</strong></a>

<strong>Use My Sticky Bar to create a beautiful notification bar for your website. You can also use this notification bar & sticky menu plugin to make your menu or header sticky on top of page, after desired number of pixels when scrolled. You can use it also to create a Welcome bar for announcements, promotion and more. This lightweight plugin will help your visitors navigate easily and reach to the navigation menu without wasting time. Install My Sticky Bar and improve your website’s user experience! If you want to change the CSS style, disable on scroll down or disable on specific pages. check out the <a href="https://premio.io/downloads/mystickymenu/" target="_blank" title="My Sticky Bar pro plans">Pro version</a>.</strong>

By default, My Sticky Bar (formerly myStickymenu) should make your menu sticky right away if you turn the sticky menu feature on - which means your menu should become sticky right out of the box. Sticky menu is designed to use element class ".navbar" as "Sticky Class". That value should be modified in plugin settings for different themes to make it work. Sticky Class is actually nothing more than an element class (or id) of the element that should become sticky.

[youtube  https://www.youtube.com/watch?v=l9aRnPVuCVY]

<strong>📢 Welcome Notification Bar:</strong> Use this powerful feature to create a beautiful floating sticky notification bar. No coding skill required, just turn it on, set up your announcement text and button, and voilà - your top notification bar is good to go! 

= 💪 BENEFITS =
* Improve user experience by letting your visitors access the navigation menu and navigate to the pages they want when they want to.
* Make your website look more modern with the sticky menu on desktop and mobile.
* Increase number of page views and visitors' time on your website
* Create your own floating Welcome Bar and let your visitors know about your latest deals, announcements, or any other important information
* The My Sticky Bar dashboard makes it easy for you to manage your top bars and sticky menus in one place!

The sticky header function is localized (multi-language support) and responsive (as far as your theme is). Also, there is the possibility to add custom CSS code which makes this plugin very flexible, customizable, and user-friendly. You can also use the notification bar in any language.

My Sticky Bar plugin is originally designed for Twenty Thirteen template but should work on any theme, like OceanWP, Nirvana Template, Twenty Sixteen, Twenty Ten, Twenty Eleven, Twenty Twenty-Five, Twenty Twenty-Four, Twenty Twenty-Three, Max Magazine, Graduate, Lawyer theme, Spacious, Suits, and more.

My Sticky Bar is tested and compatible with Elementor website builder, Divi, WPBakery, Gutenberg, Visual composer, Beaver, and other WordPress website builders.

= 🚀 WITH WELCOME BAR FEATURES YOU CAN: =
* Display your floating bar at the top position
* When the user clicks on the welcome bar button you can close the bar, redirect to another URL, or launch a <a href="https://www.poptin.com/?utm_source=msm" target="_blank" title="Poptin pop up">Poptin pop up</a>
* New! Contact Form 📝 - Include a contact form in your notification bar to collect either name and email or name and phone number. This feature can improve lead generation on your website. You’ll be able to gather your users' details using a contact form and reach them directly.
* Change the background and button colors for your hello bar
* Change display frequency after submission - show the announcement bar on every page, once a visit, or don't show the announcement bar after it's submitted
* Add an attention effect to your button and increase conversion and CTR
* New! Add any custom HTML to your notification bar including strong, underline, italic, and much more. Use custom HTML to create a beautiful notification bar
* Change font size and font family
* Set your own text and call-to-action button text
* Show the closing X on desktop/mobile
* Show the call-to-action button in your top notification bar on desktop/mobile
* Redirect your visitors to another URL on-click

= 📌 WITH MY STICKY BAR YOU CAN: =
* Show sticky header when scrolling the page
* Fade or slide effect
* Disable at a small screen
* Choose when to make visible on scroll
* Change the font color when the menu is sticky
* Change the background color
* Change the opacity
* Change the sticky transition time
* Define the z-index of the sticky header
* Set an entrance effect for your sticky menu
* Write your notification bar announcements in different languages - we support both LTR and RTL formats
* New and improved dashboard - The new My Sticky Bar dashboard will help you manage your notification bars, top bars, and sticky menus better.
* New! Disable the sticky menu - You now have the option to disable sticky menu features if you want to use only top bars or welcome bars
* Many of our users use the welcome bar to announce about their Coronavirus COVID-19 updates. You can use the notification bar as a Corona Virus - COVID-19 banner to make sure your visitors are up-to-date.

= 👌 Welcome Bar Pro Features =
* Multi Welcome Bar - Create a separate notification bar for different pages based on page targeting rules (E.g. separate notification bar for different languages, different product categories, and much more)
* New! Countdown timer ⏳ -  Add a countdown timer in your notification bar for that much-needed sense of urgency. This new countdown timer notifications bar feature can really increase your conversion rate because it creates a sense of scarcity and urgency on your promotions.
* New! Sliding texts - Display multiple lines of content in your notification bar that can scroll automatically in your desired direction. You can use it to promote different products, as a news ticker, or any other changing content in your notification bar* New! Send contact form leads via email - you can get email notifications when a visitor fills our your notification bar contact form
* New! Add a discount coupon element 🎫 - This feature lets you add a discount code coupon to your notification bar. Reduce cart abandonment and improve sales by offering your users a coupon discount code when they need it
* New! Country targeting - Show or hide your notification bar for specific countries. Create unique notification bars for different countries and tailor your messaging to specific audiences
* Display your floating bar at the bottom position
* Change the height of the sticky notification bar
* Open redirect link in a new tab
* Show your notification bar just on mobile or desktop
* Show your notification bar on a scroll or after a few seconds
* Set an expiry date for your welcome bar, including settings a time zone
* Display your floating notification bar on specific pages
* You can set a thank you screen which appears after the button is clicked (can be used for coupons or any other message you want to show your visitors after they click on the button)
* Add a rel Attribute to your notification bar button

But wait, there's more 😉

= 🔥 WHAT DO YOU GET IN THE PRO PLAN =
* Disable floating menu on Desktop/Mobile
* Disable floating menu on scroll down
* Add CSS style to the floating header
* Disable/enable floating header on specific pages

<a href="https://premio.io/downloads/mystickymenu/" target="_blank" title="My Sticky Bar pro plans"><strong>>>> See Pro plans here 🚀</strong></a> 

= Live Demo =
A live demo for My Sticky Bar plugin and notification bar is available at <a href="https://demo.premio.io/mystickymenu/?utm_source=wordpressorg" target="_blank" title="Premio.io demo site">Premio's demo site</a>.


= 🗓️ Countdown =
Do you have a limited-period offer? Want to create a sense of urgency to boost leads in your notification bar? Add a countdown to your notification bar. A highly customizable feature that can be paired with your website’s theme and colors.

Apart from setting a global date and time-based countdown, you can also start an individual countdown for every unique visitor from when they arrive on your website.


= 🔒 My Sticky Bar IS GDPR COMPLAINT  =
We don’t store any local data about your visitors. Any time your menu or notification bar appears on your site, the plugin will not store any local data about it. 
<strong>Pro tip:</strong> You can use the plugin's notification bar to let your website's visitors know about your cookie policy :)

= 📧 Grow your email list using the notification bar contact form =
Collect valuable visitor information from your website, by giving them access to a contact from any time anywhere. The notification bar contact form helps you get leads into your local database. Create effective marketing campaigns and to contact your website visitors. Using the contact form in combination with features like a countdown, coupons, etc gives you a powerful way to convince users to share their lead information with you. 

When a visitor fills in their details (email, phone, name etc) on the welcome bar, if you’re using the Pro version you can get the lead sent to your email.

This helps you get notified as soon as a visitor shows interest in your website. It is an extremely useful tool in your arsenal to convert leads quickly as soon as they enquire about your services or products.

= 🏷️ Show and Copy Coupons & Discounts Codes =
Get your visitors to use your coupons in the least amount of time and clicks without hassle! Your visitors can simply copy the coupon or discount code from the welcome bar with just one click. This feature also displays a message to the user that the coupon has been copied to their clipboard, making their journey on your website extremely pleasant.

= 🎯 Page targeting and triggers =
With the Pro version, choose to show or not show the widget on specific pages using various targeting rules. With our smart rules, you don’t have to spend hours defining all the pages for targeting; simply define what a page URL should contain and cover targeting for all relevant pages. A very powerful feature that helps you personalize your website’s experience for different types of visitors.

Use triggers to set events at which your widget pops out. Do you think your visitor might be potential lead if they scroll through 75% of your website? Show them a coupon from the welcome bar by triggering it.

= 🔝 Sticky Menu =
Easily adds a sticky menu/header to any theme using My Sticky Bar. Make your website look modern and increase accessibility by adding a menu that sticks even when the user scrolls. 


= 🤗 Testimonials =


> "Works great.
One of the key elements that I consider when choosing a theme is the sticky menu option. If the theme doesn't have it normally I don't use it. But now that’s not a problem for me anymore."<br><br>


> "This plugin rocks!
This plugin does exactly what it should. It is simple but powerful. I would suggest to anyone who wants to make their navigation menu sticky! I especially love the hide header on scroll down, show on scroll up feature that is built it. Great work!<br>




== Installation ==
After you activate the plugin, you should be able to create your first notification bar with ease, just customize the appearance and save it. If you want to create a sticky menu, in most cases the plugin should be able to find the class/ID for the menu, if not then change sticky class/id to .your_navbar_class or #your_navbar_id - you might need to purge the cache after saving.


For example:


Celestial-Lite (sticky header)
** Sticky Class => #branding
** .myfixed css class => .myfixed { margin:0 auto!important; float:none!important; border:0px!important; background:none!important; max-width:100%!important; } .myfixed #site-navigation { margin-top:0px!important; } .myfixed #logo img {width: 200px;}


Church Theme
** Sticky Class => #menu-hauptnavi


Customizr Theme
** Sticky Class => .row-fluid (for header) or .nav (for menu)


CyberChimps
** Sticky Class => .navbar-inner


Decode
** Sticky Class => .header-menu
** Make visible when Scrolled after => 370
** .myfixed css class => .myfixed { margin:0 auto!important; float:none!important; border:0px!important; background:none!important; max-width:100%!important; padding-top:5px; padding-bottom:5px; }


Genesis / Dynamik Theme
** Sticky Class(sticky header) =>.site-header
** Sticky Class(sticky menu) =>.nav-primary


Graduate
** Sticky Class => .main-navigation


Graphene
** Sticky Class => #header-menu-wrap


Illdy
** Sticky Class => .top-header
** Disable at Small Screen Sizes => 993


Illdy (mobile support)
** Sticky Class => .header-blog
** CSS Style => #mysticky-nav .myfixed { margin:0 auto; float:none; border:0px; background:none; max-width:100%; } .myfixed .bottom-header {display:none;}


Lawyer theme
** Sticky Class => .wrapper
** .myfixed { margin:0 auto!important; float:none!important; border:0px!important; background:none!important; max-width:100%!important; } .myfixed { padding-left: 0px; padding-bottom: 0px; padding-top: 0px; } .wrapfixed { -webkit-box-shadow: 0 8px 6px -6px rgba(68,68,68,0.6); -moz-box-shadow: 0 8px 6px -6px rgba(68,68,68,0.6); box-shadow: 0 8px 6px -6px rgba(68,68,68,0.6); } .myfixed .logo img { max-height: 45px; } .myfixed .quick-info { display: none; } .myfixed .mainmenu { margin: 0px } .myfixed .mainmenu { padding-top: 7px; }


Max Magazine
** Sticky Class => #nav
** Make visible when Scrolled after => 250
** .myfixed css class => .myfixed { margin:0 auto!important; float:none!important; border:0px!important; background:none!important; max-width:100%!important; } #nav { min-height:40px!important; }


Naturo Lite
** Sticky Class => .header-inner


Nirvana Template
** Sticky Class => #access


OceanWP
** Sticky Class => #site-header


Responsive
** Sticky Class => .main-nav
** Make visible when Scrolled after => 250
** .myfixed css class => .myfixed { margin:0 auto!important; float:none!important; border:0px!important; max-width:100%!important;} .myfixed .menu { background:none!important;}


Spacious
** Sticky Class => #masthead (sticky header)
** Make visible when Scrolled after => 250
** .myfixed css class => .myfixed { margin:0 auto!important; float:none!important; border:0px!important; max-width:100%!important;} .myfixed #site-description { display:none;} .myfixed #site-title a { font-size: 24px; } .myfixed #header-text { padding:0px!important;} .myfixed .main-navigation { padding-top: 0px!important; }


Storefront
** Sticky Class => .main-navigation


Suits
** Sticky Class => .navbar


Tempera Template
** Sticky Class => .menu


Travelify
** Sticky Class => #main-nav


Twenty Sixteen
** Sticky Class => .site-header-main


Twenty Ten
** Sticky Class => .menu
** Make visible when Scrolled after => 370
** .myfixed css class => .myfixed { margin:0 auto!important; float:none!important; border:0px!important; background:none!important; max-width:100%!important; } .menu {min-height:38px;}


Twenty Eleven
** Sticky Class => #access
** Make visible when Scrolled after => 600
** .myfixed css class => .myfixed { float:none!important; max-width:100%;}
.myfixed .menu { background-color:none; float:left; }
.myfixed div { margin: 0px!important ; padding: 0px 7.6%; }


Twenty Eleven (add search to sticky menu bar)
** Sticky Class => #branding
** Make visible when Scrolled after => 600
** .myfixed css class => .myfixed { margin:0 auto!important; float:none!important; border:0px!important; background:none!important; max-width:100%!important; } .myfixed a {display:none;} #mysticky-wrap {min-height:500px;} .myfixed #site-title, .myfixed #site-description{display:none;} .myfixed #access { margin: 0px auto 0px; } .myfixed #searchform { top: 0.3em!important; }


Twenty Twelve
** Sticky Class => .nav-menu
** Make visible when Scrolled after => 250


Twenty Thirteen (sticky header – add your own custom logo image and edit to suite your needs)
** Sticky Class => #masthead
** .myfixed css class => .myfixed { margin:0 auto; max-width:1070px; width:100%!important;} .myfixed .search-form, .myfixed .site-description, .myfixed .site-title { display:none;} .myfixed .home-link { max-width: 55px; min-height: 38px; margin-left:20px; float:left; background-image: url('logo.png'); } .myfixed .navbar { max-width:645px; float:right;} .wrapfixed .navbar { background-color: transparent; } .wrapfixed ul { padding-right:0px; }


Webnus netStudio theme
** Sticky Class =>.nav-wrap2

Wrock Metro Theme (entire header)
** Sticky Class =>#navigation


== Frequently Asked Questions ==


= How to find Sticky Class, what should I enter here? =
So this depends on what you want to make sticky and what theme do you use, but for example, if you want your menu to be sticky, then you can examine the code (in firefox right click and “View page source”) and find HTML element in which your menu is situated. This element has some class or id, and that’s the Sticky Class we need. If using class then don’t forget to ad dot (.) in front of the class name or hash (#) in front of id. Twenty Thirteen default working class is ".navbar" without of quotes.


= Is there any way to restrict the width to the width of the header, rather than it being full width? =
Yes, just leave the "Sticky Background Color" field blank (clear). Then if needed define a custom background color for a sticky header inside ".myfixed CSS class" field using .myfixed class. 


= Ho do I add a small logo to the menu? =
That will depend on a theme you use, but if initially your menu and logo are in one div then you can use that div class or id in My Sticky Bar settings. 


If not you can change that in your header template file and add logo and menu divs inside new div with some custom class or id, then use that class or id in My Sticky Bar settings as a sticky class.


In CSS you can style your custom class while not sticky using the custom class you added before. Furthermore, you can style your menu while sticky using .myfixed class which is added by js to your custom class while sticky is active. 


In some cases, you can use the whole header div and then just style it differently with .myfixed class, hide what you don’t need to use CSS display:none; property, and position logo and menu as you like. 


= Is there a time limit for the free plan? =
No. you can use the free plan as long as you want.


= Can I use My Sticky Bar plugin on more than 1 domain? =
There 2 ways to do it:
1. You can install the free Sticky Menu plugin on any website you want
2. You can buy the Pro plan that includes licenses for 5 domains, or the Agency plan that includes licenses for 20 domains.
Check out our <a href="https://premio.io/downloads/mystickymenu/" target="_blank" title="My Sticky Bar plans page">plans page</a>.


= How long is my paid Sticky Menu plugin license valid for? =
Once you purchase any <a href="https://premio.io/downloads/mystickymenu/" target="_blank" title="My Sticky Bar plans page">paid plan</a> of My Sticky Bar, you can use it forever. Support and updates are available for 1 year. You can renew your license each year to get another year of support and updates.


= Will Sticky Menu plugin stop working if I don’t renew my license? =
Of course NOT! 🙂
Your Sticky Menu plugin and all your settings will continue to work as before; however, you will no longer receive plugin updates including feature additions, improvements, and support.


= How do I activate my pro plan? =
Just follow our <a href="https://premio.io/wordpress-premio-pro-version-installation-guide/" target="_blank" title="WordPress installation guide">pro installation guide</a>.


= Is there a live demo My Sticky Bar =
A live demo for My Sticky Bar plugin is available at <a href="https://demo.premio.io/mystickymenu/?utm_source=wordpressorg" target="_blank" title="Premio.io demo site">Premio's demo site</a>.


= How does the Welcome Bar feature work and do I have to enable it? =
After installing the plugin, you’ll have the option to turn on the welcome bar. You can create a floating bar that’ll appear on your site, and use it for promotion, announcements, and more. You don’t have to turn that feature on if you don’t want to :)


== Screenshots ==


1. Check out the welcome bar in action
2. The notification bar and sticky menu dashboard
3. Add a countdown timer to your notification bar to create an urgency sense
4. Design your welcome bar
5. Add your custom CSS
6. You can choose your current menu from the dropdown or enter it ID or class manually
7. You can change the background text, call-to-action text, fonts, and colors
8. This is how My Sticky Bar will actually look on your site


== Changelog ==

= 2.8.3 =
WordPress Playground widget creation bug fixed

= 2.8.2 =
Improved support flow

= 2.8.1 =
Settings page bug fixed

= 2.8.0 =
Improved UI flow

= 2.7.9 =
Preview bug fixed

= 2.7.8 =
Internal UI improvement

= 2.7.7 =
WordPress admin panel CSS bug fixed

= 2.7.6 =
Option to add Poptin pop ups to your website
Improved flow to align the button compared to the text position
Various bugs fixed

= 2.7.5 =
Sticky menu settings bug fixed

= 2.7.4 =
Various bugs were fixed

= 2.7.3 =
Notification bar analytics: track the number of visitors and clicks
Option to add a background image to the notification bar
Show the bar only for logged-in or logged-out users
Option to show the coupon code right away
Improved preview for notification bar (full screen)
Set a specific date & time for your notification bar to appear
Various bugs were fixed

= 2.7.2 =
JavaScript in notification bar text bug fixed

= 2.7.1 =
Button bug fixed

= 2.7 =
Bar text bug fixed

= 2.6.9 =
WordPress 6.5 compatibility

= 2.6.8 =
Editor access bug fixed

= 2.6.7 =
Leads page export bug fixed

= 2.6.6 =
Onboarding modal bug fixed

= 2.6.5 =
Renamed My Sticky Menu to My Sticky Bar
Option to hide/show the notification bar based on tags, categories, posts, and pages (Pro feature)
Notification bar sliding texts transition period and speed settings
Fixed pagination bug
Added "defer" to our script tags to improve performance
Added an option to set an hour to the countdown
Google Page Speed Insight bug fixed
Bugs fixed

= 2.6.4 =
Bugs fixed

= 2.6.3 =
Sliding texts - Display multiple lines of content in your notification bar that can scroll automatically in your desired direction. You can use it to promote different products, as a news ticker, or any other changing content in your notification bar (Pro feature)
Country targeting - Show or hide your notification bar for specific countries. Create unique notification bars for different countries and tailor your messaging to specific audiences (Pro feature)

= 2.6.2 =
Flow changes

= 2.6.1 =
Removed character limit of the contact form's button in the notification bar

= 2.6 =
Add a contact form to your Welcome bar and collect your visitors’ contact information
Pro feature: add a discount coupon code element to your Welcome bar
Pro feature: get email notifications for contact form leads
We’ve also fixed some bugs
= 2.5.9 =
Fixed notification bar activation bug, fixed UI font bug, and added tooltips and improved the interface content
= 2.5.8 =
Plans text change
= 2.5.7 =
Improvement: Better support for languages that read from right to left (like Hebrew or Arabic)
Improvement: We’ve also added inherit & system stack font options to give you flexibility when it comes to font choices on your menu bar.
Improvement: New My Sticky Bar dashboard to help you manage your top bars and sticky menus better.
Improvement: Disabling sticky menu features - you can now disable the sticky menu feature if you want to use top bars only
Improvement: 
Fixed: Optimized the JS and fix “console.log” on mystickymenu.js bug


For Pro plan users:
Added: You can now put countdown timers on your sticky menu! Best used for promotions or launch announcements!
= 2.5.6 =
Z-index welcome bar fix
= 2.5.5 =
Fixed notification bar & WordPress admin panel bar overlap bug
= 2.5.4 =
Poptin support was added, you can now launch Poptin pop ups
= 2.5.3 =
Support route changed
= 2.5.2 =
Bugs fixed
= 2.5.1 =
WordPress 5.7 adjustments
= 2.5 =
Better instructions for the sticky menu feature & explainer video
= 2.4.9 =
Mobile bug fixed
= 2.4.8 =
JQuery bug fixed
= 2.4.7 =
Background color fix
= 2.4.6 =
Height fix 
= 2.4.5 =
Background color fix
= 2.4.4 =
New entrance effects and bug fixes
= 2.4.3 =
Infrastructure changes
= 2.4.2 =
iPhone and iPad notification bar and sticky menu frontend issues fixed
= 2.4.1 =
Credit removal
= 2.4 =
You can now change the sticky menu font color when the menu is sticky. We've also fixed a resize issue and a Divi gap bug
= 2.3.9 =
Notification bar fix
= 2.3.8 =
You can add custom HTML to your notification bar now
= 2.3.7 =
Change the welcome announcement bar frequency
= 2.3.6 =
WP 5.4 compatibility, and welcome announcement bar fixed
= 2.3.5 =
Review change
= 2.3.4 =
Frontend fixes
= 2.3.3 =
Slight CSS fixes
= 2.3.2 =
Minor bug fixes
= 2.3.1 =
If the sticky menu is on, and welcome bar is disabled, google fonts aren't downloaded
= 2.3 =
Fixed some appearance issues
= 2.2.9 =
Multiple lines support for the Welcome Bar
= 2.2.8 =
WP-admin submit button styling bug fixed
= 2.2.7 =
Added attention effects for the button
= 2.2.6 =
Sanitize input fixes
= 2.2.5 =
myStickyelements now works out-of-the-box with the following themes (which means you don't need to find your menu's class, it should work right after installation): Twenty Nineteen, Twenty Seventeen, Hello Elementor, OceanWP, Astra, Storefront, Twenty Sixteen, Neve, Hestia, Sydney, Shapely, GeneratePress, Mesmerize, Ashe, and Total. We've also added Google Lighthouse compatibility
= 2.2.4 =
Smoother transition when the sticky menu disappears
= 2.2.3 =
Pro version changes
= 2.2.2 =
Fixed apostrophes bug for Welcome bar
= 2.2.1 =
Sticky menu and welcome bar conflict fix
= 2.2 =
You can now add a welcome bar to your website
= 2.1.8 =
Upgrade page update
= 2.1.7 =
Fixed undefined indexes
= 2.1.6 =
Saving issue bug fixed
= 2.1.5 =
Security update
= 2.1.4 =
Fixed some page builders issues
= 2.1.3 =
Fixed variable output warnings bugs
= 2.1.2 =
Upgrade page change
= 2.1.1 =
* Added a testimonial
= 2.1 =
* New and easy UI, you can now select your menu from a dropdown
= 2.0.6 =
* Fixed: Enable / Disable at 404 page
= 2.0.5 =
* Added: Admin settings sidebar right
* Added: Admin separated js script
* Added: Admin separated css script
= 2.0.4 =
* Added: Hide for large screens
= 2.0.3 =
* Fixed: Missing bg color field
= 2.0.1 =
* Fixed: If user logged in but admin bar is not showing (error to calculate activation height)
* Fixed: Admin style conflict with Scripts n Styles plugin


= 2.0 =
* Added: If Make visible on Scroll is set to 0 automatic activation and deactivation scroll height will be used (useful for responsive designs). 
* Added: Tabs for settings.
* Added: option to disable on scroll down (show only on scroll up).
* Added: Class .up and .down on scroll up or down.
* Added: Reset to default settings.
* Added: remove myStickymenu settings on plugin uninstall.
* Added: index.php to plugin root to prevent directory browsing.
* Fixed: If sticky menu class does not exist (null or undefined), it will show console log instead of error.
* Fixed: slide effect now slides on deactivate as well.
* Fixed: admin bar overlap for screens less than 600 px wide.






= 1.9.1 =
* Changed: admin bar is fixed as default, and menu goes under it.


= 1.9 =
* Fixed: enable / disable for search pages.


= 1.8.9 =
* Added: New option - Disable at certain posts and pages.


= 1.8.8 =
* Fixed: removed esc_attr for echo css, since input is already sanitized.


= 1.8.7 =
* Fixed: minor bug when browser resized.


= 1.8.6 =
* Fixed: minor bug.


= 1.8.5 =
* Improved: Improved performance and optimized scripts.


= 1.8.4 =
* Fixed: changed is_home() to is_front_page() for menu activation height on front page.


= 1.8.3 =
* Change: minor cosmetic changes…


= 1.8.2 =
* Fixed: js load on https


= 1.8.1 =
* Added: “Disable CSS“. If you plan to add style manually to your style.css in order to improve your site performance disable plugin CSS style printed by default in document head element.
* Minimized mystickymenu.js to improve performance.


= 1.8 =
* Added: "Make visible when scrolled on Homepage" after number of pixels. Now it’s possible to have one activation height for home page and another for the rest of the pages.
* Added German language


= 1.7 =
* Added multi language support (localization).
* Added languages - English (default), Spanish, Serbian and Croatian.
* Added Iris color picker script.
* Fixed jumping of page on scroll while menu is activated (height is defined before scroll event).
* mystickymenu.js moved to js folder


= 1.6 =
* Added: "Make visible when scroled" after number of pixels option.
* Fixed opacity 100 bug.


= 1.5 =
* Added option to enter exact width in px when sticky menu should be disabled "Disable at Small Screen Sizes".
* Added “.myfixed css class” setting field – edit .myfixed css style via plugin settings to create custom style.
* Fixed google adsense clash and undefined index notice.
* is_user_logged_in instead of old “Remove CSS Rules for Static Admin Bar while Sticky” option


= 1.4 =
* Added fade in or slide down effect settings field for sticky class.
* Added new wrapped div around selected sticky class with id mysticky_wrap which should make menu works smoother and extend theme support.


= 1.3 =
* Added "block direct access" to the mystickymenu plugin file (for security sake).
* Added Enable / Disable at small screen sizes and Remove not necessary css for all themes without admin bar on front page.
* Added “margin-top :0px” to .myfixed class in head which should extend theme support.


= 1.2 =
* Fixed mystickymenu.js for IE browsers, so myStickymenu is now compatible with IE 10, 11
  
= 1.1 =
* Added administration options, now available through Dashboard / Settings / myStickymenu. Options are as follows: Sticky Class, Sticky z-index, Sticky Width, Sticky Background Color, Sticky Opacity, Sticky Transition Time. 
* Old mystickymenu.css file is deprecated and not in use anymore.


= 1.0 =
* First release of myStickymenu plugin


== Upgrade Notice ==


= 1.8.4 =
* Fixed: changed is_home() to is_front_page() for menu activation height on front page.


= 1.8.3 =
* Change: minor cosmetic changes…


= 1.8.2 =
* Fixed: js load on https


= 1.8.1 =
* Added: “Disable CSS“. If you plan to add style manually to your style.css in order to improve your site performance disable plugin CSS style printed by default in document head element.
* Minimized mystickymenu.js to improve performance.


= 1.8 =
* Added: "Make visible when scrolled on Homepage" after number of pixels. Now it’s possible to have one activation height for home page and another for the rest of the pages.


= 1.7 =
* Added multi language support (localization).
* Added Iris color picker script.
* Fixed jumping of page on scroll while menu is activated (height defined before scroll event).
* mystickymenu.js moved to js folder


= 1.6 =
* After plugin update go to mystickymenu plugin settings and save changes with desired value for a new parameters. Clear cache if some cache system used on your site.
* Added: “Make visible when scroled” after number of pixels option.
* Fixed opacity 100 bug.


= 1.5 =
* Added option to enter exact width in px when sticky menu should be disabled "Disable at Small Screen Sizes".
* Added “.myfixed css class” setting field – edit .myfixed css style via plugin settings to create custom style.
* Fixed google adsense clash and undefined index notice.
* is_user_logged_in instead of old "Remove CSS Rules for Static Admin Bar while Sticky" option


= 1.4 =
* Added fade in or slide down effect settings field for sticky class.
* Added new wrapped div around selected sticky class with id mysticky_wrap.


= 1.3 =
* Added "block direct access" to the mystickymenu plugin file.
* Added Enable / Disable at small screen sizes and Remove not necessary css.
* Added "margin-top :0px" to .myfixed class in head which should extend theme support.


= 1.2 =
* Fixed mystickymenu.js for IE browsers, so myStickymenu is now compatible with IE 10, 11
  
= 1.1 =
* Added administration options, now available through Dashboard / Settings / myStickymenu. Options are as follows: Sticky Class, Sticky z-index, Sticky Width, Sticky Background Color, Sticky Opacity, Sticky Transition Time. 
* Old mystickymenu.css file is deprecated and not in use anymore.