@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;600;700&display=swap");
#notificationx-frontend-root audio {
  display: none;
}

.themes-theme-four {
  border-radius: 50px;
}
.themes-theme-four div.notificationx-inner {
  border-radius: 50px;
}
.themes-theme-four div.notificationx-inner div.notificationx-image {
  border-radius: 50%;
  background: #fff;
  margin: 3px;
  margin-right: 15px;
  padding: 0;
  box-shadow: 0 0 0 10px #ead9f8;
}
.themes-theme-four div.notificationx-inner div.notificationx-image.image-square, .themes-theme-four div.notificationx-inner div.notificationx-image.image-rounded {
  padding: 15px;
}
.themes-theme-four div.notificationx-inner div.notificationx-image.position-right {
  margin-right: 0px;
  margin-left: 15px;
  order: 2;
  border: 10px solid #fff;
}
@media only screen and (max-width: 576px) {
  .themes-theme-four div.notificationx-inner div.notificationx-image {
    margin: 4px;
    margin-right: 8px;
    box-shadow: 0 0 0 2px #ead9f8;
  }
}
.themes-theme-four div.notificationx-inner div.notificationx-close {
  align-self: center;
  margin-top: 0px;
}
.themes-theme-four.img-position-right div.notificationx-close {
  order: 0;
  padding: 10px 10px 10px 15px;
}
.themes-theme-four.img-position-right div.notificationx-content {
  order: 1;
}
.themes-theme-four.img-position-right div.notificationx-image {
  order: 2;
}

.themes-conv-theme-eight,
.themes-conv-theme-six {
  border-radius: 100px;
}
.themes-conv-theme-eight .notificationx-inner,
.themes-conv-theme-six .notificationx-inner {
  border-radius: 100px;
}
.themes-conv-theme-eight .notificationx-image,
.themes-conv-theme-six .notificationx-image {
  border-radius: 100% !important;
  padding: 5px !important;
  background: #fff;
  position: relative;
  overflow: initial !important;
  box-shadow: 5px 0 5px rgba(0, 0, 0, 0.1);
}
@media only screen and (max-width: 576px) {
  .themes-conv-theme-eight .notificationx-image,
  .themes-conv-theme-six .notificationx-image {
    padding: 4px !important;
    margin: 0 4px 0 0 !important;
  }
}
.themes-conv-theme-eight .notificationx-image:not(.image-circle),
.themes-conv-theme-six .notificationx-image:not(.image-circle) {
  padding: 19px !important;
}
.themes-conv-theme-eight .notificationx-image:after,
.themes-conv-theme-six .notificationx-image:after {
  position: absolute;
  top: 3px;
  left: 3px;
  font-family: "FontAwesome";
  content: "\f07a";
  background: #7052f0;
  height: 26px;
  width: 26px;
  border-radius: 50px;
  border: 3px solid #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 10px;
  box-shadow: 0 5px 10px rgba(112, 82, 240, 0.2);
}
@media only screen and (max-width: 576px) {
  .themes-conv-theme-eight .notificationx-image:after,
  .themes-conv-theme-six .notificationx-image:after {
    top: -3px;
    left: -3px;
    height: 14px;
    width: 14px;
    border: 1.5px solid #fff;
    font-size: 8px;
    box-shadow: 0 5px 10px rgba(112, 82, 240, 0.2);
  }
}
.themes-conv-theme-eight .notificationx-image.position-right,
.themes-conv-theme-six .notificationx-image.position-right {
  box-shadow: -5px 0 5px rgba(0, 0, 0, 0.1);
}
.themes-conv-theme-eight div.notificationx-close,
.themes-conv-theme-six div.notificationx-close {
  align-self: center !important;
  margin-top: 0px !important;
}
.themes-conv-theme-eight.img-position-right div.notificationx-close,
.themes-conv-theme-six.img-position-right div.notificationx-close {
  order: 0;
  padding: 10px 10px 10px 15px;
}
.themes-conv-theme-eight.img-position-right div.notificationx-content,
.themes-conv-theme-six.img-position-right div.notificationx-content {
  order: 1;
}
.themes-conv-theme-eight.img-position-right div.notificationx-image,
.themes-conv-theme-six.img-position-right div.notificationx-image {
  order: 2;
}

.themes-maps_theme div.notificationx-inner div.notificationx-image {
  padding: 0;
  transform: scale(1.1) !important;
  transform-origin: right center;
}
.themes-maps_theme div.notificationx-inner div.notificationx-image.position-right {
  transform-origin: left center;
}
@media only screen and (max-width: 576px) {
  .themes-maps_theme div.notificationx-inner div.notificationx-image {
    margin: 0;
    transform: scale(1.2) !important;
  }
}
@media only screen and (max-width: 576px) {
  .themes-maps_theme div.notificationx-inner div.notificationx-content {
    margin-left: 10px;
  }
}
@media only screen and (max-width: 576px) {
  .themes-maps_theme div.notificationx-inner div.notificationx-content > p.nx-second-row {
    display: none;
  }
}

.notification-item.nx-notification.source-announcements.themes-theme-7 .notificationx-image {
  padding: 10px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-1 {
  border-radius: 8px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-1 .notificationx-image svg {
  position: fixed;
  top: -10px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-1 .notificationx-content {
  padding: 20px 0;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-1 .notificationx-content .nx-first-row {
  font-size: 16px;
  font-weight: 500;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-1 .notificationx-content .nx-second-row {
  font-size: 14px;
  color: #7c8db5;
  line-height: 1.4;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-1 .notificationx-content .nx-third-row {
  font-size: 12px;
  color: #7c8db5;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-2, .notification-item.nx-notification.source-announcements.themes-announcements_theme-12 {
  border-radius: 8px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-2 .notificationx-image, .notification-item.nx-notification.source-announcements.themes-announcements_theme-12 .notificationx-image {
  padding: 10px 6px 10px 10px;
  flex-basis: 120px;
  width: 120px;
  min-width: 120px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-2 .notificationx-image svg, .notification-item.nx-notification.source-announcements.themes-announcements_theme-12 .notificationx-image svg {
  width: 120px;
  height: 90px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-2.button-link, .notification-item.nx-notification.source-announcements.themes-announcements_theme-12.button-link {
  margin-top: 20px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-2 .notificationx-content, .notification-item.nx-notification.source-announcements.themes-announcements_theme-12 .notificationx-content {
  padding: 20px 0;
  margin-left: 6px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-2 .notificationx-content .nx-first-row, .notification-item.nx-notification.source-announcements.themes-announcements_theme-12 .notificationx-content .nx-first-row {
  font-size: 16px;
  font-weight: 500;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-2 .notificationx-content .nx-second-row, .notification-item.nx-notification.source-announcements.themes-announcements_theme-12 .notificationx-content .nx-second-row {
  font-size: 14px;
  color: #7c8db5;
  line-height: 1.4;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-2 .notificationx-content .nx-third-row, .notification-item.nx-notification.source-announcements.themes-announcements_theme-12 .notificationx-content .nx-third-row {
  font-size: 12px;
  color: #7c8db5;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13 {
  border-radius: 50px;
  margin-bottom: 15px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13:not(.themes-announcements_theme-13:last-child) {
  margin-bottom: 40px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13 .notificationx-inner {
  position: relative;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13 .notificationx-inner .notificationx-content {
  padding: 12px;
  margin-left: 0;
  margin-right: 0;
  position: relative;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13 .notificationx-inner .notificationx-content > p > span {
  margin-right: 0px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13 .notificationx-inner .notificationx-content > p.nx-first-row {
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1em;
  font-weight: 600;
  color: #0E1726;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13 .notificationx-inner .notificationx-content > p.nx-second-row {
  position: absolute;
  right: -20px;
  bottom: -25px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13 .notificationx-inner > a {
  font-size: 12px;
  font-weight: 600;
  line-height: 1.2;
  color: #FFFFFF;
  padding: 6px 16px 6px 6px;
  background: #FF0000;
  border-radius: 50px;
  margin: 12px 0 12px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13.img-position-right .notificationx-close {
  order: -1;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-13.img-position-right .notificationx-inner > a {
  margin: 12px 6px 12px 6px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-14 {
  border-radius: 8px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-14 .notificationx-image.announcements {
  padding: 16px 6px 16px 12px;
  align-items: flex-start;
  flex-basis: 130px;
  width: 130px;
  min-width: 130px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-14 .notificationx-image.announcements img {
  max-width: 100%;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-14 .notificationx-content {
  padding: 16px 0 20px;
  margin-left: 6px;
  margin-right: 6px;
  position: relative;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-14 .notificationx-content > p.nx-first-row {
  font-size: 16px;
  line-height: 1em;
  font-weight: 600;
  color: #344054;
  margin-bottom: 8px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-14 .notificationx-content > p.nx-second-row {
  font-size: 14px;
  line-height: 1.4;
  color: #7E8795;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-14 .notificationx-content > p.nx-third-row {
  font-size: 12px;
  color: #98A2B3;
  justify-content: flex-end;
  margin-top: 15px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-14 .notificationx-content > a {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
  color: #AB48B3;
  padding: 6px 14px;
  background: #FEF1FF;
  border-radius: 4px;
  position: absolute;
  bottom: 14px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15 {
  border-radius: 8px;
  margin-bottom: 15px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15:not(.themes-announcements_theme-15:last-child) {
  margin-bottom: 40px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15 .notificationx-inner {
  position: relative;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15 .notificationx-inner .notificationx-content {
  padding: 10px 6px 10px 16px;
  margin-left: 0;
  margin-right: 0;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15 .notificationx-inner .notificationx-content > p > span {
  margin-right: 0px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15 .notificationx-inner .notificationx-content > p.nx-first-row {
  margin-bottom: 0;
  font-size: 16px;
  line-height: 1em;
  font-weight: 600;
  color: #344054;
  flex-basis: 30%;
  min-width: 75px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15 .notificationx-inner .notificationx-content > p.nx-second-row {
  font-size: 14px;
  line-height: 1.4;
  color: #7E8795;
  margin-bottom: 0;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15 .notificationx-inner .notificationx-content > p.nx-third-row {
  position: absolute;
  right: 10px;
  bottom: -20px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15 .notificationx-inner > a {
  font-size: 12px;
  font-weight: 600;
  line-height: 1.2;
  color: #FFFFFF;
  padding: 8px 16px;
  background: #4F19CD;
  border-radius: 4px;
  margin: 10px 6px;
}
.notification-item.nx-notification.source-announcements.themes-announcements_theme-15.img-position-right .notificationx-close {
  order: -1;
}/*# sourceMappingURL=index.css.map */