.nx-license-wrapper {
  background: #fff;
  border-radius: 10px;
  padding: 30px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
.nx-license-wrapper .nx-settings-right {
  margin-top: 0 !important;
}
.nx-license-wrapper .nx-lockscreen {
  display: inline-flex;
  flex-direction: column;
  margin-bottom: 5px;
  width: 100%;
  align-items: center;
}
.nx-license-wrapper .nx-lockscreen .nx-lockscreen-icons {
  display: flex;
  align-items: center;
}
.nx-license-wrapper .nx-lockscreen .nx-lockscreen-icons svg {
  height: 50px;
}
.nx-license-wrapper .nx-lockscreen .nx-lockscreen-icons svg:nth-child(even) {
  height: 20px;
  margin-top: 5px;
}
.nx-license-wrapper .nx-lockscreen .nx-lockscreen-icons svg:not(:last-child) {
  margin-right: 15px;
}
.nx-license-wrapper .nx-lockscreen .nx-validation-title {
  font-size: 24px;
  font-weight: 500;
  color: #25396f;
  margin-top: 5px;
  line-height: 1.3;
  letter-spacing: 0.9px;
  margin-bottom: 10px;
}
.nx-license-wrapper .nx-lockscreen-unlocked {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: -20px;
}
.nx-license-wrapper .nx-lockscreen-unlocked .nx-lockscreen-unlocked-item {
  margin-bottom: 20px;
  margin-right: 20px;
  display: flex;
  align-items: center;
}
.nx-license-wrapper .nx-lockscreen-unlocked .nx-lockscreen-unlocked-item .nx-lockscreen-unlocked-icon {
  display: flex;
}
.nx-license-wrapper .nx-lockscreen-unlocked .nx-lockscreen-unlocked-item .nx-lockscreen-unlocked-icon svg,
.nx-license-wrapper .nx-lockscreen-unlocked .nx-lockscreen-unlocked-item .nx-lockscreen-unlocked-icon img {
  height: 55px;
  width: auto;
  margin-right: 10px;
}
.nx-license-wrapper .nx-lockscreen-unlocked .nx-lockscreen-unlocked-item .nx-lockscreen-unlocked-content {
  display: flex;
  flex-direction: column;
}
.nx-license-wrapper .nx-lockscreen-unlocked .nx-lockscreen-unlocked-item .nx-lockscreen-unlocked-content h4 {
  font-size: 20px;
  font-weight: 500;
  color: #25396f;
  margin-top: 0px;
  line-height: 1.1;
  letter-spacing: 0.9px;
  margin-bottom: 0;
}
.nx-license-wrapper .nx-lockscreen-unlocked .nx-lockscreen-unlocked-item .nx-lockscreen-unlocked-content p {
  font-size: 13px;
  font-weight: 400;
  color: #25396f;
  margin-top: 0px;
  margin-bottom: 0px;
  letter-spacing: 0.4px;
}
.nx-license-wrapper .nx-lockscreen-unlocked .nx-lockscreen-unlocked-item .nx-lockscreen-unlocked-content p a {
  color: #6a4bff;
}
.nx-license-wrapper .nx-license-instruction p {
  font-size: 14px;
  font-weight: 400;
  color: #25396f;
  margin-top: 0;
  margin-bottom: 10px;
  letter-spacing: 1px;
}
.nx-license-wrapper .nx-license-instruction p a {
  color: #6a4bff;
}
.nx-license-wrapper .nx-license-instruction ul,
.nx-license-wrapper .nx-license-instruction ol {
  margin-left: 20px;
  margin-top: 20px;
}
.nx-license-wrapper .nx-license-instruction ul li,
.nx-license-wrapper .nx-license-instruction ol li {
  font-size: 14px;
  font-weight: 400;
  color: #25396f;
  margin-top: 0;
  margin-bottom: 0;
  letter-spacing: 1px;
}
.nx-license-wrapper .nx-license-instruction ul li a,
.nx-license-wrapper .nx-license-instruction ol li a {
  color: #6a4bff;
}
.nx-license-wrapper .nx-license-instruction ul li:not(:last-child),
.nx-license-wrapper .nx-license-instruction ol li:not(:last-child) {
  margin-bottom: 10px;
}
.nx-license-wrapper .nx-license-input-container {
  display: flex;
  margin-top: 30px;
}
.nx-license-wrapper .nx-license-input-container .nx-license-input {
  flex: 1;
  max-width: 450px;
  position: relative;
  margin-right: 20px;
}
.nx-license-wrapper .nx-license-input-container .nx-license-input svg {
  position: absolute;
  top: 50%;
  left: 22px;
  transform: translateY(-50%);
  width: 16px;
  pointer-events: none;
  padding-bottom: 4px;
}
.nx-license-wrapper .nx-license-input-container .nx-license-input input {
  min-height: 50px;
  width: 100%;
  border-radius: 10px;
  border: 1px solid rgba(12, 207, 194, 0.05);
  background: #f6f7fe;
  padding: 2px 20px 2px 55px;
  font-size: 16px;
  font-weight: 400;
  color: #25396f;
  outline: none;
}
.nx-license-wrapper .nx-license-input-container .nx-license-input input::-moz-placeholder {
  color: #7c8db5;
}
.nx-license-wrapper .nx-license-input-container .nx-license-input input::placeholder {
  color: #7c8db5;
}
.nx-license-wrapper .nx-license-input-container .nx-license-input input:focus {
  border-color: #25396f;
}
.nx-license-wrapper .nx-license-input-container .nx-license-input input:disabled {
  border-color: rgba(20, 216, 161, 0.25);
  color: rgba(20, 216, 161, 0.5);
  font-size: 17px;
  box-shadow: none;
}
.nx-license-wrapper .nx-license-input-container .nx-license-buttons button {
  margin-top: auto;
  color: #fff;
  border: 1.5px solid #6a4bff;
  border-radius: 10px;
  display: flex;
  min-height: 50px;
  padding: 2px 35px;
  font-size: 16px;
  font-weight: 500;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  outline: none;
  background: #6a4bff;
  line-height: 1.15;
  transition: 0.3s ease-in-out;
  cursor: pointer;
}
.nx-license-wrapper .nx-license-input-container .nx-license-buttons button:disabled {
  background: #7c8db5;
  border-color: #7c8db5;
  cursor: not-allowed;
}
.nx-license-wrapper .nx-license-input-container .nx-license-buttons button:not(:disabled):hover {
  color: #fff;
  background-color: rgb(84.**********, 19.**********, 208.**********);
  border-color: rgb(84.**********, 19.**********, 208.**********);
  outline: none;
  box-shadow: 0 15px 25px -5px rgba(86, 20, 213, 0.3);
}
.nx-license-wrapper .nx-license-input-container .nx-license-buttons.activated button {
  border: 1.5px solid rgba(186, 110, 120, 0.5);
  background: rgba(186, 110, 120, 0.5);
}
.nx-license-wrapper .nx-license-input-container .nx-license-buttons.activated button:disabled {
  background: #7c8db5;
  border-color: #7c8db5;
  cursor: not-allowed;
}
.nx-license-wrapper .nx-license-input-container .nx-license-buttons.activated button:not(:disabled):hover {
  background-color: #e62d45;
  border-color: #e62d45;
  box-shadow: none;
}
.nx-license-wrapper .nx-verification-msg {
  width: 65%;
}
.nx-license-wrapper .nx-verification-msg p {
  color: #3c434a;
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
  word-wrap: break-word;
}
.nx-license-wrapper .nx-verification-msg p a {
  color: #2673ff;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  word-wrap: break-word;
  text-decoration: none;
}
.nx-license-wrapper .nx-verification-msg p span {
  font-weight: bold;
}
.nx-license-wrapper .nx-verification-msg .short-description {
  margin: 10px 0;
}
.nx-license-wrapper .nx-verification-msg .nx-verification-input-container {
  background: #f6f9ff;
  border-radius: 8px;
  padding: 20px;
}
.nx-license-wrapper .nx-verification-msg .nx-verification-input-container > p {
  color: #3c434a;
  font-size: 13px;
  font-weight: 400;
  line-height: 19.2px;
  word-wrap: break-word;
  margin-bottom: 0;
}
.nx-license-wrapper .nx-verification-msg .nx-verification-input-container > p a {
  color: #492df5;
  font-size: 13px;
  font-weight: 400;
  line-height: 19.2px;
  word-wrap: break-word;
}
.nx-license-wrapper .nx-verification-msg .nx-verification-input-container .nx-verification-input {
  width: auto;
  position: relative;
  display: flex;
  background: #fff;
  padding: 8px;
  border-radius: 8px;
  border: 1px #b7c2d8 solid;
}
.nx-license-wrapper .nx-verification-msg .nx-verification-input-container .nx-verification-input input {
  width: 100%;
  height: 40px;
  border: 0;
  box-shadow: none;
  outline: 0;
  font-size: 13px;
  font-weight: 400;
  line-height: 19.2px;
  word-wrap: break-word;
}
.nx-license-wrapper .nx-verification-msg .nx-verification-input-container .nx-verification-input button[type=button] {
  position: absolute;
  right: 0;
  align-self: center;
  line-height: 1;
  border: none;
  border-radius: 0;
  color: #fff;
  font-size: 14px;
  width: 150px;
  height: 38px;
  border-radius: 5px;
  transition: all 0.3s;
  text-shadow: none;
  box-shadow: none;
  margin: 0 10px 0 0;
  cursor: pointer;
  background: #492df5;
}
.nx-license-wrapper .nx-verification-msg .nx-verification-input-container .nx-verification-input button[type=button].disabled {
  background-color: #c8c8c8;
  cursor: not-allowed;
}

.nx-license-container {
  display: flex;
}
.nx-license-container .nx-license-wrapper {
  flex: 6;
}
.nx-license-container .nx-settings-right {
  margin-top: 0 !important;
}
@media only screen and (max-width: 1279px) {
  .nx-license-container {
    flex-direction: column-reverse;
  }
  .nx-license-container .nx-license-wrapper {
    flex: 1;
  }
  .nx-license-container .nx-settings-right {
    flex: 1;
    margin-bottom: 10px !important;
  }
}/*# sourceMappingURL=index.css.map */