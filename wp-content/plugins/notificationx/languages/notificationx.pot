# Copyright (C) 2025 WPDeveloper
# This file is distributed under the GPL-3.0+.
msgid ""
msgstr ""
"Project-Id-Version: NotificationX 3.1.3\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/notificationx\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-08-26T09:43:30+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: notificationx\n"

#. Plugin Name of the plugin
#: notificationx.php
#: includes/Admin/Settings.php:110
#: includes/Core/QuickBuild.php:115
#: includes/Extensions/GlobalFields.php:51
#: assets/admin/js/admin.js:11
#: assets/admin/js/admin.js:14
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
#: nxdev/notificationx/frontend/themes/helpers/NotificationText.js:13
msgid "NotificationX"
msgstr ""

#. Plugin URI of the plugin
#: notificationx.php
msgid "https://notificationx.com"
msgstr ""

#. Description of the plugin
#: notificationx.php
msgid "Social Proof & Recent Sales Popup, Comment Notification, Subscription Notification, Notification Bar and many more."
msgstr ""

#. Author of the plugin
#: notificationx.php
msgid "WPDeveloper"
msgstr ""

#. Author URI of the plugin
#: notificationx.php
msgid "https://wpdeveloper.com"
msgstr ""

#: blocks/Blocks.php:173
msgid "There is no data in this notification."
msgstr ""

#: includes/Admin/Admin.php:115
#: assets/admin/js/admin.js:11
msgid "All NotificationX"
msgstr ""

#: includes/Admin/Admin.php:217
msgid "We hope you're enjoying NotificationX! Could you please do us a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?"
msgstr ""

#: includes/Admin/Admin.php:222
msgid "Ok, you deserve it!"
msgstr ""

#: includes/Admin/Admin.php:226
msgid "I already did"
msgstr ""

#: includes/Admin/Admin.php:233
msgid "Maybe Later"
msgstr ""

#: includes/Admin/Admin.php:241
msgid "I need help"
msgstr ""

#: includes/Admin/Admin.php:245
msgid "Never show again"
msgstr ""

#: includes/Admin/Admin.php:278
msgid "<p><strong>Black Friday Exclusive:</strong> SAVE up to 40% & access to <strong>NotificationX Pro</strong> features.</p>"
msgstr ""

#: includes/Admin/Admin.php:278
msgid "Grab The Offer"
msgstr ""

#: includes/Admin/Admin.php:380
msgid "Want to help make <strong>NotificationX</strong> even more awesome? You can get a <strong>10% discount coupon</strong> for Premium extensions if you allow us to track the usage."
msgstr ""

#: includes/Admin/Admin.php:381
msgid ""
"We collect non-sensitive diagnostic data and plugin usage information.\n"
"\t\t\tYour site URL, WordPress & PHP version, plugins & themes and email address to send you the\n"
"\t\t\tdiscount coupon. This data lets us make sure this plugin always stays compatible with the most\n"
"\t\t\tpopular plugins and themes. No spam, I promise."
msgstr ""

#. translators: %s: no of minutes
#: includes/Admin/Cron.php:93
#: includes/Admin/Cron.php:99
msgid "Every %s minutes"
msgstr ""

#: includes/Admin/DashboardWidget.php:49
msgid "NotificationX Analytics"
msgstr ""

#: includes/Admin/ImportExport.php:50
msgid "Import/Export"
msgstr ""

#: includes/Admin/ImportExport.php:56
msgid "Export Notifications"
msgstr ""

#: includes/Admin/ImportExport.php:63
#: includes/Admin/Settings.php:293
#: includes/Core/Analytics.php:46
#: includes/Extensions/GlobalFields.php:1226
#: includes/FrontEnd/FrontEnd.php:96
#: assets/admin/js/admin.js:1
msgid "Analytics"
msgstr ""

#: includes/Admin/ImportExport.php:72
#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Status"
msgstr ""

#: includes/Admin/ImportExport.php:85
msgid "Export Settings"
msgstr ""

#: includes/Admin/ImportExport.php:93
#: includes/Admin/ImportExport.php:94
#: assets/admin/js/admin.js:1
msgid "Export"
msgstr ""

#: includes/Admin/ImportExport.php:95
msgid "Exporting..."
msgstr ""

#: includes/Admin/ImportExport.php:114
msgid "Export completed successfully."
msgstr ""

#: includes/Admin/ImportExport.php:124
msgid "Import (*.json)"
msgstr ""

#: includes/Admin/ImportExport.php:125
msgid "Change"
msgstr ""

#: includes/Admin/ImportExport.php:133
#: includes/Admin/ImportExport.php:134
#: includes/Extensions/PressBar/PressBar.php:785
#: includes/Extensions/PressBar/PressBar.php:786
#: includes/Extensions/PressBar/PressBar.php:992
#: includes/Extensions/PressBar/PressBar.php:993
msgid "Import"
msgstr ""

#: includes/Admin/ImportExport.php:135
#: includes/Extensions/PressBar/PressBar.php:787
#: includes/Extensions/PressBar/PressBar.php:994
msgid "Importing..."
msgstr ""

#: includes/Admin/ImportExport.php:147
msgid "Import completed successfully."
msgstr ""

#: includes/Admin/Notice.php:469
msgid "Install Now!"
msgstr ""

#: includes/Admin/Notice.php:905
msgid "Installing..."
msgstr ""

#: includes/Admin/Notice.php:917
msgid "Installed"
msgstr ""

#: includes/Admin/Rating/RatingEmail.php:54
msgid "[IMPORTANT] New feedback received from a NotificationX user"
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:58
msgid "Successfully Sent an Email"
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:62
msgid "Email cannot be sent for some reason."
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:65
msgid "Invalid email address."
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:67
msgid "You have to enable Reporting first."
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:220
#: includes/Admin/Settings.php:360
msgid "Once Weekly"
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:224
#: includes/Admin/Settings.php:359
msgid "Once Daily"
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:228
#: includes/Admin/Settings.php:361
msgid "Once Monthly"
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:311
msgid "No data found."
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:314
msgid "Analytics disabled. No data found."
msgstr ""

#: includes/Admin/Reports/ReportEmail.php:318
msgid "No email found."
msgstr ""

#: includes/Admin/Scanner/Scanner.php:112
msgid "Invalid scan ID"
msgstr ""

#: includes/Admin/Settings.php:127
msgid "Save Settings"
msgstr ""

#: includes/Admin/Settings.php:133
#: includes/Admin/Settings.php:168
msgid "General"
msgstr ""

#: includes/Admin/Settings.php:138
msgid "Modules"
msgstr ""

#: includes/Admin/Settings.php:161
msgid "Advanced Settings"
msgstr ""

#: includes/Admin/Settings.php:174
msgid "Enable REST API"
msgstr ""

#: includes/Admin/Settings.php:177
msgid "Forcefully enable anonymous REST API for NotificationX."
msgstr ""

#: includes/Admin/Settings.php:182
msgid "Exclude Credentials"
msgstr ""

#: includes/Admin/Settings.php:185
msgid "Enabling it will remove cookies, HTTP authentication entries, and TLS client certificates from API calls on the frontend."
msgstr ""

#: includes/Admin/Settings.php:186
msgid "Note: Recommended if you use any caching plugins."
msgstr ""

#: includes/Admin/Settings.php:191
msgid "Image Size"
msgstr ""

#: includes/Admin/Settings.php:194
msgid "100 x 100 px"
msgstr ""

#: includes/Admin/Settings.php:195
msgid "200 x 200 px"
msgstr ""

#: includes/Admin/Settings.php:196
msgid "300 x 300 px"
msgstr ""

#: includes/Admin/Settings.php:197
msgid "400 x 400 px"
msgstr ""

#: includes/Admin/Settings.php:198
msgid "500 x 500 px"
msgstr ""

#: includes/Admin/Settings.php:201
msgid "Select the size for your notification image."
msgstr ""

#: includes/Admin/Settings.php:206
msgid "Custom Notification Import Limit"
msgstr ""

#: includes/Admin/Settings.php:214
msgid "Powered By"
msgstr ""

#: includes/Admin/Settings.php:220
msgid "Disable Powered By"
msgstr ""

#: includes/Admin/Settings.php:224
msgid "Click, if you want to disable powered by text from notification"
msgstr ""

#: includes/Admin/Settings.php:231
msgid "Role Management"
msgstr ""

#: includes/Admin/Settings.php:237
msgid "Who Can View Notification?"
msgstr ""

#: includes/Admin/Settings.php:248
msgid "Who Can Create Notification?"
msgstr ""

#: includes/Admin/Settings.php:259
msgid "Who Can Edit Settings?"
msgstr ""

#: includes/Admin/Settings.php:270
msgid "Who Can Check Analytics?"
msgstr ""

#: includes/Admin/Settings.php:284
msgid "Analytics & Reporting"
msgstr ""

#: includes/Admin/Settings.php:298
msgid "Enable Analytics"
msgstr ""

#: includes/Admin/Settings.php:305
msgid "Disable Dashboard Widget"
msgstr ""

#: includes/Admin/Settings.php:308
msgid "Click, if you want to disable dashboard widget of analytics only."
msgstr ""

#: includes/Admin/Settings.php:314
msgid "Analytics From"
msgstr ""

#: includes/Admin/Settings.php:316
#: includes/Extensions/GlobalFields.php:1685
#: includes/Types/GDPR.php:263
msgid "Everyone"
msgstr ""

#: includes/Admin/Settings.php:317
msgid "Guests Only"
msgstr ""

#: includes/Admin/Settings.php:318
msgid "Registered Users Only"
msgstr ""

#: includes/Admin/Settings.php:328
msgid "Exclude Bot Analytics"
msgstr ""

#: includes/Admin/Settings.php:331
msgid "Select if you want to exclude bot analytics."
msgstr ""

#: includes/Admin/Settings.php:340
msgid "Reporting"
msgstr ""

#: includes/Admin/Settings.php:345
msgid "Disable Reporting"
msgstr ""

#: includes/Admin/Settings.php:353
msgid "Reporting Frequency"
msgstr ""

#: includes/Admin/Settings.php:371
msgid "It will be triggered on the first day of next month."
msgstr ""

#: includes/Admin/Settings.php:377
msgid "Select Reporting Day"
msgstr ""

#: includes/Admin/Settings.php:381
#: includes/Extensions/PressBar/PressBar.php:1383
msgid "Sunday"
msgstr ""

#: includes/Admin/Settings.php:382
#: includes/Extensions/PressBar/PressBar.php:1377
msgid "Monday"
msgstr ""

#: includes/Admin/Settings.php:383
#: includes/Extensions/PressBar/PressBar.php:1378
msgid "Tuesday"
msgstr ""

#: includes/Admin/Settings.php:384
#: includes/Extensions/PressBar/PressBar.php:1379
msgid "Wednesday"
msgstr ""

#: includes/Admin/Settings.php:385
#: includes/Extensions/PressBar/PressBar.php:1380
msgid "Thursday"
msgstr ""

#: includes/Admin/Settings.php:386
#: includes/Extensions/PressBar/PressBar.php:1381
msgid "Friday"
msgstr ""

#: includes/Admin/Settings.php:389
msgid "Select a day for email report."
msgstr ""

#: includes/Admin/Settings.php:399
msgid "Reporting Email"
msgstr ""

#: includes/Admin/Settings.php:407
msgid "Reporting Email Subject"
msgstr ""

#: includes/Admin/Settings.php:415
msgid "Reporting Test"
msgstr ""

#: includes/Admin/Settings.php:416
msgid "Test Report"
msgstr ""

#: includes/Admin/Settings.php:431
msgid "Successfully Sent a Test Report in Your Email."
msgstr ""

#: includes/Admin/Settings.php:444
#: includes/Admin/Settings.php:450
msgid "Cache Settings"
msgstr ""

#: includes/Admin/Settings.php:456
msgid "Cache Limit"
msgstr ""

#: includes/Admin/Settings.php:457
msgid "Number of Notification Data to be saved in Database."
msgstr ""

#: includes/Admin/Settings.php:464
msgid "Download Stats Cache Duration"
msgstr ""

#: includes/Admin/Settings.php:465
#: includes/Admin/Settings.php:473
msgid "Minutes (Schedule Duration to fetch new data)."
msgstr ""

#: includes/Admin/Settings.php:472
msgid "Reviews Cache Duration"
msgstr ""

#: includes/Admin/Settings.php:484
msgid "Miscellaneous"
msgstr ""

#: includes/Admin/Settings.php:498
msgid "Danger Zone"
msgstr ""

#: includes/Admin/Settings.php:503
#: includes/Admin/Settings.php:504
msgid "Delete Settings"
msgstr ""

#: includes/Admin/Settings.php:515
msgid "Successfully deleted Settings."
msgstr ""

#: includes/Admin/Settings.php:523
#: includes/Admin/Settings.php:524
msgid "Delete Transient"
msgstr ""

#: includes/Admin/Settings.php:535
msgid "Successfully deleted Transient."
msgstr ""

#: includes/Admin/views/analytics.views.php:7
#: includes/Admin/views/analytics.views.php:11
#: includes/Extensions/Google/YouTube.php:220
#: includes/Extensions/Google/YouTube.php:234
#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Total Views"
msgstr ""

#: includes/Admin/views/analytics.views.php:20
#: includes/Admin/views/analytics.views.php:24
#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Total Clicks"
msgstr ""

#: includes/Admin/views/analytics.views.php:33
#: includes/Admin/views/analytics.views.php:37
#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Click-Through-Rate"
msgstr ""

#. translators: html tags
#: includes/Admin/views/main.views.php:18
msgid "To work <strong><em>NotificationX</em></strong> properly you need to <strong>Enable JavaScript</strong> in your browser or make sure you have installed updated browser in your device."
msgstr ""

#: includes/Admin/XSS.php:37
#: includes/Admin/XSS.php:43
#: assets/admin/js/admin.js:1
msgid "Cross Domain Notice"
msgstr ""

#: includes/Admin/XSS.php:44
#: includes/Extensions/GlobalFields.php:615
#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:18
msgid "Click to Copy"
msgstr ""

#: includes/Admin/XSS.php:45
#: includes/Extensions/GlobalFields.php:616
msgid "Copied to clipboard."
msgstr ""

#: includes/Admin/XSS.php:49
msgid "Show your Notification Alerts in another website using <a target=\"_blank\" href=\"%s\">Cross Domain Notice</a>."
msgstr ""

#: includes/Core/Dashboard.php:36
msgid "Dashboard"
msgstr ""

#: includes/Core/Helper.php:639
#: includes/Types/GDPR.php:384
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Enabled"
msgstr ""

#: includes/Core/Helper.php:645
#: assets/admin/js/admin.js:18
msgid "Discovered"
msgstr ""

#: includes/Core/Helper.php:651
msgid "Cookie ID"
msgstr ""

#: includes/Core/Helper.php:657
msgid "Domain"
msgstr ""

#: includes/Core/Helper.php:663
msgid "Duration"
msgstr ""

#: includes/Core/Helper.php:694
msgid "Add Script"
msgstr ""

#: includes/Core/Helper.php:698
msgid "Add Script on"
msgstr ""

#: includes/Core/Helper.php:704
msgid "Header"
msgstr ""

#: includes/Core/Helper.php:705
msgid "Body"
msgstr ""

#: includes/Core/Helper.php:706
msgid "Footer"
msgstr ""

#: includes/Core/Helper.php:789
msgid "Name"
msgstr ""

#: includes/Core/Helper.php:801
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Description"
msgstr ""

#: includes/Core/Helper.php:815
msgid "Indicates when a user is logged in and who they are, for most interface use."
msgstr ""

#: includes/Core/Helper.php:824
msgid "Used for security purposes for logged-in users."
msgstr ""

#: includes/Core/Helper.php:833
msgid "Used to persist a user's WordPress admin settings."
msgstr ""

#: includes/Core/Helper.php:842
#: includes/Core/Helper.php:851
msgid "Records the time that wp-settings-{user_id} was set."
msgstr ""

#: includes/Core/Helper.php:859
msgid "Manages the cookies on the site, ensuring user consent for GDPR compliance."
msgstr ""

#: includes/Core/Helper.php:1126
msgid "All Countries"
msgstr ""

#: includes/Core/Helper.php:1127
msgid "Afghanistan"
msgstr ""

#: includes/Core/Helper.php:1128
msgid "Albania"
msgstr ""

#: includes/Core/Helper.php:1129
msgid "Algeria"
msgstr ""

#: includes/Core/Helper.php:1130
msgid "American Samoa"
msgstr ""

#: includes/Core/Helper.php:1131
msgid "Andorra"
msgstr ""

#: includes/Core/Helper.php:1132
msgid "Angola"
msgstr ""

#: includes/Core/Helper.php:1133
msgid "Anguilla"
msgstr ""

#: includes/Core/Helper.php:1134
msgid "Antarctica"
msgstr ""

#: includes/Core/Helper.php:1135
msgid "Antigua and Barbuda"
msgstr ""

#: includes/Core/Helper.php:1136
msgid "Argentina"
msgstr ""

#: includes/Core/Helper.php:1137
msgid "Armenia"
msgstr ""

#: includes/Core/Helper.php:1138
msgid "Aruba"
msgstr ""

#: includes/Core/Helper.php:1139
msgid "Australia"
msgstr ""

#: includes/Core/Helper.php:1140
msgid "Austria"
msgstr ""

#: includes/Core/Helper.php:1141
msgid "Azerbaijan"
msgstr ""

#: includes/Core/Helper.php:1142
msgid "Bahamas"
msgstr ""

#: includes/Core/Helper.php:1143
msgid "Bahrain"
msgstr ""

#: includes/Core/Helper.php:1144
msgid "Bangladesh"
msgstr ""

#: includes/Core/Helper.php:1145
msgid "Barbados"
msgstr ""

#: includes/Core/Helper.php:1146
msgid "Belarus"
msgstr ""

#: includes/Core/Helper.php:1147
msgid "Belgium"
msgstr ""

#: includes/Core/Helper.php:1148
msgid "Belize"
msgstr ""

#: includes/Core/Helper.php:1149
msgid "Benin"
msgstr ""

#: includes/Core/Helper.php:1150
msgid "Bermuda"
msgstr ""

#: includes/Core/Helper.php:1151
msgid "Bhutan"
msgstr ""

#: includes/Core/Helper.php:1152
msgid "Bolivia"
msgstr ""

#: includes/Core/Helper.php:1153
msgid "Bosnia and Herzegovina"
msgstr ""

#: includes/Core/Helper.php:1154
msgid "Botswana"
msgstr ""

#: includes/Core/Helper.php:1155
msgid "Brazil"
msgstr ""

#: includes/Core/Helper.php:1156
msgid "Brunei"
msgstr ""

#: includes/Core/Helper.php:1157
msgid "Bulgaria"
msgstr ""

#: includes/Core/Helper.php:1158
msgid "Burkina Faso"
msgstr ""

#: includes/Core/Helper.php:1159
msgid "Burundi"
msgstr ""

#: includes/Core/Helper.php:1160
msgid "Cambodia"
msgstr ""

#: includes/Core/Helper.php:1161
msgid "Cameroon"
msgstr ""

#: includes/Core/Helper.php:1162
msgid "Canada"
msgstr ""

#: includes/Core/Helper.php:1163
msgid "Cape Verde"
msgstr ""

#: includes/Core/Helper.php:1164
msgid "Central African Republic"
msgstr ""

#: includes/Core/Helper.php:1165
msgid "Chad"
msgstr ""

#: includes/Core/Helper.php:1166
msgid "Chile"
msgstr ""

#: includes/Core/Helper.php:1167
msgid "China"
msgstr ""

#: includes/Core/Helper.php:1168
msgid "Colombia"
msgstr ""

#: includes/Core/Helper.php:1169
msgid "Comoros"
msgstr ""

#: includes/Core/Helper.php:1170
msgid "Congo (Brazzaville)"
msgstr ""

#: includes/Core/Helper.php:1171
msgid "Congo (Kinshasa)"
msgstr ""

#: includes/Core/Helper.php:1172
msgid "Costa Rica"
msgstr ""

#: includes/Core/Helper.php:1173
msgid "Croatia"
msgstr ""

#: includes/Core/Helper.php:1174
msgid "Cuba"
msgstr ""

#: includes/Core/Helper.php:1175
msgid "Cyprus"
msgstr ""

#: includes/Core/Helper.php:1176
msgid "Czech Republic"
msgstr ""

#: includes/Core/Helper.php:1177
msgid "Denmark"
msgstr ""

#: includes/Core/Helper.php:1178
msgid "Djibouti"
msgstr ""

#: includes/Core/Helper.php:1179
msgid "Dominica"
msgstr ""

#: includes/Core/Helper.php:1180
msgid "Dominican Republic"
msgstr ""

#: includes/Core/Helper.php:1181
msgid "Ecuador"
msgstr ""

#: includes/Core/Helper.php:1182
msgid "Egypt"
msgstr ""

#: includes/Core/Helper.php:1183
msgid "El Salvador"
msgstr ""

#: includes/Core/Helper.php:1184
msgid "Equatorial Guinea"
msgstr ""

#: includes/Core/Helper.php:1185
msgid "Eritrea"
msgstr ""

#: includes/Core/Helper.php:1186
msgid "Estonia"
msgstr ""

#: includes/Core/Helper.php:1187
msgid "Ethiopia"
msgstr ""

#: includes/Core/Helper.php:1188
msgid "Fiji"
msgstr ""

#: includes/Core/Helper.php:1189
msgid "Finland"
msgstr ""

#: includes/Core/Helper.php:1190
msgid "France"
msgstr ""

#: includes/Core/Helper.php:1191
msgid "Gabon"
msgstr ""

#: includes/Core/Helper.php:1192
msgid "Gambia"
msgstr ""

#: includes/Core/Helper.php:1193
msgid "Georgia"
msgstr ""

#: includes/Core/Helper.php:1194
msgid "Germany"
msgstr ""

#: includes/Core/Helper.php:1195
msgid "Ghana"
msgstr ""

#: includes/Core/Helper.php:1196
msgid "Greece"
msgstr ""

#: includes/Core/Helper.php:1197
msgid "Grenada"
msgstr ""

#: includes/Core/Helper.php:1198
msgid "Guatemala"
msgstr ""

#: includes/Core/Helper.php:1199
msgid "Guinea"
msgstr ""

#: includes/Core/Helper.php:1200
msgid "Guinea-Bissau"
msgstr ""

#: includes/Core/Helper.php:1201
msgid "Guyana"
msgstr ""

#: includes/Core/Helper.php:1202
msgid "Haiti"
msgstr ""

#: includes/Core/Helper.php:1203
msgid "Honduras"
msgstr ""

#: includes/Core/Helper.php:1204
msgid "Hong Kong"
msgstr ""

#: includes/Core/Helper.php:1205
msgid "Hungary"
msgstr ""

#: includes/Core/Helper.php:1206
msgid "Iceland"
msgstr ""

#: includes/Core/Helper.php:1207
msgid "India"
msgstr ""

#: includes/Core/Helper.php:1208
msgid "Indonesia"
msgstr ""

#: includes/Core/Helper.php:1209
msgid "Iran"
msgstr ""

#: includes/Core/Helper.php:1210
msgid "Iraq"
msgstr ""

#: includes/Core/Helper.php:1211
msgid "Ireland"
msgstr ""

#: includes/Core/Helper.php:1212
msgid "Israel"
msgstr ""

#: includes/Core/Helper.php:1213
msgid "Italy"
msgstr ""

#: includes/Core/Helper.php:1214
msgid "Jamaica"
msgstr ""

#: includes/Core/Helper.php:1215
msgid "Japan"
msgstr ""

#: includes/Core/Helper.php:1216
msgid "Jordan"
msgstr ""

#: includes/Core/Helper.php:1217
msgid "Kazakhstan"
msgstr ""

#: includes/Core/Helper.php:1218
msgid "Kenya"
msgstr ""

#: includes/Core/Helper.php:1219
msgid "Kiribati"
msgstr ""

#: includes/Core/Helper.php:1220
msgid "Korea, South"
msgstr ""

#: includes/Core/Helper.php:1221
msgid "Kuwait"
msgstr ""

#: includes/Core/Helper.php:1222
msgid "Kyrgyzstan"
msgstr ""

#: includes/Core/Helper.php:1223
msgid "Laos"
msgstr ""

#: includes/Core/Helper.php:1224
msgid "Latvia"
msgstr ""

#: includes/Core/Helper.php:1225
msgid "Lebanon"
msgstr ""

#: includes/Core/Helper.php:1226
msgid "Lesotho"
msgstr ""

#: includes/Core/Helper.php:1227
msgid "Liberia"
msgstr ""

#: includes/Core/Helper.php:1228
msgid "Libya"
msgstr ""

#: includes/Core/Helper.php:1229
msgid "Liechtenstein"
msgstr ""

#: includes/Core/Helper.php:1230
msgid "Lithuania"
msgstr ""

#: includes/Core/Helper.php:1231
msgid "Luxembourg"
msgstr ""

#: includes/Core/Helper.php:1232
msgid "Madagascar"
msgstr ""

#: includes/Core/Helper.php:1233
msgid "Malawi"
msgstr ""

#: includes/Core/Helper.php:1234
msgid "Malaysia"
msgstr ""

#: includes/Core/Helper.php:1235
msgid "Maldives"
msgstr ""

#: includes/Core/Helper.php:1236
msgid "Mali"
msgstr ""

#: includes/Core/Helper.php:1237
msgid "Malta"
msgstr ""

#: includes/Core/Helper.php:1238
msgid "Marshall Islands"
msgstr ""

#: includes/Core/Helper.php:1239
msgid "Mauritania"
msgstr ""

#: includes/Core/Helper.php:1240
msgid "Mauritius"
msgstr ""

#: includes/Core/Helper.php:1241
msgid "Mexico"
msgstr ""

#: includes/Core/Helper.php:1242
msgid "Micronesia"
msgstr ""

#: includes/Core/Helper.php:1243
msgid "Moldova"
msgstr ""

#: includes/Core/Helper.php:1244
msgid "Monaco"
msgstr ""

#: includes/Core/Helper.php:1245
msgid "Mongolia"
msgstr ""

#: includes/Core/Helper.php:1246
msgid "Montenegro"
msgstr ""

#: includes/Core/Helper.php:1247
msgid "Morocco"
msgstr ""

#: includes/Core/Helper.php:1248
msgid "Mozambique"
msgstr ""

#: includes/Core/Helper.php:1249
msgid "Myanmar (Burma)"
msgstr ""

#: includes/Core/Helper.php:1250
msgid "Namibia"
msgstr ""

#: includes/Core/Helper.php:1251
msgid "Nauru"
msgstr ""

#: includes/Core/Helper.php:1252
msgid "Nepal"
msgstr ""

#: includes/Core/Helper.php:1253
msgid "Netherlands"
msgstr ""

#: includes/Core/Helper.php:1254
msgid "New Zealand"
msgstr ""

#: includes/Core/Helper.php:1255
msgid "Nicaragua"
msgstr ""

#: includes/Core/Helper.php:1256
msgid "Niger"
msgstr ""

#: includes/Core/Helper.php:1257
msgid "Nigeria"
msgstr ""

#: includes/Core/Helper.php:1258
msgid "North Macedonia"
msgstr ""

#: includes/Core/Helper.php:1259
msgid "Norway"
msgstr ""

#: includes/Core/Helper.php:1260
msgid "Oman"
msgstr ""

#: includes/Core/Helper.php:1261
msgid "Pakistan"
msgstr ""

#: includes/Core/Helper.php:1262
msgid "Palau"
msgstr ""

#: includes/Core/Helper.php:1263
msgid "Panama"
msgstr ""

#: includes/Core/Helper.php:1264
msgid "Papua New Guinea"
msgstr ""

#: includes/Core/Helper.php:1265
msgid "Paraguay"
msgstr ""

#: includes/Core/Helper.php:1266
msgid "Peru"
msgstr ""

#: includes/Core/Helper.php:1267
msgid "Philippines"
msgstr ""

#: includes/Core/Helper.php:1268
msgid "Poland"
msgstr ""

#: includes/Core/Helper.php:1269
msgid "Portugal"
msgstr ""

#: includes/Core/Helper.php:1270
msgid "Qatar"
msgstr ""

#: includes/Core/Helper.php:1271
msgid "Romania"
msgstr ""

#: includes/Core/Helper.php:1272
msgid "Russia"
msgstr ""

#: includes/Core/Helper.php:1273
msgid "Rwanda"
msgstr ""

#: includes/Core/Helper.php:1274
msgid "Saudi Arabia"
msgstr ""

#: includes/Core/Helper.php:1275
msgid "Senegal"
msgstr ""

#: includes/Core/Helper.php:1276
msgid "Serbia"
msgstr ""

#: includes/Core/Helper.php:1277
msgid "Seychelles"
msgstr ""

#: includes/Core/Helper.php:1278
msgid "Sierra Leone"
msgstr ""

#: includes/Core/Helper.php:1279
msgid "Singapore"
msgstr ""

#: includes/Core/Helper.php:1280
msgid "Slovakia"
msgstr ""

#: includes/Core/Helper.php:1281
msgid "Slovenia"
msgstr ""

#: includes/Core/Helper.php:1282
msgid "Solomon Islands"
msgstr ""

#: includes/Core/Helper.php:1283
msgid "Somalia"
msgstr ""

#: includes/Core/Helper.php:1284
msgid "South Africa"
msgstr ""

#: includes/Core/Helper.php:1285
msgid "Spain"
msgstr ""

#: includes/Core/Helper.php:1286
msgid "Sri Lanka"
msgstr ""

#: includes/Core/Helper.php:1287
msgid "Sudan"
msgstr ""

#: includes/Core/Helper.php:1288
msgid "Suriname"
msgstr ""

#: includes/Core/Helper.php:1289
msgid "Sweden"
msgstr ""

#: includes/Core/Helper.php:1290
msgid "Switzerland"
msgstr ""

#: includes/Core/Helper.php:1291
msgid "Syria"
msgstr ""

#: includes/Core/Helper.php:1292
msgid "Taiwan"
msgstr ""

#: includes/Core/Helper.php:1293
msgid "Tajikistan"
msgstr ""

#: includes/Core/Helper.php:1294
msgid "Tanzania"
msgstr ""

#: includes/Core/Helper.php:1295
msgid "Thailand"
msgstr ""

#: includes/Core/Helper.php:1296
msgid "Togo"
msgstr ""

#: includes/Core/Helper.php:1297
msgid "Tonga"
msgstr ""

#: includes/Core/Helper.php:1298
msgid "Trinidad and Tobago"
msgstr ""

#: includes/Core/Helper.php:1299
msgid "Tunisia"
msgstr ""

#: includes/Core/Helper.php:1300
msgid "Turkey"
msgstr ""

#: includes/Core/Helper.php:1301
msgid "Turkmenistan"
msgstr ""

#: includes/Core/Helper.php:1302
msgid "Uganda"
msgstr ""

#: includes/Core/Helper.php:1303
msgid "Ukraine"
msgstr ""

#: includes/Core/Helper.php:1304
msgid "United Arab Emirates"
msgstr ""

#: includes/Core/Helper.php:1305
msgid "United Kingdom"
msgstr ""

#: includes/Core/Helper.php:1306
msgid "United States"
msgstr ""

#: includes/Core/Helper.php:1307
msgid "Uruguay"
msgstr ""

#: includes/Core/Helper.php:1308
msgid "Uzbekistan"
msgstr ""

#: includes/Core/Helper.php:1309
msgid "Vanuatu"
msgstr ""

#: includes/Core/Helper.php:1310
msgid "Venezuela"
msgstr ""

#: includes/Core/Helper.php:1311
msgid "Vietnam"
msgstr ""

#: includes/Core/Helper.php:1312
msgid "Yemen"
msgstr ""

#: includes/Core/Helper.php:1313
msgid "Zambia"
msgstr ""

#: includes/Core/Helper.php:1314
msgid "Zimbabwe"
msgstr ""

#: includes/Core/Locations.php:24
msgid "Front page"
msgstr ""

#: includes/Core/Locations.php:25
msgid "Blog page"
msgstr ""

#: includes/Core/Locations.php:26
msgid "All posts, pages and custom post types"
msgstr ""

#: includes/Core/Locations.php:27
msgid "All posts"
msgstr ""

#: includes/Core/Locations.php:28
msgid "All pages"
msgstr ""

#: includes/Core/Locations.php:29
msgid "All attachments"
msgstr ""

#: includes/Core/Locations.php:30
msgid "Search results"
msgstr ""

#: includes/Core/Locations.php:31
msgid "404 error page"
msgstr ""

#: includes/Core/Locations.php:32
msgid "All archives"
msgstr ""

#: includes/Core/Locations.php:33
msgid "All category archives"
msgstr ""

#: includes/Core/Locations.php:34
msgid "All tag archives"
msgstr ""

#. translators: %s: Post Type label
#: includes/Core/Locations.php:53
msgid "All %s posts"
msgstr ""

#. translators: %s: Post Type label
#: includes/Core/Locations.php:57
msgid "All %s archives"
msgstr ""

#. translators: %s: Taxonomy label
#: includes/Core/Locations.php:63
msgid "All %s taxonomy archives"
msgstr ""

#: includes/Core/Migration.php:392
#: includes/Extensions/EDD/EDInline.php:45
#: includes/Extensions/Give/Give.php:82
#: includes/Extensions/Give/Give.php:83
#: includes/Extensions/Give/Give.php:84
#: includes/Extensions/GlobalFields.php:683
#: includes/Extensions/Google/GoogleReviews.php:55
#: includes/Extensions/Google/GoogleReviews.php:69
#: includes/Extensions/Google/GoogleReviews.php:83
#: includes/Extensions/Google/GoogleReviews.php:97
#: includes/Extensions/Google/GoogleReviews.php:111
#: includes/Extensions/Google/GoogleReviews.php:126
#: includes/Extensions/LearnPress/LearnPress.php:536
#: includes/Extensions/LearnPress/LearnPress.php:537
#: includes/Extensions/LearnPress/LearnPress.php:538
#: includes/Extensions/ReviewX/ReviewX.php:80
#: includes/Extensions/ReviewX/ReviewX.php:92
#: includes/Extensions/ReviewX/ReviewX.php:104
#: includes/Extensions/ReviewX/ReviewX.php:119
#: includes/Extensions/ReviewX/ReviewX.php:130
#: includes/Extensions/ReviewX/ReviewX.php:142
#: includes/Extensions/SureCart/SureCart.php:438
#: includes/Extensions/SureCart/SureCart.php:439
#: includes/Extensions/SureCart/SureCart.php:440
#: includes/Extensions/Tutor/Tutor.php:531
#: includes/Extensions/Tutor/Tutor.php:532
#: includes/Extensions/Tutor/Tutor.php:533
#: includes/Extensions/WooCommerce/WooCommerce.php:526
#: includes/Extensions/WooCommerce/WooCommerce.php:527
#: includes/Extensions/WooCommerce/WooCommerce.php:528
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:73
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:85
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:97
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:112
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:123
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:135
#: includes/Extensions/WooCommerce/WOOReviews.php:63
#: includes/Extensions/WooCommerce/WOOReviews.php:75
#: includes/Extensions/WooCommerce/WOOReviews.php:87
#: includes/Extensions/WooCommerce/WOOReviews.php:102
#: includes/Extensions/WooCommerce/WOOReviews.php:113
#: includes/Extensions/WooCommerce/WOOReviews.php:125
#: includes/Extensions/WordPress/WPComments.php:261
#: includes/Extensions/WordPress/WPComments.php:262
#: includes/Extensions/WordPress/WPComments.php:263
#: includes/Extensions/WordPress/WPComments.php:264
#: includes/FrontEnd/FrontEnd.php:268
#: includes/FrontEnd/FrontEnd.php:269
#: includes/FrontEnd/FrontEnd.php:270
#: includes/FrontEnd/FrontEnd.php:747
#: includes/Types/Comments.php:54
#: includes/Types/Comments.php:67
#: includes/Types/Comments.php:80
#: includes/Types/Comments.php:93
#: includes/Types/Comments.php:106
#: includes/Types/Comments.php:119
#: includes/Types/Comments.php:133
#: includes/Types/Comments.php:147
#: includes/Types/ContactForm.php:60
#: includes/Types/ContactForm.php:73
#: includes/Types/ContactForm.php:86
#: includes/Types/Conversions.php:65
#: includes/Types/Donations.php:49
#: includes/Types/ELearning.php:58
#: includes/Types/ELearning.php:71
#: includes/Types/ELearning.php:84
#: includes/Types/EmailSubscription.php:65
#: includes/Types/GDPR.php:57
#: includes/Types/GDPR.php:71
#: includes/Types/GDPR.php:85
#: includes/Types/GDPR.php:99
#: includes/Types/GDPR.php:113
#: includes/Types/GDPR.php:127
#: includes/Types/GDPR.php:141
#: includes/Types/GDPR.php:155
#: includes/Types/GDPR.php:169
#: includes/Types/GDPR.php:183
#: includes/Types/GDPR.php:197
#: includes/Types/GDPR.php:211
#: includes/Types/Reviews.php:63
#: includes/Types/Reviews.php:75
#: includes/Types/Reviews.php:87
#: includes/Types/Reviews.php:102
#: includes/Types/Reviews.php:113
#: includes/Types/Reviews.php:125
#: includes/Types/WooCommerceSales.php:63
msgid "Someone"
msgstr ""

#: includes/Core/PostType.php:77
#: includes/Extensions/GlobalFields.php:1162
#: includes/Extensions/PressBar/PressBar.php:1667
#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
#: assets/admin/js/admin.js:18
msgid "Add New"
msgstr ""

#: includes/Core/PostType.php:133
msgid "%1$s of %2$s free scans used"
msgstr ""

#: includes/Core/QuickBuild.php:47
#: assets/admin/js/admin.js:16
msgid "Quick Builder"
msgstr ""

#: includes/Core/QuickBuild.php:92
#: includes/Extensions/GlobalFields.php:629
#: includes/Extensions/GlobalFields.php:656
#: includes/Extensions/PressBar/PressBar.php:1632
msgid "Content"
msgstr ""

#: includes/Core/QuickBuild.php:254
#: includes/Extensions/PressBar/PressBar.php:66
#: includes/Types/NotificationBar.php:227
#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Notification Bar"
msgstr ""

#: includes/Core/QuickBuild.php:255
#: includes/Extensions/Google/YouTube.php:133
#: includes/Extensions/Google/YouTube.php:175
#: includes/Types/Comments.php:46
msgid "Comments"
msgstr ""

#: includes/Core/QuickBuild.php:256
#: includes/Extensions/WooCommerce/WooCommerceSales.php:198
#: includes/Extensions/WooCommerce/WooCommerceSales.php:199
#: includes/Types/Conversions.php:61
#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Sales Notification"
msgstr ""

#: includes/Core/QuickBuild.php:257
#: includes/Types/Video.php:50
msgid "Video"
msgstr ""

#: includes/Core/QuickBuild.php:258
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:65
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:66
#: includes/Types/Reviews.php:56
msgid "Reviews"
msgstr ""

#: includes/Core/QuickBuild.php:259
#: includes/Types/DownloadStats.php:49
msgid "Download Stats"
msgstr ""

#: includes/Core/QuickBuild.php:260
#: includes/Types/ELearning.php:51
msgid "eLearning"
msgstr ""

#: includes/Core/QuickBuild.php:261
msgid "Donation"
msgstr ""

#: includes/Core/QuickBuild.php:262
#: includes/Types/ContactForm.php:52
msgid "Contact Form"
msgstr ""

#: includes/Core/QuickBuild.php:263
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:66
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:67
#: includes/Types/Inline.php:45
#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Growth Alert"
msgstr ""

#: includes/Core/QuickBuild.php:264
#: includes/Extensions/GDPR/GDPR_Notification.php:47
#: includes/Types/GDPR.php:49
#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Cookie Notice"
msgstr ""

#: includes/Core/REST.php:464
msgid "Unauthorized Access: You have to logged in first."
msgstr ""

#: includes/Core/REST.php:467
msgid "Invalid Type: You have to give a type."
msgstr ""

#: includes/Core/REST.php:470
msgid "400 Bad Request."
msgstr ""

#: includes/Core/Rest/Analytics.php:71
#: includes/Core/Rest/Entries.php:63
#: includes/Core/Rest/Entries.php:78
#: includes/Core/Rest/Integration.php:70
#: includes/Core/Rest/Integration.php:87
#: includes/Core/Rest/Integration.php:111
#: includes/Core/Rest/Integration.php:128
#: includes/Core/Rest/Posts.php:89
msgid "Unique identifier for the object."
msgstr ""

#: includes/Core/Rest/Analytics.php:76
msgid "Click or View"
msgstr ""

#: includes/Core/Rest/Analytics.php:94
msgid "Start of the date range."
msgstr ""

#: includes/Core/Rest/Analytics.php:99
msgid "End of the date range."
msgstr ""

#: includes/Core/Rest/BulkAction.php:59
#: includes/Core/Rest/BulkAction.php:72
#: includes/Core/Rest/BulkAction.php:85
#: includes/Core/Rest/BulkAction.php:98
#: includes/Core/Rest/BulkAction.php:111
msgid "Array of nx_id."
msgstr ""

#: includes/Core/Rest/Integration.php:75
#: includes/Core/Rest/Integration.php:92
#: includes/Core/Rest/Integration.php:116
#: includes/Core/Rest/Integration.php:133
msgid "Unique identifier for the site."
msgstr ""

#: includes/Core/Rest/Integration.php:152
msgid "There is no notification created with this id:"
msgstr ""

#: includes/Core/Rest/Integration.php:155
msgid "Error: API Key Invalid!"
msgstr ""

#: includes/Core/Rest/Integration.php:173
msgid "Error: You should provide an API key."
msgstr ""

#: includes/Core/Rest/Integration.php:176
msgid "Error: Invalid API key."
msgstr ""

#: includes/Core/Rest/Posts.php:113
msgid "Whether to bypass Trash and force deletion."
msgstr ""

#: includes/Core/Rest/Posts.php:218
#: includes/Core/Rest/Posts.php:238
msgid "Cannot create existing post."
msgstr ""

#: includes/CoreInstaller.php:67
msgid "You don't have permission to install the plugins"
msgstr ""

#: includes/CoreInstaller.php:76
msgid "You don't have set any slug and file name to install the plugins"
msgstr ""

#: includes/Extensions/ActiveCampaign/ActiveCampaign.php:43
#: includes/Extensions/ActiveCampaign/ActiveCampaign.php:44
msgid "ActiveCampaign"
msgstr ""

#: includes/Extensions/ActiveCampaign/ActiveCampaign.php:58
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">signed in & retrieved API URL & API key from ActiveCampaign account</a> to use its campaign & email subscriptions data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%3$s\">Integration with ActiveCampaign</a></p>\n"
"\t\t<p><strong>Recommended Blogs:</strong></p>\n"
"\t\t<p>🔥 Boosting Engagement with <a target=\"_blank\" href=\"%4$s\">ActiveCampaign Email Subscription Alerts</a> via NotificationX</p>"
msgstr ""

#: includes/Extensions/CCPA/CCPA_Notification.php:42
msgid "CCPA"
msgstr ""

#: includes/Extensions/CF7/CF7.php:50
#: includes/Extensions/CF7/CF7.php:51
#: includes/Extensions/CF7/CF7.php:101
msgid "Contact Form 7"
msgstr ""

#: includes/Extensions/CF7/CF7.php:99
#: includes/Extensions/EDD/EDD.php:96
#: includes/Extensions/Elementor/From.php:61
#: includes/Extensions/FluentForm/FluentForm.php:90
#: includes/Extensions/Give/Give.php:103
#: includes/Extensions/GRVF/GravityForms.php:65
#: includes/Extensions/LearnPress/LearnPress.php:130
#: includes/Extensions/NJF/NinjaForms.php:90
#: includes/Extensions/ReviewX/ReviewX.php:210
#: includes/Extensions/SureCart/SureCart.php:453
#: includes/Extensions/Tutor/Tutor.php:129
#: includes/Extensions/WooCommerce/WooCommerce.php:102
#: includes/Extensions/WooCommerce/WOOReviews.php:231
#: includes/Extensions/WPF/WPForms.php:90
msgid "You have to install"
msgstr ""

#: includes/Extensions/CF7/CF7.php:102
#: includes/Extensions/EDD/EDD.php:99
#: includes/Extensions/Elementor/From.php:64
#: includes/Extensions/FluentForm/FluentForm.php:93
#: includes/Extensions/Give/Give.php:106
#: includes/Extensions/GRVF/GravityForms.php:68
#: includes/Extensions/LearnPress/LearnPress.php:133
#: includes/Extensions/NJF/NinjaForms.php:93
#: includes/Extensions/SureCart/SureCart.php:456
#: includes/Extensions/Tutor/Tutor.php:132
#: includes/Extensions/WooCommerce/WooCommerce.php:105
#: includes/Extensions/WooCommerce/WOOReviews.php:234
#: includes/Extensions/WPF/WPForms.php:93
msgid "plugin first."
msgstr ""

#. translators: links
#: includes/Extensions/CF7/CF7.php:278
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">Contact Form 7 installed & configured</a> to use its campaign & form subscriptions data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with Contact Form 7</a></p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 Hacks to Increase Your <a target=\"_blank\" href=\"%5$s\">WordPress Contact Forms Submission Rate</a> Using NotificationX</p>"
msgstr ""

#: includes/Extensions/ConvertKit/ConvertKit.php:43
#: includes/Extensions/ConvertKit/ConvertKit.php:44
msgid "ConvertKit"
msgstr ""

#: includes/Extensions/ConvertKit/ConvertKit.php:58
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">signed in & retrieved your API key from ConvertKit account</a> to use its campaign & email subscriptions data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with ConvertKit</a></p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 Connect <a target=\"_blank\" href=\"%5$s\">NotificationX With ConvertKit</a>: Grow Your Audience By Leveraging Social Proof</p>"
msgstr ""

#: includes/Extensions/CustomNotification/CustomNotification.php:46
#: includes/Extensions/CustomNotification/CustomNotification.php:47
#: includes/Extensions/CustomNotification/CustomNotificationConversions.php:43
#: includes/Extensions/CustomNotification/CustomNotificationConversions.php:44
#: includes/Types/CustomNotification.php:46
msgid "Custom Notification"
msgstr ""

#: includes/Extensions/CustomNotification/CustomNotification.php:91
#: includes/Extensions/CustomNotification/CustomNotificationConversions.php:68
msgid ""
"<p>You can make custom notification for its all types of campaign. For further assistance, check out our step by step <a target=\"_blank\" href=\"%1$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 Watch <a target=\"_blank\" href=\"%2$s\">video tutorial</a> to learn quickly</p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 How to <a target=\"_blank\" href=\"%3$s\">Display Custom Notification Alerts</a> On Your Website Using NotificationX</p>"
msgstr ""

#: includes/Extensions/CustomNotification/CustomNotificationConversions.php:46
msgid "<a href='https://notificationx.com/docs/custom-notification/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/CustomNotification/CustomNotificationConversions.php:47
#: includes/Extensions/Elementor/From.php:49
#: includes/Extensions/Envato/Envato.php:48
#: includes/Extensions/Freemius/FreemiusConversions.php:48
#: includes/Extensions/Freemius/FreemiusReviews.php:48
#: includes/Extensions/Freemius/FreemiusStats.php:49
#: includes/Extensions/Google/GoogleReviews.php:163
#: includes/Extensions/Google/YouTube.php:256
#: includes/Extensions/GRVF/GravityForms.php:50
#: includes/Extensions/LearnDash/LearnDash.php:50
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:156
#: includes/Extensions/Zapier/ZapierConversions.php:48
#: includes/Extensions/Zapier/ZapierReviews.php:48
#: includes/Types/EmailSubscription.php:54
#: includes/Types/FlashingTab.php:47
#: includes/Types/Inline.php:48
#: includes/Types/OfferAnnouncement.php:50
#: includes/Types/PageAnalytics.php:48
#: includes/Types/Video.php:53
msgid "<a href='https://notificationx.com/#pricing' target='_blank'>Upgrade to PRO</a>"
msgstr ""

#: includes/Extensions/CustomNotification/CustomNotificationConversions.php:48
msgid ""
"\n"
"                <span style=\"text-align:left;\">Display custom conversion notifications as pop up.</span>\n"
"                <iframe id=\"custom_notification_video\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\"\n"
"                src=\"https://www.youtube.com/embed/OuTmDZ0_TEw\">\n"
"                </iframe>\n"
"            "
msgstr ""

#: includes/Extensions/EDD/EDD.php:49
#: includes/Extensions/EDD/EDD.php:50
#: includes/Extensions/EDD/EDD.php:98
msgid "Easy Digital Downloads"
msgstr ""

#: includes/Extensions/EDD/EDD.php:129
#: includes/Extensions/WooCommerce/WooCommerce.php:477
msgid "more products"
msgstr ""

#: includes/Extensions/EDD/EDD.php:147
#: includes/Extensions/SureCart/SureCart.php:431
#: includes/Extensions/WooCommerce/WooCommerce.php:132
#: includes/Types/DownloadStats.php:199
#: includes/Types/Traits/Reviews.php:22
msgid "Product Page"
msgstr ""

#: includes/Extensions/EDD/EDD.php:353
#: includes/Extensions/Give/Give.php:85
#: includes/Extensions/LearnPress/LearnPress.php:539
#: includes/Extensions/SureCart/SureCart.php:441
#: includes/Extensions/Tutor/Tutor.php:534
#: includes/Extensions/WooCommerce/WooCommerce.php:529
#: includes/Types/Conversions.php:68
#: includes/Types/WooCommerceSales.php:66
msgid "Anonymous Product"
msgstr ""

#: includes/Extensions/EDD/EDD.php:362
msgid ""
"<p>Make sure that you have <a href=\"%1$s\" target=\"_blank\">Easy Digital Downloads installed & activated</a> to use its campaign & product sales data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%3$s\">Integration with Easy Digital Downloads</a></p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 How Does <a target=\"_blank\" href=\"%4$s\">NotificationX Increase Sales on WordPress</a> Websites?</p>"
msgstr ""

#: includes/Extensions/EDD/EDInline.php:46
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:77
#: includes/Extensions/WooCommerce/WooInline.php:81
msgid "people purchased"
msgstr ""

#: includes/Extensions/EDD/EDInline.php:50
#: includes/Extensions/LearnDash/LearnDashInline.php:50
#: includes/Extensions/LearnPress/LearnPressInline.php:48
#: includes/Extensions/LearnPress/LearnPressInline.php:62
#: includes/Extensions/Tutor/TutorInline.php:48
#: includes/Extensions/Tutor/TutorInline.php:62
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:81
#: includes/Extensions/WooCommerce/WooInline.php:85
msgid "in last {{day:7}}"
msgstr ""

#: includes/Extensions/EDD/EDInline.php:57
#: includes/Extensions/LearnDash/LearnDashInline.php:57
#: includes/Extensions/LearnPress/LearnPressInline.php:69
#: includes/Extensions/Tutor/TutorInline.php:69
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:122
#: includes/Extensions/WooCommerce/WooInline.php:126
msgid "Sales Count"
msgstr ""

#: includes/Extensions/EDD/EDInline.php:60
#: includes/Extensions/ReviewX/ReviewX.php:157
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:125
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:151
#: includes/Extensions/WooCommerce/WooInline.php:129
#: includes/Extensions/WooCommerce/WOOReviews.php:140
#: includes/Extensions/WooCommerce/WOOReviews.php:165
#: includes/Types/Conversions.php:210
#: includes/Types/Conversions.php:235
#: includes/Types/WooCommerceSales.php:207
#: includes/Types/WooCommerceSales.php:225
msgid "Product Title"
msgstr ""

#: includes/Extensions/EDD/EDInline.php:63
#: includes/Extensions/LearnDash/LearnDashInline.php:63
#: includes/Extensions/LearnPress/LearnPressInline.php:75
#: includes/Extensions/Tutor/TutorInline.php:75
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:128
#: includes/Extensions/WooCommerce/WooInline.php:132
#: includes/FrontEnd/Preview.php:278
msgid "In last 1 day"
msgstr ""

#: includes/Extensions/EDD/EDInline.php:64
#: includes/Extensions/LearnDash/LearnDashInline.php:64
#: includes/Extensions/LearnPress/LearnPressInline.php:76
#: includes/Extensions/Tutor/TutorInline.php:76
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:129
#: includes/Extensions/WooCommerce/WooInline.php:133
#: includes/FrontEnd/Preview.php:279
#: includes/Types/DownloadStats.php:136
#: includes/Types/DownloadStats.php:156
msgid "In last 7 days"
msgstr ""

#: includes/Extensions/EDD/EDInline.php:65
#: includes/Extensions/LearnDash/LearnDashInline.php:65
#: includes/Extensions/LearnPress/LearnPressInline.php:77
#: includes/Extensions/Tutor/TutorInline.php:77
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:130
#: includes/Extensions/WooCommerce/WooInline.php:134
#: includes/FrontEnd/Preview.php:280
msgid "In last 30 days"
msgstr ""

#: includes/Extensions/Elementor/From.php:45
msgid "Elementor Form"
msgstr ""

#: includes/Extensions/Elementor/From.php:46
msgid "Elementor"
msgstr ""

#: includes/Extensions/Elementor/From.php:48
msgid "<a href='https://notificationx.com/docs/elementor-form-with-notificationx/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/Elementor/From.php:50
msgid ""
"\n"
"                <span>Elementor forms that can help you keep important leads and stay in touch with your customers.</span>\n"
"            "
msgstr ""

#: includes/Extensions/Elementor/From.php:63
msgid "Elementor Pro"
msgstr ""

#. translators: links
#: includes/Extensions/Elementor/From.php:76
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">Elementor Pro installed & configured</a> to use its form submission data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 Hacks to Increase Your <a target=\"_blank\" href=\"%3$s\">WordPress Contact Forms Submission Rate</a> Using NotificationX</p>"
msgstr ""

#: includes/Extensions/Envato/Envato.php:44
#: includes/Extensions/Envato/Envato.php:45
msgid "Envato"
msgstr ""

#: includes/Extensions/Envato/Envato.php:47
msgid "<a href='https://notificationx.com/docs/envato-sales-notification/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/Envato/Envato.php:49
msgid ""
"\n"
"                <span>A resourceful online marketplace for digital assets and services.</span>\n"
"                <iframe id=\"email_subscription_video\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\"\n"
"                src=\"https://www.youtube.com/embed/-df_6KHgr7I\">\n"
"                </iframe>\n"
"            "
msgstr ""

#: includes/Extensions/Envato/Envato.php:69
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">created & signed in to Envato account</a> to use its campaign & product sales data.  For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with Envato</a></p>"
msgstr ""

#: includes/Extensions/FlashingTab/FlashingTab.php:52
#: includes/Extensions/FlashingTab/FlashingTab.php:53
#: includes/Types/FlashingTab.php:43
#: includes/Types/FlashingTab.php:44
msgid "Flashing Tab"
msgstr ""

#: includes/Extensions/FlashingTab/FlashingTab.php:63
#: includes/Extensions/FlashingTab/FlashingTab.php:83
#: includes/Extensions/FlashingTab/FlashingTab.php:98
msgid "Comeback!"
msgstr ""

#: includes/Extensions/FlashingTab/FlashingTab.php:74
msgid "Comeback! We miss you."
msgstr ""

#: includes/Extensions/FlashingTab/FlashingTab.php:87
msgid "You forgot to purchase!"
msgstr ""

#: includes/Extensions/FlashingTab/FlashingTab.php:104
msgid "{quantity} items in your cart!"
msgstr ""

#. translators: links
#: includes/Extensions/FlashingTab/FlashingTab.php:136
msgid ""
"\n"
"        <p>Make sure that you have NotificationX PRO installed and activated on your website to use Flashing Tab. For further assistance, follow the step-by-step <a target=\"_blank\" href=\"%1$s\">documentation</a>.</p>\n"
"\t\t<p>🎥 Get a quick demo from the <a target=\"_blank\" href=\"%2$s\">video tutorial</a></p>\n"
"\t\t<p>📖 Recommended Blog:</p>\n"
"\t\t<p>🔥How To <a target=\"_blank\" href=\"%3$s\">Attract Customers With Flashing Browser Tab Notification Using NotificationX?</a></p>\n"
"\t\t"
msgstr ""

#: includes/Extensions/FluentForm/FluentForm.php:48
#: includes/Extensions/FluentForm/FluentForm.php:49
#: includes/Extensions/FluentForm/FluentForm.php:92
msgid "Fluent Forms"
msgstr ""

#: includes/Extensions/FluentForm/FluentForm.php:404
msgid ""
"\n"
"        <p>To use the campaign & form subscription data, make sure that you have <a target=\"_blank\" href=\"%1$s\">Fluent Forms installed and configured</a> on your website. For detailed guidelines, follow this <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\n"
"        <p>🎥 Learn quickly from the <a target=\"_blank\" href=\"%3$s\">video tutorial</a>.</p>\n"
"\n"
"        <p>⚙️ NotificationX integration with Fluent Forms</p>\n"
"\n"
"        <p>📖 Recommended Reading: </p>\n"
"        <p>🔥How To <a target=\"_blank\" href=\"%4$s\">Display Fluent Forms Submission Alert</a> Using NotificationX?</p>\n"
"        "
msgstr ""

#: includes/Extensions/Freemius/Freemius.php:19
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">created & signed in to Freemius account</a> to use its campaign & product sales data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with Freemius</a></p>"
msgstr ""

#: includes/Extensions/Freemius/FreemiusConversions.php:44
#: includes/Extensions/Freemius/FreemiusConversions.php:45
#: includes/Extensions/Freemius/FreemiusReviews.php:44
#: includes/Extensions/Freemius/FreemiusReviews.php:45
#: includes/Extensions/Freemius/FreemiusStats.php:44
#: includes/Extensions/Freemius/FreemiusStats.php:45
msgid "Freemius"
msgstr ""

#: includes/Extensions/Freemius/FreemiusConversions.php:47
msgid "<a href='https://notificationx.com/docs/freemius-sales-notification/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/Freemius/FreemiusConversions.php:49
msgid ""
"\n"
"                <span>Fantastic platform for WordPress users to sell their items all around the world.</span>\n"
"                <iframe id=\"email_subscription_video\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\"\n"
"                src=\"https://www.youtube.com/embed/0uANsOSFmtw\">\n"
"                </iframe>\n"
"            "
msgstr ""

#: includes/Extensions/Freemius/FreemiusReviews.php:47
msgid "<a href='https://notificationx.com/docs/freemius-review-notificationx/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/Freemius/FreemiusReviews.php:49
msgid ""
"\n"
"                <span>Widely used medium to show review teasers to persuade visitors to trust your offerings.</span>\n"
"            "
msgstr ""

#: includes/Extensions/Freemius/FreemiusStats.php:50
msgid ""
"\n"
"                <span>Amazing platform to display download statistics to urge visitors to trust your items.</span>\n"
"                <iframe id=\"email_subscription_video\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\"\n"
"                src=\"https://www.youtube.com/embed/0uANsOSFmtw\">\n"
"                </iframe>\n"
"            "
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:46
msgid "GDPR"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:65
#: includes/Extensions/GlobalFields.php:237
#: includes/Extensions/GlobalFields.php:467
msgid "Design"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:75
#: includes/Extensions/GDPR/GDPR_Notification.php:157
#: includes/Extensions/GDPR/GDPR_Notification.php:201
#: includes/Extensions/GDPR/GDPR_Notification.php:239
#: includes/Extensions/GlobalFields.php:475
#: includes/Extensions/PressBar/PressBar.php:511
#: blocks/notificationx/components/inspector.js:313
#: blocks/notificationx/index.js:1
msgid "Background Color"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:81
msgid "Footer Background Color"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:87
msgid "Title Color"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:93
msgid "Title Font Size"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:98
msgid "This font size will be applied for <mark>Title</mark> only"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:101
msgid "Description Color"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:107
msgid "Description Font Size"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:112
msgid "This font size will be applied for <mark>Description</mark> only"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:115
#: includes/Extensions/PressBar/PressBar.php:548
msgid "Close Button Color"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:129
#: includes/Extensions/PressBar/PressBar.php:553
msgid "Close Button Size"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:147
msgid "Accept Button"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:163
#: includes/Extensions/GDPR/GDPR_Notification.php:207
#: includes/Extensions/GDPR/GDPR_Notification.php:245
#: includes/Extensions/GlobalFields.php:512
msgid "Border Color"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:169
#: includes/Extensions/GDPR/GDPR_Notification.php:213
#: includes/Extensions/GDPR/GDPR_Notification.php:251
#: includes/Extensions/GlobalFields.php:481
#: includes/Extensions/PressBar/PressBar.php:523
#: blocks/notificationx/components/inspector.js:307
#: blocks/notificationx/index.js:1
msgid "Text Color"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:175
#: includes/Extensions/GDPR/GDPR_Notification.php:219
#: includes/Extensions/GDPR/GDPR_Notification.php:257
#: includes/Extensions/GlobalFields.php:547
#: includes/Extensions/GlobalFields.php:555
#: includes/Extensions/GlobalFields.php:563
#: includes/Extensions/PressBar/PressBar.php:650
msgid "Font Size"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:185
msgid "Reject Button"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:229
#: includes/Types/GDPR.php:435
msgid "Customize Button"
msgstr ""

#: includes/Extensions/GDPR/GDPR_Notification.php:302
msgid ""
"<p>You can showcase Cookie Notice effortlessly on your WordPress site to ensure compliance with visitors. Need help? Follow our <a href=\"%1$s\" target=\"_blank\">step-by-step guides</a> for creating a Cookie Notice on the WordPress website.</p>\n"
"        <p>🎦 Watch the video <a target=\"_blank\" href=\"%2$s\">tutorial</a> for a quick guide.</p>\n"
"        <p><strong>Recommended Blogs:</strong></p>\n"
"        <p>🔥 <a target=\"_blank\" href=\"%3$s\">How to Display WordPress Cookie Notice Using NotificationX?</a></p>\n"
"        <p><strong>Recommended Plugins:</strong></p>\n"
"        <p>🔌 <a target=\"_blank\" href=\"%4$s\">WP Consent API</a> - Centralize cookie handling with a unified consent solution.</p>"
msgstr ""

#: includes/Extensions/Give/Give.php:46
msgid "Give"
msgstr ""

#: includes/Extensions/Give/Give.php:47
msgid "GiveWP"
msgstr ""

#: includes/Extensions/Give/Give.php:86
#: includes/Extensions/GlobalFields.php:708
#: includes/Extensions/GlobalFields.php:726
#: includes/Extensions/GlobalFields.php:2266
#: includes/Extensions/Google/GoogleReviews.php:60
#: includes/Extensions/Google/GoogleReviews.php:74
#: includes/Extensions/Google/GoogleReviews.php:88
#: includes/Extensions/Google/GoogleReviews.php:116
#: includes/Extensions/Google/GoogleReviews.php:131
#: includes/Extensions/OfferAnnouncement/Announcements.php:59
#: includes/Extensions/OfferAnnouncement/Announcements.php:79
#: includes/Extensions/OfferAnnouncement/Announcements.php:100
#: includes/Extensions/OfferAnnouncement/Announcements.php:135
#: includes/Extensions/OfferAnnouncement/Announcements.php:183
#: includes/Extensions/OfferAnnouncement/Announcements.php:197
#: includes/Extensions/OfferAnnouncement/Announcements.php:212
#: includes/Extensions/OfferAnnouncement/Announcements.php:239
#: includes/Extensions/ReviewX/ReviewX.php:84
#: includes/Extensions/ReviewX/ReviewX.php:96
#: includes/Extensions/ReviewX/ReviewX.php:123
#: includes/Extensions/ReviewX/ReviewX.php:134
#: includes/Extensions/ReviewX/ReviewX.php:146
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:77
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:89
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:116
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:127
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:139
#: includes/Extensions/WooCommerce/WOOReviews.php:67
#: includes/Extensions/WooCommerce/WOOReviews.php:79
#: includes/Extensions/WooCommerce/WOOReviews.php:106
#: includes/Extensions/WooCommerce/WOOReviews.php:117
#: includes/Extensions/WooCommerce/WOOReviews.php:129
#: includes/Extensions/WordPress/WPComments.php:266
#: includes/FrontEnd/FrontEnd.php:272
#: includes/Types/Comments.php:59
#: includes/Types/Comments.php:72
#: includes/Types/Comments.php:85
#: includes/Types/Comments.php:98
#: includes/Types/Comments.php:111
#: includes/Types/Comments.php:124
#: includes/Types/Comments.php:138
#: includes/Types/Comments.php:152
#: includes/Types/ContactForm.php:65
#: includes/Types/ContactForm.php:78
#: includes/Types/ContactForm.php:91
#: includes/Types/Conversions.php:70
#: includes/Types/Donations.php:56
#: includes/Types/ELearning.php:63
#: includes/Types/ELearning.php:76
#: includes/Types/ELearning.php:89
#: includes/Types/EmailSubscription.php:70
#: includes/Types/EmailSubscription.php:128
#: includes/Types/GDPR.php:62
#: includes/Types/GDPR.php:76
#: includes/Types/GDPR.php:90
#: includes/Types/GDPR.php:104
#: includes/Types/GDPR.php:118
#: includes/Types/GDPR.php:132
#: includes/Types/GDPR.php:146
#: includes/Types/GDPR.php:160
#: includes/Types/GDPR.php:174
#: includes/Types/GDPR.php:188
#: includes/Types/GDPR.php:202
#: includes/Types/GDPR.php:216
#: includes/Types/Reviews.php:67
#: includes/Types/Reviews.php:79
#: includes/Types/Reviews.php:106
#: includes/Types/Reviews.php:117
#: includes/Types/Reviews.php:129
#: includes/Types/WooCommerceSales.php:68
msgid "Some time ago"
msgstr ""

#: includes/Extensions/Give/Give.php:105
msgid "GiveWP Donation"
msgstr ""

#: includes/Extensions/Give/Give.php:157
#: includes/Extensions/Give/Give.php:235
msgid "for"
msgstr ""

#: includes/Extensions/Give/Give.php:297
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">GiveWP installed & configured</a> to use its campaign & donars data. For further assistance, check out our step by step <a href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with GiveWP</a></p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 How Does <a target=\"_blank\" href=\"%5$s\">NotificationX Increase Sales on WordPress</a> Websites?\"</p>"
msgstr ""

#: includes/Extensions/GlobalFields.php:71
msgid "Previous"
msgstr ""

#: includes/Extensions/GlobalFields.php:72
#: includes/Extensions/PressBar/PressBar.php:804
#: includes/Extensions/PressBar/PressBar.php:1011
msgid "Next"
msgstr ""

#: includes/Extensions/GlobalFields.php:82
#: includes/Extensions/GlobalFields.php:126
msgid "Source"
msgstr ""

#: includes/Extensions/GlobalFields.php:92
msgid "Notification Type"
msgstr ""

#: includes/Extensions/GlobalFields.php:253
#: includes/Extensions/GlobalFields.php:261
#: includes/Extensions/GlobalFields.php:639
#: includes/Extensions/GlobalFields.php:647
#: includes/Extensions/GlobalFields.php:1013
#: includes/Extensions/GlobalFields.php:1021
#: includes/Extensions/GlobalFields.php:1459
#: includes/Extensions/GlobalFields.php:1467
#: includes/Extensions/GlobalFields.php:1706
#: includes/Extensions/GlobalFields.php:1714
#: includes/FrontEnd/Preview.php:375
#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
#: assets/admin/js/admin.js:18
msgid "Preview"
msgstr ""

#: includes/Extensions/GlobalFields.php:270
msgid "Themes"
msgstr ""

#: includes/Extensions/GlobalFields.php:288
msgid "Select Theme"
msgstr ""

#: includes/Extensions/GlobalFields.php:292
msgid "Light"
msgstr ""

#: includes/Extensions/GlobalFields.php:292
msgid "Dark"
msgstr ""

#: includes/Extensions/GlobalFields.php:313
msgid "For Desktop"
msgstr ""

#: includes/Extensions/GlobalFields.php:320
msgid "For Mobile"
msgstr ""

#: includes/Extensions/GlobalFields.php:369
#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Get PRO to Unlock"
msgstr ""

#: includes/Extensions/GlobalFields.php:387
msgid "Mobile Responsive Themes"
msgstr ""

#: includes/Extensions/GlobalFields.php:394
msgid "Enable Mobile Responsive"
msgstr ""

#: includes/Extensions/GlobalFields.php:404
#: includes/Extensions/GlobalFields.php:420
#: includes/Extensions/GlobalFields.php:592
#: includes/Extensions/GlobalFields.php:1728
#: includes/Extensions/GlobalFields.php:1813
msgid "Position"
msgstr ""

#: includes/Extensions/GlobalFields.php:409
#: includes/Extensions/GlobalFields.php:1735
#: includes/Extensions/GlobalFields.php:1820
msgid "Bottom Left"
msgstr ""

#: includes/Extensions/GlobalFields.php:410
#: includes/Extensions/GlobalFields.php:1739
#: includes/Extensions/GlobalFields.php:1824
msgid "Bottom Right"
msgstr ""

#: includes/Extensions/GlobalFields.php:411
msgid "Center"
msgstr ""

#: includes/Extensions/GlobalFields.php:425
#: includes/Extensions/PressBar/PressBar.php:468
#: includes/Extensions/PressBar/PressBar.php:1142
msgid "Bottom"
msgstr ""

#: includes/Extensions/GlobalFields.php:426
#: includes/Extensions/PressBar/PressBar.php:461
#: includes/Extensions/PressBar/PressBar.php:592
#: includes/Extensions/PressBar/PressBar.php:614
#: includes/Extensions/PressBar/PressBar.php:1137
msgid "Top"
msgstr ""

#: includes/Extensions/GlobalFields.php:434
#: includes/Extensions/GlobalFields.php:444
msgid "Advanced Design"
msgstr ""

#: includes/Extensions/GlobalFields.php:460
msgid "Advanced Toggle"
msgstr ""

#: includes/Extensions/GlobalFields.php:487
msgid "Want Border?"
msgstr ""

#: includes/Extensions/GlobalFields.php:493
msgid "Border Size"
msgstr ""

#: includes/Extensions/GlobalFields.php:500
msgid "Border Style"
msgstr ""

#: includes/Extensions/GlobalFields.php:505
msgid "Solid"
msgstr ""

#: includes/Extensions/GlobalFields.php:506
msgid "Dashed"
msgstr ""

#: includes/Extensions/GlobalFields.php:507
msgid "Dotted"
msgstr ""

#: includes/Extensions/GlobalFields.php:519
msgid "Discount Text Color"
msgstr ""

#: includes/Extensions/GlobalFields.php:529
msgid "Discount Background"
msgstr ""

#: includes/Extensions/GlobalFields.php:540
#: includes/Extensions/PressBar/PressBar.php:643
#: blocks/notificationx/components/inspector.js:254
#: blocks/notificationx/index.js:1
msgid "Typography"
msgstr ""

#: includes/Extensions/GlobalFields.php:552
#: includes/Extensions/PressBar/PressBar.php:656
msgid "This font size will be applied for <mark>first</mark> row"
msgstr ""

#: includes/Extensions/GlobalFields.php:560
msgid "This font size will be applied for <mark>second</mark> row"
msgstr ""

#: includes/Extensions/GlobalFields.php:568
msgid "This font size will be applied for <mark>third</mark> row"
msgstr ""

#: includes/Extensions/GlobalFields.php:573
msgid "Image Appearance"
msgstr ""

#: includes/Extensions/GlobalFields.php:580
msgid "Image Shape"
msgstr ""

#: includes/Extensions/GlobalFields.php:586
msgid "Circle"
msgstr ""

#: includes/Extensions/GlobalFields.php:587
msgid "Rounded"
msgstr ""

#: includes/Extensions/GlobalFields.php:588
msgid "Square"
msgstr ""

#: includes/Extensions/GlobalFields.php:598
#: includes/Extensions/PressBar/PressBar.php:447
#: includes/Extensions/PressBar/PressBar.php:586
#: includes/Extensions/PressBar/PressBar.php:603
msgid "Left"
msgstr ""

#: includes/Extensions/GlobalFields.php:599
#: includes/Extensions/PressBar/PressBar.php:454
#: includes/Extensions/PressBar/PressBar.php:587
#: includes/Extensions/PressBar/PressBar.php:625
msgid "Right"
msgstr ""

#: includes/Extensions/GlobalFields.php:605
msgid "Custom CSS"
msgstr ""

#: includes/Extensions/GlobalFields.php:612
msgid "Add Custom CSS"
msgstr ""

#: includes/Extensions/GlobalFields.php:620
msgid "Use custom CSS to style this Notification."
msgstr ""

#: includes/Extensions/GlobalFields.php:662
msgid "Notification Template"
msgstr ""

#: includes/Extensions/GlobalFields.php:675
#: includes/Extensions/GlobalFields.php:700
#: includes/Extensions/GlobalFields.php:718
#: includes/Extensions/GlobalFields.php:735
#: includes/Extensions/GlobalFields.php:752
#: includes/Extensions/PressBar/PressBar.php:405
#: includes/Extensions/PressBar/PressBar.php:413
#: includes/Extensions/PressBar/PressBar.php:1346
msgid "Custom"
msgstr ""

#: includes/Extensions/GlobalFields.php:691
msgid "recently purchased"
msgstr ""

#: includes/Extensions/GlobalFields.php:767
#: includes/Extensions/GlobalFields.php:777
msgid "Advanced Template"
msgstr ""

#: includes/Extensions/GlobalFields.php:783
msgid "Random Order"
msgstr ""

#: includes/Extensions/GlobalFields.php:788
msgid "Enable to show notification in random order."
msgstr ""

#: includes/Extensions/GlobalFields.php:792
msgid "Show Purchase Of"
msgstr ""

#: includes/Extensions/GlobalFields.php:800
#: includes/Types/ELearning.php:240
msgid "All"
msgstr ""

#: includes/Extensions/GlobalFields.php:801
#: includes/Extensions/GlobalFields.php:859
msgid "Product Category"
msgstr ""

#: includes/Extensions/GlobalFields.php:802
#: includes/Extensions/GlobalFields.php:860
msgid "Selected Product"
msgstr ""

#: includes/Extensions/GlobalFields.php:807
#: includes/Extensions/GlobalFields.php:865
msgid "Select Product Category"
msgstr ""

#: includes/Extensions/GlobalFields.php:819
#: includes/Extensions/GlobalFields.php:877
msgid "Select Product"
msgstr ""

#: includes/Extensions/GlobalFields.php:850
msgid "Exclude By"
msgstr ""

#: includes/Extensions/GlobalFields.php:858
#: includes/Extensions/GlobalFields.php:970
#: includes/Extensions/GlobalFields.php:1574
#: includes/Extensions/Google/YouTube.php:221
#: includes/Extensions/Google/YouTube.php:225
#: includes/Extensions/Google/YouTube.php:235
#: includes/Extensions/Google/YouTube.php:239
#: includes/Extensions/Google/YouTube.php:243
#: includes/Types/Donations.php:170
msgid "None"
msgstr ""

#: includes/Extensions/GlobalFields.php:907
#: includes/Extensions/GlobalFields.php:923
msgid "Order Status"
msgstr ""

#: includes/Extensions/GlobalFields.php:937
msgid "Combine Multi Order"
msgstr ""

#: includes/Extensions/GlobalFields.php:942
msgid "Combine order like, 2 more products."
msgstr ""

#: includes/Extensions/GlobalFields.php:951
msgid "Cookies Content"
msgstr ""

#: includes/Extensions/GlobalFields.php:959
msgid "Link Options"
msgstr ""

#: includes/Extensions/GlobalFields.php:965
msgid "Link Type"
msgstr ""

#: includes/Extensions/GlobalFields.php:974
msgid "Button"
msgstr ""

#: includes/Extensions/GlobalFields.php:986
msgid "Enable button with link"
msgstr ""

#: includes/Extensions/GlobalFields.php:1002
msgid "Manager"
msgstr ""

#: includes/Extensions/GlobalFields.php:1030
msgid "General Settings"
msgstr ""

#: includes/Extensions/GlobalFields.php:1036
msgid "Force Reload"
msgstr ""

#: includes/Extensions/GlobalFields.php:1040
msgid "Enable Force Reload"
msgstr ""

#: includes/Extensions/GlobalFields.php:1044
msgid "Choose whether the page should reload after the user accepts cookies. If not, your analytics software won’t register the current page visit, as cookies will only be loaded during the next page load"
msgstr ""

#: includes/Extensions/GlobalFields.php:1047
msgid "Cookie Removal"
msgstr ""

#: includes/Extensions/GlobalFields.php:1051
msgid "Enable Cookie Removal"
msgstr ""

#: includes/Extensions/GlobalFields.php:1055
msgid "When cookies are not accepted, non-essential cookies are removed, ensuring compliance with GDPR requirements."
msgstr ""

#: includes/Extensions/GlobalFields.php:1058
msgid "Consent Expiry"
msgstr ""

#: includes/Extensions/GlobalFields.php:1062
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Days"
msgstr ""

#: includes/Extensions/GlobalFields.php:1085
msgid "By default, consent expires after 30 days. If needed, you can adjust this number to your preference"
msgstr ""

#: includes/Extensions/GlobalFields.php:1117
#: includes/FrontEnd/FrontEnd.php:88
msgid "Necessary"
msgstr ""

#: includes/Extensions/GlobalFields.php:1133
#: includes/Extensions/GlobalFields.php:1188
#: includes/Extensions/GlobalFields.php:1242
#: includes/Extensions/GlobalFields.php:1296
#: includes/Extensions/GlobalFields.php:1350
#: includes/Extensions/GlobalFields.php:1404
msgid " "
msgstr ""

#: includes/Extensions/GlobalFields.php:1148
#: includes/Extensions/GlobalFields.php:1203
#: includes/Extensions/GlobalFields.php:1257
#: includes/Extensions/GlobalFields.php:1311
#: includes/Extensions/GlobalFields.php:1365
#: includes/Extensions/GlobalFields.php:1419
msgid "Edit Category "
msgstr ""

#: includes/Extensions/GlobalFields.php:1172
#: includes/FrontEnd/FrontEnd.php:92
msgid "Functional"
msgstr ""

#: includes/Extensions/GlobalFields.php:1280
#: includes/FrontEnd/FrontEnd.php:100
msgid "Performance"
msgstr ""

#: includes/Extensions/GlobalFields.php:1334
#: includes/FrontEnd/FrontEnd.php:104
msgid "Advertisement"
msgstr ""

#: includes/Extensions/GlobalFields.php:1388
#: includes/FrontEnd/FrontEnd.php:108
msgid "Uncategorized"
msgstr ""

#: includes/Extensions/GlobalFields.php:1448
msgid "Display"
msgstr ""

#: includes/Extensions/GlobalFields.php:1476
msgid "IMAGE"
msgstr ""

#: includes/Extensions/GlobalFields.php:1484
msgid "Show Default Image"
msgstr ""

#: includes/Extensions/GlobalFields.php:1490
msgid "Choose an Image"
msgstr ""

#: includes/Extensions/GlobalFields.php:1494
msgid "If checked, this will show in notifications."
msgstr ""

#: includes/Extensions/GlobalFields.php:1502
msgid "Verified"
msgstr ""

#: includes/Extensions/GlobalFields.php:1507
msgid "Flames"
msgstr ""

#: includes/Extensions/GlobalFields.php:1512
msgid "Flames GIF"
msgstr ""

#: includes/Extensions/GlobalFields.php:1517
msgid "Pink Face"
msgstr ""

#: includes/Extensions/GlobalFields.php:1522
msgid "Blue Face"
msgstr ""

#: includes/Extensions/GlobalFields.php:1529
msgid "Upload an Image"
msgstr ""

#: includes/Extensions/GlobalFields.php:1531
#: includes/Extensions/PressBar/PressBar.php:518
#: assets/admin/js/admin.js:18
msgid "Upload"
msgstr ""

#: includes/Extensions/GlobalFields.php:1537
#: includes/Extensions/OfferAnnouncement/Announcements.php:243
msgid "Image"
msgstr ""

#: includes/Extensions/GlobalFields.php:1578
msgid "Featured Image"
msgstr ""

#: includes/Extensions/GlobalFields.php:1611
msgid "Gravatar"
msgstr ""

#: includes/Extensions/GlobalFields.php:1649
#: includes/Types/GDPR.php:237
msgid "Visibility"
msgstr ""

#: includes/Extensions/GlobalFields.php:1654
#: includes/Types/GDPR.php:244
msgid "Show On"
msgstr ""

#: includes/Extensions/GlobalFields.php:1660
#: includes/Types/GDPR.php:250
msgid "Show Everywhere"
msgstr ""

#: includes/Extensions/GlobalFields.php:1661
msgid "Show On Selected"
msgstr ""

#: includes/Extensions/GlobalFields.php:1662
msgid "Hide On Selected"
msgstr ""

#: includes/Extensions/GlobalFields.php:1666
msgid "Locations"
msgstr ""

#: includes/Extensions/GlobalFields.php:1679
#: includes/Extensions/GlobalFields.php:2129
#: includes/Types/GDPR.php:257
msgid "Display For"
msgstr ""

#: includes/Extensions/GlobalFields.php:1686
msgid "Logged Out User"
msgstr ""

#: includes/Extensions/GlobalFields.php:1687
msgid "Logged In User"
msgstr ""

#: includes/Extensions/GlobalFields.php:1689
msgid "More Control in Pro"
msgstr ""

#: includes/Extensions/GlobalFields.php:1696
#: includes/Types/GDPR.php:440
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
#: nxdev/notificationx/frontend/gdpr/utils/GdprActions.jsx:141
msgid "Customize"
msgstr ""

#: includes/Extensions/GlobalFields.php:1723
#: includes/Extensions/GlobalFields.php:1808
msgid "Appearance"
msgstr ""

#: includes/Extensions/GlobalFields.php:1745
msgid "Close Icon Position"
msgstr ""

#: includes/Extensions/GlobalFields.php:1752
msgid "Top Right"
msgstr ""

#: includes/Extensions/GlobalFields.php:1756
msgid "Top Left"
msgstr ""

#: includes/Extensions/GlobalFields.php:1763
#: includes/Extensions/GlobalFields.php:1830
msgid "Notification Size"
msgstr ""

#: includes/Extensions/GlobalFields.php:1787
#: includes/Extensions/GlobalFields.php:1854
msgid "Set a max width for notification."
msgstr ""

#: includes/Extensions/GlobalFields.php:1790
msgid "Display Close Option"
msgstr ""

#: includes/Extensions/GlobalFields.php:1795
msgid "Display a close button."
msgstr ""

#: includes/Extensions/GlobalFields.php:1798
msgid "Mobile Visibility"
msgstr ""

#: includes/Extensions/GlobalFields.php:1803
msgid "Hide NotificationX on mobile."
msgstr ""

#: includes/Extensions/GlobalFields.php:1857
msgid "Display Close Button"
msgstr ""

#: includes/Extensions/GlobalFields.php:1866
#: includes/Extensions/GlobalFields.php:1894
msgid "Desktop"
msgstr ""

#: includes/Extensions/GlobalFields.php:1873
#: includes/Extensions/GlobalFields.php:1901
msgid "Tablet"
msgstr ""

#: includes/Extensions/GlobalFields.php:1880
#: includes/Extensions/GlobalFields.php:1908
msgid "Mobile"
msgstr ""

#: includes/Extensions/GlobalFields.php:1885
msgid "Notification Visibility"
msgstr ""

#: includes/Extensions/GlobalFields.php:1915
msgid "Animation"
msgstr ""

#: includes/Extensions/GlobalFields.php:1921
msgid "Notification Show"
msgstr ""

#: includes/Extensions/GlobalFields.php:1929
#: includes/Extensions/GlobalFields.php:2009
msgid "Default"
msgstr ""

#: includes/Extensions/GlobalFields.php:1934
msgid "Fade In"
msgstr ""

#: includes/Extensions/GlobalFields.php:1939
msgid "Fade In Up"
msgstr ""

#: includes/Extensions/GlobalFields.php:1944
msgid "Fade In Down"
msgstr ""

#: includes/Extensions/GlobalFields.php:1949
msgid "Fade In Down Big"
msgstr ""

#: includes/Extensions/GlobalFields.php:1954
msgid "Fade In Left"
msgstr ""

#: includes/Extensions/GlobalFields.php:1959
msgid "Fade In Right"
msgstr ""

#: includes/Extensions/GlobalFields.php:1964
msgid "Light Speed In Left"
msgstr ""

#: includes/Extensions/GlobalFields.php:1969
msgid "Light Speed In Right"
msgstr ""

#: includes/Extensions/GlobalFields.php:1974
msgid "Zoom In"
msgstr ""

#: includes/Extensions/GlobalFields.php:1979
msgid "Slide In Up"
msgstr ""

#: includes/Extensions/GlobalFields.php:1984
msgid "Slide In Left"
msgstr ""

#: includes/Extensions/GlobalFields.php:1989
msgid "Slide In Right"
msgstr ""

#: includes/Extensions/GlobalFields.php:1994
msgid "Slide In Down"
msgstr ""

#: includes/Extensions/GlobalFields.php:2001
msgid "Notification Hide"
msgstr ""

#: includes/Extensions/GlobalFields.php:2013
msgid "Fade Out"
msgstr ""

#: includes/Extensions/GlobalFields.php:2018
msgid "Fade Out Down"
msgstr ""

#: includes/Extensions/GlobalFields.php:2023
msgid "Fade Out Right"
msgstr ""

#: includes/Extensions/GlobalFields.php:2028
msgid "Fade Out Up"
msgstr ""

#: includes/Extensions/GlobalFields.php:2033
msgid "Light Speed Out Left"
msgstr ""

#: includes/Extensions/GlobalFields.php:2038
msgid "Zoom Out"
msgstr ""

#: includes/Extensions/GlobalFields.php:2043
msgid "Slide Out Down"
msgstr ""

#: includes/Extensions/GlobalFields.php:2048
msgid "Slide Out Left"
msgstr ""

#: includes/Extensions/GlobalFields.php:2053
msgid "Slide Out Right"
msgstr ""

#: includes/Extensions/GlobalFields.php:2058
msgid "Slide Out Up"
msgstr ""

#: includes/Extensions/GlobalFields.php:2090
msgid "Queue Management"
msgstr ""

#: includes/Extensions/GlobalFields.php:2098
msgid "Enable Global Queue"
msgstr ""

#: includes/Extensions/GlobalFields.php:2104
msgid "Activate global queue system for this notification."
msgstr ""

#: includes/Extensions/GlobalFields.php:2104
msgid "Check out this doc."
msgstr ""

#: includes/Extensions/GlobalFields.php:2109
msgid "Timing"
msgstr ""

#: includes/Extensions/GlobalFields.php:2117
msgid "Delay Before First Notification"
msgstr ""

#: includes/Extensions/GlobalFields.php:2122
#: includes/Extensions/PressBar/PressBar.php:1241
#: includes/Extensions/PressBar/PressBar.php:1246
#: includes/Types/GDPR.php:275
msgid "Initial Delay"
msgstr ""

#: includes/Extensions/GlobalFields.php:2123
#: includes/Extensions/GlobalFields.php:2130
#: includes/Extensions/GlobalFields.php:2139
#: includes/Extensions/PressBar/PressBar.php:1188
#: includes/Extensions/PressBar/PressBar.php:1247
msgid "seconds"
msgstr ""

#: includes/Extensions/GlobalFields.php:2131
msgid "Display each notification for * seconds"
msgstr ""

#: includes/Extensions/GlobalFields.php:2138
msgid "Delay Between"
msgstr ""

#: includes/Extensions/GlobalFields.php:2140
msgid "Delay between each notification"
msgstr ""

#: includes/Extensions/GlobalFields.php:2147
msgid "Behavior"
msgstr ""

#: includes/Extensions/GlobalFields.php:2156
msgid "Display The Last"
msgstr ""

#: includes/Extensions/GlobalFields.php:2165
msgid "Display From The Last"
msgstr ""

#: includes/Extensions/GlobalFields.php:2179
msgid "Hours"
msgstr ""

#: includes/Extensions/GlobalFields.php:2188
msgid "Minutes"
msgstr ""

#: includes/Extensions/GlobalFields.php:2201
msgid "Loop Notification"
msgstr ""

#: includes/Extensions/GlobalFields.php:2209
msgid "Open Link In New Tab"
msgstr ""

#: includes/Extensions/GlobalFields.php:2254
msgid "Full Name"
msgstr ""

#: includes/Extensions/GlobalFields.php:2255
msgid "First Name"
msgstr ""

#: includes/Extensions/GlobalFields.php:2256
msgid "Last Name"
msgstr ""

#: includes/Extensions/GlobalFields.php:2259
msgid "Display Name"
msgstr ""

#: includes/Extensions/GlobalFields.php:2265
#: includes/Extensions/Google/GoogleReviews.php:148
#: includes/Extensions/OfferAnnouncement/Announcements.php:238
#: includes/Extensions/ReviewX/ReviewX.php:163
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:157
#: includes/Extensions/WooCommerce/WOOReviews.php:146
#: includes/Types/Comments.php:218
#: includes/Types/Comments.php:235
#: includes/Types/ContactForm.php:145
#: includes/Types/Conversions.php:213
#: includes/Types/Donations.php:177
#: includes/Types/ELearning.php:188
#: includes/Types/EmailSubscription.php:127
#: includes/Types/Reviews.php:209
#: includes/Types/WooCommerceSales.php:210
msgid "Definite Time"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:46
#: includes/Extensions/Google/GoogleReviews.php:47
msgid "Google Reviews"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:56
#: includes/Extensions/Google/GoogleReviews.php:127
#: includes/Extensions/ReviewX/ReviewX.php:81
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:74
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:192
#: includes/Extensions/WooCommerce/WOOReviews.php:64
#: includes/Types/Reviews.php:64
#: includes/Types/Reviews.php:139
msgid "people rated"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:58
#: includes/Extensions/Google/GoogleReviews.php:72
#: includes/Extensions/Google/GoogleReviews.php:86
#: includes/Extensions/Google/GoogleReviews.php:100
#: includes/Extensions/Google/GoogleReviews.php:114
#: includes/Extensions/Google/GoogleReviews.php:129
msgid "Anonymous Place"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:70
#: includes/Extensions/Google/GoogleReviews.php:84
#: includes/Extensions/Google/GoogleReviews.php:98
#: includes/Extensions/Google/GoogleReviews.php:112
#: includes/Extensions/ReviewX/ReviewX.php:93
#: includes/Extensions/ReviewX/ReviewX.php:120
#: includes/Extensions/ReviewX/ReviewX.php:131
#: includes/Extensions/ReviewX/ReviewX.php:143
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:86
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:113
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:124
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:136
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:202
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:212
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:222
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:232
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:242
#: includes/Extensions/WooCommerce/WOOReviews.php:76
#: includes/Extensions/WooCommerce/WOOReviews.php:103
#: includes/Extensions/WooCommerce/WOOReviews.php:114
#: includes/Extensions/WooCommerce/WOOReviews.php:126
#: includes/Types/Reviews.php:76
#: includes/Types/Reviews.php:103
#: includes/Types/Reviews.php:114
#: includes/Types/Reviews.php:126
#: includes/Types/Reviews.php:149
#: includes/Types/Reviews.php:159
#: includes/Types/Reviews.php:169
#: includes/Types/Reviews.php:179
#: includes/Types/Reviews.php:189
msgid "just reviewed"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:139
#: includes/Extensions/ReviewX/ReviewX.php:153
#: includes/Extensions/ReviewX/ReviewX.php:175
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:147
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:169
#: includes/Extensions/WooCommerce/WOOReviews.php:136
#: includes/Extensions/WooCommerce/WOOReviews.php:158
#: includes/Types/Reviews.php:199
#: includes/Types/Reviews.php:221
msgid "Username"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:140
#: includes/Extensions/ReviewX/ReviewX.php:154
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:148
#: includes/Extensions/WooCommerce/WOOReviews.php:137
#: includes/Types/Reviews.php:200
msgid "Rated"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:143
msgid "Place Name"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:144
#: includes/Extensions/ReviewX/ReviewX.php:158
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:152
#: includes/Extensions/WooCommerce/WOOReviews.php:141
#: includes/Types/Reviews.php:204
msgid "Review"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:147
#: includes/Extensions/ReviewX/ReviewX.php:162
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:156
#: includes/Extensions/WooCommerce/WOOReviews.php:145
#: includes/Types/Reviews.php:208
msgid "Rating"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:161
#: includes/Extensions/Google/YouTube.php:254
msgid ""
"<a href='https://notificationx.com/docs/google-reviews-with-notificationx/\n"
"            ' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:164
#: includes/Extensions/Google/YouTube.php:257
msgid ""
"\n"
"                <span>Google reviews provide helpful information and make your business stand out.</span>\n"
"            "
msgstr ""

#: includes/Extensions/Google/GoogleReviews.php:185
msgid ""
"<p>Make sure that you have configured your <a target=\"_blank\" href=\"%1$s\">Google Reviews API</a> key, to showcase your reviews. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\n"
"\t\t<p>👉NotificationX <a target=\"_blank\" href=\"%3$s\">Integration with Google Reviews</a>.</p>"
msgstr ""

#: includes/Extensions/Google/Google_Analytics.php:50
#: includes/Extensions/Google/Google_Analytics.php:51
msgid "Google Analytics"
msgstr ""

#: includes/Extensions/Google/Google_Analytics.php:65
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">signed in to Google Analytics site</a>, to use its campaign & page analytics data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with Google Analytics</a></p>"
msgstr ""

#: includes/Extensions/Google/YouTube.php:52
#: includes/Extensions/Google/YouTube.php:53
msgid "YouTube"
msgstr ""

#: includes/Extensions/Google/YouTube.php:60
#: includes/Extensions/Google/YouTube.php:85
msgid "Follow "
msgstr ""

#: includes/Extensions/Google/YouTube.php:62
#: includes/Extensions/Google/YouTube.php:87
#: includes/Extensions/ReviewX/ReviewX.php:159
#: includes/Extensions/ReviewX/ReviewX.php:179
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:153
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:173
#: includes/Extensions/WooCommerce/WOOReviews.php:142
#: includes/Extensions/WooCommerce/WOOReviews.php:162
#: includes/FrontEnd/FrontEnd.php:271
#: includes/Types/Donations.php:54
#: includes/Types/EmailSubscription.php:68
#: includes/Types/Reviews.php:205
#: includes/Types/Reviews.php:225
msgid "Anonymous Title"
msgstr ""

#: includes/Extensions/Google/YouTube.php:63
#: includes/Extensions/Google/YouTube.php:88
msgid "YouTube Channel"
msgstr ""

#: includes/Extensions/Google/YouTube.php:66
#: includes/Extensions/Google/YouTube.php:128
#: includes/Extensions/Google/YouTube.php:170
#: assets/admin/js/admin.js:1
msgid "Views"
msgstr ""

#: includes/Extensions/Google/YouTube.php:69
msgid "Videos"
msgstr ""

#: includes/Extensions/Google/YouTube.php:76
#: includes/Extensions/Google/YouTube.php:99
msgid "Subscribe Now"
msgstr ""

#: includes/Extensions/Google/YouTube.php:106
#: includes/Extensions/Google/YouTube.php:125
#: includes/Extensions/Google/YouTube.php:147
#: includes/Extensions/Google/YouTube.php:167
msgid "Check our latest video "
msgstr ""

#: includes/Extensions/Google/YouTube.php:119
#: includes/Extensions/Google/YouTube.php:141
#: includes/Extensions/Google/YouTube.php:160
#: includes/Extensions/Google/YouTube.php:183
msgid "Watch Now"
msgstr ""

#: includes/Extensions/Google/YouTube.php:131
msgid "Likes"
msgstr ""

#: includes/Extensions/Google/YouTube.php:173
msgid "Links"
msgstr ""

#: includes/Extensions/Google/YouTube.php:217
msgid "Channel Title"
msgstr ""

#: includes/Extensions/Google/YouTube.php:224
msgid "Total Videos"
msgstr ""

#: includes/Extensions/Google/YouTube.php:238
msgid "Total Likes"
msgstr ""

#: includes/Extensions/Google/YouTube.php:242
msgid "Total Comments"
msgstr ""

#: includes/Extensions/Google/YouTube.php:284
msgid ""
"<p>To create YouTube notification popups, make sure that you have configured your <a target=\"_blank\" href=\"%1$s\">YouTube API</a> key, Check out our step-by-step documentation for further assistance. <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\n"
"\t\t<p>👉NotificationX <a target=\"_blank\" href=\"%3$s\">Integration with Youtube</a>.</p>"
msgstr ""

#: includes/Extensions/GRVF/GravityForms.php:46
#: includes/Extensions/GRVF/GravityForms.php:47
#: includes/Extensions/GRVF/GravityForms.php:67
msgid "Gravity Forms"
msgstr ""

#: includes/Extensions/GRVF/GravityForms.php:49
msgid "<a href='https://notificationx.com/docs/gravity-forms/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/GRVF/GravityForms.php:51
msgid ""
"\n"
"                <span>A WordPress contact forms plugin that can help you keep important leads and stay in touch with your customers.</span>\n"
"                <iframe id=\"email_subscription_video\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\"\n"
"                src=\"https://www.youtube.com/embed/1Gl3XRd1TxY\">\n"
"                </iframe>\n"
"            "
msgstr ""

#: includes/Extensions/GRVF/GravityForms.php:79
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">Gravity Forms installed & configured</a>, to use its campaign & form subscriptions data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with Ninja Forms</a></p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥Hacks to Increase Your <a target=\"_blank\" href=\"%5$s\">WordPress Contact Forms Submission Rate</a> Using NotificationX</p>"
msgstr ""

#: includes/Extensions/IFTTT/IFTTT.php:41
#: includes/Extensions/IFTTT/IFTTT.php:42
msgid "IFTTT"
msgstr ""

#: includes/Extensions/LearnDash/LearnDash.php:46
#: includes/Extensions/LearnDash/LearnDash.php:47
msgid "LearnDash"
msgstr ""

#: includes/Extensions/LearnDash/LearnDash.php:49
msgid "<a href='https://notificationx.com/docs/how-to-display-learndash-course-enrollment-alert-using-notificationx/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/LearnDash/LearnDash.php:51
msgid ""
"\n"
"                <span>A widely used WordPress learning management system.</span>\n"
"            "
msgstr ""

#: includes/Extensions/LearnDash/LearnDash.php:60
msgid "You have to install <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.learndash.com\">LearnDash</a> plugin first."
msgstr ""

#: includes/Extensions/LearnDash/LearnDash.php:70
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">LearnDash installed & configured</a> to use its campaign & course selling data.  For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with LearnDash</a> </p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 How to Increase Your <a target=\"_blank\" href=\"%5$s\">LearnDash Course Enrollment Rates</a> With NotificationX</p>"
msgstr ""

#: includes/Extensions/LearnDash/LearnDashInline.php:46
#: includes/Extensions/LearnPress/LearnPressInline.php:44
#: includes/Extensions/LearnPress/LearnPressInline.php:58
#: includes/Extensions/Tutor/TutorInline.php:44
#: includes/Extensions/Tutor/TutorInline.php:58
msgid "people enrolled"
msgstr ""

#: includes/Extensions/LearnDash/LearnDashInline.php:60
#: includes/Extensions/LearnPress/LearnPressInline.php:72
#: includes/Extensions/Tutor/TutorInline.php:72
#: includes/Types/ELearning.php:185
#: includes/Types/ELearning.php:201
msgid "Course Title"
msgstr ""

#: includes/Extensions/LearnPress/LearnPress.php:48
#: includes/Extensions/LearnPress/LearnPress.php:49
#: includes/Extensions/LearnPress/LearnPress.php:132
msgid "LearnPress"
msgstr ""

#: includes/Extensions/LearnPress/LearnPress.php:545
msgid ""
"<p>Make sure that you have <a href=\"%1$s\" target=\"_blank\">LearnPress LMS installed & configured</a> to use its campaign & course selling data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 Watch <a target=\"_blank\" href=\"%3$s\">video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with LearnPress LMS</a></p>"
msgstr ""

#: includes/Extensions/MailChimp/MailChimp.php:43
#: includes/Extensions/MailChimp/MailChimp.php:44
msgid "MailChimp"
msgstr ""

#: includes/Extensions/MailChimp/MailChimp.php:58
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">signed in & retrieved API key from MailChimp account</a> to use its campaign & email subscriptions data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with MailChimp</a></p>\n"
"\t\t<p><strong>Recommended Blogs:</strong></p>\n"
"\t\t<p>🔥 How To Improve Your <a target=\"_blank\" href=\"%5$s\">Email Marketing Strategy</a> With Social Proof</p>\n"
"\t\t<p>🚀 Hacks To Grow Your <a target=\"_blank\" href=\"%6$s\">Email Subscription List</a> On WordPress Website</p>"
msgstr ""

#: includes/Extensions/NJF/NinjaForms.php:47
#: includes/Extensions/NJF/NinjaForms.php:48
#: includes/Extensions/NJF/NinjaForms.php:92
msgid "Ninja Forms"
msgstr ""

#: includes/Extensions/NJF/NinjaForms.php:404
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">Ninja Forms installed & configured</a> to use its campaign & form subscriptions data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with Ninja Forms</a></p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 Hacks to Increase Your <a target=\"_blank\" href=\"%5$s\">WordPress Contact Forms Submission Rate</a> Using NotificationX</p>"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:47
#: includes/Extensions/OfferAnnouncement/Announcements.php:48
msgid "Discount Announcement"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:55
#: includes/Extensions/OfferAnnouncement/Announcements.php:66
#: includes/Extensions/OfferAnnouncement/Announcements.php:75
#: includes/Extensions/OfferAnnouncement/Announcements.php:86
#: includes/Extensions/OfferAnnouncement/Announcements.php:96
#: includes/Extensions/OfferAnnouncement/Announcements.php:108
#: includes/Extensions/OfferAnnouncement/Announcements.php:179
#: includes/Extensions/OfferAnnouncement/Announcements.php:193
msgid "Flash Sale: Limited Time Offer!"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:57
#: includes/Extensions/OfferAnnouncement/Announcements.php:67
#: includes/Extensions/OfferAnnouncement/Announcements.php:77
#: includes/Extensions/OfferAnnouncement/Announcements.php:87
#: includes/Extensions/OfferAnnouncement/Announcements.php:98
#: includes/Extensions/OfferAnnouncement/Announcements.php:133
#: includes/Extensions/OfferAnnouncement/Announcements.php:181
#: includes/Extensions/OfferAnnouncement/Announcements.php:195
#: includes/Extensions/OfferAnnouncement/Announcements.php:210
msgid "Enjoy flat 50% Off on NotificationX PRO Valid till this week"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:68
#: includes/Extensions/OfferAnnouncement/Announcements.php:88
#: includes/Types/PageAnalytics.php:125
msgid "Grab Now"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:106
#: includes/Types/Conversions.php:118
#: includes/Types/Conversions.php:128
msgid "Buy Now"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:131
#: includes/Extensions/OfferAnnouncement/Announcements.php:142
#: includes/Extensions/OfferAnnouncement/Announcements.php:150
#: includes/Extensions/OfferAnnouncement/Announcements.php:157
#: includes/Extensions/OfferAnnouncement/Announcements.php:208
#: includes/Extensions/OfferAnnouncement/Announcements.php:220
msgid "Hi There!"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:141
msgid "Get It Now"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:152
#: includes/Extensions/OfferAnnouncement/Announcements.php:158
#: includes/Extensions/OfferAnnouncement/Announcements.php:222
msgid "Enjoy flat 50% Off on NotificationX PRO"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:156
msgid "Book Now"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:169
msgid "Flash Sale:"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:231
msgid "Offer Title"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:234
msgid "Offer Description"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:242
msgid "Discount"
msgstr ""

#: includes/Extensions/OfferAnnouncement/Announcements.php:269
msgid ""
"<p>You can showcase the discount alert popup on your WordPress website to make visitors take purchasing action immediately. For further assistance, check out our step-by-step <a target=\"_blank\" href=\"%1$s\">documentation</a>.</p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥Introducing Discount Alert By NotificationX <a target=\"_blank\" href=\"%2$s\">Guide To Notify Customers About On-Sale Products</a> </p>"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:65
msgid "Press Bar"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:79
msgid "<b>We're excited to introduce something new!</b>"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:80
msgid "Show Me!"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:96
#: includes/Extensions/PressBar/PressBar.php:113
msgid "<b>Save Big & Get Lifetime unlimited <strong>NotificationX</strong> for $99</b>"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:97
#: includes/Extensions/PressBar/PressBar.php:1714
msgid "Get Offer"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:114
msgid "Get Offer!"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:124
msgid "<p><span style=\"color: #F54747;\">4 Years</span> Of Seamlessly Creating NotificationX!</p>"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:125
msgid "Grab Deal Now"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:139
msgid "<p><span style=\"color: #fff;\">🎁 Flash 30%</span> Sale is On Now! Don’t miss out on this opportunity</p>"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:146
msgid "Save $20"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:156
msgid "<p><span style=\"color: #000;\">🎁 Flash 30%</span> Sale is On Now! Don’t miss out on this opportunity</p>"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:162
#: includes/Extensions/PressBar/PressBar.php:177
#: includes/Extensions/PressBar/PressBar.php:1740
msgid "Shop Now"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:171
msgid "<p><span style=\"color: #9F7800;\">4 years</span> Of Seamlessly Creating NotificationX!</p>"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:397
msgid "Presets"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:422
msgid "NX Bar"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:444
msgid "Border Radius"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:516
msgid "Background Image"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:538
msgid "Countdown Background Color"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:543
msgid "Countdown Text Color"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:572
#: includes/Extensions/PressBar/PressBar.php:581
msgid "Close Button Position"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:591
#: includes/Extensions/PressBar/PressBar.php:613
msgid "Close Button Position Top"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:602
msgid "Close Button Position Left"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:624
msgid "Close Button Position Right"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:662
msgid "Activate"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:662
msgid "Install"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:717
msgid "Edit With Elementor"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:733
#: includes/Extensions/PressBar/PressBar.php:939
msgid "Remove"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:765
msgid "Build With Elementor"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:825
#: includes/Extensions/PressBar/PressBar.php:1032
msgid "Choose Your "
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:861
msgid "Activate Elementor"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:861
msgid "Install Elementor"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:862
msgid "Activated Elementor"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:862
msgid "Installed Elementor"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:863
msgid "Activating Elementor..."
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:863
msgid "Installing Elementor..."
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:890
msgid "Successfully Activated"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:924
msgid "Edit With Gutenberg"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:972
msgid "Build With Gutenberg"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1088
msgid "Bar Reappearance"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1096
msgid "Don't show the Bar again for the user"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1097
msgid "Show the Bar again when the user visits the website next time"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1098
msgid "Show the Bar when the user refreshes/goes to another page"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1148
msgid "Sticky Bar?"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1153
msgid "If checked, this will fixed Notification Bar at top or bottom."
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1158
msgid "Display Overlapping"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1163
msgid "Show Notification Bar overlapping content instead of pushing."
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1173
msgid "Auto Hide"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1178
msgid "If checked, notification bar will be hidden after the time set below."
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1183
msgid "Hide After"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1189
msgid "Hide after 60 seconds"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1194
msgid "Notification Bar will Appear"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1203
msgid "After a few seconds"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1207
msgid "On Scroll"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1215
msgid "Scroll Offset"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1230
msgid "PX"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1231
msgid "VH"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1232
msgid "%"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1255
msgid "Targeting"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1265
msgid "Country Targeting"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1288
msgid "Show for All Users"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1293
msgid "Set Target Audience"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1320
#: assets/admin/js/admin.js:1
msgid "Schedule"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1328
msgid "Schedule Type"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1336
msgid "Daily"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1341
msgid "Weekly"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1354
#: includes/Extensions/PressBar/PressBar.php:1390
#: includes/Extensions/PressBar/PressBar.php:1417
#: assets/admin/js/admin.js:18
msgid "From"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1363
#: includes/Extensions/PressBar/PressBar.php:1399
#: includes/Extensions/PressBar/PressBar.php:1426
#: assets/admin/js/admin.js:18
msgid "To"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1372
msgid "Select Days"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1382
msgid "Saturday"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1408
msgid "Custom Schedule"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1493
msgid "NotificationX Bar"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1526
msgid "NotificationX Bar (Gutenberg)"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1624
msgid "Text Content"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1638
#: includes/Extensions/PressBar/PressBar.php:1653
msgid "Static Text"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1643
msgid "Slide Multiple Text"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1654
msgid "Write something here..."
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1664
msgid "Sliding Text Items"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1671
msgid "🚀 Supercharge Your Marketing Forever!"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1674
msgid "Get <strong>Lifetime Access to NotificationX</strong> for just <strong>$299</strong>"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1681
msgid "Slide Text"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1693
msgid "Sliding Interval"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1697
msgid "Time interval between slides in milliseconds"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1712
#: includes/Extensions/PressBar/PressBar.php:1802
#: includes/Types/GDPR.php:423
#: includes/Types/GDPR.php:431
#: includes/Types/GDPR.php:439
msgid "Button Text"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1723
msgid "Button URL"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1734
msgid "Button Icon"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1744
msgid "Shop Now White "
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1747
msgid "Select an icon to display before the button text"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1754
msgid "Transition Speed"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1760
msgid "Transition speed in milliseconds"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1767
msgid "Transition Style"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1773
msgid "Slide in Right"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1774
msgid "Slide in Left"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1784
msgid "Coupon"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1796
msgid "Enable Coupon"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1804
#: includes/Extensions/PressBar/PressBar.php:1814
msgid "SAVE20"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1812
msgid "Coupon Code"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1822
msgid "Coupon Copied Text"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1824
msgid "Copied!"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1832
msgid "Coupon Tooltip"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1834
msgid "Use this coupon code to get 20% off"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1842
msgid "Coupon Background Color"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1852
msgid "Coupon Text Color"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1862
msgid "Coupon Border Color"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1878
msgid "Countdown Timer"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1884
msgid "Enable Countdown"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1890
msgid "Evergreen Timer"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1894
msgid "To configure Evergreen Timer"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1899
msgid "Countdown Text"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1901
msgid "Ending in"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1910
msgid "Expired Text"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1912
msgid "Expired"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1922
msgid "Start Date"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1929
msgid "End Date"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1937
msgid "Randomize"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1943
msgid "Time Between"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1949
msgid "Start Time"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1956
msgid "End Time"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1965
msgid "Time Rotation"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1973
msgid "Daily Time Reset"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1979
msgid "Permanent Close"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:1984
msgid "Close After Expire"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:2192
msgid "Install Essential Blocks"
msgstr ""

#: includes/Extensions/PressBar/PressBar.php:2202
msgid ""
"<p>You can showcase the notification bar to run instant popup campaigns on WordPress sites. For further assistance, check out our step-by-step guides on adding notification bars built with both <a target=\"_blank\" href=\"%1$s\">Elementor</a> and <a target=\"_blank\" href=\"%2$s\">Gutenberg</a>.</p>\n"
"\t\t<p>🎦 Watch the <a target = \"_blank\" href = \"%3$s\">video tutorial</a> for a quick guide.</p>\n"
"\t\t<p><strong>Recommended Blog                     : </strong></p>\n"
"\t\t<p>🔥 How to <a target=\"_blank\" href=\"%4$s\">design a Notification Bar with Elementor Page Builder.</a></p>\n"
"\t\t<p>🔥 <a href=\"%5$s\" target=\"_blank\">Evergreen Dynamic Notification Bar</a> to Boost Sales in WordPress.</p>"
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:72
#: includes/Extensions/ReviewX/ReviewX.php:73
#: includes/Extensions/ReviewX/ReviewX.php:212
msgid "ReviewX"
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:105
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:98
#: includes/Extensions/WooCommerce/WOOReviews.php:88
#: includes/Types/Reviews.php:88
msgid "saying"
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:107
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:100
#: includes/Extensions/WooCommerce/WOOReviews.php:90
#: includes/Types/Reviews.php:90
msgid "Excellent"
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:108
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:101
#: includes/Extensions/WooCommerce/WOOReviews.php:91
#: includes/Types/Reviews.php:91
msgid "about"
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:111
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:104
#: includes/Extensions/WooCommerce/WOOReviews.php:94
#: includes/Types/Reviews.php:94
msgid "Try it now"
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:178
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:172
#: includes/Extensions/WooCommerce/WOOReviews.php:161
#: includes/Types/Reviews.php:224
msgid "Review Title"
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:182
#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:176
#: includes/Types/Reviews.php:203
#: includes/Types/Reviews.php:228
msgid "Plugin Name"
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:213
msgid "plugin."
msgstr ""

#: includes/Extensions/ReviewX/ReviewX.php:279
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">WooCommerce</a> & <a target=\"_blank\" href=\"%2$s\">ReviewX</a> installed & activated to use this campaign. For further assistance, check out our step by step <a target=\"_blank\" href=\"%3$s\">documentation</a>.</p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🚀 How to <a target=\"_blank\" href=\"%4$s\">boost WooCommerce Sales</a> Using NotificationX</p>"
msgstr ""

#: includes/Extensions/SureCart/SureCart.php:40
#: includes/Extensions/SureCart/SureCart.php:41
#: includes/Extensions/SureCart/SureCart.php:455
msgid "SureCart"
msgstr ""

#: includes/Extensions/SureCart/SureCart.php:117
msgid "Processing"
msgstr ""

#: includes/Extensions/SureCart/SureCart.php:118
msgid "Unfulfilled"
msgstr ""

#: includes/Extensions/SureCart/SureCart.php:119
msgid "Fulfilled"
msgstr ""

#: includes/Extensions/SureCart/SureCart.php:120
msgid "Shipped"
msgstr ""

#: includes/Extensions/SureCart/SureCart.php:121
msgid "Delivered"
msgstr ""

#: includes/Extensions/SureCart/SureCart.php:122
msgid "Not Shipped"
msgstr ""

#: includes/Extensions/SureCart/SureCart.php:467
msgid ""
"<p>Make sure that you have the <a target=\"_blank\" href=\"%1$s\">SureCart WordPress plugin installed & configured</a> to use its campaign and selling data. For detailed guidelines, check out the step-by-step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"        <a target=\"_blank\" href=\"%3$s\">👉 NotificationX Integration with SureCart</a>"
msgstr ""

#: includes/Extensions/Tutor/Tutor.php:47
msgid "Tutor"
msgstr ""

#: includes/Extensions/Tutor/Tutor.php:48
#: includes/Extensions/Tutor/Tutor.php:131
msgid "Tutor LMS"
msgstr ""

#: includes/Extensions/Tutor/Tutor.php:540
msgid ""
"<p>Make sure that you have <a href=\"%1$s\" target=\"_blank\">Tutor LMS installed & configured</a> to use its campaign & course selling data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 Watch <a target=\"_blank\" href=\"%3$s\">video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with Tutor LMS</a></p>"
msgstr ""

#: includes/Extensions/Vimeo/Vimeo.php:42
msgid "Vimeo"
msgstr ""

#: includes/Extensions/Wistia/Wistia.php:41
msgid "Wistia"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerce.php:54
#: includes/Extensions/WooCommerce/WooCommerce.php:55
#: includes/Extensions/WooCommerce/WooCommerce.php:104
#: includes/Extensions/WooCommerce/WOOReviews.php:55
#: includes/Extensions/WooCommerce/WOOReviews.php:56
#: includes/Extensions/WooCommerce/WOOReviews.php:233
#: includes/Types/WooCommerceSales.php:57
msgid "WooCommerce"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerce.php:57
#: includes/Extensions/WooCommerce/WooCommerce.php:58
msgid "Product Title Raw"
msgstr ""

#. translators: %1$s: title, %2$s: number of product, %3$s: Combine Multi Order Text.
#: includes/Extensions/WooCommerce/WooCommerce.php:480
msgid "%1$s & %2$s %3$s"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerce.php:537
#: includes/Extensions/WooCommerce/WooCommerceSales.php:203
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:167
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">WooCommerce installed & activated</a> to use this campaign. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a href=\"%3$s\" target=\"_blank\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>⭐ NotificationX Integration with WooCommerce</p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🔥 Why NotificationX is The <a target=\"_blank\" href=\"%4$s\">Best FOMO and Social Proof Plugin</a> for WooCommerce?</p>\n"
"\t\t<p>🚀 How to <a target=\"_blank\" href=\"%5$s\">boost WooCommerce Sales</a> Using NotificationX</p>"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:76
#: includes/Extensions/WooCommerce/WooInline.php:80
msgid "99"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:92
#: includes/Extensions/WooCommerce/WooInline.php:96
msgid "Only"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:96
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:141
#: includes/Extensions/WooCommerce/WooInline.php:100
#: includes/Extensions/WooCommerce/WooInline.php:145
msgid "left in stock"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:98
#: includes/Extensions/WooCommerce/WooInline.php:102
msgid "- order soon."
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:109
#: includes/Extensions/WooCommerce/WooInline.php:113
msgid "In high demand - only"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:113
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:142
#: includes/Extensions/WooCommerce/WooInline.php:117
#: includes/Extensions/WooCommerce/WooInline.php:146
msgid "left"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:115
#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:146
#: includes/Extensions/WooCommerce/WooInline.php:119
#: includes/Extensions/WooCommerce/WooInline.php:150
msgid "on our site!"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:138
#: includes/Extensions/WooCommerce/WooInline.php:142
msgid "Stock Count"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:145
#: includes/Extensions/WooCommerce/WooInline.php:149
msgid "order soon."
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:155
#: includes/Types/Inline.php:47
msgid "<a href='https://notificationx.com/growth-alert/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesInline.php:157
msgid ""
"\n"
"                <span>Highlight your sales, low stock updates with inline growth alert to boost sales</span>\n"
"                <iframe id=\"email_subscription_video\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\"\n"
"                src=\"https://www.youtube.com/embed/vXMtBPvizDw\">\n"
"                </iframe>\n"
"            "
msgstr ""

#: includes/Extensions/WooCommerce/WooCommerceSalesReviews.php:251
#: includes/Extensions/WooCommerce/WOOReviews.php:493
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">WooCommerce installed & activated</a> to use this campaign. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 Watch <a target=\"_blank\" href=\"%3$s\">video tutorial</a> to learn quickly</p>\n"
"\t\t<p><strong>Recommended Blog:</strong></p>\n"
"\t\t<p>🚀 How to <a target=\"_blank\" href=\"%4$s\">boost WooCommerce Sales</a> Using NotificationX</p>"
msgstr ""

#: includes/Extensions/WooCommerce/WOOReviews.php:213
msgid "someone"
msgstr ""

#: includes/Extensions/WooCommerce/WOOReviews.php:214
#: includes/Extensions/WordPress/WPOrgReview.php:185
msgid "try it out"
msgstr ""

#: includes/Extensions/WooCommerce/WOOReviews.php:215
#: includes/Extensions/WordPress/WPOrgReview.php:186
msgid "Anonymous"
msgstr ""

#: includes/Extensions/WooCommerce/WOOReviews.php:216
msgid "Some review content"
msgstr ""

#: includes/Extensions/WordPress/Wordpress.php:12
msgid ""
"<p>Make sure that you have a <a target=\"_blank\" href=\"%1$s\">wordpress.org</a> account to use its campaign on blog comments, reviews and download stats data. For further assistance, check out our step by step documentation on <a target=\"_blank\" href=\"%2$s\">comments popup</a>, <a target=\"_blank\" href=\"%3$s\">plugin reviews</a> & <a target=\"_blank\" href=\"%4$s\">downloads stats</a>.</p>\n"
"\t\t<p>🎦 Watch video tutorial on <a target=\"_blank\" href=\"%5$s\">blog comments</a>, <a target=\"_blank\" href=\"%6$s\">reviews</a> & <a target=\"_blank\" href=\"%6$s\">downloads stats</a> to learn quickly</p>\n"
"\t\t<p><strong>Recommended Blogs:</strong></p>\n"
"\t\t<p>🔥 Proven Hacks To <a target=\"_blank\" href=\"%7$s\">Get More Comments on Your WordPress Blog</a> Posts</p>\n"
"\t\t<p>🚀 How To Increase <a target=\"_blank\" href=\"%8$s\">WordPress Plugin Download Rates & Increase Sales</a> in 2023</p>"
msgstr ""

#: includes/Extensions/WordPress/WPComments.php:47
msgid "WP Comments"
msgstr ""

#: includes/Extensions/WordPress/WPComments.php:48
#: includes/Extensions/WordPress/WPOrgReview.php:56
#: includes/Extensions/WordPress/WPOrgStats.php:57
msgid "WordPress"
msgstr ""

#: includes/Extensions/WordPress/WPComments.php:265
#: includes/Types/Comments.php:57
#: includes/Types/Comments.php:70
#: includes/Types/Comments.php:83
#: includes/Types/Comments.php:96
#: includes/Types/Comments.php:109
#: includes/Types/Comments.php:122
#: includes/Types/Comments.php:136
#: includes/Types/Comments.php:150
#: includes/Types/GDPR.php:60
#: includes/Types/GDPR.php:74
#: includes/Types/GDPR.php:88
#: includes/Types/GDPR.php:102
#: includes/Types/GDPR.php:116
#: includes/Types/GDPR.php:130
#: includes/Types/GDPR.php:144
#: includes/Types/GDPR.php:158
#: includes/Types/GDPR.php:172
#: includes/Types/GDPR.php:186
#: includes/Types/GDPR.php:200
#: includes/Types/GDPR.php:214
msgid "Anonymous Post"
msgstr ""

#: includes/Extensions/WordPress/WPComments.php:267
msgid "Some comment"
msgstr ""

#: includes/Extensions/WordPress/WPOrgReview.php:55
msgid "WP.Org Reviews"
msgstr ""

#: includes/Extensions/WordPress/WPOrgReview.php:138
#: includes/Extensions/WordPress/WPOrgStats.php:131
msgid "Product Type"
msgstr ""

#: includes/Extensions/WordPress/WPOrgReview.php:144
#: includes/Extensions/WordPress/WPOrgStats.php:135
msgid "Plugin"
msgstr ""

#: includes/Extensions/WordPress/WPOrgReview.php:150
#: includes/Extensions/WordPress/WPOrgStats.php:144
msgid "Slug"
msgstr ""

#: includes/Extensions/WordPress/WPOrgReview.php:159
#: includes/Extensions/WordPress/WPOrgStats.php:151
msgid "Product Name Length"
msgstr ""

#: includes/Extensions/WordPress/WPOrgReview.php:177
#: includes/Extensions/WordPress/WPOrgStats.php:169
msgid "Set a max content length for product name."
msgstr ""

#: includes/Extensions/WordPress/WPOrgStats.php:56
msgid "WP.Org Stats"
msgstr ""

#: includes/Extensions/WordPress/WPOrgStats.php:136
msgid "Theme"
msgstr ""

#: includes/Extensions/WordPress/WPOrgStats.php:181
#: includes/Extensions/WordPress/WPOrgStats.php:184
msgid "Try It Out"
msgstr ""

#: includes/Extensions/WordPress/WPOrgStats.php:182
#: includes/Types/DownloadStats.php:143
msgid "Get Started for Free."
msgstr ""

#: includes/Extensions/WordPress/WPOrgStats.php:183
#: includes/Types/DownloadStats.php:144
msgid "Why Don't You?"
msgstr ""

#. translators: %s: number of downloads today.
#: includes/Extensions/WordPress/WPOrgStats.php:197
msgid "%s times today"
msgstr ""

#. translators: %s: number of downloads yesterday.
#. translators: %s: number of downloads of all time.
#: includes/Extensions/WordPress/WPOrgStats.php:199
#: includes/Extensions/WordPress/WPOrgStats.php:203
msgid "%s times"
msgstr ""

#. translators: %s: number of downloads in last 7 days.
#: includes/Extensions/WordPress/WPOrgStats.php:201
msgid "%s times in last 7 days"
msgstr ""

#: includes/Extensions/WPF/WPForms.php:49
#: includes/Extensions/WPF/WPForms.php:50
msgid "WPForms"
msgstr ""

#: includes/Extensions/WPF/WPForms.php:92
msgid "WP Forms"
msgstr ""

#: includes/Extensions/WPF/WPForms.php:342
msgid ""
"<p>Make sure that you have <a target=\"_blank\" href=\"%1$s\">WPForms installed & configured</a>  to use its campaign & form subscriptions data. For further assistance, check out our step by step <a target=\"_blank\" href=\"%2$s\">documentation</a>.</p>\n"
"\t\t<p>🎦 <a target=\"_blank\" href=\"%3$s\">Watch video tutorial</a> to learn quickly</p>\n"
"\t\t<p>👉 NotificationX <a target=\"_blank\" href=\"%4$s\">Integration with WPForms</a></p>\n"
"\t\t<p><strong>Recommended Blogs:</strong></p>\n"
"\t\t<p>🔥Hacks to Increase Your <a target=\"_blank\" href=\"%5$s\">WordPress Contact Forms Submission Rate</a> Using NotificationX</p>"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:44
#: includes/Extensions/Zapier/ZapierConversions.php:45
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:44
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:45
#: includes/Extensions/Zapier/ZapierReviews.php:44
#: includes/Extensions/Zapier/ZapierReviews.php:45
msgid "Zapier"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:47
#: includes/Extensions/Zapier/ZapierReviews.php:47
msgid "<a href='https://notificationx.com/docs/zapier-notification-alert/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:49
msgid ""
"\n"
"                <span>A well-known web-based tool to connect with any of your web-based applications & boost productivity.</span>\n"
"                <iframe id=\"email_subscription_video\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\"\n"
"                src=\"https://www.youtube.com/embed/KjdLv5YMByQ\">\n"
"                </iframe>\n"
"            "
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:71
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:61
#: includes/Extensions/Zapier/ZapierReviews.php:68
msgid "Field Name:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:71
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:61
#: includes/Extensions/Zapier/ZapierReviews.php:68
msgid "Field Key"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:72
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:62
msgid "Full Name:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:73
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:63
msgid "First Name:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:74
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:64
msgid "Last Name:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:75
msgid "Sales Count:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:76
msgid "Customer Email:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:77
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:66
msgid "Title, Product Title:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:78
msgid "Anonymous Title, Product:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:79
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:68
#: includes/Extensions/Zapier/ZapierReviews.php:77
msgid "Definite Time:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:80
msgid "Sometime:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:81
msgid "In last 1 day:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:82
msgid "In last 7 days:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:83
msgid "In last 30 days:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:84
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:70
msgid "City:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:85
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:71
msgid "Country:"
msgstr ""

#: includes/Extensions/Zapier/ZapierConversions.php:86
#: includes/Extensions/Zapier/ZapierEmailSubscription.php:72
msgid "City,Country:"
msgstr ""

#: includes/Extensions/Zapier/ZapierEmailSubscription.php:65
#: includes/Extensions/Zapier/ZapierReviews.php:70
msgid "Email:"
msgstr ""

#: includes/Extensions/Zapier/ZapierEmailSubscription.php:67
#: includes/Extensions/Zapier/ZapierReviews.php:75
msgid "Anonymous Title:"
msgstr ""

#: includes/Extensions/Zapier/ZapierEmailSubscription.php:69
#: includes/Extensions/Zapier/ZapierReviews.php:78
msgid "Some time ago:"
msgstr ""

#: includes/Extensions/Zapier/ZapierReviews.php:49
msgid ""
"\n"
"                <span>Display review alerts from popular social media networks & encourage visitors to place trust in your business.</span>\n"
"            "
msgstr ""

#: includes/Extensions/Zapier/ZapierReviews.php:69
msgid "Username:"
msgstr ""

#: includes/Extensions/Zapier/ZapierReviews.php:71
msgid "Rated:"
msgstr ""

#: includes/Extensions/Zapier/ZapierReviews.php:72
msgid "Plugin Name:"
msgstr ""

#: includes/Extensions/Zapier/ZapierReviews.php:73
msgid "Plugin Review:"
msgstr ""

#: includes/Extensions/Zapier/ZapierReviews.php:74
msgid "Review Title:"
msgstr ""

#: includes/Extensions/Zapier/ZapierReviews.php:76
msgid "Rating:"
msgstr ""

#: includes/FrontEnd/FrontEnd.php:89
msgid "Necessary cookies are needed to ensure the basic functions of this site, like allowing secure log-ins and managing your consent settings. These cookies do not collect any personal information."
msgstr ""

#: includes/FrontEnd/FrontEnd.php:93
msgid "Functional cookies assist in performing tasks like sharing website content on social media, collecting feedback, and enabling other third-party features."
msgstr ""

#: includes/FrontEnd/FrontEnd.php:97
msgid "Analytical cookies help us understand how visitors use the website. They provide data on metrics like the number of visitors, bounce rate, traffic sources etc."
msgstr ""

#: includes/FrontEnd/FrontEnd.php:101
msgid "Performance cookies help analyze the website's key performance indicators, which in turn helps improve the user experience for visitors."
msgstr ""

#: includes/FrontEnd/FrontEnd.php:105
msgid "Advertisement cookies help analyze the website's key advertising indicators, which in turn helps improve the user experience for visitors."
msgstr ""

#: includes/FrontEnd/FrontEnd.php:109
msgid "Uncategorized cookies are those that don't fall into any specific category but may still be used for various purposes on the site. These cookies help us improve user experience by tracking interactions that don't fit into other cookie types."
msgstr ""

#: includes/FrontEnd/Preview.php:367
msgid "Talk to Support"
msgstr ""

#: includes/NotificationX.php:172
#: assets/admin/js/admin.js:11
msgid "Settings"
msgstr ""

#: includes/NotificationX.php:177
msgid "Go Pro"
msgstr ""

#: includes/Types/Comments.php:55
#: includes/Types/Comments.php:68
#: includes/Types/Comments.php:81
#: includes/Types/Comments.php:94
#: includes/Types/Comments.php:107
#: includes/Types/Comments.php:120
#: includes/Types/Comments.php:134
#: includes/Types/Comments.php:148
#: includes/Types/GDPR.php:58
#: includes/Types/GDPR.php:72
#: includes/Types/GDPR.php:86
#: includes/Types/GDPR.php:100
#: includes/Types/GDPR.php:114
#: includes/Types/GDPR.php:128
#: includes/Types/GDPR.php:142
#: includes/Types/GDPR.php:156
#: includes/Types/GDPR.php:170
#: includes/Types/GDPR.php:184
#: includes/Types/GDPR.php:198
#: includes/Types/GDPR.php:212
msgid "commented on"
msgstr ""

#: includes/Types/Comments.php:215
#: includes/Types/Comments.php:231
msgid "Post Title"
msgstr ""

#: includes/Types/Comments.php:232
msgid "Post Comment"
msgstr ""

#: includes/Types/Comments.php:265
#: includes/Types/OfferAnnouncement.php:79
msgid "Comment URL"
msgstr ""

#: includes/Types/ContactForm.php:61
#: includes/Types/ContactForm.php:74
#: includes/Types/ContactForm.php:87
msgid "recently contacted via"
msgstr ""

#: includes/Types/ContactForm.php:102
#: includes/Types/ContactForm.php:113
#: includes/Types/ContactForm.php:124
msgid "just contacted via"
msgstr ""

#: includes/Types/ContactForm.php:135
msgid "Select A Tag"
msgstr ""

#: includes/Types/ContactForm.php:141
msgid "Form Title"
msgstr ""

#: includes/Types/ContactForm.php:230
msgid "Select a Form"
msgstr ""

#: includes/Types/Conversions.php:66
#: includes/Types/WooCommerceSales.php:64
msgid "just purchased"
msgstr ""

#: includes/Types/Donations.php:46
msgid "Donations"
msgstr ""

#: includes/Types/Donations.php:50
msgid "recently donated for"
msgstr ""

#: includes/Types/Donations.php:52
msgid "100"
msgstr ""

#: includes/Types/Donations.php:169
msgid "Donation Amount"
msgstr ""

#: includes/Types/Donations.php:173
#: includes/Types/Donations.php:192
msgid "Donation For Title"
msgstr ""

#: includes/Types/Donations.php:226
msgid "Donation Form Page"
msgstr ""

#: includes/Types/DownloadStats.php:57
#: includes/Types/DownloadStats.php:70
#: includes/Types/DownloadStats.php:94
msgid "has been downloaded"
msgstr ""

#: includes/Types/DownloadStats.php:83
msgid "people are actively using"
msgstr ""

#: includes/Types/DownloadStats.php:131
#: includes/Types/DownloadStats.php:161
msgid "Plugin/Theme Name"
msgstr ""

#: includes/Types/DownloadStats.php:135
#: includes/Types/DownloadStats.php:155
msgid "Today"
msgstr ""

#: includes/Types/DownloadStats.php:137
#: includes/Types/DownloadStats.php:157
msgid "Total"
msgstr ""

#: includes/Types/DownloadStats.php:138
#: includes/Types/DownloadStats.php:158
msgid "Total Active Install"
msgstr ""

#: includes/Types/DownloadStats.php:142
msgid "Try it out"
msgstr ""

#: includes/Types/DownloadStats.php:145
msgid "in total active"
msgstr ""

#: includes/Types/ELearning.php:59
msgid "just enrolled"
msgstr ""

#: includes/Types/ELearning.php:61
#: includes/Types/ELearning.php:74
#: includes/Types/ELearning.php:87
msgid "Anonymous Course"
msgstr ""

#: includes/Types/ELearning.php:72
#: includes/Types/ELearning.php:85
msgid "recently enrolled"
msgstr ""

#: includes/Types/ELearning.php:235
msgid "Show Notification Of"
msgstr ""

#: includes/Types/ELearning.php:241
msgid "By Course"
msgstr ""

#: includes/Types/ELearning.php:248
msgid "Select Course"
msgstr ""

#: includes/Types/ELearning.php:285
msgid "Course Page"
msgstr ""

#: includes/Types/EmailSubscription.php:51
msgid "Email Subscription"
msgstr ""

#: includes/Types/EmailSubscription.php:53
msgid "<a href='https://notificationx.com/docs/mailchimp-email-subscription-alert/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Types/EmailSubscription.php:55
msgid ""
"\n"
"                <span>Show popups to display which users subscribed to your Newsletter.</span>\n"
"                <video id=\"pro_alert_video_popup\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\" autoplay loop muted>\n"
"                    <source src=\"https://notificationx.com/wp-content/uploads/2024/01/How-to-Display-Email-Subscription-Alerts-using-NotificationX.mp4\" type=\"video/mp4\">\n"
"                </video>\n"
"            "
msgstr ""

#: includes/Types/EmailSubscription.php:66
msgid "just subscribed to"
msgstr ""

#: includes/Types/EmailSubscription.php:123
msgid "List Title"
msgstr ""

#: includes/Types/FlashingTab.php:46
msgid "<a href='https://notificationx.com/flashing-tab/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Types/FlashingTab.php:48
msgid ""
"\n"
"                <span>Revive lost visitors and convert them into customers with captivating Flashing Tab alerts.</span>\n"
"                <video id=\"pro_alert_video_popup\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\" autoplay loop muted>\n"
"                    <source src=\"https://notificationx.com/wp-content/uploads/2024/01/How-To-Configure-Flashing-Tab-Alert-With-NotificationX.mp4\" type=\"video/mp4\">\n"
"                </video>\n"
"            "
msgstr ""

#: includes/Types/GDPR.php:270
msgid "Delay Before Appearance"
msgstr ""

#: includes/Types/GDPR.php:274
msgid "Seconds"
msgstr ""

#: includes/Types/GDPR.php:293
#: includes/Types/GDPR.php:296
#: includes/Types/GDPR.php:403
msgid "Title"
msgstr ""

#: includes/Types/GDPR.php:297
msgid "Customized Cookie Preferences"
msgstr ""

#: includes/Types/GDPR.php:300
#: includes/Types/GDPR.php:303
msgid "Privacy Overview"
msgstr ""

#: includes/Types/GDPR.php:304
msgid "We use cookies and similar technologies to enhance your experience and analyze site usage. Manage your preferences to control which data is collected."
msgstr ""

#: includes/Types/GDPR.php:307
msgid "Show Google Privacy Policy"
msgstr ""

#: includes/Types/GDPR.php:311
msgid "If you use services offered by Google, such as AdSense, Firebase, and Analytics on your website, the Digital Markets Act (DMA) requires you to display Google's Privacy Policy on the second layer of your banner."
msgstr ""

#: includes/Types/GDPR.php:314
#: includes/Types/GDPR.php:317
#: includes/Types/GDPR.php:411
#: includes/Types/GDPR.php:416
msgid "Message"
msgstr ""

#: includes/Types/GDPR.php:318
msgid "To learn more about how Google's third-party cookies function and manage your data, read from here."
msgstr ""

#: includes/Types/GDPR.php:324
msgid "Link text"
msgstr ""

#: includes/Types/GDPR.php:327
#: includes/Types/GDPR.php:328
msgid "Google Privacy Policy"
msgstr ""

#: includes/Types/GDPR.php:334
msgid "URL"
msgstr ""

#: includes/Types/GDPR.php:337
msgid "Google Privacy Policy URL"
msgstr ""

#: includes/Types/GDPR.php:344
msgid "Save My Preferences Button"
msgstr ""

#: includes/Types/GDPR.php:347
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Save My Preferences"
msgstr ""

#: includes/Types/GDPR.php:348
#: includes/Types/GDPR.php:355
#: includes/Types/GDPR.php:362
msgid "Button text"
msgstr ""

#: includes/Types/GDPR.php:351
msgid "See more Button"
msgstr ""

#: includes/Types/GDPR.php:354
msgid "See more"
msgstr ""

#: includes/Types/GDPR.php:358
msgid "See less Button"
msgstr ""

#: includes/Types/GDPR.php:361
msgid "See less"
msgstr ""

#: includes/Types/GDPR.php:375
msgid "Show Cookie List"
msgstr ""

#: includes/Types/GDPR.php:381
msgid "Enabled Label"
msgstr ""

#: includes/Types/GDPR.php:385
#: includes/Types/GDPR.php:391
msgid "Label text"
msgstr ""

#: includes/Types/GDPR.php:388
msgid "No Cookies Available Label"
msgstr ""

#: includes/Types/GDPR.php:392
msgid "No Cookies Available"
msgstr ""

#: includes/Types/GDPR.php:407
msgid "We value your privacy"
msgstr ""

#: includes/Types/GDPR.php:408
msgid "Cookie Notice Title"
msgstr ""

#: includes/Types/GDPR.php:415
msgid "We use cookies to improve your experience. By continuing to use our site, you agree to our use of cookies and data collection. You can learn more in our Privacy Policy and change your preferences anytime."
msgstr ""

#: includes/Types/GDPR.php:419
msgid "Accept All Button"
msgstr ""

#: includes/Types/GDPR.php:424
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
#: nxdev/notificationx/frontend/gdpr/utils/GdprActions.jsx:133
msgid "Accept All"
msgstr ""

#: includes/Types/GDPR.php:427
msgid "Reject All Button"
msgstr ""

#: includes/Types/GDPR.php:432
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
#: nxdev/notificationx/frontend/gdpr/utils/GdprActions.jsx:152
msgid "Reject All"
msgstr ""

#: includes/Types/GDPR.php:443
msgid "Cookies Policy Link"
msgstr ""

#: includes/Types/GDPR.php:450
msgid "Cookies Policy Link Text"
msgstr ""

#: includes/Types/GDPR.php:454
msgid "Link Text"
msgstr ""

#: includes/Types/GDPR.php:455
msgid "Cookies Policy"
msgstr ""

#: includes/Types/GDPR.php:461
#: includes/Types/GDPR.php:465
msgid "Cookies Policy URL"
msgstr ""

#: includes/Types/GDPR.php:471
msgid "Custom Logo"
msgstr ""

#: includes/Types/Inline.php:44
msgid "Growth Alert 🚀"
msgstr ""

#: includes/Types/Inline.php:49
msgid ""
"\n"
"                <span>Highlight your sales, low stock updates with inline growth alert to boost sales</span>\n"
"                <video id=\"pro_alert_video_popup\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\" autoplay loop muted>\n"
"                    <source src=\"https://notificationx.com/wp-content/uploads/2024/01/Introducing-Growth-Alert-Instant-Sales-Booster-With-NotificationX.mp4\" type=\"video/mp4\">\n"
"                </video>\n"
"            "
msgstr ""

#: includes/Types/OfferAnnouncement.php:47
msgid "Discount Alert"
msgstr ""

#: includes/Types/OfferAnnouncement.php:49
msgid "<a href='https://notificationx.com/docs/configure-discount-alert/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Types/OfferAnnouncement.php:51
msgid ""
"\n"
"                <span>Discount Alert by NotificationX will allow you to display offers/discounts of your products/services on your website interactively & easily.</span>\n"
"                <video id=\"pro_alert_video_popup\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\" autoplay loop muted>\n"
"                    <source src=\"https://notificationx.com/wp-content/uploads/2024/01/NX-Discount-Alert-1.mp4\" type=\"video/mp4\">\n"
"                </video>\n"
"            "
msgstr ""

#: includes/Types/PageAnalytics.php:45
msgid "Page Analytics"
msgstr ""

#: includes/Types/PageAnalytics.php:47
msgid "<a href='https://notificationx.com/docs/google-analytics/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Types/PageAnalytics.php:49
msgid ""
"\n"
"                <span>Connect Google Analytics to display the total number of real-time site visitors</span>\n"
"                <video id=\"pro_alert_video_popup\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\" autoplay loop muted>\n"
"                    <source src=\"https://notificationx.com/wp-content/uploads/2024/01/Google-Analytics-Integration-With-NotificationX-How-To-Show-Active-Users-Traffic-in-WordPress.mp4\" type=\"video/mp4\">\n"
"                </video>\n"
"            "
msgstr ""

#: includes/Types/PageAnalytics.php:61
msgid "marketers"
msgstr ""

#: includes/Types/PageAnalytics.php:63
msgid "Surfed this page"
msgstr ""

#: includes/Types/PageAnalytics.php:64
#: includes/Types/PageAnalytics.php:82
msgid "in last "
msgstr ""

#: includes/Types/PageAnalytics.php:65
#: includes/Types/PageAnalytics.php:102
#: includes/Types/PageAnalytics.php:121
msgid "30"
msgstr ""

#: includes/Types/PageAnalytics.php:79
msgid "people visited"
msgstr ""

#: includes/Types/PageAnalytics.php:81
msgid "this page"
msgstr ""

#: includes/Types/PageAnalytics.php:83
msgid "1"
msgstr ""

#: includes/Types/PageAnalytics.php:97
msgid "people looking"
msgstr ""

#: includes/Types/PageAnalytics.php:99
msgid "this deal"
msgstr ""

#: includes/Types/PageAnalytics.php:100
msgid "right now"
msgstr ""

#: includes/Types/PageAnalytics.php:116
msgid "People Is Now Visiting"
msgstr ""

#: includes/Types/PageAnalytics.php:118
msgid "Holiday Deal Page"
msgstr ""

#: includes/Types/PageAnalytics.php:119
msgid "Check out now & grab exceptional deals"
msgstr ""

#: includes/Types/PageAnalytics.php:154
msgid "Total Site View"
msgstr ""

#: includes/Types/PageAnalytics.php:155
msgid "Realtime site view"
msgstr ""

#: includes/Types/PageAnalytics.php:158
msgid "Site Title"
msgstr ""

#: includes/Types/PageAnalytics.php:161
#: includes/Types/PageAnalytics.php:179
msgid "Day"
msgstr ""

#: includes/Types/PageAnalytics.php:162
#: includes/Types/PageAnalytics.php:180
msgid "Month"
msgstr ""

#: includes/Types/PageAnalytics.php:163
#: includes/Types/PageAnalytics.php:181
msgid "Year"
msgstr ""

#: includes/Types/PageAnalytics.php:173
msgid "Current Page View"
msgstr ""

#: includes/Types/PageAnalytics.php:176
msgid "Page Title"
msgstr ""

#: includes/Types/Traits/Reviews.php:40
msgid "About"
msgstr ""

#: includes/Types/Traits/Reviews.php:97
msgctxt "nx_preview"
msgid "NotificationX"
msgstr ""

#: includes/Types/Video.php:52
msgid "<a href='https://notificationx.com/docs/youtube-video-activities-popups/' target='_blank'>More Info</a>"
msgstr ""

#: includes/Types/Video.php:54
msgid ""
"\n"
"                <span>NotificationX will help you increasing engagement of your YouTube channel and gaining more credibility.</span>\n"
"                <video id=\"pro_alert_video_popup\" type=\"text/html\" allowfullscreen width=\"450\" height=\"235\" autoplay loop muted>\n"
"                    <source src=\"https://notificationx.com/wp-content/uploads/2024/01/How-To-Show-YouTube-Activities-Popup-With-NotificationX.mp4\" type=\"video/mp4\">\n"
"                </video>\n"
"            "
msgstr ""

#: notificationx.php:84
msgid "<strong>Recommended: </strong> Seems like you haven't updated the NotificationX Pro version. Please make sure to update NotificationX Pro plugin from <a href='%s'><strong>wp-admin -> Plugins</strong></a>."
msgstr ""

#: assets/admin/js/223.js:1
#: assets/admin/js/328.js:1
#: assets/admin/js/435.js:1
#: assets/admin/js/468.js:1
#: assets/admin/js/605.js:1
#: assets/admin/js/635.js:1
#: assets/admin/js/736.js:1
#: assets/admin/js/929.js:1
#: assets/public/4468.js:1
#: assets/public/5223.js:1
#: assets/public/6435.js:1
#: assets/public/8736.js:1
msgid "OFF"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "\"allOf\" condition requires an array as #3 argument"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "\"anyOf\" condition requires an array as #3 argument"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "\"not\" can have only one comparison rule, multiple rules given"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Invalid comparison rule %s."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "There are no tabs defined!"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Field must have a #type. see documentation."
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:18
msgid "Copied to Clipboard."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "File can't be empty."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Invalid file type."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "You should give a #fields arguments to a group field."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Loading..."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "#options is a required arguments for RadioCard field."
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/GradientPicker.jsx:62
msgid "Reset"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:9
#: assets/admin/js/admin.js:11
msgid "Something went wrong."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Button has a required params #text."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Modal needs button/body with it."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Save Changes"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "There are no #tabs args defined in props."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Not an array."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "NotificationXContext context is undefined, please verify you are calling useNotificationXContext() as child of a <NotificationX> component."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Title Goes Here: title"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Text Goes Here: text"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "You need to upgrade to the <strong><a href='%s' target='_blank'>Premium Version</a></strong> to use this feature."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Opps..."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "You are not authorized to perform this action. Please contact the administrator or check your access rights."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Access Denied"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:18
msgid "Close"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "NotificationX:"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "NotificationX Pro:"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "NX Not Created"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Error: "
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:9
#: assets/admin/js/admin.js:18
msgid "Are you sure?"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:9
msgid "You won't be able to revert this!"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:9
msgid "Yes, Delete It"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:9
msgid "No, Cancel"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Notification Alert has been Deleted."
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Oops, Something went wrong. Please try again."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Delete Error: "
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:14
msgid "Publish"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Notification Alert has been Enabled."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Notification Alert has been Disabled."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "You need to upgrade to the <strong><a target='_blank' href='%s' style='color:red'>Premium Version</a></strong> to use this feature."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Disabled"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "You need to upgrade to the <strong><a target='_blank' href='%s' style='color:red'>Premium Version</a></strong> to use multiple notification."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Active"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Inactive"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Scheduled For"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Published On"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Publish On"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:18
msgid "Update"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "NotificationX Instructions"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Rate NotificationX"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Help us make it better!"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Please share what went wrong with The NotificationX so that we can improve further"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Send"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "We’re glad that you liked us! 😍"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "We appreciate it!"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "If you don’t mind, could you take 30 seconds to review us on WordPress? Your feedback will help us improve and grow. Thank you in advance! 🙏"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "A heartfelt gratitude for managing the time to share your thoughts with us"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Rate the Plugin"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Need help? We're here"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Ready to assist you every step of the way"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Let's Chat"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Want to explore more?"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Dive in and discover all the premium features"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Upgrade To PRO"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Clicks"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "CTR"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "All Combined"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "All Separated"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Analytics Data"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Inline Notification Alert has been copied to Clipboard."
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:18
msgid "Cancel"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Regular Notification Alert has been copied to Clipboard."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Three Dots"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:16
#: assets/admin/js/admin.js:18
msgid "Edit"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Translate"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "You need to Install, Activate & Setup WPML Multilingual CMS & WPML String Translation plugins to use this feature."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Duplicate"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Shortcode"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "ShortCode"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:9
msgid "Cross Domain Notice code has been copied to Clipboard."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "You need to upgrade to the <strong><a target='_blank' href='%s' style='color:red'>Premium Version</a></strong> to use <a target='_blank' href='%s' style='color:red'>Cross Domain Notice</a> feature."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Are you sure you want to Regenerate?"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Regenerating will fetch new data based on settings"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Regenerate"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Notification Alert has been Regenerated."
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Are you sure you want to Reset?"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Reset will delete All analytics report for this notification"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Notification Alert has been Reset."
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:18
msgid "Delete"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "%s clicks"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "%s views"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Published"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
#: assets/admin/js/admin.js:16
msgid "NotificationX Title"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Type"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Stats"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Date"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "Action"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Bulk Action"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Enable"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Disable"
msgstr ""

#: assets/admin/js/admin.js:1
#: assets/admin/js/admin.js:11
msgid "NotificationX Fetch Error: "
msgstr ""

#: assets/admin/js/admin.js:1
msgid "All (%d)"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Enabled (%d)"
msgstr ""

#: assets/admin/js/admin.js:1
msgid "Disabled (%d)"
msgstr ""

#. translators: %d: Number of Notification Alerts Regenerated.
#: assets/admin/js/admin.js:3
msgid "%d Notification Alerts have been Regenerated."
msgstr ""

#. translators: %d: Number of Notification Alerts Reset.
#: assets/admin/js/admin.js:5
msgid "%d Notification Alerts have been Reset."
msgstr ""

#. translators: %d: Number of Notification Alerts Enabled.
#: assets/admin/js/admin.js:7
msgid "%d Notification Alerts have been Enabled."
msgstr ""

#. translators: %d: Number of Notification Alerts Disabled.
#: assets/admin/js/admin.js:9
msgid "%d Notification Alerts have been Disabled."
msgstr ""

#: assets/admin/js/admin.js:9
msgid "Unable to complete bulk action."
msgstr ""

#: assets/admin/js/admin.js:9
msgid "You're about to delete %s notification alert,<br />"
msgid_plural "You're about to delete %s notification alerts,<br />"
msgstr[0] ""
msgstr[1] ""

#: assets/admin/js/admin.js:9
msgid "error"
msgstr ""

#. translators: %d: Number of Notification Alerts deleted.
#: assets/admin/js/admin.js:11
msgid ""
"%d notification Alerts have been\n"
"                Deleted."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Applying..."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Apply"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "loading..."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "No notifications are found."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Seems like you haven’t created any notification alerts."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Hit on %1$s\"Add New\"%2$s button to get started"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "No notifications are enabled."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "There’s no enabled Notification Alerts."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Simply use the toggle switch to turn your notifications from %1$s\"All NotificationX\"%2$s page."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "No notifications are disabled."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "There’s no disabled Notification Alerts."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Manage License"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Upgrade to Pro"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Display Social Proof Alerts With NotificationX"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Accelerate website engagement & conversions by creating dynamic notifications, sales banners, and more. Follow this guide to explore all advanced NotificationX's features & quickly get started.  "
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "NO NOTIFICATIONS ARE FOUND."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "It seems like you haven't created any notification alerts.Hit on \"Add New\" button to get started"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "How To Configure 'WooCommerce Sales Alerts' In NotificationX?"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "How to Configure Cookies Policy for Website Using NotificationX’s Cookie Notice?"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "How to Use Notification Bar in NotificationX?"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Display WooCommerce Growth Alerts With NotificationX"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "How To Create Custom Notification With NotificationX?"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "How To Configure NotificationX Cross Domain Notice?"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "How To Configure Google Reviews With NotificationX?"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Create Now"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Display your latest sales to boost credibility and drive more conversions."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Inform users and stay privacy compliant quickly and effortlessly."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Display latest sales, discounts, or announcements to boost sales."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Display sales count, low stock notification to influence viewer to make instant purchases."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Flashing Tabs"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Encourage visitors or potential customers to take action & turn browsing into buying."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Cross - Domain Notice"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Copy & Explore"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Display single, multiple, or all live alerts on multiple WordPress websites or others."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Show Your Love"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "We love having you in the NotificationX family. We are making it more awesome every day. Please take two minutes to review the plugin and spread the love to encourage us to keep it going."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
#: nxdev/notificationx/admin/Dashboard/HelpReviewSection.jsx:31
msgid "Leave a Review"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Explore Our Knowledge Base"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Get started by spending some time with the documentation to familiarize yourself with NotificationX and boost your website conversions immediately."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Documentation"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/admin/js/admin.js:18
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Join Our Community"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Join the Facebook community to discuss with fellow developers, connect with others, and stay updated."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Need Any Help?"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "If you encounter issues or need assistance, we're here to help or report specific problems on our GitHub issues page."
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Report a Bug"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Changes Saved Successfully."
msgstr ""

#: assets/admin/js/admin.js:11
msgid "NotificationX Logo"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "video-widget"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Launch Setup Wizard"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Watch Tutorials"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "link-icon"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Notifications"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "View All Notifications"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Exclusive Features & Notification Types"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Get various types of notification support including"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Helpful Resources"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Explore More"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Customer Success Stories"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Learn More"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "eCom Founder Web & Ops AI Consultant"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "author img"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Emilio Johann"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "San Diego, CA"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Converting Prospects to Customers: Barn2's Success Story with NotificationX"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "WordPress Plugin Developer Company"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Katie Keith"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "Co-Founder & CEO at Barn2"
msgstr ""

#: assets/admin/js/admin.js:11
#: assets/admin/js/admin.js:18
msgid "Integrations"
msgstr ""

#: assets/admin/js/admin.js:11
msgid "View All Integrations"
msgstr ""

#: assets/admin/js/admin.js:11
#: nxdev/notificationx/admin/Dashboard/HelpReviewSection.jsx:10
msgid "Need Help?"
msgstr ""

#: assets/admin/js/admin.js:11
#: nxdev/notificationx/admin/Dashboard/HelpReviewSection.jsx:11
msgid "Our dedicated support team is here to assist you with all your inquiries, anytime you need."
msgstr ""

#: assets/admin/js/admin.js:11
#: nxdev/notificationx/admin/Dashboard/HelpReviewSection.jsx:15
msgid "Contact Us"
msgstr ""

#: assets/admin/js/admin.js:11
#: nxdev/notificationx/admin/Dashboard/HelpReviewSection.jsx:26
msgid "Love NotificationX?"
msgstr ""

#: assets/admin/js/admin.js:11
#: nxdev/notificationx/admin/Dashboard/HelpReviewSection.jsx:27
msgid "Your quick feedback helps us grow and build more awesome features for you!"
msgstr ""

#. translators: %1$s: title, %2$s: link to the All NotificationX page.
#: assets/admin/js/admin.js:14
msgid "You are about to publish %1$s. You can rename this and edit everything whenever you want from %2$s Page."
msgstr ""

#: assets/admin/js/admin.js:14
msgid "QuickBuilder Error: "
msgstr ""

#: assets/admin/js/admin.js:14
msgid "Publishing..."
msgstr ""

#. translators: Title of the new Notification Alert. %1$s: title, %2$s: current date.
#: assets/admin/js/admin.js:16
msgid "NotificationX - %1$s - %2$s"
msgstr ""

#: assets/admin/js/admin.js:16
msgid "Successfully Updated."
msgstr ""

#: assets/admin/js/admin.js:16
msgid "Successfully Created."
msgstr ""

#. translators: Postfix for notice created by duplicate button.
#: assets/admin/js/admin.js:18
msgid " - Copy"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Unlock pro Features"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Get Support"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Suggest a Feature"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "NX-Close-Img"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "CSV data imported successfully!"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Invalid File Type!"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Please upload a CSV file to import custom notification data."
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Import Limit Exceeded."
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Continue"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Error processing the CSV file"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Sample CSV"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Change Time"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "This will effect on all selected Items"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Apply Changes"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Select All"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Selected custom notification Time updated successfully!"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Do you really want to delete the selected items? This process cannot be undone."
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Yes, Delete Them"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "No, Keep Them"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Custom Notification Preview"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Items Per Page"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Edit Cookie"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Are you sure you want to delete this Cookie?"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "The cookie <strong>%s</strong> will be permanently deleted. This cookie will no longer be displayed on your cookie list nor be blocked prior to receiving user consent."
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Delete Cookie"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Edit Custom Cookie"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Add Custom Cookie"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Save"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Custom Cookies"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Edit Category"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/ModalContent/ScannerHistory.jsx:14
msgid "History"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/ModalContent/ScannerHistory.jsx:24
msgid "Scan Date"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/ModalContent/ScannerHistory.jsx:25
msgid "Scan Status"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/ModalContent/ScannerHistory.jsx:26
msgid "Category"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/ModalContent/ScannerHistory.jsx:27
msgid "Cookies"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/ModalContent/ScannerHistory.jsx:28
#: nxdev/notificationx/fields/ModalContent/ScannerHistory.jsx:47
msgid "More Info"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/ModalContent/ScannerHistory.jsx:54
msgid "No scan history available"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/ModalContent/HistoryMoreInfo.jsx:14
#: nxdev/notificationx/fields/ModalContent/HistoryMoreInfo.jsx:24
msgid "Scanned URL"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Scan failed. Try again."
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Scan complete! Your results are now available."
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Last Successfully Scan"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "No scan history found"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Start your first scan now"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Scanning"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Scan Now"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Scan limit exceeded for this website."
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Ready to start scanning?"
msgstr ""

#: assets/admin/js/admin.js:18
msgid "Your existing cookie list (cookies discovered in the previous scan) will be replaced with the cookies discovered in this scan. Therefore, make sure you don’t exclude the pages that sets cookies."
msgstr ""

#: assets/admin/js/admin.js:18
#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
#: nxdev/notificationx/fields/helpers/PressbarAdminPreview.jsx:361
msgid "You should setup NX Bar properly"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/IconPicker.jsx:239
msgid "Icons"
msgstr ""

#: assets/admin/js/admin.js:18
#: nxdev/notificationx/fields/NxBarPresets.jsx:47
msgid "Create with Your Preferred Builder/Editor"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
#: nxdev/notificationx/frontend/themes/helpers/NXBranding.js:9
msgid "by"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgctxt "Announcements: 5 days remaining"
msgid " remaining"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Cookie"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Show more"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Show less"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Powered by"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Hrs"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Mins"
msgstr ""

#: assets/public/js/crossSite.js:1
#: assets/public/js/frontend.js:1
msgid "Secs"
msgstr ""

#: assets/public/js/crossSite.js:1
msgid "You are using old version of cross-domain scripts for NotificationX Pro. Please update this from your NotificationX Settings page."
msgstr ""

#: blocks/notificationx/components/inspector.js:112
#: blocks/notificationx/components/inspector.js:133
#: blocks/notificationx/index.js:1
msgid "Select"
msgstr ""

#: blocks/notificationx/components/inspector.js:183
#: blocks/notificationx/index.js:1
msgid "Choose Notification"
msgstr ""

#: blocks/notificationx/components/inspector.js:194
#: blocks/notificationx/index.js:1
msgid "Choose Course"
msgstr ""

#: blocks/notificationx/components/inspector.js:196
#: blocks/notificationx/index.js:1
msgid "Choose Product"
msgstr ""

#: blocks/notificationx/components/inspector.js:203
#: blocks/notificationx/index.js:1
msgid "Please type 3 or more characters"
msgstr ""

#: blocks/notificationx/components/inspector.js:215
#: blocks/notificationx/index.js:1
msgid "Note: "
msgstr ""

#: blocks/notificationx/components/inspector.js:216
#: blocks/notificationx/index.js:1
msgid "You have no notification enabled. Please Enable notifications to get here."
msgstr ""

#: blocks/notificationx/components/inspector.js:226
#: blocks/notificationx/components/inspector.js:260
#: blocks/notificationx/index.js:1
msgid "Alignment"
msgstr ""

#: blocks/notificationx/components/inspector.js:295
#: blocks/notificationx/index.js:1
msgid "Color"
msgstr ""

#: blocks/notificationx/components/inspector.js:301
#: blocks/notificationx/index.js:1
msgid "Link Color"
msgstr ""
