import { __ } from "@wordpress/i18n";

export const GET_STARTED_TXT = __('Display Social Proof Alerts With NotificationX', 'notificationx');
export const GET_STARTED_DESC = __('Accelerate website engagement & conversions by creating dynamic notifications, sales banners, and more. Follow this guide to explore all advanced NotificationX\'s features & quickly get started.  ', 'notificationx');
export const NOT_FOUND_TITLE = __('NO NOTIFICATIONS ARE FOUND.', 'notificationx')
export const NOT_FOUND_DESC = __('It seems like you haven\'t created any notification alerts.Hit on "Add New" button to get started', 'notificationx')
export const DOCS = [
    {
        'svg' : `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2514 3.35257C11.9195 3.15569 11.6917 2.81875 11.6495 2.43288L11.5286 1.34895C11.5033 1.12423 11.3458 0.936352 11.1292 0.871945C9.73701 0.459352 8.26329 0.459352 6.8711 0.871945C6.65454 0.936352 6.49703 1.12423 6.47172 1.34895C6.47172 1.34895 6.41545 1.85238 6.35076 2.35273V2.36257C6.30576 2.7625 6.07515 3.11801 5.72921 3.32219C5.37484 3.52244 4.94734 3.55647 4.57609 3.39363L3.63951 2.98441C3.43138 2.89357 3.19234 2.93576 3.0264 3.09157C1.97453 4.09001 1.23763 5.36716 0.897321 6.7782C0.846696 6.99841 0.931074 7.22847 1.11107 7.36263C1.11107 7.36263 1.54703 7.68297 1.9689 7.99319C2.27828 8.22044 2.46109 8.581 2.46109 8.96463C2.46109 9.3896 2.2586 9.78954 1.91548 10.0415L1.11107 10.633C0.931074 10.7672 0.846696 10.9972 0.897321 11.2174C1.23763 12.6285 1.97453 13.9056 3.0264 14.9043C3.19234 15.0599 3.43138 15.1021 3.63951 15.0112C3.63951 15.0112 4.17109 14.7789 4.67452 14.5671C4.67734 14.566 4.67734 14.5649 4.68015 14.564C5.02609 14.4136 5.41985 14.433 5.7461 14.6166C6.07798 14.8073 6.30295 15.1392 6.34795 15.5203L6.47172 16.6512C6.49703 16.8751 6.65172 17.0624 6.86829 17.1276C8.23516 17.5402 9.75673 17.5281 11.1264 17.129C11.3458 17.0655 11.5033 16.877 11.5286 16.6512C11.5286 16.6512 11.5961 16.0271 11.6692 15.459C11.6692 15.4567 11.6692 15.4542 11.6692 15.4519C11.7086 15.0945 11.9139 14.7764 12.2233 14.5936C12.5411 14.4102 12.9236 14.3826 13.2583 14.5294L14.3636 15.0138C14.5717 15.104 14.8108 15.0627 14.9739 14.9086C16.0145 13.9315 16.7654 12.6082 17.1058 11.2222C17.1592 11.0014 17.0749 10.77 16.892 10.6353L15.9723 9.95772C15.6545 9.72541 15.4689 9.35669 15.4689 8.96491C15.4689 8.61448 15.6348 8.28485 15.9189 8.07701L16.892 7.3601C17.072 7.22679 17.1592 6.99785 17.1058 6.77876C16.7795 5.3891 16.0089 4.07791 14.9795 3.09101C14.8136 2.93379 14.5717 2.89076 14.3636 2.98188C14.3636 2.98188 13.8573 3.20379 13.3652 3.41022C13.3623 3.41135 13.3595 3.41248 13.3567 3.41388C12.9995 3.57026 12.5917 3.54747 12.2514 3.35257ZM9.00015 6.18757C7.44765 6.18757 6.18765 7.44785 6.18765 9.00007C6.18765 10.5523 7.44765 11.8126 9.00015 11.8126C10.5527 11.8126 11.8126 10.5523 11.8126 9.00007C11.8126 7.44785 10.5527 6.18757 9.00015 6.18757Z" fill="#ADA6D6" />
            </svg>`,
        'desc'  : __('How To Configure \'WooCommerce Sales Alerts\' In NotificationX?', 'notificationx'),
        'url'   : 'https://notificationx.com/docs/configure-woocommerce-sales-alert/',
    },
    {
        'svg' : `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2514 3.35257C11.9195 3.15569 11.6917 2.81875 11.6495 2.43288L11.5286 1.34895C11.5033 1.12423 11.3458 0.936352 11.1292 0.871945C9.73701 0.459352 8.26329 0.459352 6.8711 0.871945C6.65454 0.936352 6.49703 1.12423 6.47172 1.34895C6.47172 1.34895 6.41545 1.85238 6.35076 2.35273V2.36257C6.30576 2.7625 6.07515 3.11801 5.72921 3.32219C5.37484 3.52244 4.94734 3.55647 4.57609 3.39363L3.63951 2.98441C3.43138 2.89357 3.19234 2.93576 3.0264 3.09157C1.97453 4.09001 1.23763 5.36716 0.897321 6.7782C0.846696 6.99841 0.931074 7.22847 1.11107 7.36263C1.11107 7.36263 1.54703 7.68297 1.9689 7.99319C2.27828 8.22044 2.46109 8.581 2.46109 8.96463C2.46109 9.3896 2.2586 9.78954 1.91548 10.0415L1.11107 10.633C0.931074 10.7672 0.846696 10.9972 0.897321 11.2174C1.23763 12.6285 1.97453 13.9056 3.0264 14.9043C3.19234 15.0599 3.43138 15.1021 3.63951 15.0112C3.63951 15.0112 4.17109 14.7789 4.67452 14.5671C4.67734 14.566 4.67734 14.5649 4.68015 14.564C5.02609 14.4136 5.41985 14.433 5.7461 14.6166C6.07798 14.8073 6.30295 15.1392 6.34795 15.5203L6.47172 16.6512C6.49703 16.8751 6.65172 17.0624 6.86829 17.1276C8.23516 17.5402 9.75673 17.5281 11.1264 17.129C11.3458 17.0655 11.5033 16.877 11.5286 16.6512C11.5286 16.6512 11.5961 16.0271 11.6692 15.459C11.6692 15.4567 11.6692 15.4542 11.6692 15.4519C11.7086 15.0945 11.9139 14.7764 12.2233 14.5936C12.5411 14.4102 12.9236 14.3826 13.2583 14.5294L14.3636 15.0138C14.5717 15.104 14.8108 15.0627 14.9739 14.9086C16.0145 13.9315 16.7654 12.6082 17.1058 11.2222C17.1592 11.0014 17.0749 10.77 16.892 10.6353L15.9723 9.95772C15.6545 9.72541 15.4689 9.35669 15.4689 8.96491C15.4689 8.61448 15.6348 8.28485 15.9189 8.07701L16.892 7.3601C17.072 7.22679 17.1592 6.99785 17.1058 6.77876C16.7795 5.3891 16.0089 4.07791 14.9795 3.09101C14.8136 2.93379 14.5717 2.89076 14.3636 2.98188C14.3636 2.98188 13.8573 3.20379 13.3652 3.41022C13.3623 3.41135 13.3595 3.41248 13.3567 3.41388C12.9995 3.57026 12.5917 3.54747 12.2514 3.35257ZM9.00015 6.18757C7.44765 6.18757 6.18765 7.44785 6.18765 9.00007C6.18765 10.5523 7.44765 11.8126 9.00015 11.8126C10.5527 11.8126 11.8126 10.5523 11.8126 9.00007C11.8126 7.44785 10.5527 6.18757 9.00015 6.18757Z" fill="#ADA6D6" />
            </svg>`,
        'desc'  : __('How to Configure Cookies Policy for Website Using NotificationX’s Cookie Notice?', 'notificationx'),
        'url'   : 'https://notificationx.com/docs/how-to-configure-cookies-policy-for-website/',
    },
    {
        'svg' : `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2514 3.35257C11.9195 3.15569 11.6917 2.81875 11.6495 2.43288L11.5286 1.34895C11.5033 1.12423 11.3458 0.936352 11.1292 0.871945C9.73701 0.459352 8.26329 0.459352 6.8711 0.871945C6.65454 0.936352 6.49703 1.12423 6.47172 1.34895C6.47172 1.34895 6.41545 1.85238 6.35076 2.35273V2.36257C6.30576 2.7625 6.07515 3.11801 5.72921 3.32219C5.37484 3.52244 4.94734 3.55647 4.57609 3.39363L3.63951 2.98441C3.43138 2.89357 3.19234 2.93576 3.0264 3.09157C1.97453 4.09001 1.23763 5.36716 0.897321 6.7782C0.846696 6.99841 0.931074 7.22847 1.11107 7.36263C1.11107 7.36263 1.54703 7.68297 1.9689 7.99319C2.27828 8.22044 2.46109 8.581 2.46109 8.96463C2.46109 9.3896 2.2586 9.78954 1.91548 10.0415L1.11107 10.633C0.931074 10.7672 0.846696 10.9972 0.897321 11.2174C1.23763 12.6285 1.97453 13.9056 3.0264 14.9043C3.19234 15.0599 3.43138 15.1021 3.63951 15.0112C3.63951 15.0112 4.17109 14.7789 4.67452 14.5671C4.67734 14.566 4.67734 14.5649 4.68015 14.564C5.02609 14.4136 5.41985 14.433 5.7461 14.6166C6.07798 14.8073 6.30295 15.1392 6.34795 15.5203L6.47172 16.6512C6.49703 16.8751 6.65172 17.0624 6.86829 17.1276C8.23516 17.5402 9.75673 17.5281 11.1264 17.129C11.3458 17.0655 11.5033 16.877 11.5286 16.6512C11.5286 16.6512 11.5961 16.0271 11.6692 15.459C11.6692 15.4567 11.6692 15.4542 11.6692 15.4519C11.7086 15.0945 11.9139 14.7764 12.2233 14.5936C12.5411 14.4102 12.9236 14.3826 13.2583 14.5294L14.3636 15.0138C14.5717 15.104 14.8108 15.0627 14.9739 14.9086C16.0145 13.9315 16.7654 12.6082 17.1058 11.2222C17.1592 11.0014 17.0749 10.77 16.892 10.6353L15.9723 9.95772C15.6545 9.72541 15.4689 9.35669 15.4689 8.96491C15.4689 8.61448 15.6348 8.28485 15.9189 8.07701L16.892 7.3601C17.072 7.22679 17.1592 6.99785 17.1058 6.77876C16.7795 5.3891 16.0089 4.07791 14.9795 3.09101C14.8136 2.93379 14.5717 2.89076 14.3636 2.98188C14.3636 2.98188 13.8573 3.20379 13.3652 3.41022C13.3623 3.41135 13.3595 3.41248 13.3567 3.41388C12.9995 3.57026 12.5917 3.54747 12.2514 3.35257ZM9.00015 6.18757C7.44765 6.18757 6.18765 7.44785 6.18765 9.00007C6.18765 10.5523 7.44765 11.8126 9.00015 11.8126C10.5527 11.8126 11.8126 10.5523 11.8126 9.00007C11.8126 7.44785 10.5527 6.18757 9.00015 6.18757Z" fill="#ADA6D6" />
            </svg>`,
        'desc'  : __('How to Use Notification Bar in NotificationX?', 'notificationx'),
        'url'   : 'https://notificationx.com/docs/notification-bar/',
    },
    {
        'svg' : `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2514 3.35257C11.9195 3.15569 11.6917 2.81875 11.6495 2.43288L11.5286 1.34895C11.5033 1.12423 11.3458 0.936352 11.1292 0.871945C9.73701 0.459352 8.26329 0.459352 6.8711 0.871945C6.65454 0.936352 6.49703 1.12423 6.47172 1.34895C6.47172 1.34895 6.41545 1.85238 6.35076 2.35273V2.36257C6.30576 2.7625 6.07515 3.11801 5.72921 3.32219C5.37484 3.52244 4.94734 3.55647 4.57609 3.39363L3.63951 2.98441C3.43138 2.89357 3.19234 2.93576 3.0264 3.09157C1.97453 4.09001 1.23763 5.36716 0.897321 6.7782C0.846696 6.99841 0.931074 7.22847 1.11107 7.36263C1.11107 7.36263 1.54703 7.68297 1.9689 7.99319C2.27828 8.22044 2.46109 8.581 2.46109 8.96463C2.46109 9.3896 2.2586 9.78954 1.91548 10.0415L1.11107 10.633C0.931074 10.7672 0.846696 10.9972 0.897321 11.2174C1.23763 12.6285 1.97453 13.9056 3.0264 14.9043C3.19234 15.0599 3.43138 15.1021 3.63951 15.0112C3.63951 15.0112 4.17109 14.7789 4.67452 14.5671C4.67734 14.566 4.67734 14.5649 4.68015 14.564C5.02609 14.4136 5.41985 14.433 5.7461 14.6166C6.07798 14.8073 6.30295 15.1392 6.34795 15.5203L6.47172 16.6512C6.49703 16.8751 6.65172 17.0624 6.86829 17.1276C8.23516 17.5402 9.75673 17.5281 11.1264 17.129C11.3458 17.0655 11.5033 16.877 11.5286 16.6512C11.5286 16.6512 11.5961 16.0271 11.6692 15.459C11.6692 15.4567 11.6692 15.4542 11.6692 15.4519C11.7086 15.0945 11.9139 14.7764 12.2233 14.5936C12.5411 14.4102 12.9236 14.3826 13.2583 14.5294L14.3636 15.0138C14.5717 15.104 14.8108 15.0627 14.9739 14.9086C16.0145 13.9315 16.7654 12.6082 17.1058 11.2222C17.1592 11.0014 17.0749 10.77 16.892 10.6353L15.9723 9.95772C15.6545 9.72541 15.4689 9.35669 15.4689 8.96491C15.4689 8.61448 15.6348 8.28485 15.9189 8.07701L16.892 7.3601C17.072 7.22679 17.1592 6.99785 17.1058 6.77876C16.7795 5.3891 16.0089 4.07791 14.9795 3.09101C14.8136 2.93379 14.5717 2.89076 14.3636 2.98188C14.3636 2.98188 13.8573 3.20379 13.3652 3.41022C13.3623 3.41135 13.3595 3.41248 13.3567 3.41388C12.9995 3.57026 12.5917 3.54747 12.2514 3.35257ZM9.00015 6.18757C7.44765 6.18757 6.18765 7.44785 6.18765 9.00007C6.18765 10.5523 7.44765 11.8126 9.00015 11.8126C10.5527 11.8126 11.8126 10.5523 11.8126 9.00007C11.8126 7.44785 10.5527 6.18757 9.00015 6.18757Z" fill="#ADA6D6" />
            </svg>`,
        'desc'  : __('Display WooCommerce Growth Alerts With NotificationX', 'notificationx'),
        'url'   : 'https://notificationx.com/docs/woocommerce-growth-alert/',
    },
    {
        'svg' : `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2514 3.35257C11.9195 3.15569 11.6917 2.81875 11.6495 2.43288L11.5286 1.34895C11.5033 1.12423 11.3458 0.936352 11.1292 0.871945C9.73701 0.459352 8.26329 0.459352 6.8711 0.871945C6.65454 0.936352 6.49703 1.12423 6.47172 1.34895C6.47172 1.34895 6.41545 1.85238 6.35076 2.35273V2.36257C6.30576 2.7625 6.07515 3.11801 5.72921 3.32219C5.37484 3.52244 4.94734 3.55647 4.57609 3.39363L3.63951 2.98441C3.43138 2.89357 3.19234 2.93576 3.0264 3.09157C1.97453 4.09001 1.23763 5.36716 0.897321 6.7782C0.846696 6.99841 0.931074 7.22847 1.11107 7.36263C1.11107 7.36263 1.54703 7.68297 1.9689 7.99319C2.27828 8.22044 2.46109 8.581 2.46109 8.96463C2.46109 9.3896 2.2586 9.78954 1.91548 10.0415L1.11107 10.633C0.931074 10.7672 0.846696 10.9972 0.897321 11.2174C1.23763 12.6285 1.97453 13.9056 3.0264 14.9043C3.19234 15.0599 3.43138 15.1021 3.63951 15.0112C3.63951 15.0112 4.17109 14.7789 4.67452 14.5671C4.67734 14.566 4.67734 14.5649 4.68015 14.564C5.02609 14.4136 5.41985 14.433 5.7461 14.6166C6.07798 14.8073 6.30295 15.1392 6.34795 15.5203L6.47172 16.6512C6.49703 16.8751 6.65172 17.0624 6.86829 17.1276C8.23516 17.5402 9.75673 17.5281 11.1264 17.129C11.3458 17.0655 11.5033 16.877 11.5286 16.6512C11.5286 16.6512 11.5961 16.0271 11.6692 15.459C11.6692 15.4567 11.6692 15.4542 11.6692 15.4519C11.7086 15.0945 11.9139 14.7764 12.2233 14.5936C12.5411 14.4102 12.9236 14.3826 13.2583 14.5294L14.3636 15.0138C14.5717 15.104 14.8108 15.0627 14.9739 14.9086C16.0145 13.9315 16.7654 12.6082 17.1058 11.2222C17.1592 11.0014 17.0749 10.77 16.892 10.6353L15.9723 9.95772C15.6545 9.72541 15.4689 9.35669 15.4689 8.96491C15.4689 8.61448 15.6348 8.28485 15.9189 8.07701L16.892 7.3601C17.072 7.22679 17.1592 6.99785 17.1058 6.77876C16.7795 5.3891 16.0089 4.07791 14.9795 3.09101C14.8136 2.93379 14.5717 2.89076 14.3636 2.98188C14.3636 2.98188 13.8573 3.20379 13.3652 3.41022C13.3623 3.41135 13.3595 3.41248 13.3567 3.41388C12.9995 3.57026 12.5917 3.54747 12.2514 3.35257ZM9.00015 6.18757C7.44765 6.18757 6.18765 7.44785 6.18765 9.00007C6.18765 10.5523 7.44765 11.8126 9.00015 11.8126C10.5527 11.8126 11.8126 10.5523 11.8126 9.00007C11.8126 7.44785 10.5527 6.18757 9.00015 6.18757Z" fill="#ADA6D6" />
            </svg>`,
        'desc'  : __('How To Create Custom Notification With NotificationX?', 'notificationx'),
        'url'   : 'https://notificationx.com/docs/custom-notification/',
    },
    {
        'svg' : `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2514 3.35257C11.9195 3.15569 11.6917 2.81875 11.6495 2.43288L11.5286 1.34895C11.5033 1.12423 11.3458 0.936352 11.1292 0.871945C9.73701 0.459352 8.26329 0.459352 6.8711 0.871945C6.65454 0.936352 6.49703 1.12423 6.47172 1.34895C6.47172 1.34895 6.41545 1.85238 6.35076 2.35273V2.36257C6.30576 2.7625 6.07515 3.11801 5.72921 3.32219C5.37484 3.52244 4.94734 3.55647 4.57609 3.39363L3.63951 2.98441C3.43138 2.89357 3.19234 2.93576 3.0264 3.09157C1.97453 4.09001 1.23763 5.36716 0.897321 6.7782C0.846696 6.99841 0.931074 7.22847 1.11107 7.36263C1.11107 7.36263 1.54703 7.68297 1.9689 7.99319C2.27828 8.22044 2.46109 8.581 2.46109 8.96463C2.46109 9.3896 2.2586 9.78954 1.91548 10.0415L1.11107 10.633C0.931074 10.7672 0.846696 10.9972 0.897321 11.2174C1.23763 12.6285 1.97453 13.9056 3.0264 14.9043C3.19234 15.0599 3.43138 15.1021 3.63951 15.0112C3.63951 15.0112 4.17109 14.7789 4.67452 14.5671C4.67734 14.566 4.67734 14.5649 4.68015 14.564C5.02609 14.4136 5.41985 14.433 5.7461 14.6166C6.07798 14.8073 6.30295 15.1392 6.34795 15.5203L6.47172 16.6512C6.49703 16.8751 6.65172 17.0624 6.86829 17.1276C8.23516 17.5402 9.75673 17.5281 11.1264 17.129C11.3458 17.0655 11.5033 16.877 11.5286 16.6512C11.5286 16.6512 11.5961 16.0271 11.6692 15.459C11.6692 15.4567 11.6692 15.4542 11.6692 15.4519C11.7086 15.0945 11.9139 14.7764 12.2233 14.5936C12.5411 14.4102 12.9236 14.3826 13.2583 14.5294L14.3636 15.0138C14.5717 15.104 14.8108 15.0627 14.9739 14.9086C16.0145 13.9315 16.7654 12.6082 17.1058 11.2222C17.1592 11.0014 17.0749 10.77 16.892 10.6353L15.9723 9.95772C15.6545 9.72541 15.4689 9.35669 15.4689 8.96491C15.4689 8.61448 15.6348 8.28485 15.9189 8.07701L16.892 7.3601C17.072 7.22679 17.1592 6.99785 17.1058 6.77876C16.7795 5.3891 16.0089 4.07791 14.9795 3.09101C14.8136 2.93379 14.5717 2.89076 14.3636 2.98188C14.3636 2.98188 13.8573 3.20379 13.3652 3.41022C13.3623 3.41135 13.3595 3.41248 13.3567 3.41388C12.9995 3.57026 12.5917 3.54747 12.2514 3.35257ZM9.00015 6.18757C7.44765 6.18757 6.18765 7.44785 6.18765 9.00007C6.18765 10.5523 7.44765 11.8126 9.00015 11.8126C10.5527 11.8126 11.8126 10.5523 11.8126 9.00007C11.8126 7.44785 10.5527 6.18757 9.00015 6.18757Z" fill="#ADA6D6" />
            </svg>`,
        'desc'  : __('How To Configure NotificationX Cross Domain Notice?', 'notificationx'),
        'url'   : 'https://notificationx.com/docs/notificationx-cross-domain-notice/',
    },
    {
        'svg' : `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2514 3.35257C11.9195 3.15569 11.6917 2.81875 11.6495 2.43288L11.5286 1.34895C11.5033 1.12423 11.3458 0.936352 11.1292 0.871945C9.73701 0.459352 8.26329 0.459352 6.8711 0.871945C6.65454 0.936352 6.49703 1.12423 6.47172 1.34895C6.47172 1.34895 6.41545 1.85238 6.35076 2.35273V2.36257C6.30576 2.7625 6.07515 3.11801 5.72921 3.32219C5.37484 3.52244 4.94734 3.55647 4.57609 3.39363L3.63951 2.98441C3.43138 2.89357 3.19234 2.93576 3.0264 3.09157C1.97453 4.09001 1.23763 5.36716 0.897321 6.7782C0.846696 6.99841 0.931074 7.22847 1.11107 7.36263C1.11107 7.36263 1.54703 7.68297 1.9689 7.99319C2.27828 8.22044 2.46109 8.581 2.46109 8.96463C2.46109 9.3896 2.2586 9.78954 1.91548 10.0415L1.11107 10.633C0.931074 10.7672 0.846696 10.9972 0.897321 11.2174C1.23763 12.6285 1.97453 13.9056 3.0264 14.9043C3.19234 15.0599 3.43138 15.1021 3.63951 15.0112C3.63951 15.0112 4.17109 14.7789 4.67452 14.5671C4.67734 14.566 4.67734 14.5649 4.68015 14.564C5.02609 14.4136 5.41985 14.433 5.7461 14.6166C6.07798 14.8073 6.30295 15.1392 6.34795 15.5203L6.47172 16.6512C6.49703 16.8751 6.65172 17.0624 6.86829 17.1276C8.23516 17.5402 9.75673 17.5281 11.1264 17.129C11.3458 17.0655 11.5033 16.877 11.5286 16.6512C11.5286 16.6512 11.5961 16.0271 11.6692 15.459C11.6692 15.4567 11.6692 15.4542 11.6692 15.4519C11.7086 15.0945 11.9139 14.7764 12.2233 14.5936C12.5411 14.4102 12.9236 14.3826 13.2583 14.5294L14.3636 15.0138C14.5717 15.104 14.8108 15.0627 14.9739 14.9086C16.0145 13.9315 16.7654 12.6082 17.1058 11.2222C17.1592 11.0014 17.0749 10.77 16.892 10.6353L15.9723 9.95772C15.6545 9.72541 15.4689 9.35669 15.4689 8.96491C15.4689 8.61448 15.6348 8.28485 15.9189 8.07701L16.892 7.3601C17.072 7.22679 17.1592 6.99785 17.1058 6.77876C16.7795 5.3891 16.0089 4.07791 14.9795 3.09101C14.8136 2.93379 14.5717 2.89076 14.3636 2.98188C14.3636 2.98188 13.8573 3.20379 13.3652 3.41022C13.3623 3.41135 13.3595 3.41248 13.3567 3.41388C12.9995 3.57026 12.5917 3.54747 12.2514 3.35257ZM9.00015 6.18757C7.44765 6.18757 6.18765 7.44785 6.18765 9.00007C6.18765 10.5523 7.44765 11.8126 9.00015 11.8126C10.5527 11.8126 11.8126 10.5523 11.8126 9.00007C11.8126 7.44785 10.5527 6.18757 9.00015 6.18757Z" fill="#ADA6D6" />
            </svg>`,
        'desc'  : __('How To Configure Google Reviews With NotificationX?', 'notificationx'),
        'url'   : 'https://notificationx.com/docs/google-reviews-with-notificationx/',
    },
]

export const NotificationType = [
    {
        'img'        : 'https://notificationx.com/wp-content/uploads/2024/09/sales_notification.gif',
        'title'      : __('Sales Notification', 'notificationx'),
        'type'       : 'conversions',
        'button_text': __('Create Now', 'notificationx'),
        'desc'       : __('Display your latest sales to boost credibility and drive more conversions.', 'notificationx'),
        'source'     : 'woocommerce',
    },
    {
        'img'        : 'https://notificationx.com/wp-content/uploads/2025/06/GDPR.gif',
        'title'      : __('Cookie Notice', 'notificationx'),
        'type'       : 'gdpr',
        'button_text': __('Create Now', 'notificationx'),
        'desc'       : __('Inform users and stay privacy compliant quickly and effortlessly.', 'notificationx'),
        'source'     : 'gdpr',
    },
    {
        'img'        : 'https://notificationx.com/wp-content/uploads/2024/09/notification_bar.gif',
        'title'      : __('Notification Bar', 'notificationx'),
        'type'       : 'notification_bar',
        'button_text': __('Create Now', 'notificationx'),
        'desc'       : __('Display latest sales, discounts, or announcements to boost sales.', 'notificationx'),
        'source'     : 'press_bar',
    },
    {
        'img'        : 'https://notificationx.com/wp-content/uploads/2024/09/popup_notification.gif',
        'title'      : __('Popup Notification', 'notificationx'),
        'type'       : 'popup',
        'button_text': __('Create Now', 'notificationx'),
        'desc'       : __('Display engaging popup notifications to capture attention and drive conversions.', 'notificationx'),
        'source'     : 'popup_notification',
    },
    {
        'img'        : 'https://notificationx.com/wp-content/uploads/2024/09/growth_alert.gif',
        'title'      : __('Growth Alert', 'notificationx'),
        'type'       : 'inline',
        'button_text': __('Create Now', 'notificationx'),
        'desc'       : __('Display sales count, low stock notification to influence viewer to make instant purchases.', 'notificationx'),
        'source'     : 'woo_inline',
    },
    {
        'img'        : 'https://notificationx.com/wp-content/uploads/2024/09/flash_tab.gif',
        'title'      : __('Flashing Tabs', 'notificationx'),
        'type'       : 'flashing_tab',
        'button_text': __('Create Now', 'notificationx'),
        'desc'       : __('Encourage visitors or potential customers to take action & turn browsing into buying.', 'notificationx'),
        'source'     : 'flashing_tab',
    },
    {
        'img'        : 'https://notificationx.com/wp-content/uploads/2024/09/cross_domain.gif',
        'title'      : __('Cross - Domain Notice', 'notificationx'),
        'type'       : 'cross-domain',
        'button_text': __('Copy & Explore', 'notificationx'),
        'desc'       : __('Display single, multiple, or all live alerts on multiple WordPress websites or others.', 'notificationx'),
    }
]
export const FOOTER_DOCS = [
    {
        'image'      : 'love.svg',
        'title'      : __('Show Your Love', 'notificationx'),
        'desc'       : __('We love having you in the NotificationX family. We are making it more awesome every day. Please take two minutes to review the plugin and spread the love to encourage us to keep it going.', 'notificationx'),
        'button_text': __('Leave a Review', 'notificationx'),
        'button_url' : 'https://wpdeveloper.com/review-notificationx',
    },
    {
        'image'      : 'docs.svg',
        'title'      : __('Explore Our Knowledge Base', 'notificationx'),
        'desc'       : __('Get started by spending some time with the documentation to familiarize yourself with NotificationX and boost your website conversions immediately.', 'notificationx'),
        'button_text': __('Documentation', 'notificationx'),
        'button_url' : 'https://notificationx.com/docs/',
    },
    {
        'image'      : 'help.svg',
        'title'      : __('Join Our Community', 'notificationx'),
        'desc'       : __('Join the Facebook community to discuss with fellow developers, connect with others, and stay updated.', 'notificationx'),
        'button_text': __('Join Our Community', 'notificationx'),
        'button_url' : 'https://www.facebook.com/groups/NotificationX.Community/',
    },
    {
        'image'      : 'contribute.svg',
        'title'      : __('Need Any Help?', 'notificationx'),
        'desc'       : __('If you encounter issues or need assistance, we\'re here to help or report specific problems on our GitHub issues page.', 'notificationx'),
        'button_text': __('Report a Bug', 'notificationx'),
        'button_url' : 'https://wpdeveloper.com/support/new-ticket/',
    }
]

export const proFeaturePopupConfigCrossDomain = {
    showConfirmButton: true,
    showCloseButton: true,
    title: "Opps! This is PRO Feature.",
    customClass: {
        container: "pro-video-popup",
        closeButton: "pro-video-close-button",
        icon: "pro-video-icon",
        title: "pro-video-title",
        content: "pro-video-content",
        actions: "nx-pro-alert-actions",
        confirmButton: "pro-video-confirm-button",
        denyButton: "pro-video-deny-button"
    },
    denyButtonText: "<a href=' https://notificationx.com/docs/notificationx-cross-domain-notice/' target='_blank'>More Info</a>",
    confirmButtonText: "<a href='https://notificationx.com/#pricing' target='_blank'>Upgrade to PRO</a>",
    html: `
        <span>Boost credibility by displaying single, multiple or all live alerts on multiple websites.</span>
        <video id="pro_alert_video_popup" type="text/html" allowfullscreen width="450" height="235" autoplay loop muted>
            <source src="https://notificationx.com/wp-content/uploads/2024/09/Introducing-NotificationX-CrossDomain-Notice.mp4" type="video/mp4">
        </video>
    `
};

export const modalStyle = {
    overlay: {
        position: "fixed",
        display: "flex",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(3, 6, 60, 0.7)",
        zIndex: 9999999,
        padding: "60px 15px",
    },
    content: {
        position: "static",
        width: '900px',
        margin: "auto",
        border: "0px solid #5414D0",
        // background: "#5414D0",
        overflow: "auto",
        WebkitOverflowScrolling: "touch",
        borderRadius: "4px",
        outline: "none",
        padding: "15px",
    }
}