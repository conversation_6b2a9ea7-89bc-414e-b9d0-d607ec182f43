@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap");

.notificationx-frontend,
#notificationx-frontend,
#notificationx-frontend-crosssite {
    font-family: "Open Sans", sans-serif;
    text-shadow: none;
    color: #000;

    .nx-container {
        position: fixed;
        // overflow-y: scroll;
        // height    : 85%;
        z-index: 99998;
        display: flex;
        flex-direction: column;
        pointer-events: none;

        &.nxc-bottom_left {
            bottom: 0;
            left: 0;
            right: 0;
            align-items: flex-start;

            @media only screen and (max-width: 480px) {
                bottom: 10px;
                left: 10px;
                right: 10px;
            }
        }

        &.nxc-bottom_right {
            bottom: 30px;
            right: 30px;
            left: 0px;
            align-items: flex-end;

            @media only screen and (max-width: 480px) {
                bottom: 10px;
                right: 10px;
                left: 10px;
            }
        }

        &.nxc-top_right {
            top: 30px;
            right: 30px;
            align-items: flex-end;

            @media only screen and (max-width: 480px) {
                top: 10px;
                right: 10px;
                left: 10px;
            }
        }

        &.nxc-top_left {
            left: 30px;
            top: 30px;
            align-items: flex-end;

            @media only screen and (max-width: 480px) {
                top: 10px;
                left: 10px;
                right: 10px;
            }

            .notification-item {}
        }

        &.nxc-center {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100vh;
            bottom: 0;
        }

        .nx-notification {
            pointer-events: initial;
        }
    }
}

.notification-item.nx-notification.position-bottom_right {
    right: 0px !important;
    left: auto !important;
    bottom: 0 !important;
}

@keyframes SlideBottom {
    0% {
        transform: translateY(0%);
    }

    100% {
        transform: translateY(1000%);
    }
}

@keyframes SlideTop {
    0% {
        transform: translateY(1000%);
    }

    100% {
        transform: translateY(0);
    }
}

@keyframes FadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}