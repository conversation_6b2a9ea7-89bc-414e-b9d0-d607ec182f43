// NX PRESS BAR CSS
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap");

#nx-bar-bottom {
    position: relative;
}

.notificationx-shortcode-wrapper>.nx-bar,
.nx-bar {
    @extend %posa;
    left: 0;
    top: 0;
    width: 100%;
    background-color: #ddd;
    text-align: center;
    z-index: 99997;
    font-size: 14px;
    box-shadow: 0 0 70px -30px lighten(#000, 50%);
    @include transition(all 0.2s ease-in);
    font-family: "Open Sans", sans-serif;
    text-shadow: none;
    color: #000;

    @media screen and (max-width: 782px) {
        top: 46px !important;
    }

    &.nx-sticky-bar {
        position: fixed;
        width: 100%;

    }

    &.nx-position-top {
        &.nx-bar-out {
            top: -100% !important;
        }
    }

    &.nx-position-bottom {
        position: absolute;
        top: 0;

        &.nx-bar-out {
            bottom: -100%;
        }

        &.nx-sticky-bar {
            position: fixed;
            top: auto;
            bottom: 0;
        }
    }

    .nx-bar-inner {
        padding: 20px;
        display: inline-block;

        .nx-bar-content-wrap {
            @extend %flex;
            @extend %flex-align-center;

            .nx-countdown-wrapper {
                @extend %flex;
                @extend %flex-align-center;

                .nx-countdown-text {
                    margin-right: 10px;

                    &.nx-expired {
                        display: none;
                    }
                }

                .nx-countdown {
                    overflow: hidden;
                    background-color: #262626;
                    color: #fff;
                    display: flex;

                    .nx-time-section {
                        margin: 0 5px;
                        padding: 10px;
                        position: relative;
                        width: 36px;

                        >span {
                            line-height: 1;
                            display: block;
                        }

                        .nx-countdown-time-text {
                            display: none;
                            font-size: 14px;
                        }

                        &:after {
                            content: ":";
                            @extend %posa;
                            right: -6px;
                            top: 9px;
                            line-height: 1;
                        }

                        &:last-of-type:after {
                            display: none;
                        }
                    }

                    .nx-expired-text {
                        padding: 10px;
                        display: inline-block;

                        &:not(.nx-countdown-expired) {
                            display: none;
                        }
                    }

                    &.nx-expired {
                        padding: 10px;
                        letter-spacing: 1px;

                        .nx-expired-text {
                            display: block;
                        }

                        .nx-time-section {
                            display: none;

                            >span {
                                display: none;
                            }
                        }
                    }
                }
            }

            .nx-inner-content-wrapper {
                @extend %flex;
                @extend %flex-align-center;
            }

            .nx-bar-content {
                display: inline-block;
                margin-left: 15px;
                &.nx-bar-slide-wrapper {
                    min-width: 500px;
                    @media only screen and (max-width: 786px) {
                        min-width: 100%;
                    }
                }
                p {
                    margin-top: 0;
                    margin-bottom: 0;
                }
            }
            .notificationx-link-wrapper {
                margin-left: 15px;
            }
            a.nx-bar-button {
                padding: 6px 15px;
                text-decoration: none;
                display: flex;
                justify-content: center;
                align-items: center;
                color: unset;
                border-radius: 3px;
                cursor: pointer;
                border: none;
            }

            &:not(:last-child) {
                .nx-inner-content-wrapper {
                    margin-right: 40px;
                    @media only screen and (max-width: 786px) {
                        margin-right: 0;
                    }
                }
            }
        }

        .notificationx-close {
            margin: 0;
            @extend %posa;
            right: 30px;
            margin-top: -5px;
            cursor: pointer;
            top: 25%;
            transform: translateY(-50%);
            color: #fff;
            border-radius: 50%;
            width: 10px;
            height: 10px;
            line-height: 17px;
            font-size: 14px;
        }
        .notificationx-close.pressbar.position-top-right {
            right: 20px;
            top: 20px;
            left: auto;
        }
        .notificationx-close.pressbar.position-top-left {
            left: 20px;
            top: 20px;
            right: auto;
        }
    }

    &.nx-close-left {
        .nx-bar-inner {
            .notificationx-close {
                left: 30px;
                right: auto;
            }
        }
    }

    &.nx-bar-has-elementor {
        .nx-bar-inner {
            display: block;
            padding: 0;

            .nx-bar-content-wrap {
                display: block;
            }
        }
    }

     // TWO
    &.press_bar_theme-one {
        .notificationx-link-wrapper {
            color: #fff;
            background-color: #262626;
        }
    }

    // TWO
    &.press_bar_theme-two {
        color: #fff;
        background-color: #5704a2;
        .notificationx-link-wrapper {
            background-color:#9b2aff;
        }
        .nx-bar-inner {
            .notificationx-close {
                fill: #fff;
            }
        }
    }

    // THREE
    &.press_bar_theme-three {
        background-color: #3f4462;
        color: #fff;

        .nx-bar-inner {
            .notificationx-close {
                fill: #fff;
            }

            .notificationx-link-wrapper {
                background-color: #6549fe;
            }

            .nx-bar-content-wrap {
                .nx-countdown-wrapper {
                    .nx-countdown {
                        background: transparent;
                        display: flex;

                        .nx-time-section {
                            width: 52px;
                            background-color: #6549fe;

                            &:after {
                                @extend %content;
                            }

                            >span.nx-countdown-time-text {
                                display: block;
                            }
                        }

                        .nx-expired-text {
                            // margin-bottom: 10px;
                            background-color: #6549fe;

                            @media only screen and (max-width: 786px) {
                                font-size: 10px;
                                line-height: 1.2em;
                                padding: 8px 10px;
                            }
                        }
                    }
                }
            }
        }
    }
    &.press_bar_theme-four, &.press_bar_theme-five {
        .nx-bar-inner {
            .nx-bar-content-wrap {
                .nx-countdown-wrapper {
                    .nx-countdown {
                        .nx-time-section {
                            >span.nx-countdown-time-text {
                                display: block;
                            }
                        }
                    }
                }
            }
        }
    }

    &.press_bar_theme-four { 
        .nx-bar-inner {
            padding: 10px;
            @media screen and (max-width: 1900px) {
                padding: 10px 30px;
            }
            .nx-bar-content-wrap {
                flex-wrap: nowrap;
                justify-content: center;
                gap: 12px;
                .nx-countdown-wrapper {
                    .nx-countdown-text {
                        font-size: 18px;
                        font-weight: 500;
                        line-height: 1em;
                        color: #000123;
                        margin-right: 12px;
                        @media only screen and (max-width: 1024px) {
                            display: none;
                        }
                    }
                    .nx-countdown {
                        overflow: hidden;
                        background: #FFFFFF80;
                        color: #230600;
                        border: 1px solid #F26D6D;
                        border-radius: 4px;
                        display: flex;
                        .nx-time-section {
                            float: left;
                            margin: 0;
                            padding: 12px;
                            position: relative;
                            width: auto;
                            &::after {
                                display: none;
                            }
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 20px;
                                font-weight: 600;
                                line-height: .8em;
                                margin-bottom: 4px;
                            }
                            .nx-countdown-time-text {
                                font-size: 12px;
                                line-height: 1em;
                                font-weight: 400;
                                color: #000123;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 20px;
                        p {
                            font-size: 16px;
                            font-weight: 500;
                            line-height: 1.2em;
                            color: #000123;
                            span {
                                font-size: 16px;
                                font-weight: 600;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 40px;
                        background: #fff;
                        border-radius: 10px;
                        .nx-bar-button {
                            padding: 10px 14px;
                            text-decoration: none;
                            display: inline-block;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: 100%;
                            color: #000123;
                            border: none;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            img {
                                max-height: 20px;
                                max-width: 100%;
                                width: 20px !important;
                                margin-right: 4px !important;
                            }
                        }
                    }   
                }

            }
        }
    }
    &.press_bar_theme-five { 
        .nx-bar-inner {
            padding: 10px;
            @media screen and (max-width: 1900px) {
                padding: 10px 30px;
            }
            .nx-bar-content-wrap {
                justify-content: center;
                gap: 12px;
                .nx-countdown-wrapper {
                    .nx-countdown-text {
                        font-size: 18px;
                        font-weight: 500;
                        line-height: 1em;
                        color: #FFFFFF;
                        margin-right: 12px;
                    }
                    .nx-countdown {
                        overflow: hidden;
                        background: transparent;
                        color: #FFFFFF;
                        border: none;
                        border-radius: unset;
                        display: flex;
                        gap: 8px;
                        .nx-time-section {
                            float: left;
                            margin: 0;
                            padding: 10px 8px;
                            border: 1px solid #8CB1FC;
                            background: #2D72FF;
                            border-radius: 3px;
                            position: relative;
                            width: 60px;
                            max-height: 60px;
                            text-align: center;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            box-sizing: border-box;
                            &::after {
                                display: none;
                            }
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 20px;
                                line-height: 1em;
                                color: #FFFFFF;
                                margin-bottom: 4px;
                            }
                            .nx-countdown-time-text {
                                font-size: 12px;
                                line-height: 1em;
                                font-weight: 500;
                                color: #D1E1FF;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 20px;
                        p {
                            font-size: 16px;
                            font-weight: 500;
                            line-height: 1.2em;
                            color: #CDE1FF;
                            span {
                                font-size: 16px;
                                font-weight: 500;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 40px;
                        background: #2D72FF;
                        border-radius: 40px;
                        .nx-bar-button {
                            padding: 10px 14px;
                            text-decoration: none;
                            display: inline-block;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: 100%;
                            color: #FFFFFF;
                            border: none;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            img {
                                max-height: 20px;
                                max-width: 100%;
                                width: 20px !important;
                                margin-right: 4px !important;
                            }
                        }
                    }   
                }

            }
        }
    }
    &.press_bar_theme-six { 
        .nx-bar-inner {
            padding: 10px;
            @media screen and (max-width: 1900px) {
                padding: 10px 30px;
            }
            .nx-bar-content-wrap {
                flex-wrap: nowrap;
                justify-content: center;
                gap: 12px;
                .nx-countdown-wrapper {
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        overflow: hidden;
                        background: #FFFFFF80;
                        color: #230600;
                        border-radius: 4px;
                        .nx-time-section:not(:last-child) {
                            border-right: 1px solid #FFF;
                        }
                        .nx-time-section {
                            float: left;
                            margin: 12px 0;
                            padding: 0 12px;
                            position: relative;
                            width: auto;
                            &::after {
                                display: none;
                            }
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 20px;
                                font-weight: 600;
                                line-height: .8em;
                                color: #230600;
                                margin-bottom: 4px;
                            }
                            .nx-countdown-time-text {
                                display: block;
                                font-size: 12px;
                                line-height: 1em;
                                font-weight: 400;
                                color: #230600;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 20px;
                        p {
                            font-size: 16px;
                            font-weight: 500;
                            line-height: 1.2em;
                            color: #0D062D;
                            span {
                                font-size: 16px;
                                font-weight: 500;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 40px;
                        background: #280631;
                        border-radius: 40px;
                        .nx-bar-button {
                            padding: 10px 14px;
                            text-decoration: none;
                            display: inline-block;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: 100%;
                            color: #FFFFFF;
                            border: none;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            img {
                                max-height: 20px;
                                max-width: 100%;
                                width: 20px !important;
                                margin-right: 4px !important;
                            }
                        }
                    }   
                }

            }
        }
    }
    &.press_bar_theme-seven { 
        .nx-bar-inner {
            padding: 10px;
            @media screen and (max-width: 1900px) {
                padding: 10px 30px;
            }
            .nx-bar-content-wrap {
                flex-wrap: wrap;
                justify-content: center;
                gap: 12px;
                .nx-inner-content-wrapper {
                    margin-right: 0;
                    flex-wrap: nowrap;
                    justify-content: center;
                    gap: 12px;
                    .nx-bar-content {
                        margin-left: 0;
                        p {
                            font-size: 16px;
                            font-weight: 500;
                            line-height: 1.2em;
                            color: #0D062D;
                            span {
                                font-size: 16px;
                                font-weight: 500;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 28px;
                        background: #E3DAC2;
                        border-radius: 0;
                        @media screen and (max-width: 1271px) {
                            margin-left: 0;
                        }
                        .nx-bar-button {
                            padding: 10px 14px;
                            text-decoration: none;
                            display: inline-block;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: 100%;
                            color: #342600;
                            border: none;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            img {
                                max-height: 20px;
                                max-width: 100%;
                                width: 20px !important;
                                margin-right: 4px !important;
                            }
                        }
                    }   
                }

            }
        }
    }

    @media only screen and (max-width: 786px) {
        .nx-bar-inner {
            .nx-bar-content-wrap {
                // flex-direction: column;

                .nx-countdown-wrapper {
                    margin-bottom: 15px;
                }

                .nx-inner-content-wrapper {
                    // display: grid;
                    grid-gap: 10px;
                    grid-template-areas: "bar-content bar-content"
                        "link-wrapper coupon-wrapper";
                    margin-right: 0 !important;
                    .nx-bar-coupon-wrapper{
                        grid-area: coupon-wrapper;
                        text-align: left;
                    }
                    .nx-bar-content {
                        margin-left: 0px !important;
                        grid-area: bar-content;
                        p {
                            line-height: 1.2em;
                        }
                    }
                    .notificationx-link-wrapper {
                        grid-area: link-wrapper;
                        .nx-bar-button {
                            margin-left: 0 !important;
                        }
                    }
                }
            }

            .notificationx-close {
                bottom: auto;
                top: 10px;
                right: 10px;
                transform: translateY(0px);
            }
        }

        &.nx-close-left {
            .nx-bar-inner {
                .notificationx-close {
                    bottom: auto;
                    top: 10px;
                    left: 10px;
                    right: auto;
                    transform: translateY(0px);
                }
            }
        }
    }

    // Sliding notification bar related styles
    .nx-bar-slide-wrapper {
        position: relative;
        overflow: hidden;
        min-height: 20px;
    }

    .nx-bar-slide {
        opacity: 0;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        /* Transition is set inline via JS to match settings */
        transform: translateX(0);
        h1,h2,h3,h4,h5,h6,p {
            margin: 0;
        }
    }

    .nx-bar-slide.active {
        opacity: 1;
        position: relative;
        transform: translateX(0);
    }

    // Left direction sliding
    .slide-direction-left {
        .nx-bar-slide:not(.active) {
            transform: translateX(100%);
        }

        .nx-bar-slide.left-exit {
            opacity: 0;
            transform: translateX(-100%);
        }
    }

    // Right direction sliding
    .slide-direction-right {
        .nx-bar-slide:not(.active) {
            transform: translateX(-100%);
        }

        .nx-bar-slide.right-exit {
            opacity: 0;
            transform: translateX(100%);
        }
    }

    // &.nx-bar-default-design {
    //     &.press_bar_theme-five .nx-bar-inner .nx-bar-content-wrap .nx-inner-content-wrapper .nx-bar-content p span {
    //         color: #FFFFFF !important;
    //     }
    //    &.press_bar_theme-six .nx-bar-inner .nx-bar-content-wrap .nx-inner-content-wrapper .nx-bar-content p span {
    //         color: #0D062D !important;
    //    }
    //    &.press_bar_theme-four .nx-bar-inner .nx-bar-content-wrap .nx-inner-content-wrapper .nx-bar-content p span {
    //         color: #F54747 !important;
    //    }
    // }

}

.notificationx-shortcode-wrapper>.nx-bar-has-gutenberg .nx-bar-inner,
.nx-bar-has-gutenberg .nx-bar-inner {
    padding: 0;
    display: block;

    .nx-bar-content-wrap {
        display: block;
    }
    .nx-bar-content-wrap {
        .eb-row-inner {
            align-items: center;
        }
    }
    .eb-parent-wrapper .wp-block-essential-blocks-column h2.wp-block-heading {
        margin: 0;
    }
}

// .notificationx-shortcode-wrapper>.nx-bar .nx-bar-inner .nx-bar-content-wrap,
// .nx-bar .nx-bar-inner .nx-bar-content-wrap {
//     display: flex;
//     flex-direction: column;
// }

.nx-bar.nx-bar-tablet-device {
    max-width: 768px;
    margin: 0 auto;
    
    .nx-bar-inner {
        justify-content: center;
        align-items: center;
        .nx-bar-content {
            min-width: auto;
        }
        .nx-bar-content-wrap {
            // flex-direction: column;
            flex-wrap: nowrap;
            gap: 8px;
            .nx-countdown {
                margin-bottom: 0;
                .nx-time-section {
                    width: 44px;
                    margin: 0 2px;
                    span {
                        font-size: 12px;
                    }
                }
            }
        }
    }
    .notificationx-close {
        top: 11px !important;
        right: 8px !important;
    }
}

.nx-bar.nx-bar-mobile-device {
    max-width: 520px;
    margin: 0 auto;
    
    .nx-bar-inner {
        justify-content: center;
        align-items: center;
        .nx-bar-content {
            min-width: auto;
        }
        .nx-bar-content-wrap {
            flex-direction: column;
            gap: 8px;
            &:not(:last-child) {
                .nx-inner-content-wrapper {
                    margin-right: 0;
                }
            }
            .nx-countdown {
                margin-bottom: 0;
                .nx-time-section {
                    width: 44px;
                    margin: 0 2px;
                    span {
                        font-size: 12px;
                    }
                }
            }
        }
        .notificationx-close {
            top: 4px !important;
            right: 4px !important;
            svg {
                max-height: 6px;
            }
        }
    }
}

.nx-bar.press_bar_theme-one {
    &.nx-bar-tablet-device {
        .nx-bar-inner {
            padding: 10px 24px 10px 10px;
            .nx-bar-content-wrap {
                flex-direction: row;
                gap: 16px;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        .nx-time-section {
                            width: 24px;
                            padding: 11px 6px;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 11px;
                            }
                            .nx-countdown-time-text {
                                display: none;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 10px;
                            line-height: 1.2em;
                            span {
                                font-size: 10px;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 16px;
                        border-radius: 0;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 6px 12px;
                            font-size: 10px;
                            line-height: 1.2em;
                            min-width: auto;
                            img {
                                width: 12px !important;
                                height: 12px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
    &.nx-bar-mobile-device {
        .nx-bar-inner {
            padding: 8px 18px 8px 8px;
            .nx-bar-content-wrap {
                gap: 12px;
                flex-direction: row;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        .nx-time-section {
                            width: 24px;
                            padding: 12px 6px;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 10px;
                            }
                            .nx-countdown-time-text {
                                display: none;
                            }
                            &:after {
                                content: ":";
                                right: -4px;
                                top: 10px;
                                font-size: 12px;
                                line-height: 1;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 10px;
                            line-height: 1.2em;
                            span {
                                font-size: 10px;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 12px;
                        border-radius: 0;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 4px 8px;
                            font-size: 8px;
                            line-height: 1.2em;
                            min-width: auto;
                            img {
                                width: 10px !important;
                                height: 10px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

.nx-bar.press_bar_theme-two {
    &.nx-bar-tablet-device {
        .nx-bar-inner {
            padding: 18px 24px;
            .nx-bar-content-wrap {
                .nx-inner-content-wrapper {
                    gap: 16px;
                    margin-right: 0;
                    flex-direction: row;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 12px;
                            line-height: 1.2em;
                            span {
                                font-size: 12px;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 0;
                        border-radius: 0;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 8px 14px;
                            font-size: 12px;
                            line-height: 1.2em;
                            min-width: auto;
                            img {
                                width: 12px !important;
                                height: 12px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
    &.nx-bar-mobile-device {
        .nx-bar-inner {
            padding: 12px 18px;
            .nx-bar-content-wrap {
                .nx-inner-content-wrapper {
                    flex-direction: row;
                    gap: 12px;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 10px;
                            line-height: 1.2em;
                            span {
                                font-size: 10px;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 0;
                        border-radius: 0;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 4px 8px;
                            font-size: 8px;
                            line-height: 1.2em;
                            min-width: auto;
                            img {
                                width: 10px !important;
                                height: 10px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

.nx-bar.press_bar_theme-three {
    &.nx-bar-tablet-device {
        .nx-bar-inner {
            padding: 10px 24px 10px 10px;
            .nx-bar-content-wrap {
                flex-direction: row;
                gap: 16px;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        gap: 4px;
                        .nx-time-section {
                            padding: 6px;
                            width: 32px;
                            max-height: 46px;
                            margin: 0;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 14px;
                                margin-bottom: 6px;
                            }
                            .nx-countdown-time-text {
                                font-size: 8px;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 10px;
                            line-height: 1.2em;
                            span {
                                font-size: 10px;
                                line-height: 1.2em;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 16px;
                        border-radius: 2px;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 8px 10px;
                            font-size: 10px;
                            line-height: 1.2em;
                            min-width: auto;
                            img {
                                width: 12px !important;
                                height: 12px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
    &.nx-bar-mobile-device {
        .nx-bar-inner {
            padding: 8px 18px 8px 8px;
            .nx-bar-content-wrap {
                gap: 12px;
                flex-direction: row;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        gap: 2px;
                        .nx-time-section {
                            padding: 4px;
                            width: 24px;
                            max-height: 34px;
                            margin: 0;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 9px;
                                margin-bottom: 4px;
                            }
                            .nx-countdown-time-text {
                                font-size: 6px;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 9px;
                            span {
                                font-size: 9px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 12px;
                        border-radius: 2px;
                        min-width: auto;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 4px 8px;
                            font-size: 8px;
                            line-height: 1.2em;
                            min-width: auto;
                            img {
                                width: 10px !important;
                                height: 10px !important;
                                margin-right: 2px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

.nx-bar.press_bar_theme-four {
    &.nx-bar-tablet-device {
        .nx-bar-inner {
            padding: 10px 24px;
            .nx-bar-content-wrap {
                flex-direction: row;
                gap: 16px;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        .nx-time-section {
                            padding: 8px;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 16px;
                            }
                            .nx-countdown-time-text {
                                font-size: 9px;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 10px;
                            span {
                                font-size: 11px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 16px;
                        border-radius: 8px;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 6px 12px;
                            font-size: 10px;
                            min-width: auto;
                            img {
                                width: 12px !important;
                                height: 12px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
    &.nx-bar-mobile-device {
        .nx-bar-inner {
            padding: 8px 18px 8px 8px;
            .nx-bar-content-wrap {
                gap: 12px;
                flex-direction: row;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        .nx-time-section {
                            padding: 4px 6px;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 10px;
                            }
                            .nx-countdown-time-text {
                                font-size: 6px;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 10px;
                            span {
                                font-size: 11px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 12px;
                        border-radius: 4px;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 4px 6px;
                            font-size: 7px;
                            min-width: auto;
                            img {
                                width: 10px !important;
                                height: 10px !important;
                                margin-right: 2px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

.nx-bar.press_bar_theme-five {
    &.nx-bar-tablet-device {
        .nx-bar-inner {
            padding: 10px 24px;
            .nx-bar-content-wrap {
                flex-direction: row;
                gap: 16px;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        gap: 4px;
                        .nx-time-section {
                            padding: 8px;
                            width: 42px;
                            max-height: 46px;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 14px;
                                margin-bottom: 6px;
                            }
                            .nx-countdown-time-text {
                                font-size: 8px;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 10px;
                            span {
                                font-size: 11px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 16px;
                        border-radius: 36px;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 8px 10px;
                            font-size: 10px;
                            min-width: auto;
                            img {
                                width: 12px !important;
                                height: 12px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
    &.nx-bar-mobile-device {
        .nx-bar-inner {
            padding: 8px 18px 8px 8px;
            .nx-bar-content-wrap {
                gap: 12px;
                flex-direction: row;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        gap: 2px;
                        .nx-time-section {
                            padding: 6px;
                            width: 34px;
                            max-height: 34px;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 9px;
                                margin-bottom: 4px;
                            }
                            .nx-countdown-time-text {
                                font-size: 6px;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 9px;
                            span {
                                font-size: 9px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 12px;
                        border-radius: 20px;
                        min-width: auto;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 4px 8px;
                            font-size: 8px;
                            min-width: auto;
                            img {
                                width: 10px !important;
                                height: 10px !important;
                                margin-right: 2px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

.nx-bar.press_bar_theme-six {
    &.nx-bar-tablet-device {
        .nx-bar-inner {
            padding: 10px 24px;
            .nx-bar-content-wrap {
                flex-direction: row;
                gap: 16px;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        .nx-time-section {
                            margin: 12px 4px;
                            padding: 0 4px;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 16px;
                                margin-bottom: 5px;
                            }
                            .nx-countdown-time-text {
                                font-size: 8px;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 12px;
                            span {
                                font-size: 12px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 16px;
                        border-radius: 26px;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 8px;
                            font-size: 11px;
                            min-width: auto;
                            img {
                                width: 12px !important;
                                height: 12px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
    &.nx-bar-mobile-device {
        .nx-bar-inner {
            padding: 8px 18px 8px 8px;
            .nx-bar-content-wrap {
                flex-direction: row;
                gap: 12px;
                .nx-countdown-wrapper {
                    margin-bottom: 0;
                    .nx-countdown-text {
                        display: none;
                    }
                    .nx-countdown {
                        .nx-time-section {
                            margin: 8px 2px;
                            padding: 0 4px;
                            .nx-days,
                            .nx-hours,
                            .nx-minutes,
                            .nx-seconds {
                                font-size: 11px;
                                margin-bottom: 4px;
                            }
                            .nx-countdown-time-text {
                                font-size: 6px;
                            }
                        }
                    }
                }
                .nx-inner-content-wrapper {
                    display: flex;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 9px;
                            span {
                                font-size: 9px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 12px;
                        border-radius: 20px;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 5px 8px;
                            font-size: 8px;
                            min-width: auto;
                            img {
                                width: 10px !important;
                                height: 10px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

.nx-bar.press_bar_theme-seven {
    &.nx-bar-tablet-device {
        .nx-bar-inner {
            padding: 18px 24px;
            .nx-bar-content-wrap {
                .nx-inner-content-wrapper {
                    gap: 24px;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 12px;
                            span {
                                font-size: 12px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 0;
                        border-radius: 0;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 8px 16px;
                            font-size: 12px;
                            min-width: auto;
                            img {
                                width: 12px !important;
                                height: 12px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
    &.nx-bar-mobile-device {
        .nx-bar-inner {
            padding: 12px 18px;
            .nx-bar-content-wrap {
                .nx-inner-content-wrapper {
                    gap: 12px;
                    margin-right: 0;
                    .nx-bar-content {
                        margin-left: 0;
                        p,
                        .nx-bar-slide {
                            font-size: 9px;
                            span {
                                font-size: 9px;
                            }
                        }
                    }
                    .notificationx-link-wrapper {
                        margin-left: 0;
                        border-radius: 0;
                        white-space: nowrap;
                        .nx-bar-button {
                            padding: 4px 8px;
                            font-size: 8px;
                            min-width: auto;
                            img {
                                width: 10px !important;
                                height: 10px !important;
                                margin-right: 3px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

