#notificationx {
    .wp-react-form.wprf-tabs-wrapper {
        .wprf-tab-content-wrapper {
             #themes {
                .wprf-section-fields {
                    .wprf-control-wrapper {
                        &.wprf-name-advance_edit {
                            display: none;
                        }
                        &.wprf-name-advance_edit + .nx_bar_import_design{
                            display: none;
                        }
                    }
                    .wprf-control-wrapper.wprf-name-advance_edit + .wprf-control.wprf-nx-bar_with_elementor_install_message-message, 
                    .wprf-control-wrapper.wprf-name-advance_edit + #nxbar_with_elementor + #nx_bar_import_design
                    {
                        display: none;
                    }
                }
            }
            .advance_design_section {
                .wprf-section-title {
                    margin-bottom: 0;
                    border-bottom-left-radius: 10px;
                    border-bottom-right-radius: 10px;
                }
                &:not(.advanced_active) {
                    > .wprf-section-fields {
                        padding: 0 !important;
                    }
                }
                .wprf-section-fields {
                    padding: 0;
                    .wprf-control-wrapper {
                        &.wprf-name-advance_edit {
                            margin: 0;
                        }
                    }
                    .wprf-control-section {
                        .wprf-section-title {
                            padding: 0;
                        }
                        .wprf-section-fields {
                            padding: 0;
                        }
                        &.bar_editor {
                            margin-bottom: 16px;
                            .wprf-section-title {
                                display: none;
                            }
                            .wprf-section-fields {
                                background: transparent;
                                padding: 0;
                                margin: 0;
                                display: flex;
                                gap: 24px;
                                flex-wrap: wrap;
                                .wprf-control {
                                    width: auto;
                                    margin-bottom: 0;
                                    &.wprf-normal-message {
                                        p {
                                            margin: 0;
                                            margin-top: -20px;
                                        }
                                    }
                                    &.wprf-modal {
                                        .wprf-control-wrapper {
                                            padding: 0 !important;
                                            .wprf-button {
                                                min-height: 35px;
                                                border: 1px solid #6a4bff;
                                                border-radius: 6px;
                                                padding: 6px 16px;
                                                font-size: 14px;
                                                font-weight: 500;
                                                background: transparent;
                                                color: #6a4bff;
                                                line-height: 1em;
                                            }
                                        }
                                    }
                                }
                                .wprf-control-wrapper {
                                    &.wprf-name-nx-bar_with_elementor_install {
                                        padding: 0 !important;
                                        .wprf-button {
                                            min-height: 35px;
                                            border: 1px solid #6a4bff;
                                            border-radius: 6px;
                                            padding: 6px 16px;
                                            font-size: 14px;
                                            font-weight: 500;
                                            background: transparent;
                                            color: #6a4bff;
                                            line-height: 1em;
                                        }
                                    }
                                }
                                span {
                                    display: none;
                                }
                            }
                        }
                        &.bar_design {
                            .wprf-section-title {
                                background: transparent;
                            }
                        }
                    }
                    .wprf-name-advance_edit {
                        .wprf-control-label {
                            display: none;
                        }
                        .wprf-control-field {
                            position: absolute;
                            top: 20px;
                            right: 16px;
                            min-height: auto;
                        }
                    }
                }
                &.advanced_active {
                    .wprf-section-title {
                        margin-bottom: 2px;
                        border-bottom-left-radius: 0;
                        border-bottom-right-radius: 0;
                    }
                    .wprf-section-fields {
                        padding: 30px;
                        transition: none;
                        // .wprf-name-enable_coupon {
                        //     margin-bottom: 0;
                        // }
                    }
                }
            }
            &.press_bar {
                .wprf-tab-content {
                    .wprf-control-section {
                        &.main_preview {
                            position: sticky;
                            top: 32px;
                            z-index: 1000;
                            box-shadow: 0 0px 15px #e2d5d56b;
                            border-radius: 0;
                            .wprf-section-title {
                                padding: 12px 24px;
                                min-height: auto;
                                margin: 0;
                            }
                            .wprf-section-fields {
                                padding: 12px 24px;
                                text-align: center;
                                .nx-bar-responsive {
                                    .nx-admin-modal-head {
                                        padding: 0;
                                        position: absolute;
                                        top: 12px;
                                        right: 24px;
                                        .nx-admin-modal-preview-button {
                                            width: 32px;
                                            height: 32px;
                                            background: #F5F7FD;
                                            border-radius: 4px;
                                            svg {
                                                width: 17px !important;
                                                path {
                                                    fill: #7884A0;
                                                }
                                            }
                                            &.active {
                                                svg path {
                                                    fill: #5453fd;
                                                }
                                            }
                                        }
                                    }
                                }
                                .nx-bar {
                                    .nx-bar-inner {
                                        .nx-bar-content-wrap {
                                            flex-wrap: nowrap;
                                        }
                                    }

                                    &.nx-bar-section-preview {
                                        // position: static;
                                        position: sticky; // modified for bar close icon
                                        .nx-bar-inner {
                                            justify-content: center;
                                            align-items: center;
                                            .nx-bar-content {
                                                min-width: auto;
                                            }
                                        }
                                    }

                                    &.nx-bar-desktop {
                                        max-width: 100%;
                                        margin: 0 auto;
                                    }
                                    
                                    &.nx-bar-tablet {
                                        max-width: 768px;
                                        margin: 0 auto;
                                        
                                        .nx-bar-inner {
                                            justify-content: center;
                                            align-items: center;
                                            .nx-bar-content {
                                                min-width: auto;
                                            }
                                            .nx-bar-content-wrap {
                                                // flex-direction: column;
                                                flex-wrap: nowrap;
                                                gap: 8px;
                                                .nx-countdown {
                                                    margin-bottom: 0;
                                                    .nx-time-section {
                                                        width: 44px;
                                                        margin: 0 2px;
                                                        span {
                                                            font-size: 12px;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        .notificationx-close {
                                            top: 11px !important;
                                            right: 8px !important;
                                        }
                                    }

                                    &.nx-bar-phone {
                                        max-width: 520px;
                                        margin: 0 auto;
                                        
                                        .nx-bar-inner {
                                            justify-content: center;
                                            align-items: center;
                                            .nx-bar-content {
                                                min-width: auto;
                                            }
                                            .nx-bar-content-wrap {
                                                flex-direction: column;
                                                gap: 8px;
                                                &:not(:last-child) {
                                                    .nx-inner-content-wrapper {
                                                        margin-right: 0;
                                                    }
                                                }
                                                .nx-countdown {
                                                    margin-bottom: 0;
                                                    .nx-time-section {
                                                        width: 44px;
                                                        margin: 0 2px;
                                                        span {
                                                            font-size: 12px;
                                                        }
                                                    }
                                                }
                                            }
                                            .notificationx-close {
                                                top: 4px !important;
                                                right: 4px !important;
                                                svg {
                                                    max-height: 6px;
                                                }
                                            }
                                        }
                                    }

                                    &.press_bar_theme-one {
                                        &.nx-bar-tablet {
                                            .nx-bar-inner {
                                                padding: 10px 24px 10px 10px;
                                                .nx-bar-content-wrap {
                                                    gap: 16px;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            .nx-time-section {
                                                                width: 16px;
                                                                padding: 11px 6px;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 11px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    display: none;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        margin-right: 0;
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 10px;
                                                                span {
                                                                    font-size: 10px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 16px;
                                                            border-radius: 0;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 6px 12px;
                                                                font-size: 10px;
                                                                line-height: 1.2em;
                                                                min-width: auto;
                                                                img {
                                                                    width: 12px !important;
                                                                    height: 12px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        &.nx-bar-phone {
                                            .nx-bar-inner {
                                                padding: 8px 18px 8px 8px;
                                                .nx-bar-content-wrap {
                                                    gap: 12px;
                                                    flex-direction: row;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            .nx-time-section {
                                                                width: 14px;
                                                                padding: 12px 6px;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 10px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    display: none;
                                                                }
                                                                &:after {
                                                                    content: ":";
                                                                    right: -4px;
                                                                    top: 10px;
                                                                    font-size: 12px;
                                                                    line-height: 1;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        margin-right: 0;
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 10px;
                                                                span {
                                                                    font-size: 10px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 12px;
                                                            border-radius: 0;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 4px 8px;
                                                                font-size: 8px;
                                                                line-height: 1.2em;
                                                                min-width: auto;
                                                                img {
                                                                    width: 10px !important;
                                                                    height: 10px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    &.press_bar_theme-two {
                                        &.nx-bar-tablet {
                                            .nx-bar-inner {
                                                padding: 18px 24px;
                                                .nx-bar-content-wrap {
                                                    .nx-inner-content-wrapper {
                                                        gap: 16px;
                                                        margin-right: 0;
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 12px;
                                                                span {
                                                                    font-size: 12px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 0;
                                                            border-radius: 0;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 8px 14px;
                                                                font-size: 12px;
                                                                line-height: 1.2em;
                                                                min-width: auto;
                                                                img {
                                                                    width: 12px !important;
                                                                    height: 12px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        &.nx-bar-phone {
                                            .nx-bar-inner {
                                                padding: 12px 18px;
                                                .nx-bar-content-wrap {
                                                    .nx-inner-content-wrapper {
                                                        gap: 12px;
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 10px;
                                                                span {
                                                                    font-size: 10px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 0;
                                                            border-radius: 0;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 4px 8px;
                                                                font-size: 8px;
                                                                line-height: 1.2em;
                                                                min-width: auto;
                                                                img {
                                                                    width: 10px !important;
                                                                    height: 10px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    &.press_bar_theme-three {
                                        &.nx-bar-tablet {
                                            .nx-bar-inner {
                                                padding: 10px 24px 10px 10px;
                                                .nx-bar-content-wrap {
                                                    gap: 16px;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            gap: 4px;
                                                            .nx-time-section {
                                                                padding: 6px;
                                                                width: 32px;
                                                                max-height: 46px;
                                                                margin: 0;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 14px;
                                                                    margin-bottom: 6px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    font-size: 8px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        margin-right: 0;
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 10px;
                                                                span {
                                                                    font-size: 10px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 16px;
                                                            border-radius: 2px;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 8px 10px;
                                                                font-size: 10px;
                                                                line-height: 1.2em;
                                                                min-width: auto;
                                                                img {
                                                                    width: 12px !important;
                                                                    height: 12px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        &.nx-bar-phone {
                                            .nx-bar-inner {
                                                padding: 8px 18px 8px 8px;
                                                .nx-bar-content-wrap {
                                                    gap: 12px;
                                                    flex-direction: row;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            gap: 2px;
                                                            .nx-time-section {
                                                                padding: 4px;
                                                                width: 24px;
                                                                max-height: 34px;
                                                                margin: 0;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 9px;
                                                                    margin-bottom: 4px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    font-size: 6px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 9px;
                                                                span {
                                                                    font-size: 9px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 12px;
                                                            border-radius: 2px;
                                                            min-width: auto;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 4px 8px;
                                                                font-size: 8px;
                                                                line-height: 1.2em;
                                                                min-width: auto;
                                                                img {
                                                                    width: 10px !important;
                                                                    height: 10px !important;
                                                                    margin-right: 2px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    &.press_bar_theme-four {
                                        &.nx-bar-tablet {
                                            .nx-bar-inner {
                                                padding: 10px 24px;
                                                .nx-bar-content-wrap {
                                                    gap: 16px;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            .nx-time-section {
                                                                padding: 8px;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 16px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    font-size: 9px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 10px;
                                                                span {
                                                                    font-size: 11px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 16px;
                                                            border-radius: 8px;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 6px 12px;
                                                                font-size: 10px;
                                                                min-width: auto;
                                                                img {
                                                                    width: 12px !important;
                                                                    height: 12px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        &.nx-bar-phone {
                                            .nx-bar-inner {
                                                padding: 8px 18px 8px 8px;
                                                .nx-bar-content-wrap {
                                                    gap: 12px;
                                                    flex-direction: row;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            .nx-time-section {
                                                                padding: 4px 6px;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 10px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    font-size: 6px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 10px;
                                                                span {
                                                                    font-size: 11px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 12px;
                                                            border-radius: 4px;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 4px 6px;
                                                                font-size: 7px;
                                                                min-width: auto;
                                                                img {
                                                                    width: 10px !important;
                                                                    height: 10px !important;
                                                                    margin-right: 2px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    &.press_bar_theme-five {
                                        &.nx-bar-tablet {
                                            .nx-bar-inner {
                                                padding: 10px 24px;
                                                .nx-bar-content-wrap {
                                                    gap: 16px;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            gap: 4px;
                                                            .nx-time-section {
                                                                padding: 8px;
                                                                width: 42px;
                                                                max-height: 46px;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 14px;
                                                                    margin-bottom: 6px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    font-size: 8px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 10px;
                                                                span {
                                                                    font-size: 11px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 16px;
                                                            border-radius: 36px;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 8px 10px;
                                                                font-size: 10px;
                                                                min-width: auto;
                                                                img {
                                                                    width: 12px !important;
                                                                    height: 12px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        &.nx-bar-phone {
                                            .nx-bar-inner {
                                                padding: 8px 18px 8px 8px;
                                                .nx-bar-content-wrap {
                                                    gap: 12px;
                                                    flex-direction: row;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            gap: 2px;
                                                            .nx-time-section {
                                                                padding: 6px;
                                                                width: 34px;
                                                                max-height: 34px;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 9px;
                                                                    margin-bottom: 4px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    font-size: 6px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 9px;
                                                                span {
                                                                    font-size: 9px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 12px;
                                                            border-radius: 20px;
                                                            min-width: auto;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 4px 8px;
                                                                font-size: 8px;
                                                                min-width: auto;
                                                                img {
                                                                    width: 10px !important;
                                                                    height: 10px !important;
                                                                    margin-right: 2px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    &.press_bar_theme-six {
                                        &.nx-bar-tablet {
                                            .nx-bar-inner {
                                                padding: 10px 24px;
                                                .nx-bar-content-wrap {
                                                    gap: 16px;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            .nx-time-section {
                                                                margin: 12px 4px;
                                                                padding: 0 4px;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 16px;
                                                                    margin-bottom: 5px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    font-size: 8px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 12px;
                                                                span {
                                                                    font-size: 12px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 16px;
                                                            border-radius: 26px;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 8px;
                                                                font-size: 11px;
                                                                min-width: auto;
                                                                img {
                                                                    width: 12px !important;
                                                                    height: 12px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        &.nx-bar-phone {
                                            .nx-bar-inner {
                                                padding: 8px 18px 8px 8px;
                                                .nx-bar-content-wrap {
                                                    flex-direction: row;
                                                    gap: 12px;
                                                    .nx-countdown-wrapper {
                                                        .nx-countdown-text {
                                                            display: none;
                                                        }
                                                        .nx-countdown {
                                                            .nx-time-section {
                                                                margin: 8px 2px;
                                                                padding: 0 4px;
                                                                .nx-days,
                                                                .nx-hours,
                                                                .nx-minutes,
                                                                .nx-seconds {
                                                                    font-size: 11px;
                                                                    margin-bottom: 4px;
                                                                }
                                                                .nx-countdown-time-text {
                                                                    font-size: 6px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    .nx-inner-content-wrapper {
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 9px;
                                                                span {
                                                                    font-size: 9px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 12px;
                                                            border-radius: 20px;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 5px 8px;
                                                                font-size: 8px;
                                                                min-width: auto;
                                                                img {
                                                                    width: 10px !important;
                                                                    height: 10px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    &.press_bar_theme-seven {
                                        &.nx-bar-tablet {
                                            .nx-bar-inner {
                                                padding: 18px 24px;
                                                .nx-bar-content-wrap {
                                                    .nx-inner-content-wrapper {
                                                        gap: 24px;
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 12px;
                                                                span {
                                                                    font-size: 12px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 0;
                                                            border-radius: 0;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 8px 16px;
                                                                font-size: 12px;
                                                                min-width: auto;
                                                                img {
                                                                    width: 12px !important;
                                                                    height: 12px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        &.nx-bar-phone {
                                            .nx-bar-inner {
                                                padding: 12px 18px;
                                                .nx-bar-content-wrap {
                                                    .nx-inner-content-wrapper {
                                                        gap: 12px;
                                                        .nx-bar-content {
                                                            margin-left: 0;
                                                            p,
                                                            .nx-bar-slide {
                                                                font-size: 9px;
                                                                span {
                                                                    font-size: 9px;
                                                                }
                                                            }
                                                        }
                                                        .notificationx-link-wrapper {
                                                            margin-left: 0;
                                                            border-radius: 0;
                                                            white-space: nowrap;
                                                            .nx-bar-button {
                                                                padding: 4px 8px;
                                                                font-size: 8px;
                                                                min-width: auto;
                                                                img {
                                                                    width: 10px !important;
                                                                    height: 10px !important;
                                                                    margin-right: 3px !important;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    &.press_bar_theme-four { 
                                        .nx-bar-inner {
                                            padding: 8px;
                                            @media screen and (max-width: 1900px) {
                                                padding: 8px 30px;
                                            }
                                            .nx-bar-content-wrap {
                                                justify-content: center;
                                                gap: 12px;
                                                .nx-countdown-wrapper {
                                                    .nx-countdown-text {
                                                        font-size: 16px;
                                                        font-weight: 500;
                                                        line-height: 1em;
                                                        color: #000123;
                                                        margin-right: 10px;
                                                        min-width: 76px;
                                                    }
                                                    .nx-countdown {
                                                        overflow: hidden;
                                                        background: #FFFFFF80;
                                                        color: #230600;
                                                        border: 1px solid #F26D6D;
                                                        border-radius: 4px;
                                                        display: flex;
                                                        .nx-time-section {
                                                            float: left;
                                                            margin: 0;
                                                            padding: 12px;
                                                            position: relative;
                                                            width: auto;
                                                            &::after {
                                                                display: none;
                                                            }
                                                            .nx-days,
                                                            .nx-hours,
                                                            .nx-minutes,
                                                            .nx-seconds {
                                                                font-size: 20px;
                                                                font-weight: 600;
                                                                line-height: .8em;
                                                                margin-bottom: 4px;
                                                            }
                                                            .nx-countdown-time-text {
                                                                font-size: 12px;
                                                                line-height: 1em;
                                                                font-weight: 400;
                                                                color: #000123;
                                                            }
                                                        }
                                                    }
                                                }
                                                .nx-inner-content-wrapper {
                                                    margin-right: 0;
                                                    .nx-bar-content {
                                                        margin-left: 8px;
                                                        p {
                                                            font-size: 14px;
                                                            font-weight: 500;
                                                            line-height: 1.2em;
                                                            color: #000123;
                                                            span {
                                                                font-size: 16px;
                                                                font-weight: 600;
                                                                line-height: 1.2em;
                                                            }
                                                        }
                                                    }
                                                    .notificationx-link-wrapper {
                                                        margin-left: 20px;
                                                        background: #fff;
                                                        border-radius: 10px;
                                                        .nx-bar-button {
                                                            padding: 10px 14px;
                                                            text-decoration: none;
                                                            display: inline-block;
                                                            font-size: 14px;
                                                            font-weight: 500;
                                                            line-height: 100%;
                                                            color: #000123;
                                                            min-width: 104px;
                                                            border: none;
                                                            display: flex;
                                                            align-items: center;
                                                            justify-content: center;
                                                            cursor: pointer;
                                                            img {
                                                                max-height: 20px;
                                                                max-width: 100%;
                                                                width: 20px !important;
                                                                margin-right: 4px !important;
                                                            }
                                                        }
                                                    }   
                                                }

                                            }
                                        }
                                    }

                                    &.press_bar_theme-five { 
                                        .nx-bar-inner {
                                            padding: 10px;
                                            @media screen and (max-width: 1900px) {
                                                padding: 10px 30px;
                                            }
                                            .nx-bar-content-wrap {
                                                justify-content: center;
                                                gap: 12px;
                                                .nx-countdown-wrapper {
                                                    .nx-countdown-text {
                                                        font-size: 16px;
                                                        font-weight: 500;
                                                        line-height: 1em;
                                                        color: #FFFFFF;
                                                        margin-right: 10px;
                                                        min-width: 76px;
                                                    }
                                                    .nx-countdown {
                                                        overflow: hidden;
                                                        background: transparent;
                                                        color: #FFFFFF;
                                                        border: none;
                                                        border-radius: unset;
                                                        display: flex;
                                                        gap: 8px;
                                                        .nx-time-section {
                                                            float: left;
                                                            margin: 0;
                                                            padding: 10px 8px;
                                                            border: 1px solid #8CB1FC;
                                                            background: #2D72FF;
                                                            border-radius: 3px;
                                                            position: relative;
                                                            width: 60px;
                                                            max-height: 60px;
                                                            text-align: center;
                                                            display: flex;
                                                            flex-direction: column;
                                                            justify-content: center;
                                                            align-items: center;
                                                            box-sizing: border-box;
                                                            &::after {
                                                                display: none;
                                                            }
                                                            .nx-days,
                                                            .nx-hours,
                                                            .nx-minutes,
                                                            .nx-seconds {
                                                                font-size: 20px;
                                                                line-height: 1em;
                                                                color: #FFFFFF;
                                                                margin-bottom: 4px;
                                                            }
                                                            .nx-countdown-time-text {
                                                                font-size: 12px;
                                                                line-height: 1em;
                                                                font-weight: 500;
                                                                color: #D1E1FF;
                                                            }
                                                        }
                                                    }
                                                }
                                                .nx-inner-content-wrapper {
                                                    margin-right: 0;
                                                    .nx-bar-content {
                                                        margin-left: 8px;
                                                        p {
                                                            font-size: 14px;
                                                            font-weight: 500;
                                                            line-height: 1.2em;
                                                            color: #CDE1FF;
                                                            span {
                                                                font-size: 16px;
                                                                font-weight: 500;
                                                                line-height: 1.2em;
                                                            }
                                                        }
                                                    }
                                                    .notificationx-link-wrapper {
                                                        margin-left: 20px;
                                                        background: #2D72FF;
                                                        border-radius: 40px;
                                                        .nx-bar-button {
                                                            padding: 10px 14px;
                                                            text-decoration: none;
                                                            font-size: 14px;
                                                            font-weight: 500;
                                                            line-height: 100%;
                                                            color: #FFFFFF;
                                                            min-width: 62px;
                                                            border: none;
                                                            display: flex;
                                                            align-items: center;
                                                            justify-content: center;
                                                            cursor: pointer;
                                                            img {
                                                                max-height: 20px;
                                                                max-width: 100%;
                                                                width: 20px !important;
                                                                margin-right: 4px !important;
                                                            }
                                                        }
                                                    }   
                                                }

                                            }
                                        }
                                    }

                                    &.press_bar_theme-six { 
                                        .nx-bar-inner {
                                            padding: 8px;
                                            @media screen and (max-width: 1900px) {
                                                padding: 8px 30px;
                                            }
                                            .nx-bar-content-wrap {
                                                flex-wrap: nowrap;
                                                justify-content: center;
                                                gap: 12px;
                                                .nx-countdown-wrapper {
                                                    .nx-countdown-text {
                                                        display: none;
                                                    }
                                                    .nx-countdown {
                                                        overflow: hidden;
                                                        background: #FFFFFF80;
                                                        color: #230600;
                                                        border-radius: 4px;
                                                        display: flex;
                                                        .nx-time-section:not(:last-child) {
                                                            border-right: 1px solid #FFF;
                                                        }
                                                        .nx-time-section {
                                                            float: left;
                                                            margin: 12px 0;
                                                            padding: 0 12px;
                                                            position: relative;
                                                            width: auto;
                                                            &::after {
                                                                display: none;
                                                            }
                                                            .nx-days,
                                                            .nx-hours,
                                                            .nx-minutes,
                                                            .nx-seconds {
                                                                font-size: 20px;
                                                                font-weight: 600;
                                                                line-height: .8em;
                                                                color: #230600;
                                                                margin-bottom: 4px;
                                                            }
                                                            .nx-countdown-time-text {
                                                                display: block;
                                                                font-size: 12px;
                                                                line-height: 1em;
                                                                font-weight: 400;
                                                                color: #230600;
                                                            }
                                                        }
                                                    }
                                                }
                                                .nx-inner-content-wrapper {
                                                    margin-right: 0;
                                                    .nx-bar-content {
                                                        margin-left: 8px;
                                                        p {
                                                            font-size: 14px;
                                                            font-weight: 500;
                                                            line-height: 1.2em;
                                                            color: #0D062D;
                                                            span {
                                                                font-size: 16px;
                                                                font-weight: 500;
                                                                line-height: 1.2em;
                                                            }
                                                        }
                                                    }
                                                    .notificationx-link-wrapper {
                                                        margin-left: 20px;
                                                        background: #280631;
                                                        border-radius: 40px;
                                                        .nx-bar-button {
                                                            padding: 10px 14px;
                                                            text-decoration: none;
                                                            display: inline-block;
                                                            font-size: 14px;
                                                            font-weight: 500;
                                                            line-height: 100%;
                                                            color: #FFFFFF;
                                                            border: none;
                                                            display: flex;
                                                            align-items: center;
                                                            justify-content: center;
                                                            cursor: pointer;
                                                            img {
                                                                max-height: 20px;
                                                                max-width: 100%;
                                                                width: 20px !important;
                                                                margin-right: 4px !important;
                                                            }
                                                        }
                                                    }   
                                                }

                                            }
                                        }
                                    }

                                    &.press_bar_theme-seven { 
                                        .nx-bar-inner {
                                            padding: 8px;
                                            @media screen and (max-width: 1900px) {
                                                padding: 8px 30px;
                                            }
                                            .nx-bar-content-wrap {
                                                .nx-inner-content-wrapper {
                                                    margin-right: 0;
                                                    justify-content: center;
                                                    gap: 12px;
                                                    .nx-bar-content {
                                                        margin-left: 0;
                                                        p {
                                                            font-size: 14px;
                                                            font-weight: 500;
                                                            line-height: 1.2em;
                                                            color: #0D062D;
                                                            span {
                                                                font-size: 16px;
                                                                font-weight: 500;
                                                                line-height: 1.2em;
                                                            }
                                                        }
                                                    }
                                                    .notificationx-link-wrapper {
                                                        margin-left: 12px;
                                                        background: #E3DAC2;
                                                        border-radius: 0;
                                                        @media screen and (max-width: 1271px) {
                                                        margin-left: 0;
                                                        }
                                                        .nx-bar-button {
                                                            padding: 10px 14px;
                                                            text-decoration: none;
                                                            display: inline-block;
                                                            font-size: 14px;
                                                            font-weight: 500;
                                                            line-height: 100%;
                                                            color: #342600;
                                                            border: none;
                                                            display: flex;
                                                            align-items: center;
                                                            justify-content: center;
                                                            cursor: pointer;
                                                            img {
                                                                max-height: 20px;
                                                                max-width: 100%;
                                                                width: 20px !important;
                                                                margin-right: 4px !important;
                                                            }
                                                        }
                                                    }   
                                                }

                                            }
                                        }
                                    }
                                }
                                div > img {
                                    max-width: 100%;
                                }
                            }
                        }
                        &.sliding_text {
                            .wprf-repeater-control .wprf-repeater-field-title .wprf-repeater-field-controls .dashicons-admin-page {
                                display: none;
                            }
                            .wprf-section-fields .wprf-name-press_content .wprf-control-label{
                                opacity: 0;
                            }
                            .wprf-section-title {
                                display: none;
                            }
                            .wprf-section-fields {
                                padding: 0;
                            }
                            .wprf-type-nx-editor {
                                .wprf-editor {
                                    background-color: #F0F3F5;
                                }
                            }
                        }
                        &.pro-activated {
                            .wprf-section-fields {
                                .radio-card-v2 {
                                    .wprf-control-field {
                                        .wprf-row {
                                            .wprf-input-radio-option {
                                                .wprf-badge {
                                                    display: none;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        .wprf-section-fields {
                            .wprf-control-wrapper {
                                &.radio-card-v2 {
                                    .wprf-control-label {
                                        margin: 0;
                                    }
                                    .wprf-control-field {
                                        min-height: 28px;

                                        .wprf-control {
                                            .wprf-row {    
                                                margin: 0;
                                                display: flex;
                                                flex-wrap: nowrap;
                                                gap: 16px;
                                                &:before {
                                                    display: none;
                                                }
                                                &:after {
                                                    display: none;
                                                }
                                                .wprf-column {
                                                    width: auto;
                                                    height: auto;
                                                    padding: 0;
                                                    .wprf-input-radio-option {
                                                        margin-top: 0;
                                                        border-radius: 0;
                                                        box-shadow: none;
                                                        display: flex;
                                                        align-items: unset;
                                                        flex-direction: unset;
                                                        min-height: auto;
                                                        height: auto;
                                                        .wprf-input-label {
                                                            background-color: transparent !important;
                                                            border: none;
                                                            font-size: 14px;
                                                            font-weight: 400;
                                                            color: #092161 !important;
                                                            border-radius: 0;
                                                            flex-grow: unset;
                                                            padding: 0;
                                                            padding-left: 20px;
                                                            line-height: 1em;
                                                            text-align: center;
                                                            cursor: pointer;
                                                            position: relative;

                                                            &:before {
                                                                content: "";
                                                                position: absolute;
                                                                top: 50%;
                                                                left: 0;
                                                                transform: translateY(-50%);
                                                                background: transparent;
                                                                width: 14px;
                                                                height: 14px;
                                                                border: 1.5px solid #DBE3EE;
                                                                border-radius: 50%;
                                                                box-sizing: border-box;
                                                            }
                                                            .wprf-badge {
                                                                top: 50%;
                                                                transform: translateY(-50%);
                                                                left: auto;
                                                                right: -26px;
                                                            }
                                                        }

                                                        &.wprf-option-selected {
                                                            .wprf-input-label {
                                                                &:before {
                                                                    border: 4px solid #6a4bff;
                                                                }
                                                            }
                                                            &:before {
                                                                display: none;
                                                            }
                                                            &:after {
                                                                display: none;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    + .wprf-repeater-control {
                                        width: 70%;
                                        margin-left: auto;
                                        margin-bottom: 30px;

                                        @media only screen and (max-width: 1599px) {
                                            width: 75%;
                                        }
                                        @media only screen and (max-width: 700px) {
                                            width: 100%;
                                        }

                                        .wprf-repeater-field {
                                            .wprf-repeater-field-title {
                                                padding: 12px;
                                                cursor: pointer;
                                                font-size: 12px;
                                                background-color: #F8FAFB;
                                                display: flex;
                                                align-items: center;
                                            }
                                            .wprf-repeater-inner-field {
                                                padding: 12px;
                                                background-color: #F8FAFB;
                                                border: none;
                                                .wprf-control-wrapper {
                                                    align-items: flex-start;
                                                    background: #FFF;
                                                    border: 1px solid #F1F1F1;
                                                    border-radius: 4px;
                                                    .wprf-control-label {
                                                        display: none;
                                                    }
                                                    .wprf-control-field {
                                                        width: 100%;
                                                        .rdw-editor-toolbar {
                                                            padding: 7px 6px 0;
                                                            border-radius: 2px;
                                                            border: none;
                                                            display: flex;
                                                            justify-content: flex-start;
                                                            background: white;
                                                            flex-wrap: wrap;
                                                            font-size: 15px;
                                                            margin-bottom: 5px;
                                                            user-select: none;
                                                        }
                                                        .wprf-editor .wprf-editor-main {
                                                            height: calc(100% - 46px);
                                                            padding: 5px 16px;
                                                            box-sizing: border-box;
                                                            border: none;
                                                            border-top: 0px;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        .wprf-repeater-label {
                                            .wprf-repeater-button {
                                                margin-top: 20px;
                                                border: 1px solid #6A4BFF;
                                                padding: 10px 12px;
                                                background: #6A4BFF;
                                                color: #FFFFFF;
                                                line-height: 1em;
                                                box-sizing: border-box;
                                                gap: 4px;
                                                cursor: pointer;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        &.bar_coupon {
                            .wprf-section-title {
                                margin-bottom: 0;
                                border-bottom-left-radius: 10px;
                                border-bottom-right-radius: 10px;
                            }
                            .wprf-section-fields {
                                padding: 0;
                                .wprf-name-enable_coupon {
                                    .wprf-control-label {
                                        display: none;
                                    }
                                    .wprf-control-field {
                                        position: absolute;
                                        top: 20px;
                                        right: 16px;
                                        min-height: auto;
                                    }
                                }
                            }
                            &.coupon_active {
                                .wprf-section-title {
                                    margin-bottom: 2px;
                                    border-bottom-left-radius: 0;
                                    border-bottom-right-radius: 0;
                                }
                                .wprf-section-fields {
                                    padding: 30px;
                                    .wprf-name-enable_coupon {
                                        margin-bottom: 0;
                                    }
                                }
                            }
                        }
                        &.appearance {
                            .wprf-section-fields {
                                .wprf-control-section {
                                    &.schedule {
                                        .wprf-section-title {
                                            display: none;
                                        }
                                        .wprf-section-fields {
                                            padding: 0;
                                            .wprf-control-wrapper {
                                                &.wprf-name-daily_from_time,
                                                &.wprf-name-weekly_from_time,
                                                &.wprf-name-custom_from_time {
                                                    flex-direction: column;
                                                    display: inline-flex;
                                                    margin-left: 30%;
                                                    margin-right: 16px;
                                        
                                                    @media only screen and (max-width: 1599px) {
                                                        margin-left: 25%;
                                                    }
                                                    @media only screen and (max-width: 700px) {
                                                        margin-left: 0;
                                                    }

                                                    .wprf-control-label {
                                                        flex: 1;
                                                        display: inline-flex;
                                                        margin: 0;
                                                        label {
                                                            margin: 0;
                                                            margin-bottom: 8px;
                                                        }
                                                    }
                                                }
                                                &.wprf-name-daily_to_time,
                                                &.wprf-name-weekly_to_time,
                                                &.wprf-name-custom_to_time {
                                                    flex-direction: column;
                                                    display: inline-flex;
                                                    .wprf-control-label {
                                                        flex: 1;
                                                        display: inline-flex;
                                                        margin: 0;
                                                        label {
                                                            margin: 0;
                                                            margin-bottom: 8px;
                                                        }
                                                    }
                                                }
                                                &.wprf-name-weekly_days,
                                                &.wprf-name-custom_schedule {
                                                    flex-direction: column;
                                                    width: 70%;
                                                    margin-left: auto;
                                        
                                                    @media only screen and (max-width: 1599px) {
                                                        width: 75%;
                                                    }
                                                    @media only screen and (max-width: 700px) {
                                                        width: 1000%;
                                                    }

                                                    .wprf-control-label {
                                                        flex: 1;
                                                        display: inline-flex;
                                                        margin: 0;
                                                        label {
                                                            margin: 0;
                                                            margin-bottom: 8px;
                                                        }
                                                    }
                                                }
                                                .wprf-control-field {
                                                    .wprf-control {
                                                        &.wprf-control-daterange {
                                                            min-width: 400px;
                                                            .wprf-daterange-dropdown {
                                                                .rdrCalendarWrapper {
                                                                    .rdrDateDisplayWrapper {
                                                                        display: none;
                                                                    }
                                                                    .rdrMonthAndYearWrapper {
                                                                        padding-top: 0;
                                                                    }
                                                                    .rdrMonth {
                                                                        .rdrDay {
                                                                            .rdrDayNumber {
                                                                                span {
                                                                                    // color: #535D77 !important;
                                                                                }
                                                                            }
                                                                            &.rdrDayToday {
                                                                                color: #6A4BFF;
                                                                                .rdrDayNumber span:after {
                                                                                    content: '';
                                                                                    position: absolute;
                                                                                    bottom: 4px;
                                                                                    left: 50%;
                                                                                    transform: translate(-50%, 0);
                                                                                    width: 4px;
                                                                                    height: 4px;
                                                                                    border-radius: 50%;
                                                                                    background: #6A4BFF;
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                    .rdrStartEdge {
                                                                        border-top-left-radius: 8px;
                                                                        border-bottom-left-radius: 8px;
                                                                        background-color: #6A4BFF;
                                                                    }
                                                                    .rdrInRange {
                                                                        background: #aba7c2;
                                                                    }
                                                                    .rdrEndEdge {
                                                                        border-top-right-radius: 8px;
                                                                        border-bottom-right-radius: 8px;
                                                                        background-color: #6A4BFF;
                                                                    }
                                                                }
                                                                .wprf-daterange-actions {
                                                                    margin-top: 0;
                                                                    .wprf-daterange-buttons {
                                                                        .wprf-daterange-apply {
                                                                            background-color: #6A4BFF;
                                                                            color: #fff;
                                                                            border-color: #6A4BFF;
                                                                            &:hover {
                                                                                background-color: #6A4BFF;
                                                                                border-color: #6A4BFF;
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        &.wprf-control-timepicker {
                                                            min-width: 192px;
                                                            .wprf-timepicker-formatted {
                                                                display: none;
                                                            }
                                                        }
                                                    }
                                                    .wprf-select-wrapper {
                                                        min-width: 400px;
                                                        margin-right: 0;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        &.timing {
                            .wprf-section-fields {
                                .wprf-name-scroll_offset {
                                    .wprf-control-field {
                                        .wprf-group-control-inner {
                                            gap: 16px;
                                            .wprf-control-wrapper {
                                                margin-bottom: 0;
                                                &.wprf-name-scroll_trigger_value {
                                                    flex-basis: 50%;
                                                    .wprf-control-field input[type=number] {
                                                        max-width: 170px;
                                                        margin-right: 0;
                                                    }
                                                }
                                                &.wprf-name-scroll_trigger_mode {
                                                    .wprf-select-wrapper {
                                                        width: 100%;
                                                        max-width: 110px;
                                                        min-width: 110px;
                                                        margin-right: 0;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #source_section {
                    display: none;
                }
            }
        }
    }
}