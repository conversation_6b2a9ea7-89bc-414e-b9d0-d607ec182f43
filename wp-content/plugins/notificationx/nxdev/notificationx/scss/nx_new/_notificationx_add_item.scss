.wp-react-form.wprf-tabs-wrapper {
    background: transparent;
    border: none;
    margin: 0;

    .wprf-tab-menu-wrapper {
        flex-basis: initial;
        background-color: transparent;
        margin-bottom: 0;

        &.wprf-tab-menu-sidebar {
            .wprf-tab-nav {
                .wprf-tab-nav-item {
                    height: 120px;
                    width: 130px;
                    min-width: 130px;
                    border-radius: 25px;
                    padding: 5px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    margin-bottom: 30px;
                    background: rgba(#6a4bff, 0.05);
                    border: 2px solid #e8e7fe;
                    color: #6a4bff;
                    box-sizing: border-box;

                    @media only screen and (max-width: 1599px) {
                        height: 100px;
                        width: 100px;
                        min-width: 100px;
                        border-radius: 15px;
                    }

                    @media only screen and (max-width: 782px) {
                        height: 50px;
                        width: 50px;
                        min-width: 50px;
                        border-radius: 10px;
                        margin-bottom: 20px;
                    }

                    svg,
                    img {
                        width: 35px;
                        display: flex;
                        fill: #6a4bff;
                        margin-top: 5px;

                        @media only screen and (max-width: 1599px) {
                            width: 30px;
                            margin-top: 0;
                        }

                        @media only screen and (max-width: 782px) {
                            width: 22px;
                        }
                    }

                    span {
                        font-size: 16px;
                        font-weight: 500;
                        line-height: 1.4;
                        text-transform: uppercase;
                        margin-top: 15px;
                        color: #6a4bff;
                        letter-spacing: 0;

                        @media only screen and (max-width: 1599px) {
                            margin-top: 7px;
                            font-size: 13px;
                        }

                        @media only screen and (max-width: 782px) {
                            display: none;
                        }
                    }

                    &:not(:last-child) {
                        position: relative;

                        &:after {
                            content: "";
                            position: absolute;
                            top: calc(100% + 2px);
                            left: 50%;
                            transform: translateX(-50%);
                            height: 30px;
                            width: 2px;
                            background: #e8e7fe;

                            @media only screen and (max-width: 782px) {
                                height: 20px;
                            }
                        }
                    }

                    &.wprf-tab-complete,
                    &.wprf-active-nav {
                        background-color: #6a4bff;
                        border-color: #6a4bff;
                        color: #fff;

                        svg,
                        img {
                            fill: #fff;
                        }

                        span {
                            color: #fff;
                        }

                        &:not(:last-child) {
                            position: relative;

                            &:after {
                                background: #6a4bff;
                            }
                        }
                    }
                }
            }
        }

        &:not(.wprf-tab-menu-sidebar) {
            .wprf-tab-nav {
                margin-bottom: -10px;
                flex-wrap: wrap;
                margin-left: -10px;
                margin-right: -10px;

                .wprf-tab-nav-item {
                    background: rgba(#6a4bff, 0.05);
                    border: 2px solid rgba(#6a4bff, 0.05);
                    border-radius: 15px;
                    color: #25396f;
                    margin-bottom: 10px;
                    text-decoration: none;
                    text-transform: uppercase;
                    display: inline-flex;
                    justify-content: center;
                    align-items: center;
                    text-align: center;
                    min-height: 50px;
                    padding: 5px 25px;
                    font-size: 16px;
                    font-weight: 500;
                    box-sizing: border-box;
                    box-shadow: none;
                    transition: 0.3s ease-in-out;
                    margin-left: 10px;
                    margin-right: 10px;

                    @media only screen and (max-width: 1599px) {
                        font-size: 12px;
                        min-height: 42px;
                        padding: 3px 15px;
                        border-radius: 10px;
                        margin-right: 10px;
                        margin-bottom: 10px;
                    }

                    @media only screen and (max-width: 1399px) {
                        font-size: 12px;
                        min-height: 40px;
                        padding: 3px 15px;
                        border-radius: 10px;
                        margin-right: 10px;
                        margin-bottom: 10px;
                    }

                    &.wprf-active-nav {
                        background: #6a4bff;
                        border-color: #6a4bff;
                        color: #fff;
                    }
                }
            }
        }
    }

    .wprf-tab-content-wrapper {

        padding: 0;
        background: transparent;

        .wprf-tab-content {
            >h4 {
                padding: 0;
                margin-top: 0;
                margin-bottom: 30px;
                font-size: 30px;
                font-weight: bold;
                color: #25396f;
                text-transform: initial;
                letter-spacing: 0;
                line-height: 1.3;
                background: transparent;

                @media only screen and (max-width: 1399px) {
                    margin-bottom: 20px;
                }
            }

            .wprf-tab-heading-wrapper {
                position: relative;
                display: flex;
                gap: 10px;
                align-items: center;
                flex-wrap: wrap;
                justify-content: space-between;
                margin-bottom: 30px;
                flex-wrap: wrap;

                @media only screen and (max-width: 1399px) {
                    margin-bottom: 20px;
                }

                h4 {
                    padding: 0;
                    margin-top: 0;
                    margin-bottom: 0;
                    font-size: 30px;
                    font-weight: bold;
                    color: #25396f;
                    text-transform: initial;
                    letter-spacing: 0;
                    line-height: 1.3;
                    background: transparent;
                }

                >div {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .wprf-btn {
                    background: #6a4bff;
                    border: 1.5px solid #6a4bff;
                    border-radius: 10px;
                    display: flex;
                    min-height: 50px;
                    padding: 2px 25px;
                    font-size: 16px;
                    font-weight: 500;
                    align-items: center;
                    justify-content: center;
                    text-decoration: none;
                    outline: none;
                    color: #fff;
                    line-height: 1.15;
                    transition: 0.3s ease-in-out;
                    cursor: pointer;

                    &:hover {
                        color: #fff;
                        background-color: darken(#5614d5, 1%);
                        border-color: darken(#5614d5, 1%);
                        outline: none;
                        box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                    }

                    &.wprf-step-btn-next {
                        margin-left: auto;
                    }
                }
                .wprf-name-talk_to_support {
                    margin-bottom: 0;
                    .wprf-button {
                        line-height: 1em;
                        min-height: 50px;
                        border: 1px solid #00C185;
                        border-radius: 10px;
                        padding: 2px 24px;
                        background: #00c185;
                        &:hover,
                        &:focus {
                            outline: none;
                            background: #029e6c;
                            box-shadow: 0 15px 25px -5px rgba(0, 193, 133, 0.5);
                        }
                    }
                }
            }

            .wprf-control-section:not(.wprf-no-bg) {
                border: none;
                padding: 0;
                margin-bottom: 0;

                >.wprf-section-title {
                    flex: 0 0 100%;
                    padding: 5px 30px;
                    display: flex;
                    align-items: center;
                    min-height: 60px;
                    background: #fff;
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                    margin-bottom: 2px;
                    box-sizing: border-box;

                    @media only screen and (max-width: 1399px) {
                        padding: 5px 20px;
                        min-height: 50px;
                    }

                    h4 {
                        padding: 0;
                        margin: 0;
                        background: transparent;
                        font-size: 18px;
                        font-weight: bold;
                        color: #25396f;
                        line-height: 1.3;
                        letter-spacing: 0;
                        text-transform: uppercase;
                    }
                }

                >.wprf-section-fields {
                    flex: 0 0 100%;
                    background: #fff;
                    padding: 30px;
                    box-sizing: border-box;

                    @media only screen and (max-width: 1399px) {
                        padding: 20px;
                    }

                    &:first-child {
                        border-top-left-radius: 10px;
                        border-top-right-radius: 10px;
                    }

                    &:last-child {
                        border-bottom-left-radius: 10px;
                        border-bottom-right-radius: 10px;
                    }
                }

                &:not(:last-child) {
                    margin-bottom: 30px;

                    @media only screen and (max-width: 1399px) {
                        margin-bottom: 20px;
                    }
                }
                .wprf-name-display_from {
                    .wprf-control-field {
                        width: 150px;
                        position: relative;
                        input {
                            padding: 2px 70px 2px 15px;
                            border: 1px solid #e6effb;
                            min-height: 40px;
                            width: 100%;
                            @media only screen and (max-width: 1150px) {
                                max-width: 100px;
                            }
                        }
                        p.wprf-description {
                            position: absolute;
                            right: 16px;
                            margin: 0;
                            font-size: 14px;
                            font-weight: 400;
                            color: #7c8db5;
                            left: 105px;
                            @media only screen and (max-width: 1150px) {
                                left: 55px;
                            }
                        }
                    }
                }
                #hour_minutes_section
                 .wprf-section-fields{
                    padding: 0;
                    display: flex;
                    align-items: baseline;
                    gap: 20px;
                    // margin-left: 30%;

                    @media only screen and (max-width: 1599px) {
                        // margin-left: 25%;
                    }
                    @media only screen and (max-width: 700px) {
                        margin-left: 0;
                    }
                    @media only screen and (max-width: 1150px) {
                        gap: 8px;
                    }

                    .wprf-control-label {
                        display: none;
                    }
                    .wprf-name-display_from_hour,
                    .wprf-name-display_from_minute {
                        margin-bottom: 0;

                        .wprf-control-field {
                            width: 100%;
                            max-width: 150px;
                            position: relative;
                            flex-basis: 100%;

                            @media only screen and (max-width: 1150px) {
                                width: 100px;
                            }

                            input {
                                padding: 2px 70px 2px 15px;
                                max-width: 150px;
                                border: 1px solid #e6effb;
                                min-height: 40px;
                                width: 100%;
                            }
                            p.wprf-help {
                                position: absolute;
                                right: 16px;
                                margin: 0;
                                font-size: 14px;
                                font-weight: 400;
                                color: #7c8db5;
                            }
                        }
                    }
                }
                #close_button_section
                 .wprf-section-fields{
                    padding: 0;
                    display: flex;
                    align-items: baseline;
                    .closed_button_section_label {
                        flex-basis: 30%;
                        @media only screen and (max-width: 1599px) {
                            flex: 20%;
                        }
                    }
                    .wprf-control-label {
                        display: none;
                    }
                    .wprf-closed_button_section_label_text-message p {
                        font-size: 15px;
                        font-weight: 500;
                        color: #7c8db5;
                        line-height: 1.2;
                        margin-top: 5px;
                        margin-bottom: 5px;
                        letter-spacing: -0.03px;
                    }
                    .wprf-name-bar_position_left_top .wprf-control-field, 
                    .wprf-name-bar_position_right_top .wprf-control-field, 
                    .wprf-name-bar_position_right_right .wprf-control-field , 
                    .wprf-name-bar_position_left_left .wprf-control-field {
                        width: 190px;
                        position: relative;
                        input {
                            padding: 2px 70px 2px 15px;
                        }
                        p.wprf-help {
                            position: absolute;
                            right: 40px;
                        }
                        p.wprf-description {
                            position: absolute;
                            left: 38px;
                        }
                    }
                }
            }

            #themes.themes {
                .wprf-nx-bar_with_elementor_install_message-message, .wprf-name-elementor_edit_link, .wprf-name-gutenberg_edit_link, .wprf-name-nx-bar_with_elementor_install {
                    padding: 0 32px 10px !important;
                }
                #wprf-modal-nx-bar_with_gutenberg {
                    margin-bottom: 0;
                    .wprf-name-build_with_gutenberg {
                        padding: 0 32px 5px;
                        button[name="build_with_gutenberg"] {
                            margin-bottom: 15px;
                        }
                    }
                }
                #nx_bar_import_design {
                    margin-bottom: 0;
                    .wprf-section-fields {
                        gap: 0;
                    }
                    .wprf-name-nx-bar_with_gutenberg-remove, .wprf-name-nx-bar_with_elementor-remove {
                        padding: 0 32px 10px;
                    }
                    .wprf-name-gutenberg_edit_link, .wprf-name-elementor_edit_link {
                        justify-content: start;
                        align-items: start;
                    }
                }
                #nx_bar_import_design {
                    padding: 0 32px;
                }
            }
            #wprf-modal-nx-bar_with_elementor {
                .wprf-name-build_with_elementor {
                    padding: 0 32px 5px;
                }
            }
            .nx_bar_import_design {
                .wprf-control {
                    margin-bottom: 0px;
                    width: auto;
                }

                .wprf-section-fields {
                    display: flex;
                    gap: 16px;
                    margin: -20px 0 0 -30px;

                    @media only screen and (max-width: 1399px) {
                        margin: -25px 0 0 -20px;
                    }
                }
            }

            .wprf-control-section.wprf-no-bg {
                margin-top: 30px;
                padding-bottom: 0;

                @media only screen and (max-width: 1399px) {
                    margin-top: 20px;
                }

                >.wprf-section-title {
                    >h4 {
                        padding: 0;
                        margin-top: 0;
                        margin-bottom: 30px;
                        font-size: 30px;
                        font-weight: bold;
                        color: #25396f;
                        text-transform: initial;
                        letter-spacing: 0;
                        line-height: 1.3;
                        background: transparent;

                        @media only screen and (max-width: 1399px) {
                            margin-bottom: 20px;
                        }
                    }
                }

                >.wprf-section-fields {
                    padding: 0;
                }
            }
        }

        .wprf-stepped-button {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;

            @media only screen and (max-width: 1399px) {
                margin-top: 20px;
            }

            .wprf-btn {
                background: transparent;
                border: 1.5px solid #6a4bff;
                border-radius: 10px;
                display: flex;
                min-height: 50px;
                padding: 2px 35px;
                font-size: 16px;
                font-weight: 500;
                align-items: center;
                justify-content: center;
                text-decoration: none;
                outline: none;
                color: #6a4bff;
                line-height: 1.15;
                transition: 0.3s ease-in-out;
                cursor: pointer;

                &:hover {
                    color: #fff;
                    background-color: darken(#5614d5, 1%);
                    border-color: darken(#5614d5, 1%);
                    outline: none;
                    box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                }

                &.wprf-step-btn-next {
                    margin-left: auto;
                }
            }
        }

        .wprf-submit {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;

            @media only screen and (max-width: 1399px) {
                margin-top: 20px;
            }

            .wprf-submit-button {
                color: #fff;
                border: 1.5px solid #6a4bff;
                border-radius: 10px;
                display: flex;
                min-height: 50px;
                padding: 2px 35px;
                font-size: 16px;
                font-weight: 500;
                align-items: center;
                justify-content: center;
                text-decoration: none;
                outline: none;
                background: #6a4bff;
                line-height: 1.15;
                transition: 0.3s ease-in-out;
                cursor: pointer;

                @media only screen and (max-width: 1399px) {
                    min-height: 40px;
                    padding: 2px 20px;
                    font-size: 14px;
                    border-radius: 5px;
                }

                &:hover,
                &:focus {
                    color: #fff;
                    background-color: darken(#5614d5, 1%);
                    border-color: darken(#5614d5, 1%);
                    outline: none;
                    box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                }
            }
        }
    }

    &.wprf-tab-menu-as-sidebar {
        .wprf-tab-content-wrapper {
            margin-left: 30px;

            @media only screen and (max-width: 1399px) {
                margin-left: 20px;
            }
        }
    }

    &:not(.wprf-tab-menu-as-sidebar) {
        .wprf-tab-content-wrapper {
            margin-top: 30px;

            @media only screen and (max-width: 1399px) {
                margin-top: 20px;
            }
        }
    }

}

.youtube, .gdpr_notification {
    #source_section {
        .wprf-type-radio-card {
            .wprf-control.wprf-radio-card {

                .wprf-flex .wprf-column:nth-child(2),
                .wprf-flex .wprf-column:last-child {
                    
                    .wprf-input-radio-option {
                        user-select: none;

                        &:before {
                            content: "Coming Soon";
                            position: absolute;
                            top: 8px;
                            right: 8px;
                            text-align: center;
                            font-size: 13px;
                            line-height: 16px;
                            font-weight: 500;
                            letter-spacing: 0.5px;
                            color: #ffffff;
                            z-index: 9;
                            pointer-events: none;
                            background: url('data:image/svg+xml,%3Csvg xmlns%3D"http%3A//www.w3.org/2000/svg" width%3D"16" height%3D"16" viewBox%3D"0 0 16 16" fill%3D"none"%3E%3Crect width%3D"16" height%3D"16" rx%3D"8" fill%3D"white"/%3E%3Cg clip-path%3D"url(%23clip0_3669_3140)"%3E%3Cpath d%3D"M4.83239 7.50004H4.83218L4.05585 7.89965C3.90288 7.97837 3.76647 8.08732 3.65442 8.22029C3.54236 8.35327 3.45685 8.50765 3.40276 8.67464C3.34868 8.84162 3.32709 9.01793 3.33922 9.19351C3.35134 9.36908 3.39695 9.54047 3.47344 9.69791L3.86173 10.4971C4.01625 10.815 4.28712 11.0567 4.61478 11.1691C4.94244 11.2815 5.30006 11.2554 5.60901 11.0965L5.97239 10.9094L6.97735 12.8978L7.74738 12.4855L5.12699 7.30078C5.03403 7.37506 4.93553 7.44169 4.83239 7.50004Z" fill%3D"url(%23paint0_linear_3669_3140)"/%3E%3Cpath d%3D"M6.86595 5.68458C6.42558 6.154 5.96183 6.59957 5.47656 7.0195L7.20757 10.4444C7.70161 10.3258 8.34914 10.2029 9.01891 10.1159C9.92552 9.99813 10.5289 9.98846 10.9113 10.0175L8.07793 4.18555C7.87105 4.51783 7.50149 5.00878 6.86595 5.68458Z" fill%3D"url(%23paint1_linear_3669_3140)"/%3E%3Cpath d%3D"M8.90937 2.90508L8.13281 3.30469L11.6273 10.4976L12.4039 10.098L8.90937 2.90508Z" fill%3D"url(%23paint2_linear_3669_3140)"/%3E%3C/g%3E%3Cdefs%3E%3ClinearGradient id%3D"paint0_linear_3669_3140" x1%3D"5.54166" y1%3D"7.30078" x2%3D"5.54166" y2%3D"12.8978" gradientUnits%3D"userSpaceOnUse"%3E%3Cstop stop-color%3D"%236A4BFF"/%3E%3Cstop offset%3D"1" stop-color%3D"%23A274FF"/%3E%3C/linearGradient%3E%3ClinearGradient id%3D"paint1_linear_3669_3140" x1%3D"8.19395" y1%3D"4.18555" x2%3D"8.19395" y2%3D"10.4444" gradientUnits%3D"userSpaceOnUse"%3E%3Cstop stop-color%3D"%236A4BFF"/%3E%3Cstop offset%3D"1" stop-color%3D"%23A274FF"/%3E%3C/linearGradient%3E%3ClinearGradient id%3D"paint2_linear_3669_3140" x1%3D"8.52109" y1%3D"3.10489" x2%3D"12.1791" y2%3D"10.2136" gradientUnits%3D"userSpaceOnUse"%3E%3Cstop stop-color%3D"%236A4BFF"/%3E%3Cstop offset%3D"1" stop-color%3D"%23A274FF"/%3E%3C/linearGradient%3E%3CclipPath id%3D"clip0_3669_3140"%3E%3Crect width%3D"9.33333" height%3D"10.6667" fill%3D"white" transform%3D"translate(3.33203 2.66602)"/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E');
                            background-size: 16px 16px;
                            background-repeat: no-repeat;
                            padding-left: 20px;
                            
                        }

                        &:after {
                            content: '';
                            position: absolute;
                            inset: 0;
                            background-color: #1E125963;
                            border-radius: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: auto;
                        }
                    }
                }

                img {
                    width: 150px !important;
                }
            }
        }
    }
}

.woocommerce_sales_inline, .announcements, .flashing_tab, .woo_inline, .edd_inline, .tutor_inline, .learndash_inline, .learnpress_inline, .custom_notification {
    #design_tab #themes_section .wprf-section-fields .wprf-tabs-wrapper {
        .woocommerce_sales_inline, .announcements, .flashing_tab, .woo_inline, .edd_inline, .tutor_inline, .learndash_inline, .learnpress_inline, .custom_notification {
            display: none;
        }
    }
}
.press_bar #design_tab .wprf-control-section.themes .wprf-section-fields > .wprf-control-wrapper{
    padding: 32px !important;
}

#design_tab {
    .wprf-control-section.themes {
        .wprf-section-fields {
            padding: 0;

            .wp-react-form.wprf-tabs-wrapper {
                margin: 0;
                
                .wprf-tab-menu-wrapper {
                    border-bottom: 1px solid #F6F7FE;
                    padding: 0 32px;
                    .wprf-tab-nav {
                        margin: 0;
                        .wprf-tab-nav-item {
                            font-size: 16px;
                            line-height: 24px;
                            font-weight: 500;
                            text-transform: capitalize;
                            color: #778095;
                            min-height: auto;
                            padding: 20px;
                            border-radius: 0;
                            margin: 0;
                            background: transparent;
                            border: none;
                            border-bottom: 2px solid transparent;
                            gap: 8px;

                            img{
                                height: 20px;
                            }
                            svg {
                                height: 20px;

                                path {
                                    fill: #778095;
                                }
                            }
                            &.wprf-active-nav {
                                background: transparent;
                                border-color: #6A4BFF;
                                color: #092161;
                                svg {
                                    path {
                                        fill: #778095;
                                    }
                                }
                            }

                            &:nth-child(2) {
                                position: relative;
                                &::after {
                                    content: '';
                                    position: absolute;
                                    width: 20px;
                                    height: 20px;
                                    top: 10px;
                                    right: -2px;
                                    overflow: hidden;
                                    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iMjAiIGZpbGw9IiNGQkU4Q0MiLz4KPHBhdGggZD0iTTI3LjY3OTkgMjcuMjhIMTIuMzE5OUMxMS41MjYgMjcuMjggMTAuODc5OSAyNy45MjYxIDEwLjg3OTkgMjguNzJDMTAuODc5OSAyOS41MTM5IDExLjUyNiAzMC4xNiAxMi4zMTk5IDMwLjE2SDI3LjY3OTlDMjguNDczOCAzMC4xNiAyOS4xMTk5IDI5LjUxMzkgMjkuMTE5OSAyOC43MkMyOS4xMTk5IDI3LjkyNjEgMjguNDczOCAyNy4yOCAyNy42Nzk5IDI3LjI4WiIgZmlsbD0iI0ZGOTkwMCIvPgo8cGF0aCBkPSJNMzAuMDggMTIuODhDMjkuMDIxMSAxMi44OCAyOC4xNiAxMy43NDExIDI4LjE2IDE0LjhDMjguMTYgMTUuNTExNCAyOC41NTM2IDE2LjEyNjcgMjkuMTMwNSAxNi40NTg5QzI4LjAxOTggMTkuMDg5MyAyNi4yODcgMjAuNzAzIDI0LjY2NzUgMjAuNTU3MUMyMi44NjY1IDIwLjQxMDIgMjEuMzk3NyAxOC4yODA5IDIwLjU4NzUgMTQuNzE4NEMyMS42MjYyIDE0LjQ1NTMgMjIuNCAxMy41MTkzIDIyLjQgMTIuNEMyMi40IDExLjA3NjIgMjEuMzIzOCAxMCAyMCAxMEMxOC42NzYxIDEwIDE3LjYgMTEuMDc2MiAxNy42IDEyLjRDMTcuNiAxMy41MTk0IDE4LjM3MzcgMTQuNDU1NCAxOS40MTI0IDE0LjcxODRDMTguNjAyMiAxOC4yODA5IDE3LjEzMzQgMjAuNDEwMiAxNS4zMzI0IDIwLjU1NzFDMTMuNzE5NiAyMC43MDMgMTEuOTc5MSAxOS4wODkzIDEwLjg2OTQgMTYuNDU4OUMxMS40NDYzIDE2LjEyNjcgMTEuODQgMTUuNTExMyAxMS44NCAxNC44QzExLjg0IDEzLjc0MTEgMTAuOTc4OCAxMi44OCA5LjkxOTk1IDEyLjg4QzguODYxMTQgMTIuODggOCAxMy43NDExIDggMTQuOEM4IDE1Ljc4NSA4Ljc0ODc4IDE2LjU4OTUgOS43MDQ5NCAxNi42OTg5TDExLjU1MzkgMjYuMzJIMjguNDQ2MUwzMC4yOTUgMTYuNjk4OUMzMS4yNTEyIDE2LjU4OTUgMzIgMTUuNzg1IDMyIDE0LjhDMzIgMTMuNzQxMSAzMS4xMzg5IDEyLjg4IDMwLjA4IDEyLjg4WiIgZmlsbD0idXJsKCNwYWludDBfbGluZWFyXzU1XzM0NDcpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfNTVfMzQ0NyIgeDE9IjIwIiB5MT0iMTAiIHgyPSIyMCIgeTI9IjI2LjMyIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiNGRkMwNDUiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjRkY5OTAwIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+Cg==);
                                    background-repeat: no-repeat;
                                }
                            }
                        }
                    }
                    &.press_bar {
                        .wprf-tab-nav {
                            .wprf-tab-nav-item {
                                &:nth-child(1) {
                                    display: none;
                                }
                                &:nth-child(2) {
                                    &::after {
                                        display: none;
                                    }
                                }
                                
                            }
                        }
                    }
                }

                .wprf-tab-content-wrapper {
                    margin: 0;
                    .wprf-tab-content {
                        .wprf-tab-heading-wrapper {
                            display: none;
                        }
                        .wprf-control-section {
                            .wprf-section-fields {
                                padding: 0;
                                border-radius: 0;
                                .wprf-control-wrapper {
                                    padding: 32px;
                                }
                            }
                            &.nxbar_custom {
                                .wprf-section-title {
                                    display: none;
                                }
                                .wprf-section-fields {
                                    .nxbar-presets-empty-state {
                                        text-align: center;
                                        padding: 70px 0;
                                        img {
                                            max-width: 100%;
                                        }
                                        h4 {
                                            font-size: 16px;
                                            font-weight: 500;
                                            margin: 0;
                                            margin-top: 20px;
                                            margin-bottom: 20px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .wprf-control-wrapper {
                padding: 10px 32px 32px;
                align-items: center;
                &.wprf-name-gdpr_theme {
                    padding-top: 14px;
                    padding-bottom: 14px;
                    margin-bottom: 0;
                }
                .wprf-control-label {
                    margin: 0;
                    label {
                        margin: 0;
                    }
                }
                .wprf-control-field {
                    min-height: auto;
                }
            }

            #nxbar_with_gutenberg + .wprf-control-section.nx_bar_import_design, #nxbar_with_elementor + .wprf-control-section.nx_bar_import_design {
                .wprf-section-fields {
                    justify-content: end;
                    flex-direction: row-reverse;
                }
            }
            .wprf-control-section.nx_bar_import_design {
                .wprf-section-fields {
                    justify-content: center;
                    margin: 0;
                    .wprf-control-wrapper {
                        .wprf-button {
                            display: inline-flex;
                            min-height: 40px;
                            background: #F7F8FA;
                            border: 1px solid #D9DBE9;
                            border-radius: 8px;
                            padding: 12px 16px 12px 40px;
                            font-size: 14px;
                            font-weight: 500;
                            align-items: center;
                            justify-content: center;
                            text-decoration: none;
                            outline: none;
                            color: #092161;
                            line-height: 1em;
                            transition: 0.25s ease-in-out;
                            box-sizing: border-box;
                            margin-bottom: 0 !important;
                            cursor: pointer;

                            &:hover {
                                color: #092161;
                                background: #F7F8FA;
                                border: 1px solid #D9DBE9;
                                box-shadow: none;
                            }
                            &:focus {
                                box-shadow: none;
                            }
                        }
                        &.wprf-name-build_with_elementor,
                        &.wprf-name-elementor_edit_link, 
                        &.wprf-name-nx-bar_with_elementor_install {
                            padding: 0 6px !important;
                            margin-bottom: 0;
                            .wprf-control-field {
                                .wprf-button {
                                    background-image: url(../../../assets/admin/images/new-img/elementor-icon.svg);
                                    background-size: 20px 20px;
                                    background-repeat: no-repeat;
                                    background-position: left center;
                                    background-position-x: 16px;
                                }
                            }
                        }
                        &.wprf-name-build_with_gutenberg,
                        &.wprf-name-gutenberg_edit_link {
                            padding: 0 6px !important;
                            margin-bottom: 0;
                            .wprf-control-field {
                                .wprf-button {
                                    background-image: url(../../../assets/admin/images/new-img/gutenberg-icon.svg);
                                    background-size: 20px 20px;
                                    background-repeat: no-repeat;
                                    background-position: left center;
                                    background-position-x: 16px;
                                }
                            }
                        }
                        &.wprf-name-nx-bar_with_elementor-remove,
                        &.wprf-name-nx-bar_with_gutenberg-remove {
                            padding: 0 6px !important;
                            margin-bottom: 0;
                            .wprf-control-field {
                                .wprf-button {
                                    padding: 12px 16px;
                                    color: #FB2A04;
                                    background: transparent;
                                    border: none;
                                }
                            }
                        }
                    }
                }
            }
        }
        .pro-deactivated.responsive_themes {
            position: relative;
            .res_get_pro_btn.wprf-name-res_get_pro_btn {
                position: absolute;
                display: flex;
                justify-content: center;
                z-index: 999;
                transform: translate(-50%, -50%);
                left: 50%;
                top:50%;
                padding: 0;
                margin: 0 !important;
                a {
                    background-color: #6A4BFF;
                    color: #fff;
                    border: none;
                    padding: 2px 30px;
                    font-size: 16px;
                    font-weight: 500;
                    border-radius: 10px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 50px;
                    box-sizing: border-box;
                    transition: 0.3s ease-in-out;
                }
            }
            .wprf-control-wrapper.wprf-name-responsive_themes {
                .wprf-control-field {
                    .wprf-input-radio-option {
                        &:before,
                        &:after {
                            display: none;
                        }
                    }
                }
            }
            .wprf-name-responsive_themes .wprf-control-field .wprf-control{
                &::before {
                    position: absolute;
                    content: "";
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background: rgb(105 74 233 / 19%);
                    z-index: 99;
                }
                .nx-stats-pro-tease {
                    position: absolute;
                    z-index: 99;
                    background-color: #6A4BFF;
                    color: #fff;
                    border: none;
                    padding: 2px 30px;
                    font-size: 16px;
                    font-weight: 500;
                    border-radius: 10px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 50px;
                    box-sizing: border-box;
                    transition: 0.3s ease-in-out;
                    a {
                        text-decoration: none;
                        color: #fff;
                    }
                    &:hover {
                        color: #fff;
                        background-color: darken(#5614d5, 1%);
                        outline: none;
                        box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                    }
                }
                .wprf-input-radio-option .wprf-badge{
                    background-image: none;
                }
            }
        }
        &#themes_section.pro-deactivated {
            margin-bottom: 0;
        }
        .pro-activated {
            &.responsive_themes {
                .res_get_pro_btn {
                    display: none;
                }
            }
            .wp-react-form.wprf-tabs-wrapper {
                .wprf-tab-nav-item[data-key="for_mobile"] {
                    &::after {
                        display: none;
                    }
                }
            }
        }
    }

}

.wp-react-form.wprf-tabs-wrapper .wprf-tab-content-wrapper.press_bar .wprf-tab-content .wprf-control-section.themes {
    .wprf-section-fields {
        .wprf-control-section {
            margin-bottom: 0 !important;
            .wprf-section-fields {
                .wprf-tab-content-wrapper.press_bar {
                    .wprf-tab-content {
                        .wprf-control-section.nxbar_custom {
                            .wprf-section-fields {
                                .nxbar-presets-empty-state {
                                    padding-bottom: 0 !important;
                                }
                                .nxbar-selected-presets {
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: center;
                                    margin: 32px 24px;
                                    .nxbar-selected-presets-gutenberg {
                                        display: flex;
                                        justify-content: center;
                                        flex-direction: column;
                                        flex: 1;
                                        padding: 20px 40px;
                                        background: #EAF4FB;
                                        border: 1px solid #EAF4FB;
                                        border-radius: 8px;
                                        img {
                                            max-width: 100%;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .wprf-control-section.nx_bar_import_design {
            padding-bottom: 70px !important;
        }
    }
}