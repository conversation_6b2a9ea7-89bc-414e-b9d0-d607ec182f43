{"name": "notificationx", "version": "3.1.3", "main": "nxdev/index.js", "author": "WPDeveloper", "scripts": {"admin": "wp-scripts build", "admin-watch": "wp-scripts start", "frontend": "wp-scripts build --config webpack.frontend.config.js --webpack-no-externals", "frontend-watch": "wp-scripts start --config webpack.frontend.config.js --webpack-no-externals", "start": "npm run admin-watch & npm run frontend-watch", "build": "npm run admin && npm run frontend", "up": "npm install github:WPDevelopers/quickbuilder#notificationx", "pot": "wp i18n make-pot . languages/notificationx.pot --exclude='nxbuild'", "bb": "wp-scripts build --config webpack.blocks.config.js", "blocks": "wp-scripts start --config webpack.blocks.config.js", "release": "npm run build && npm run bb && npm run pot", "zip": "npm run release && wp dist-archive .", "packages-update": "wp-scripts packages-update"}, "plugins": ["@babel/plugin-transform-runtime", "@babel/plugin-proposal-class-properties"], "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-typescript": "^7.23.3", "@babel/runtime": "7.23.6", "@types/node": "20.10.5", "@types/react": "^17.0.73", "@types/react-router-dom": "^5.3.3", "@wordpress/babel-preset-default": "7.6.0", "@wordpress/eslint-plugin": "13.6.0", "@wordpress/scripts": "^24.6.0", "animate.css": "^4.1.1", "react-select": "^5.8.3", "ts-loader": "^9.5.1", "typescript": "^4.9.5", "url-loader": "^4.1.1"}, "dependencies": {"@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@svgr/webpack": "^6.5.1", "@wordpress/api-fetch": "^6.45.0", "@wordpress/components": "^22.1.0", "@wordpress/compose": "^7.7.0", "@wordpress/data": "^7.6.0", "@wordpress/date": "^4.48.0", "@wordpress/dom-ready": "^3.48.0", "@wordpress/element": "4.20.0", "@wordpress/escape-html": "^2.48.0", "@wordpress/hooks": "^3.48.0", "@wordpress/i18n": "^4.48.0", "@wordpress/icons": "9.13.0", "@wordpress/media-utils": "^4.13.0", "@wordpress/url": "^4.0.0", "apexcharts": "^3.28.1", "classnames": "^2.3.1", "copy-to-clipboard": "^3.3.1", "delegate": "^3.2.0", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "emoji-mart": "^5.5.2", "favloader": "^0.4.4", "html-react-parser": "^3.0.16", "html-to-draftjs": "^1.5.0", "immutable": "^4.3.4", "js-base64": "^3.7.5", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.29.4", "quickbuilder": "github:WPDevelopers/quickbuilder#notificationx", "rc-pagination": "^3.7.0", "react": "^17.0.2", "react-apexcharts": "^1.4.1", "react-cookies": "^0.1.1", "react-copy-to-clipboard": "^5.1.0", "react-date-range": "^2.0.1", "react-dom": "^17.0.2", "react-draft-wysiwyg": "^1.15.0", "react-gcolor-picker": "^1.3.3", "react-lazy-with-preload": "^2.2.1", "react-modal": "^3.16.1", "react-power-tooltip": "^1.0.2", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "react-sortablejs": "^6.1.4", "react-toastify": "^8.2.0", "react-window": "^1.8.10", "sort-array": "^4.1.5", "sweetalert2": "11.4.8", "tiny-warning": "^1.0.3", "uuid": "^8.3.2"}}