<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'NotificationX\\Admin\\Admin' => $baseDir . '/includes/Admin/Admin.php',
    'NotificationX\\Admin\\Cron' => $baseDir . '/includes/Admin/Cron.php',
    'NotificationX\\Admin\\DashboardWidget' => $baseDir . '/includes/Admin/DashboardWidget.php',
    'NotificationX\\Admin\\Entries' => $baseDir . '/includes/Admin/Entries.php',
    'NotificationX\\Admin\\ImportExport' => $baseDir . '/includes/Admin/ImportExport.php',
    'NotificationX\\Admin\\Notice' => $baseDir . '/includes/Admin/Notice.php',
    'NotificationX\\Admin\\PluginInsights' => $baseDir . '/includes/Admin/PluginInsights.php',
    'NotificationX\\Admin\\Rating\\EmailTemplate' => $baseDir . '/includes/Admin/Rating/EmailTemplate.php',
    'NotificationX\\Admin\\Rating\\RatingEmail' => $baseDir . '/includes/Admin/Rating/RatingEmail.php',
    'NotificationX\\Admin\\Reports\\EmailTemplate' => $baseDir . '/includes/Admin/Reports/EmailTemplate.php',
    'NotificationX\\Admin\\Reports\\ReportEmail' => $baseDir . '/includes/Admin/Reports/ReportEmail.php',
    'NotificationX\\Admin\\Scanner\\Scanner' => $baseDir . '/includes/Admin/Scanner/Scanner.php',
    'NotificationX\\Admin\\Settings' => $baseDir . '/includes/Admin/Settings.php',
    'NotificationX\\Admin\\XSS' => $baseDir . '/includes/Admin/XSS.php',
    'NotificationX\\Blocks\\Blocks' => $baseDir . '/blocks/Blocks.php',
    'NotificationX\\Blocks\\StyleHandler' => $baseDir . '/blocks/style-handler/style-handler.php',
    'NotificationX\\CoreInstaller' => $baseDir . '/includes/CoreInstaller.php',
    'NotificationX\\Core\\Ajax' => $baseDir . '/includes/Core/Ajax.php',
    'NotificationX\\Core\\Analytics' => $baseDir . '/includes/Core/Analytics.php',
    'NotificationX\\Core\\Dashboard' => $baseDir . '/includes/Core/Dashboard.php',
    'NotificationX\\Core\\Database' => $baseDir . '/includes/Core/Database.php',
    'NotificationX\\Core\\GetData' => $baseDir . '/includes/Core/GetData.php',
    'NotificationX\\Core\\Helper' => $baseDir . '/includes/Core/Helper.php',
    'NotificationX\\Core\\Inline' => $baseDir . '/includes/Features/Inline.php',
    'NotificationX\\Core\\Limiter' => $baseDir . '/includes/Core/Limiter.php',
    'NotificationX\\Core\\Locations' => $baseDir . '/includes/Core/Locations.php',
    'NotificationX\\Core\\Migration' => $baseDir . '/includes/Core/Migration.php',
    'NotificationX\\Core\\Modules' => $baseDir . '/includes/Core/Modules.php',
    'NotificationX\\Core\\PostType' => $baseDir . '/includes/Core/PostType.php',
    'NotificationX\\Core\\QuickBuild' => $baseDir . '/includes/Core/QuickBuild.php',
    'NotificationX\\Core\\REST' => $baseDir . '/includes/Core/REST.php',
    'NotificationX\\Core\\Rest\\Analytics' => $baseDir . '/includes/Core/Rest/Analytics.php',
    'NotificationX\\Core\\Rest\\BulkAction' => $baseDir . '/includes/Core/Rest/BulkAction.php',
    'NotificationX\\Core\\Rest\\Entries' => $baseDir . '/includes/Core/Rest/Entries.php',
    'NotificationX\\Core\\Rest\\Integration' => $baseDir . '/includes/Core/Rest/Integration.php',
    'NotificationX\\Core\\Rest\\Posts' => $baseDir . '/includes/Core/Rest/Posts.php',
    'NotificationX\\Core\\Rule' => $baseDir . '/includes/Core/Rule.php',
    'NotificationX\\Core\\Rules' => $baseDir . '/includes/Core/Rules.php',
    'NotificationX\\Core\\ShortcodeInline' => $baseDir . '/includes/Features/ShortcodeInline.php',
    'NotificationX\\Core\\Upgrader' => $baseDir . '/includes/Core/Upgrader.php',
    'NotificationX\\Core\\WPDRoleManagement' => $baseDir . '/includes/Core/WPDRoleManagement.php',
    'NotificationX\\Extensions\\ActiveCampaign\\ActiveCampaign' => $baseDir . '/includes/Extensions/ActiveCampaign/ActiveCampaign.php',
    'NotificationX\\Extensions\\CCPA\\CCPA_Notification' => $baseDir . '/includes/Extensions/CCPA/CCPA_Notification.php',
    'NotificationX\\Extensions\\CF7\\CF7' => $baseDir . '/includes/Extensions/CF7/CF7.php',
    'NotificationX\\Extensions\\ConvertKit\\ConvertKit' => $baseDir . '/includes/Extensions/ConvertKit/ConvertKit.php',
    'NotificationX\\Extensions\\CustomNotification\\CustomNotification' => $baseDir . '/includes/Extensions/CustomNotification/CustomNotification.php',
    'NotificationX\\Extensions\\CustomNotification\\CustomNotificationConversions' => $baseDir . '/includes/Extensions/CustomNotification/CustomNotificationConversions.php',
    'NotificationX\\Extensions\\EDD\\EDD' => $baseDir . '/includes/Extensions/EDD/EDD.php',
    'NotificationX\\Extensions\\EDD\\EDDInline' => $baseDir . '/includes/Extensions/EDD/EDInline.php',
    'NotificationX\\Extensions\\Elementor\\From' => $baseDir . '/includes/Extensions/Elementor/From.php',
    'NotificationX\\Extensions\\Envato\\Envato' => $baseDir . '/includes/Extensions/Envato/Envato.php',
    'NotificationX\\Extensions\\Extension' => $baseDir . '/includes/Extensions/Extension.php',
    'NotificationX\\Extensions\\ExtensionFactory' => $baseDir . '/includes/Extensions/ExtensionFactory.php',
    'NotificationX\\Extensions\\FlashingTab\\FlashingTab' => $baseDir . '/includes/Extensions/FlashingTab/FlashingTab.php',
    'NotificationX\\Extensions\\FluentForm\\FluentForm' => $baseDir . '/includes/Extensions/FluentForm/FluentForm.php',
    'NotificationX\\Extensions\\Freemius\\Freemius' => $baseDir . '/includes/Extensions/Freemius/Freemius.php',
    'NotificationX\\Extensions\\Freemius\\FreemiusConversions' => $baseDir . '/includes/Extensions/Freemius/FreemiusConversions.php',
    'NotificationX\\Extensions\\Freemius\\FreemiusReviews' => $baseDir . '/includes/Extensions/Freemius/FreemiusReviews.php',
    'NotificationX\\Extensions\\Freemius\\FreemiusStats' => $baseDir . '/includes/Extensions/Freemius/FreemiusStats.php',
    'NotificationX\\Extensions\\GDPR\\GDPR_Notification' => $baseDir . '/includes/Extensions/GDPR/GDPR_Notification.php',
    'NotificationX\\Extensions\\GRVF\\GravityForms' => $baseDir . '/includes/Extensions/GRVF/GravityForms.php',
    'NotificationX\\Extensions\\Give\\Give' => $baseDir . '/includes/Extensions/Give/Give.php',
    'NotificationX\\Extensions\\GlobalFields' => $baseDir . '/includes/Extensions/GlobalFields.php',
    'NotificationX\\Extensions\\Google\\GoogleReviews' => $baseDir . '/includes/Extensions/Google/GoogleReviews.php',
    'NotificationX\\Extensions\\Google\\YouTube' => $baseDir . '/includes/Extensions/Google/YouTube.php',
    'NotificationX\\Extensions\\Google_Analytics\\Google_Analytics' => $baseDir . '/includes/Extensions/Google/Google_Analytics.php',
    'NotificationX\\Extensions\\IFTTT\\IFTTT' => $baseDir . '/includes/Extensions/IFTTT/IFTTT.php',
    'NotificationX\\Extensions\\LearnDash\\LearnDash' => $baseDir . '/includes/Extensions/LearnDash/LearnDash.php',
    'NotificationX\\Extensions\\LearnDash\\LearnDashInline' => $baseDir . '/includes/Extensions/LearnDash/LearnDashInline.php',
    'NotificationX\\Extensions\\LearnPress\\LearnPress' => $baseDir . '/includes/Extensions/LearnPress/LearnPress.php',
    'NotificationX\\Extensions\\LearnPress\\LearnPressInline' => $baseDir . '/includes/Extensions/LearnPress/LearnPressInline.php',
    'NotificationX\\Extensions\\MailChimp\\MailChimp' => $baseDir . '/includes/Extensions/MailChimp/MailChimp.php',
    'NotificationX\\Extensions\\NJF\\NinjaForms' => $baseDir . '/includes/Extensions/NJF/NinjaForms.php',
    'NotificationX\\Extensions\\OfferAnnouncement\\Announcements' => $baseDir . '/includes/Extensions/OfferAnnouncement/Announcements.php',
    'NotificationX\\Extensions\\Popup\\PopupNotification' => $baseDir . '/includes/Extensions/PopupNotification/PopupNotification.php',
    'NotificationX\\Extensions\\PressBar\\Importer' => $baseDir . '/includes/Extensions/PressBar/importer.php',
    'NotificationX\\Extensions\\PressBar\\PressBar' => $baseDir . '/includes/Extensions/PressBar/PressBar.php',
    'NotificationX\\Extensions\\ReviewX\\ReviewX' => $baseDir . '/includes/Extensions/ReviewX/ReviewX.php',
    'NotificationX\\Extensions\\SureCart\\SureCart' => $baseDir . '/includes/Extensions/SureCart/SureCart.php',
    'NotificationX\\Extensions\\Tutor\\Tutor' => $baseDir . '/includes/Extensions/Tutor/Tutor.php',
    'NotificationX\\Extensions\\Tutor\\TutorInline' => $baseDir . '/includes/Extensions/Tutor/TutorInline.php',
    'NotificationX\\Extensions\\Vimeo\\Vimeo' => $baseDir . '/includes/Extensions/Vimeo/Vimeo.php',
    'NotificationX\\Extensions\\WPF\\WPForms' => $baseDir . '/includes/Extensions/WPF/WPForms.php',
    'NotificationX\\Extensions\\Wistia\\Wistia' => $baseDir . '/includes/Extensions/Wistia/Wistia.php',
    'NotificationX\\Extensions\\WooCommerce\\Woo' => $baseDir . '/includes/Extensions/WooCommerce/Woo.php',
    'NotificationX\\Extensions\\WooCommerce\\WooCommerce' => $baseDir . '/includes/Extensions/WooCommerce/WooCommerce.php',
    'NotificationX\\Extensions\\WooCommerce\\WooCommerceSales' => $baseDir . '/includes/Extensions/WooCommerce/WooCommerceSales.php',
    'NotificationX\\Extensions\\WooCommerce\\WooCommerceSalesInline' => $baseDir . '/includes/Extensions/WooCommerce/WooCommerceSalesInline.php',
    'NotificationX\\Extensions\\WooCommerce\\WooCommerceSalesReviews' => $baseDir . '/includes/Extensions/WooCommerce/WooCommerceSalesReviews.php',
    'NotificationX\\Extensions\\WooCommerce\\WooInline' => $baseDir . '/includes/Extensions/WooCommerce/WooInline.php',
    'NotificationX\\Extensions\\WooCommerce\\WooReviews' => $baseDir . '/includes/Extensions/WooCommerce/WOOReviews.php',
    'NotificationX\\Extensions\\WordPress\\WPComments' => $baseDir . '/includes/Extensions/WordPress/WPComments.php',
    'NotificationX\\Extensions\\WordPress\\WPOrgReview' => $baseDir . '/includes/Extensions/WordPress/WPOrgReview.php',
    'NotificationX\\Extensions\\WordPress\\WPOrgStats' => $baseDir . '/includes/Extensions/WordPress/WPOrgStats.php',
    'NotificationX\\Extensions\\WordPress\\WPOrg_Helper' => $baseDir . '/includes/Extensions/WordPress/WPOrg_Helper.php',
    'NotificationX\\Extensions\\WordPress\\WordPress' => $baseDir . '/includes/Extensions/WordPress/Wordpress.php',
    'NotificationX\\Extensions\\Zapier\\Zapier' => $baseDir . '/includes/Extensions/Zapier/Zapier.php',
    'NotificationX\\Extensions\\Zapier\\ZapierConversions' => $baseDir . '/includes/Extensions/Zapier/ZapierConversions.php',
    'NotificationX\\Extensions\\Zapier\\ZapierEmailSubscription' => $baseDir . '/includes/Extensions/Zapier/ZapierEmailSubscription.php',
    'NotificationX\\Extensions\\Zapier\\ZapierReviews' => $baseDir . '/includes/Extensions/Zapier/ZapierReviews.php',
    'NotificationX\\FrontEnd\\FrontEnd' => $baseDir . '/includes/FrontEnd/FrontEnd.php',
    'NotificationX\\FrontEnd\\Preview' => $baseDir . '/includes/FrontEnd/Preview.php',
    'NotificationX\\GetInstance' => $baseDir . '/includes/GetInstance.php',
    'NotificationX\\NotificationX' => $baseDir . '/includes/NotificationX.php',
    'NotificationX\\ThirdParty\\VisualPortfolio' => $baseDir . '/includes/ThirdParty/VisualPortfolio.php',
    'NotificationX\\ThirdParty\\WPML' => $baseDir . '/includes/ThirdParty/WPML.php',
    'NotificationX\\Types\\Comments' => $baseDir . '/includes/Types/Comments.php',
    'NotificationX\\Types\\ContactForm' => $baseDir . '/includes/Types/ContactForm.php',
    'NotificationX\\Types\\Conversions' => $baseDir . '/includes/Types/Conversions.php',
    'NotificationX\\Types\\CustomNotification' => $baseDir . '/includes/Types/CustomNotification.php',
    'NotificationX\\Types\\Donations' => $baseDir . '/includes/Types/Donations.php',
    'NotificationX\\Types\\DownloadStats' => $baseDir . '/includes/Types/DownloadStats.php',
    'NotificationX\\Types\\ELearning' => $baseDir . '/includes/Types/ELearning.php',
    'NotificationX\\Types\\EmailSubscription' => $baseDir . '/includes/Types/EmailSubscription.php',
    'NotificationX\\Types\\FlashingTab' => $baseDir . '/includes/Types/FlashingTab.php',
    'NotificationX\\Types\\GDPR' => $baseDir . '/includes/Types/GDPR.php',
    'NotificationX\\Types\\Inline' => $baseDir . '/includes/Types/Inline.php',
    'NotificationX\\Types\\NotificationBar' => $baseDir . '/includes/Types/NotificationBar.php',
    'NotificationX\\Types\\OfferAnnouncement' => $baseDir . '/includes/Types/OfferAnnouncement.php',
    'NotificationX\\Types\\PageAnalytics' => $baseDir . '/includes/Types/PageAnalytics.php',
    'NotificationX\\Types\\Popup' => $baseDir . '/includes/Types/Popup.php',
    'NotificationX\\Types\\Reviews' => $baseDir . '/includes/Types/Reviews.php',
    'NotificationX\\Types\\Traits\\Conversions' => $baseDir . '/includes/Types/Traits/Conversions.php',
    'NotificationX\\Types\\Traits\\Reviews' => $baseDir . '/includes/Types/Traits/Reviews.php',
    'NotificationX\\Types\\TypeFactory' => $baseDir . '/includes/Types/TypesFactory.php',
    'NotificationX\\Types\\Types' => $baseDir . '/includes/Types/Types.php',
    'NotificationX\\Types\\Video' => $baseDir . '/includes/Types/Video.php',
    'NotificationX\\Types\\WooCommerceSales' => $baseDir . '/includes/Types/WooCommerceSales.php',
    'UsabilityDynamics\\Job' => $vendorDir . '/wpdeveloper/lib-utility/lib/class-job.php',
    'UsabilityDynamics\\Loader' => $vendorDir . '/wpdeveloper/lib-utility/lib/class-loader.php',
    'UsabilityDynamics\\Settings' => $vendorDir . '/wpdeveloper/lib-settings/lib/class-settings.php',
    'UsabilityDynamics\\Structure' => $vendorDir . '/wpdeveloper/lib-utility/lib/class-structure.php',
    'UsabilityDynamics\\Term' => $vendorDir . '/wpdeveloper/lib-utility/lib/class-term.php',
    'UsabilityDynamics\\Utility' => $vendorDir . '/wpdeveloper/lib-utility/lib/class-utility.php',
    'UsabilityDynamics\\Utility\\Guid_Fix' => $vendorDir . '/wpdeveloper/lib-utility/lib/class-guid-fix.php',
);
