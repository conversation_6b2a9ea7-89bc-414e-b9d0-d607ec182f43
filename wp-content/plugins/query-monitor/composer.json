{"name": "johnbillion/query-monitor", "description": "The Developer Tools panel for WordPress.", "license": "GPL-2.0-or-later", "type": "wordpress-plugin", "authors": [{"name": "<PERSON>", "homepage": "https://johnblackbourn.com/"}], "homepage": "https://querymonitor.com/", "support": {"issues": "https://github.com/johnbillion/query-monitor/issues", "forum": "https://wordpress.org/support/plugin/query-monitor", "security": "https://patchstack.com/database/vdp/query-monitor", "source": "https://github.com/johnbillion/query-monitor"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/johnbillion"}], "require": {"php": ">=7.4.0", "composer/installers": "^1.0 || ^2.0"}, "require-dev": {"behat/gherkin": "< 4.13.0", "codeception/module-asserts": "^1.0", "codeception/module-db": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "dealerdirect/phpcodesniffer-composer-installer": "0.7.2", "guzzlehttp/guzzle": "^7.0", "johnbillion/plugin-infrastructure": "1.2.1", "johnbillion/wp-compat": "0.3.1", "lucatume/wp-browser": "3.2.3", "phpcompatibility/phpcompatibility-wp": "2.1.5", "phpstan/phpstan": "1.12.11", "phpstan/phpstan-deprecation-rules": "1.2.1", "phpstan/phpstan-phpunit": "1.4.1", "roots/wordpress-core-installer": "1.100.0", "roots/wordpress-full": "*", "squizlabs/php_codesniffer": "3.11.1", "szepeviktor/phpstan-wordpress": "1.3.5", "wp-coding-standards/wpcs": "2.3.0"}, "autoload": {"classmap": ["classes", "data", "output"]}, "autoload-dev": {"psr-4": {"QM\\Tests\\": "tests/integration"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "roots/wordpress-core-installer": true, "composer/installers": true}, "classmap-authoritative": true, "preferred-install": "dist", "prepend-autoloader": false, "sort-packages": true}, "extra": {"wordpress-install-dir": "vendor/wordpress/wordpress"}, "scripts": {"test": ["@composer validate --strict --no-check-lock", "@test:phpstan", "@test:phpcs", "@test:start", "@test:integration", "@test:acceptance", "@test:stop"], "test:acceptance": ["npm run build #", "acceptance-tests"], "test:integration": ["integration-tests"], "test:phpcs": ["phpcs -nps --colors --report-code --report-summary --report-width=80 --basepath='./' ."], "test:phpstan": ["codecept build", "phpstan analyze -v --memory-limit=1024M"], "test:start": ["tests-start"], "test:stop": ["tests-stop"]}}