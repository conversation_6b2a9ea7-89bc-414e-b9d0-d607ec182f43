<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInit648cba886c65ae1aaa32611df205c382
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        require __DIR__ . '/platform_check.php';

        spl_autoload_register(array('ComposerAutoloaderInit648cba886c65ae1aaa32611df205c382', 'loadClassLoader'), true, false);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInit648cba886c65ae1aaa32611df205c382', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInit648cba886c65ae1aaa32611df205c382::getInitializer($loader));

        if (method_exists($loader,"setClassMapAuthoritative")){
            $loader->setClassMapAuthoritative(true);
        }
        $loader->register(false);

        return $loader;
    }
}
