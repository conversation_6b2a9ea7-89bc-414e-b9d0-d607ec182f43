/**
 * activation.scss
 * Styles applied to elements displayed on activation
 */
/**
 * Styling begins
 */
div.woocommerce-message {
  overflow: hidden;
  position: relative;
}
div.woocommerce-message.updated {
  border-left-color: var(--wp-admin-theme-color, #720EEC) !important;
}

p.woocommerce-actions a.woocommerce-message-close,
.woocommerce-message a.woocommerce-message-close {
  position: static;
  float: right;
  top: 0;
  right: 0;
  padding: 0 15px 10px 28px;
  margin-top: -10px;
  font-size: 13px;
  line-height: 1.23076923;
  text-decoration: none;
}
p.woocommerce-actions a.woocommerce-message-close::before,
.woocommerce-message a.woocommerce-message-close::before {
  position: relative;
  top: 18px;
  left: -20px;
  transition: all 0.1s ease-in-out;
}
p.woocommerce-actions .button-primary,
p.woocommerce-actions .button-secondary,
.woocommerce-message .button-primary,
.woocommerce-message .button-secondary {
  text-decoration: none !important;
}
p.woocommerce-actions .twitter-share-button,
.woocommerce-message .twitter-share-button {
  margin-top: -3px;
  margin-left: 3px;
  vertical-align: middle;
}

p.woocommerce-actions,
.woocommerce-about-text {
  margin-bottom: 1em !important;
}/*# sourceMappingURL=activation.css.map */