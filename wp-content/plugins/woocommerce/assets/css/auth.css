/**
 * WooCommerce CSS Variables
 */
:root {
  --woocommerce: #720eec;
  --wc-green: #7ad03a;
  --wc-red: #a00;
  --wc-orange: #ffba00;
  --wc-blue: #2ea2cc;
  --wc-primary: #720eec;
  --wc-primary-text: rgb(252.3245945946, 250.73, 254.27);
  --wc-secondary: #e9e6ed;
  --wc-secondary-text: #515151;
  --wc-highlight: rgb(149.3408, 142.38, 8.8592);
  --wc-highligh-text: white;
  --wc-content-bg: #fff;
  --wc-subtext: #767676;
  --wc-form-border-color: rgba(32, 7, 7, 0.8);
  --wc-form-border-radius: 4px;
  --wc-form-border-width: 1px;
}

body {
  background: #f1f1f1;
  box-shadow: none;
  margin: 100px auto 24px;
  padding: 0;
}

#wc-logo {
  border: 0;
  margin: 33px 24px;
  padding: 0;
  text-align: center;
  font-size: 0;
}
#wc-logo img {
  max-width: 50%;
  min-height: 50px;
}

.wc-auth-content {
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.13);
  overflow: hidden;
  padding: 24px 24px 0;
  zoom: 1;
}
.wc-auth-content h1, .wc-auth-content h2, .wc-auth-content h3, .wc-auth-content table {
  border: 0;
  clear: none;
  color: #666;
  margin: 0 0 24px;
  padding: 0;
}
.wc-auth-content p, .wc-auth-content ul {
  color: #666;
  font-size: 1em;
  line-height: 1.75em;
  margin: 0 0 24px;
}
.wc-auth-content p {
  padding: 0;
}
.wc-auth-content a {
  color: #720eec;
}
.wc-auth-content a:hover, .wc-auth-content a:focus {
  color: rgb(20.976, 2.576, 43.424);
}
.wc-auth-content .wc-auth-login label {
  color: #999;
  display: block;
  margin-bottom: 0.5em;
}
.wc-auth-content .wc-auth-login input {
  box-sizing: border-box;
  font-size: 1.3em;
  padding: 0.5em;
  width: 100%;
}
.wc-auth-content .wc-auth-login .wc-auth-actions {
  padding: 0;
}
.wc-auth-content .wc-auth-login .wc-auth-actions .wc-auth-login-button {
  float: none;
  width: 100%;
}

.wc-auth-permissions {
  list-style: disc inside;
  padding: 0;
}
.wc-auth-permissions li {
  font-size: 1em;
}

.wc-auth-logged-in-as {
  background: #f5f5f5;
  border-bottom: 2px solid #eee;
  line-height: 70px;
  margin: 0 0 24px;
  padding: 0 1em 0 0;
}
.wc-auth-logged-in-as p {
  margin: 0;
  line-height: 70px;
}
.wc-auth-logged-in-as img {
  float: left;
  height: 70px;
  margin: 0 1em 0 0;
}
.wc-auth-logged-in-as .wc-auth-logout {
  float: right;
}

.wc-auth .wc-auth-actions {
  overflow: hidden;
  padding-left: 24px;
}
.wc-auth .wc-auth-actions .button {
  background: #f7f7f7;
  border-bottom-width: 2px;
  border: 1px solid #d7d7d7;
  box-sizing: border-box;
  color: #777;
  float: right;
  font-size: 1.25em;
  height: auto;
  line-height: 1em;
  padding: 1em 2em;
  text-align: center;
  width: 50%;
}
.wc-auth .wc-auth-actions .button:hover, .wc-auth .wc-auth-actions .button:focus {
  background: #fcfcfc;
}
.wc-auth .wc-auth-actions .button-primary {
  background: #720eec;
  border-color: #720eec;
  box-shadow: 0 0 0;
  color: #fff;
  float: right;
  opacity: 1;
  text-shadow: none;
}
.wc-auth .wc-auth-actions .button-primary:hover, .wc-auth .wc-auth-actions .button-primary:focus {
  background: rgb(127.432, 33.632, 241.868);
  color: #fff;
}
.wc-auth .wc-auth-actions .wc-auth-approve {
  float: right;
}
.wc-auth .wc-auth-actions .wc-auth-deny {
  float: left;
  margin-left: -24px;
}/*# sourceMappingURL=auth.css.map */