/**
 * Styles for the deprecated version of the coming soon block for
 * coming soon template created before WooCommerce 9.8.0.
 */
body:has(.woocommerce-coming-soon-entire-site) {
  margin: 0;
  background-color: var(--woocommerce-coming-soon-color);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  min-width: 320px;
  --wp--preset--color--contrast: #111;
  --wp--style--global--wide-size: 1280px;
  /* Reset */
}
body:has(.woocommerce-coming-soon-entire-site) h1,
body:has(.woocommerce-coming-soon-entire-site) p,
body:has(.woocommerce-coming-soon-entire-site) a {
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: baseline;
}
body:has(.woocommerce-coming-soon-entire-site) ol,
body:has(.woocommerce-coming-soon-entire-site) ul {
  list-style: none;
}
body:has(.woocommerce-coming-soon-entire-site) .is-layout-constrained > .alignwide {
  margin: 0 auto;
}
body:has(.woocommerce-coming-soon-entire-site) .woocommerce-coming-soon-banner.alignwide {
  max-width: 820px;
}
body:has(.woocommerce-coming-soon-entire-site) .wp-container-core-group-is-layout-4.wp-container-core-group-is-layout-4 {
  justify-content: space-between;
}
body:has(.woocommerce-coming-soon-entire-site) .is-layout-flex {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
}
body:has(.woocommerce-coming-soon-entire-site) .wp-block-social-links {
  gap: 0.5em 18px;
}
body:has(.woocommerce-coming-soon-entire-site) .wp-block-spacer {
  margin: 0;
}
body:has(.woocommerce-coming-soon-entire-site) .woocommerce-coming-soon-banner-container {
  padding-inline: min(5.5rem, 8vw);
  margin: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media (max-width: 660px) {
  body:has(.woocommerce-coming-soon-entire-site) .woocommerce-coming-soon-banner-container {
    padding-inline: 0;
  }
}
body:has(.woocommerce-coming-soon-entire-site) .woocommerce-coming-soon-banner-container > .wp-block-group__inner-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
body:has(.woocommerce-coming-soon-entire-site) .coming-soon-is-vertically-aligned-center {
  width: 100%;
  align-items: stretch;
}
body:has(.woocommerce-coming-soon-entire-site) .coming-soon-cover .wp-block-cover__background {
  background-color: var(--woocommerce-coming-soon-color) !important;
}
body:has(.woocommerce-coming-soon-entire-site) h1.wp-block-heading.woocommerce-coming-soon-banner {
  font-size: clamp(27px, 1.74rem + (1vw - 3px) * 2, 48px);
  font-weight: 400;
  line-height: 58px;
  font-family: Cardo, serif;
  letter-spacing: normal;
  text-align: center;
  font-style: normal;
  max-width: 820px;
  color: var(--wp--preset--color--contrast);
  margin: 0 auto;
  text-wrap: balance;
}
body:has(.woocommerce-coming-soon-entire-site) .wp-block-loginout {
  background-color: #000;
  border-radius: 6px;
  display: flex;
  height: 40px;
  width: 74px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
}
body:has(.woocommerce-coming-soon-entire-site) .wp-block-loginout a {
  color: #fff;
  text-decoration: none;
  line-height: 17px;
  font-size: 14px;
  font-weight: 500;
}

.woocommerce-coming-soon-entire-site .wp-block-loginout a {
  text-decoration: none;
}

/* End of styles for the deprecated version of the coming soon block *//*# sourceMappingURL=coming-soon-entire-site-deprecated.css.map */