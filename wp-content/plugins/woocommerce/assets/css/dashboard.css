/**
 * dashboard.scss
 * Styles for WooCommerce dashboard widgets, only loaded on the dashboard itself.
 */
/**
 * Imports
 */
/**
 * Deprecated
 * Fallback for bourbon equivalent
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include transform(scale(1.5));`
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include box-sizing(border-box);`
 */
/**
 * Objects
 */
/**
 * WooCommerce CSS Variables
 */
:root {
  --woocommerce: #720eec;
  --wc-green: #7ad03a;
  --wc-red: #a00;
  --wc-orange: #ffba00;
  --wc-blue: #2ea2cc;
  --wc-primary: #720eec;
  --wc-primary-text: rgb(252.3245945946, 250.73, 254.27);
  --wc-secondary: #e9e6ed;
  --wc-secondary-text: #515151;
  --wc-highlight: rgb(149.3408, 142.38, 8.8592);
  --wc-highligh-text: white;
  --wc-content-bg: #fff;
  --wc-subtext: #767676;
  --wc-form-border-color: rgba(32, 7, 7, 0.8);
  --wc-form-border-radius: 4px;
  --wc-form-border-width: 1px;
}

/**
 * _fonts.scss
 * Custom WooCommerce fonts.
 */
@font-face {
  font-family: "star";
  src: url("../fonts/WooCommerce.woff2") format("woff2"), url("../fonts/WooCommerce.woff") format("woff"), url("../fonts/WooCommerce.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "WooCommerce";
  src: url("../fonts/WooCommerce.woff2") format("woff2"), url("../fonts/WooCommerce.woff") format("woff"), url("../fonts/WooCommerce.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/**
 * Styling begins
 */
ul.woocommerce_stats {
  overflow: hidden;
  zoom: 1;
}
ul.woocommerce_stats li {
  width: 25%;
  padding: 0 1em;
  text-align: center;
  float: left;
  font-size: 0.8em;
  border-left: 1px solid #fff;
  border-right: 1px solid #ececec;
  box-sizing: border-box;
}
ul.woocommerce_stats li:first-child {
  border-left: 0;
}
ul.woocommerce_stats li:last-child {
  border-right: 0;
}
ul.woocommerce_stats strong {
  font-family: Georgia, "Times New Roman", "Bitstream Charter", Times, serif;
  font-size: 4em;
  line-height: 1.2em;
  font-weight: normal;
  text-align: center;
  display: block;
}

#woocommerce_dashboard_status .wc-status-widget-loading {
  padding: 0 10px;
}
#woocommerce_dashboard_status .inside {
  padding: 0;
  margin: 0;
}
#woocommerce_dashboard_status .best-seller-this-month a strong {
  margin-right: 48px;
}
#woocommerce_dashboard_status .wc_status_list {
  overflow: hidden;
  margin: 0;
}
#woocommerce_dashboard_status .wc_status_list li {
  width: 50%;
  float: left;
  padding: 0;
  box-sizing: border-box;
  margin: 0;
  border-top: 1px solid #ececec;
  color: #aaa;
}
#woocommerce_dashboard_status .wc_status_list li a {
  display: block;
  color: #aaa;
  padding: 9px 12px;
  transition: all ease 0.5s;
  position: relative;
  font-size: 12px;
}
#woocommerce_dashboard_status .wc_status_list li a .wc_sparkline {
  width: 4em;
  height: 2em;
  display: block;
  float: right;
  position: absolute;
  right: 0;
  top: 50%;
  margin-right: 12px;
  margin-top: -1.25em;
}
#woocommerce_dashboard_status .wc_status_list li a strong {
  font-size: 18px;
  line-height: 1.2em;
  font-weight: normal;
  display: block;
  color: #21759b;
}
#woocommerce_dashboard_status .wc_status_list li a:hover {
  color: #2ea2cc;
}
#woocommerce_dashboard_status .wc_status_list li a:hover::before,
#woocommerce_dashboard_status .wc_status_list li a:hover strong {
  color: #2ea2cc !important;
}
#woocommerce_dashboard_status .wc_status_list li a::before {
  font-family: "WooCommerce";
  speak: never;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  margin: 0;
  text-indent: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  content: "\e001";
  font-size: 2em;
  position: relative;
  width: auto;
  line-height: 1.2em;
  color: #464646;
  float: left;
  margin-right: 12px;
  margin-bottom: 12px;
}
#woocommerce_dashboard_status .wc_status_list li:first-child {
  border-top: 0;
}
#woocommerce_dashboard_status .wc_status_list li.sales-this-month {
  width: 100%;
}
#woocommerce_dashboard_status .wc_status_list li.sales-this-month a::before {
  font-family: "Dashicons";
  content: "\f185";
}
#woocommerce_dashboard_status .wc_status_list li.best-seller-this-month {
  width: 100%;
}
#woocommerce_dashboard_status .wc_status_list li.best-seller-this-month a::before {
  content: "\e006";
}
#woocommerce_dashboard_status .wc_status_list li.processing-orders {
  border-right: 1px solid #ececec;
}
#woocommerce_dashboard_status .wc_status_list li.processing-orders a::before {
  content: "\e011";
  color: #7ad03a;
}
#woocommerce_dashboard_status .wc_status_list li.on-hold-orders a::before {
  content: "\e033";
  color: #999;
}
#woocommerce_dashboard_status .wc_status_list li.low-in-stock {
  border-right: 1px solid #ececec;
}
#woocommerce_dashboard_status .wc_status_list li.low-in-stock a::before {
  content: "\e016";
  color: #ffba00;
}
#woocommerce_dashboard_status .wc_status_list li.out-of-stock a::before {
  content: "\e013";
  color: #a00;
}

#woocommerce_dashboard_recent_reviews li {
  line-height: 1.5em;
  margin-bottom: 12px;
}
#woocommerce_dashboard_recent_reviews h4.meta {
  line-height: 1.4;
  margin: -0.2em 0 0 0;
  font-weight: normal;
  color: #999;
}
#woocommerce_dashboard_recent_reviews blockquote {
  padding: 0;
  margin: 0;
}
#woocommerce_dashboard_recent_reviews .avatar {
  float: left;
  margin: 0 10px 5px 0;
}
#woocommerce_dashboard_recent_reviews .star-rating {
  float: right;
  overflow: hidden;
  position: relative;
  height: 1.5em;
  line-height: 1.5;
  margin-left: 0.5em;
  width: 5.4em;
  font-family: "WooCommerce" !important;
}
#woocommerce_dashboard_recent_reviews .star-rating::before {
  content: "\e021\e021\e021\e021\e021";
  color: rgb(178.5, 178.5, 178.5);
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  letter-spacing: 0.1em;
  letter-spacing: 0\9 ;
}
#woocommerce_dashboard_recent_reviews .star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}
#woocommerce_dashboard_recent_reviews .star-rating span::before {
  content: "\e020\e020\e020\e020\e020";
  top: 0;
  position: absolute;
  left: 0;
  letter-spacing: 0.1em;
  letter-spacing: 0\9 ;
  color: var(--wc-primary);
}

#dash-right-now li.product-count a::before {
  font-family: "WooCommerce";
  content: "\e01d";
}

#dashboard_activity #activity-widget #the-comment-list .review.comment-item .avatar {
  margin-right: 12px;
  position: relative;
  top: 0;
  float: left;
}/*# sourceMappingURL=dashboard.css.map */