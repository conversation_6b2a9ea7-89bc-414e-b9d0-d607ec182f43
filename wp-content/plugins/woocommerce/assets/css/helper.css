/*------------------------------------------------------------------------------
  General table styling
------------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
  Tab navigation
------------------------------------------------------------------------------*/
.wc-helper .nav-tab-wrapper {
  margin-bottom: 22px;
}
@media only screen and (max-width: 784px) {
  .wc-helper .nav-tab {
    max-width: 40%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/*------------------------------------------------------------------------------
  Buttons
------------------------------------------------------------------------------*/
.wc-helper .button,
.wc-helper .button:hover,
.wc-helper .button:focus,
.wc-helper .button:active {
  background-color: var(--wp-admin-theme-color);
  border-width: 0;
  box-shadow: none;
  border-radius: 3px;
  color: #fff;
  height: auto;
  padding: 3px 14px;
  text-align: center;
  white-space: normal !important;
}
@media only screen and (max-width: 782px) {
  .wc-helper .button,
  .wc-helper .button:hover,
  .wc-helper .button:focus,
  .wc-helper .button:active {
    line-height: 2;
  }
}
.wc-helper .button.button-secondary,
.wc-helper .button:hover.button-secondary,
.wc-helper .button:focus.button-secondary,
.wc-helper .button:active.button-secondary {
  background-color: #e6e6e6;
  color: #3c3c3c;
  text-shadow: none;
}
.wc-helper .button:hover {
  opacity: 0.8;
}

.wc-helper .subscription-filter {
  font-size: 13px;
  line-height: 13px;
  margin: 22px 0;
}
.wc-helper .subscription-filter label {
  display: none;
  position: relative;
}
.wc-helper .subscription-filter label .chevron {
  color: #e1e1e1;
  border-bottom-width: 0;
  line-height: 1;
  padding: 0;
  position: absolute;
  top: 10px;
  right: 14px;
}
.wc-helper .subscription-filter li {
  display: inline-block;
  padding: 0 4px 0 8px;
  position: relative;
}
.wc-helper .subscription-filter li::before {
  background-color: #979797;
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 1px;
}
.wc-helper .subscription-filter li:first-of-type::before {
  display: none;
}
.wc-helper .subscription-filter a {
  text-decoration: none;
}
.wc-helper .subscription-filter a.current {
  color: #000;
  font-weight: 600;
}
.wc-helper .subscription-filter .count {
  color: #555d66;
  font-weight: 400;
}
@media only screen and (max-width: 600px) {
  .wc-helper .subscription-filter {
    background-color: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    font-size: 14px;
  }
  .wc-helper .subscription-filter label,
  .wc-helper .subscription-filter li {
    line-height: 21px;
    padding: 8px 16px;
    margin: 0;
  }
  .wc-helper .subscription-filter label:last-child,
  .wc-helper .subscription-filter li:last-child {
    border-bottom: none;
  }
  .wc-helper .subscription-filter li {
    border-bottom: 1px solid #e1e1e1;
  }
  .wc-helper .subscription-filter label,
  .wc-helper .subscription-filter span.chevron {
    display: block;
  }
  .wc-helper .subscription-filter label {
    text-decoration: none;
  }
  .wc-helper .subscription-filter li {
    display: none;
  }
  .wc-helper .subscription-filter li::before {
    display: none;
  }
  .wc-helper .subscription-filter a {
    cursor: pointer;
  }
  .wc-helper .subscription-filter span.chevron {
    color: #555;
    opacity: 0.5;
    transform: rotateX(180deg);
  }
  .wc-helper .subscription-filter:focus, .wc-helper .subscription-filter:hover {
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  }
  .wc-helper .subscription-filter:focus label, .wc-helper .subscription-filter:hover label {
    border-bottom: 1px solid #e1e1e1;
  }
  .wc-helper .subscription-filter:focus li, .wc-helper .subscription-filter:hover li {
    display: block;
  }
  .wc-helper .subscription-filter:focus span.chevron, .wc-helper .subscription-filter:hover span.chevron {
    transform: rotateX(0deg);
  }
}

/*------------------------------------------------------------------------------
  Subscriptions Header
------------------------------------------------------------------------------*/
.wc-helper .subscriptions-header {
  margin: 3em 0 0;
  position: relative;
  z-index: 10;
}
.wc-helper .subscriptions-header h2 {
  display: inline-block;
  line-height: 25px;
  margin: 0 0 1.5em 0;
}
.wc-helper .button-update,
.wc-helper .button-update:hover {
  background-color: #e6e6e6;
  border-radius: 4px;
  color: #333;
  font-weight: 800;
  font-size: 10px;
  line-height: 20px;
  margin-left: 6px;
  opacity: 0.75;
  padding: 3px 7px;
  text-transform: uppercase;
}
.wc-helper .button-update .dashicons,
.wc-helper .button-update:hover .dashicons {
  font-size: 12px;
  height: 12px;
  width: 12px;
  vertical-align: text-bottom;
}
.wc-helper .button-update:hover {
  opacity: 1;
}
.wc-helper .user-info {
  background-color: #fff;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  font-size: 12px;
  line-height: 26px;
  position: absolute;
  top: -10px;
  right: 0;
  transition: all 0.1s ease-in;
}
@media only screen and (max-width: 600px) {
  .wc-helper .user-info {
    position: relative;
    width: 100%;
  }
}
.wc-helper .user-info p {
  line-height: 26px;
  margin: 0;
}
.wc-helper .user-info:hover {
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
}
.wc-helper .user-info header {
  color: #555;
  font-weight: 600;
  padding: 6px 14px;
  position: relative;
}
.wc-helper .user-info header p {
  padding-right: 26px;
}
.wc-helper .user-info header .dashicons {
  opacity: 0.5;
  position: absolute;
  top: 9px;
  right: 14px;
}
.wc-helper .user-info header:hover {
  cursor: pointer;
}
.wc-helper .user-info section {
  display: none;
}
.wc-helper .user-info section p {
  border-top: 1px solid #e1e1e1;
  padding: 6px 14px;
  text-align: center;
}
.wc-helper .user-info section .actions {
  border-top: 1px solid #e1e1e1;
  display: flex;
}
.wc-helper .user-info section a {
  cursor: pointer;
  font-weight: 600;
  line-height: 38px;
  padding: 0 14px;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  width: 50%;
}
.wc-helper .user-info section a .dashicons {
  margin-top: -3px;
  vertical-align: middle;
}
.wc-helper .user-info section a:first-child {
  border-right: 1px solid #e1e1e1;
}
.wc-helper .user-info section a:hover {
  background-color: var(--wp-admin-theme-color);
  color: #fff;
}
.wc-helper .user-info section .avatar {
  border: 1px solid #ece1ea;
  border-radius: 50%;
  height: auto;
  margin-right: 6px;
  width: 24px;
  vertical-align: bottom;
}
.wc-helper .user-info:hover header .dashicons,
.wc-helper .user-info:focus header .dashicons,
.wc-helper .user-info:active header .dashicons {
  transform: rotateX(180deg);
}
.wc-helper .user-info:hover section,
.wc-helper .user-info:focus section,
.wc-helper .user-info:active section {
  display: block;
}

/*------------------------------------------------------------------------------
  Subscription table
------------------------------------------------------------------------------*/
.wc-helper .striped > tbody > :nth-child(odd),
.wc-helper ul.striped > :nth-child(odd),
.wc-helper .alternate {
  background-color: #fff;
}
.wc-helper table.widefat,
.wc-helper .wp-editor-container,
.wc-helper .stuffbox,
.wc-helper p.popular-tags,
.wc-helper .widgets-holder-wrap,
.wc-helper .popular-tags,
.wc-helper .feature-filter,
.wc-helper .imgedit-group,
.wc-helper .comment-ays {
  padding-top: 5px;
}
.wc-helper .widefat thead tr th,
.wc-helper .widefat thead tr td,
.wc-helper .widefat tfoot tr th,
.wc-helper .widefat tfoot tr td {
  color: #32373c;
  padding-bottom: 15px;
  padding-top: 10px;
}
.wc-helper .widefat td {
  padding-bottom: 15px;
  padding-top: 15px;
}
.wc-helper .wp-list-table {
  border: 0;
  box-shadow: none;
  padding-top: 0 !important;
  z-index: 1;
}
@media only screen and (max-width: 782px) {
  .wc-helper .button {
    font-size: 11px;
  }
}
.wc-helper .wp-list-table__row {
  background-color: rgba(0, 0, 0, 0);
}
.wc-helper .wp-list-table__row td {
  align-items: center;
  background-color: #fff;
  border: 0;
  padding: 16px 22px;
  vertical-align: middle;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__row td {
    padding: 16px;
  }
}
.wc-helper .wp-list-table__row td.color-bar {
  border-left: 0;
}
.wc-helper .wp-list-table__row.is-ext-header td {
  border-top: 1px solid #e1e1e1;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__row.is-ext-header {
    display: inline-flex;
    flex-flow: row wrap;
    width: 100%;
  }
  .wc-helper .wp-list-table__row.is-ext-header .wp-list-table__ext-details {
    display: block;
    flex: 2;
  }
  .wc-helper .wp-list-table__row.is-ext-header .wp-list-table__ext-actions {
    display: block;
    flex: 1;
    min-width: 0;
  }
}
.wc-helper .wp-list-table__row:last-child td {
  border-bottom: 24px solid #f1f1f1;
  box-shadow: inset 0 -1px 0 #e1e1e1;
}
.wc-helper .wp-list-table__ext-details,
.wc-helper .wp-list-table__ext-status,
.wc-helper .wp-list-table__licence-container {
  padding-right: 22px;
  position: relative;
  width: 100%;
}
.wc-helper .wp-list-table__ext-details::before,
.wc-helper .wp-list-table__ext-status::before,
.wc-helper .wp-list-table__licence-container::before {
  background-color: #e1e1e1;
  content: " ";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0 !important;
  width: 1px !important;
}
.wc-helper .wp-list-table__ext-details {
  display: flex;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__ext-details {
    display: table;
  }
}
.wc-helper .wp-list-table__ext-title {
  color: var(--wp-admin-theme-color);
  font-size: 18px;
  font-weight: 600;
  width: 60%;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__ext-title {
    margin-bottom: 12px;
    width: 100%;
  }
}
@media only screen and (max-width: 320px) {
  .wc-helper .wp-list-table__ext-title {
    max-width: 120px;
  }
}
.wc-helper .wp-list-table__ext-description {
  color: #333;
  padding-left: 12px;
  width: 40%;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__ext-description {
    padding-left: 0;
    width: 100%;
  }
}
.wc-helper .wp-list-table__ext-status {
  position: relative;
}
.wc-helper .wp-list-table__ext-status.update-available::after {
  background-color: #ffc322;
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 5px;
}
.wc-helper .wp-list-table__ext-status.expired::after {
  background-color: #b81c23;
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 5px;
}
.wc-helper .wp-list-table__ext-status .dashicons-update {
  color: #ffc322;
}
.wc-helper .wp-list-table__ext-status .dashicons-info {
  color: #b81c23;
}
.wc-helper .wp-list-table__ext-status p {
  color: #333;
  margin: 0;
}
.wc-helper .wp-list-table__ext-status .dashicons {
  margin-right: 5px;
}
.wc-helper .wp-list-table__ext-actions {
  min-width: 150px;
  position: relative;
  width: 25%;
  text-align: right;
}
.wc-helper .wp-list-table__ext-actions::after {
  background-color: #e1e1e1;
  content: " ";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
}
.wc-helper .wp-list-table__ext-updates td,
.wc-helper .wp-list-table__ext-licence td {
  position: relative;
}
.wc-helper .wp-list-table__ext-updates td::before,
.wc-helper .wp-list-table__ext-licence td::before {
  background-color: #e1e1e1;
  content: " ";
  height: 1px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
.wc-helper .wp-list-table__ext-updates td.wp-list-table__ext-status::before,
.wc-helper .wp-list-table__ext-updates td.wp-list-table__licence-container::before,
.wc-helper .wp-list-table__ext-licence td.wp-list-table__ext-status::before,
.wc-helper .wp-list-table__ext-licence td.wp-list-table__licence-container::before {
  left: 22px !important;
  width: auto !important;
}
.wc-helper .wp-list-table__ext-updates td.wp-list-table__ext-actions::before,
.wc-helper .wp-list-table__ext-licence td.wp-list-table__ext-actions::before {
  right: 22px;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__ext-updates,
  .wc-helper .wp-list-table__ext-licence {
    display: flex;
  }
  .wc-helper .wp-list-table__ext-updates .wp-list-table__ext-status,
  .wc-helper .wp-list-table__ext-licence .wp-list-table__ext-status {
    flex: 2;
  }
  .wc-helper .wp-list-table__ext-updates .wp-list-table__ext-status::before,
  .wc-helper .wp-list-table__ext-licence .wp-list-table__ext-status::before {
    left: 0 !important;
    width: 100% !important;
  }
  .wc-helper .wp-list-table__ext-updates .wp-list-table__ext-actions,
  .wc-helper .wp-list-table__ext-licence .wp-list-table__ext-actions {
    flex: 1;
    min-width: 0;
  }
  .wc-helper .wp-list-table__ext-updates .wp-list-table__ext-actions::before,
  .wc-helper .wp-list-table__ext-licence .wp-list-table__ext-actions::before {
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
  }
}
.wc-helper .wp-list-table__licence-container {
  padding: 0 !important;
}
.wc-helper .wp-list-table__licence-container::after {
  background-color: #e1e1e1;
  content: " ";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
}
.wc-helper .wp-list-table__licence-form {
  display: flex;
  padding: 16px 22px;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__licence-form {
    display: block;
  }
}
.wc-helper .wp-list-table__licence-form::before {
  background-color: #e1e1e1;
  content: " ";
  height: 1px;
  position: absolute;
  top: 0;
  right: 22px;
  left: 22px;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__licence-form::before {
    right: 0;
    left: 0;
  }
}
.wc-helper .wp-list-table__licence-form div {
  padding-right: 16px;
  vertical-align: middle;
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__licence-form div {
    padding: 0;
  }
}
.wc-helper .wp-list-table__licence-form p {
  margin: 0 !important;
}
.wc-helper .wp-list-table__licence-label label {
  color: #23282d;
  font-weight: 600;
  line-height: 30px;
}
.wc-helper .wp-list-table__licence-field input {
  height: 32px;
}
@media only screen and (max-width: 480px) {
  .wc-helper .wp-list-table__licence-field input {
    width: 100%;
  }
}
@media only screen and (max-width: 782px) {
  .wc-helper .wp-list-table__licence-field {
    padding: 8px 0 16px !important;
  }
}
.wc-helper .wp-list-table__licence-actions {
  flex-grow: 2;
  padding-right: 0 !important;
}
.wc-helper .wp-list-table__licence-actions .button {
  margin-right: 8px;
}
.wc-helper .wp-list-table__licence-actions .button-secondary {
  float: right;
  margin: 0 0 0 8px;
}
@media only screen and (max-width: 480px) {
  .wc-helper .wp-list-table__licence-actions {
    text-align: right;
  }
}

/*------------------------------------------------------------------------------
  Expired notification bar
------------------------------------------------------------------------------*/
.wc-helper td.color-bar {
  border-left: solid 4px transparent;
}
.wc-helper td.color-bar.expired {
  border-left-color: #b81c23;
}
.wc-helper td.color-bar.expiring {
  border-left-color: orange;
}
.wc-helper td.color-bar.update-available {
  border-left-color: #8fae1b;
}
.wc-helper td.color-bar.expiring.update-available {
  border-left-color: #8fae1b;
}

/*------------------------------------------------------------------------------
  Connected account table
------------------------------------------------------------------------------*/
.wc-helper .connect-wrapper {
  background-color: #fff;
  border: 1px solid #e5e5e5;
  margin-bottom: 25px;
  overflow: auto;
}
.wc-helper .connected {
  display: flex;
}
.wc-helper .connected .user-info {
  display: flex;
  padding: 20px;
  width: 100%;
  vertical-align: middle;
}
.wc-helper .connected img {
  border: 1px solid #e5e5e5;
  height: 34px;
  width: 34px;
}
.wc-helper .connected .buttons {
  padding: 20px;
  white-space: nowrap;
}
.wc-helper .connected p {
  flex: 2;
  margin: 10px 0 0 20px;
}
.wc-helper .connected .chevron {
  display: none;
}
.wc-helper .connected .chevron:hover {
  color: var(--wp-admin-theme-color);
  cursor: pointer;
}
@media only screen and (max-width: 784px) {
  .wc-helper .connected {
    display: block;
  }
  .wc-helper .connected strong {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .wc-helper .connected p {
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 80%;
  }
  .wc-helper .connected .user-info {
    padding-right: 0;
    width: auto;
  }
  .wc-helper .connected .avatar {
    margin-right: 12px;
  }
  .wc-helper .connected .chevron {
    color: #e1e1e1;
    display: block;
    margin: 10px;
    transform: rotateX(0deg);
  }
  .wc-helper .connected .buttons {
    display: none;
    border-top: 1px solid #e1e1e1;
    padding: 10px 20px;
  }
  .wc-helper .connected .buttons.active {
    display: block;
  }
}

/*------------------------------------------------------------------------------
  Initial connection screen
------------------------------------------------------------------------------*/
.wc-helper .start-container {
  background-color: #fff;
  border-left: 4px solid var(--wp-admin-theme-color);
  padding: 45px 20px 20px 30px;
  position: relative;
  overflow: hidden;
}
.wc-helper .start-container h2,
.wc-helper .start-container p {
  max-width: 800px;
}
.wc-helper .start-container::before {
  color: #e9e9e9;
  content: "\e01c";
  display: block;
  font-family: WooCommerce;
  font-size: 192px;
  line-height: 1;
  position: absolute;
  top: 65%;
  right: -3%;
  text-align: center;
  width: 1em;
}
.wc-helper .start-container h2 {
  font-size: 24px;
  line-height: 29px;
  position: relative;
}
.wc-helper .start-container p {
  font-size: 16px;
  margin-bottom: 30px;
  position: relative;
}
.wc-helper .button-helper-connect {
  height: 37px;
  line-height: 37px;
  min-width: 124px;
  padding: 0 13px;
  text-shadow: none;
}
.wc-helper .button-helper-connect:hover, .wc-helper .button-helper-connect:active, .wc-helper .button-helper-connect:focus {
  padding: 0 13px;
}

.form-toggle__wrapper {
  position: relative;
}
.form-toggle__wrapper label {
  cursor: default;
}

.form-toggle {
  cursor: pointer;
  display: block;
  position: absolute;
  top: 0;
  bottom: -1px;
  left: 0;
  right: 0;
  text-align: left;
  text-indent: -100000px;
  z-index: 2;
}
.form-toggle:focus {
  box-shadow: none;
}
.form-toggle.disabled {
  cursor: default;
}

.form-toggle__switch {
  align-self: flex-start;
  background: #999;
  border-radius: 12px;
  box-sizing: border-box;
  display: inline-block;
  padding: 2px;
  outline: 0;
  position: relative;
  width: 40px;
  height: 24px;
  transition: all 0.4s ease, box-shadow 0s;
  vertical-align: middle;
}
.form-toggle__switch::before, .form-toggle__switch::after {
  content: "";
  display: block;
  position: relative;
  width: 20px;
  height: 20px;
}
.form-toggle__switch::after {
  border-radius: 50%;
  background: #fff;
  left: 0;
  transition: all 0.2s ease;
}
.form-toggle__switch::before {
  display: none;
}
.accessible-focus .form-toggle__switch:focus {
  box-shadow: 0 0 0 2px var(--wp-admin-theme-color);
}

.form-toggle__label {
  vertical-align: bottom;
  z-index: 1;
}
.form-toggle__label .form-toggle__label-content {
  color: rgb(46.2909090909, 67.7090909091, 82.9090909091);
  flex: 0 1 100%;
  font-size: 13px;
  line-height: 16px;
  margin-left: 12px;
  margin-right: 8px;
  vertical-align: top;
  text-transform: uppercase;
}
@media only screen and (max-width: 480px) {
  .form-toggle__label .form-toggle__label-content {
    display: none;
  }
}

.accessible-focus .form-toggle:focus + .form-toggle__label .form-toggle__switch {
  box-shadow: 0 0 0 2px var(--wp-admin-theme-color);
}
.accessible-focus .form-toggle:focus:checked + .form-toggle__label .form-toggle__switch {
  box-shadow: 0 0 0 2px var(--wp-admin-theme-color);
}
.form-toggle + .form-toggle__label .form-toggle__switch {
  background: #999;
}
.form-toggle:not(:disabled) + .form-toggle__label:hover .form-toggle__switch {
  background: #999;
}
.form-toggle.active + .form-toggle__label .form-toggle__switch {
  background: var(--wp-admin-theme-color);
}
.form-toggle.active + .form-toggle__label .form-toggle__switch::after {
  left: 8px;
}
.form-toggle.active + .form-toggle__label:hover .form-toggle__switch {
  background: var(--wp-admin-theme-color);
}
.form-toggle.disabled + label.form-toggle__label span.form-toggle__switch {
  opacity: 0.25;
}

.form-toggle.is-toggling + .form-toggle__label .form-toggle__switch {
  background: var(--wp-admin-theme-color);
}
.form-toggle.is-toggling:checked + .form-toggle__label .form-toggle__switch {
  background: #999;
}

.form-toggle.is-compact + .form-toggle__label .form-toggle__switch {
  border-radius: 8px;
  width: 24px;
  height: 16px;
}
.form-toggle.is-compact + .form-toggle__label .form-toggle__switch::before, .form-toggle.is-compact + .form-toggle__label .form-toggle__switch::after {
  height: 12px;
  width: 12px;
}
.form-toggle.is-compact:checked + .form-toggle__label .form-toggle__switch::after {
  left: 8px;
}/*# sourceMappingURL=helper.css.map */