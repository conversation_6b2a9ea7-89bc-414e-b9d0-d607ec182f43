/**
 * menu.scss
 * Styles applied to dashboard menu items added via WooCommerce.
 * Adds icons to top level menu items, etc.
 */
/**
 * Imports
 */
/**
 * Deprecated
 * Fallback for bourbon equivalent
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include transform(scale(1.5));`
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include box-sizing(border-box);`
 */
/**
 * Objects
 */
/**
 * WooCommerce CSS Variables
 */
:root {
  --woocommerce: #720eec;
  --wc-green: #7ad03a;
  --wc-red: #a00;
  --wc-orange: #ffba00;
  --wc-blue: #2ea2cc;
  --wc-primary: #720eec;
  --wc-primary-text: rgb(252.3245945946, 250.73, 254.27);
  --wc-secondary: #e9e6ed;
  --wc-secondary-text: #515151;
  --wc-highlight: rgb(149.3408, 142.38, 8.8592);
  --wc-highligh-text: white;
  --wc-content-bg: #fff;
  --wc-subtext: #767676;
  --wc-form-border-color: rgba(32, 7, 7, 0.8);
  --wc-form-border-radius: 4px;
  --wc-form-border-width: 1px;
}

/**
 * _fonts.scss
 * Custom WooCommerce fonts.
 */
@font-face {
  font-family: "star";
  src: url("../fonts/WooCommerce.woff2") format("woff2"), url("../fonts/WooCommerce.woff") format("woff"), url("../fonts/WooCommerce.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "WooCommerce";
  src: url("../fonts/WooCommerce.woff2") format("woff2"), url("../fonts/WooCommerce.woff") format("woff"), url("../fonts/WooCommerce.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/**
 * Styling begins
 */
span.mce_woocommerce_shortcodes_button {
  background-image: none !important;
  display: block;
  text-indent: -9999px;
  position: relative;
  height: 1em;
  width: 1em;
}
span.mce_woocommerce_shortcodes_button::before {
  font-family: "WooCommerce";
  speak: never;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  margin: 0;
  text-indent: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  content: "\e01d";
  font-size: 0.9em;
  line-height: 1.2;
}

#woocommerce-update .updating-message .wc_plugin_upgrade_notice {
  display: none;
}
#woocommerce-update .dummy {
  display: none;
}
#woocommerce-update .wc_plugin_upgrade_notice {
  font-weight: normal;
  background: #fff8e5 !important;
  border-left: 4px solid #ffb900;
  border-top: 1px solid #ffb900;
  padding: 9px 0 9px 12px !important;
  margin: 0 -12px 0 -16px !important;
}
#woocommerce-update .wc_plugin_upgrade_notice::before {
  content: "\f348";
  display: inline-block;
  font: 400 18px/1 dashicons;
  speak: never;
  margin: 0 8px 0 -2px;
  vertical-align: top;
}
#woocommerce-update .wc_plugin_upgrade_notice.minor, #woocommerce-update .wc_plugin_upgrade_notice.major {
  padding: 20px 0 !important;
}
#woocommerce-update .wc_plugin_upgrade_notice.minor::before, #woocommerce-update .wc_plugin_upgrade_notice.major::before {
  display: none;
}
#woocommerce-update .wc_plugin_upgrade_notice.minor p, #woocommerce-update .wc_plugin_upgrade_notice.major p {
  padding: 0 20px;
  margin: 0;
  max-width: 700px;
  line-height: 1.5em;
}
#woocommerce-update .wc_plugin_upgrade_notice.minor p::before, #woocommerce-update .wc_plugin_upgrade_notice.major p::before {
  content: "";
  display: none;
}
#woocommerce-update .wc_plugin_upgrade_notice.minor table.plugin-details-table, #woocommerce-update .wc_plugin_upgrade_notice.major table.plugin-details-table {
  margin: 0.75em 0 0;
}
#woocommerce-update .wc_plugin_upgrade_notice.minor table.plugin-details-table tr, #woocommerce-update .wc_plugin_upgrade_notice.major table.plugin-details-table tr {
  background: transparent none !important;
  border: 0 !important;
}
#woocommerce-update .wc_plugin_upgrade_notice.minor table.plugin-details-table th,
#woocommerce-update .wc_plugin_upgrade_notice.minor table.plugin-details-table td, #woocommerce-update .wc_plugin_upgrade_notice.major table.plugin-details-table th,
#woocommerce-update .wc_plugin_upgrade_notice.major table.plugin-details-table td {
  background: transparent none !important;
  margin: 0;
  padding: 0.75em 20px 0;
  border: 0 !important;
  font-size: 1em;
  box-shadow: none;
}
#woocommerce-update .wc_plugin_upgrade_notice.minor table.plugin-details-table th, #woocommerce-update .wc_plugin_upgrade_notice.major table.plugin-details-table th {
  font-weight: bold;
}

#wc_untested_extensions_modal {
  display: none;
}

.wc_untested_extensions_modal_container {
  border-radius: 4px;
  padding: 0;
}
.wc_untested_extensions_modal_container #TB_closeAjaxWindow {
  display: none;
}
.wc_untested_extensions_modal_container #TB_title {
  display: none;
}
.wc_untested_extensions_modal_container #TB_ajaxContent {
  height: 100% !important;
  padding: 0;
  margin: 0;
  width: 100% !important;
}
.wc_untested_extensions_modal_container #TB_ajaxContent p {
  margin: 0 0 1em;
}

.wc_untested_extensions_modal--content h1 {
  margin: 2px 2px 0.5em;
  padding: 0.75em 1.154em;
  line-height: 1.5em;
  font-size: 1.6em;
  border-bottom: 1px solid #eee;
  background: #e9e9e9;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  text-shadow: none;
}
.wc_untested_extensions_modal--content .extensions_warning {
  padding: 0 2em;
}
.wc_untested_extensions_modal--content .plugin-details-table-container {
  max-height: 40vh;
  overflow-y: auto;
}
.wc_untested_extensions_modal--content table.plugin-details-table {
  margin: 20px 0;
}
.wc_untested_extensions_modal--content table.plugin-details-table th,
.wc_untested_extensions_modal--content table.plugin-details-table td {
  background: transparent none !important;
  margin: 0;
  padding: 0.75em 20px 0;
  border: 0 !important;
  font-size: 1em;
  box-shadow: none;
}
.wc_untested_extensions_modal--content table.plugin-details-table th {
  font-weight: bold;
  margin-top: 0;
}
.wc_untested_extensions_modal--content .actions {
  border-top: 1px solid #eee;
  margin: 0;
  padding: 1em 0 2em 0;
  overflow: hidden;
}
.wc_untested_extensions_modal--content .actions .woocommerce-actions {
  display: inline-block;
}
.wc_untested_extensions_modal--content .actions a.button-primary {
  float: right;
}/*# sourceMappingURL=menu.css.map */