#woocommerce_network_orders .inside {
  margin: 0;
  padding: 0;
}
#woocommerce_network_orders .woocommerce-network-orders-no-orders,
#woocommerce_network_orders .woocommerce-network-order-table-loading,
#woocommerce_network_orders .woocommerce-network-order-table {
  width: 100%;
  display: none;
}
#woocommerce_network_orders .woocommerce-network-orders-no-orders.is-active,
#woocommerce_network_orders .woocommerce-network-order-table-loading.is-active,
#woocommerce_network_orders .woocommerce-network-order-table.is-active {
  display: block;
}
#woocommerce_network_orders .woocommerce-network-orders-no-orders p,
#woocommerce_network_orders .woocommerce-network-order-table-loading p {
  text-align: center;
}
#woocommerce_network_orders .woocommerce-network-order-table {
  margin-top: 0;
}
#woocommerce_network_orders .woocommerce-network-order-table.is-active {
  display: table;
}
#woocommerce_network_orders .woocommerce-network-order-table thead td {
  padding: 0.5em 1em;
}
#woocommerce_network_orders .spinner {
  margin-top: 0;
  float: none;
}
#woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody th, #woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody td {
  border-top: 1px solid #f5f5f5;
}
#woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody td {
  vertical-align: middle;
  padding: 1em;
}
#woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody td .order-status {
  display: inline-flex;
  padding: 0px 1em;
  line-height: 2.5em;
  color: #777;
  background: #E5E5E5;
  border-radius: 4px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  margin: -0.5em 0;
  cursor: inherit !important;
}
#woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody td .order-status.status-completed {
  background: #C8D7E1;
  color: #2e4453;
}
#woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody td .order-status.status-on-hold {
  background: #f8dda7;
  color: #94660c;
}
#woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody td .order-status.status-failed {
  background: #eba3a3;
  color: #761919;
}
#woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody td .order-status.status-processing {
  background: #C6E1C6;
  color: #5B841B;
}
#woocommerce_network_orders .post-type-shop_order .woocommerce-network-order-table tbody td .order-status.status-trash {
  background: #eba3a3;
  color: #761919;
}/*# sourceMappingURL=network-order-widget.css.map */