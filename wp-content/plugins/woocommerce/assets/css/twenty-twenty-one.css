@charset "UTF-8";
/**
 * Deprecated
 * Fallback for bourbon equivalent
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include transform(scale(1.5));`
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include box-sizing(border-box);`
 */
/**
 * Objects
 */
/**
 * Sass variables
 */
/**
 * Fonts
 */
/**
 * _fonts.scss
 * Custom WooCommerce fonts.
 */
@font-face {
  font-family: "star";
  src: url("../fonts/WooCommerce.woff2") format("woff2"), url("../fonts/WooCommerce.woff") format("woff"), url("../fonts/WooCommerce.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "WooCommerce";
  src: url("../fonts/WooCommerce.woff2") format("woff2"), url("../fonts/WooCommerce.woff") format("woff"), url("../fonts/WooCommerce.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/**
 * Forms
 */
.woocommerce {
  /**
   * Generic forms styles used in places such as my account and the shortcode based checkout.
   */
}
.woocommerce form .form-row {
  padding: 3px;
  margin: 0 0 6px;
}
.woocommerce form .form-row [placeholder]:focus::-webkit-input-placeholder {
  -webkit-transition: opacity 0.5s 0.5s ease;
  transition: opacity 0.5s 0.5s ease;
  opacity: 0;
}
.woocommerce form .form-row label {
  line-height: 2;
}
.woocommerce form .form-row label.hidden {
  visibility: hidden;
}
.woocommerce form .form-row label.inline {
  display: inline;
}
.woocommerce form .form-row .woocommerce-input-wrapper .description {
  background: #1e85be;
  color: #fff;
  border-radius: 3px;
  padding: 1em;
  margin: 0.5em 0 0;
  clear: both;
  display: none;
  position: relative;
}
.woocommerce form .form-row .woocommerce-input-wrapper .description a {
  color: #fff;
  text-decoration: underline;
  border: 0;
  box-shadow: none;
}
.woocommerce form .form-row .woocommerce-input-wrapper .description::before {
  left: 50%;
  top: 0%;
  margin-top: -4px;
  transform: translateX(-50%) rotate(180deg);
  content: "";
  position: absolute;
  border-width: 4px 6px 0 6px;
  border-style: solid;
  border-color: #1e85be transparent transparent transparent;
  z-index: 100;
  display: block;
}
.woocommerce form .form-row .input-checkbox {
  display: inline;
  margin: -2px 8px 0 0;
  text-align: center;
  vertical-align: middle;
}
.woocommerce form .form-row .input-text,
.woocommerce form .form-row select {
  font-family: inherit;
  font-weight: normal;
  letter-spacing: normal;
  padding: 0.5em;
  display: block;
  background-color: var(--wc-form-color-background, #fff);
  border: var(--wc-form-border-width) solid var(--wc-form-border-color);
  border-radius: var(--wc-form-border-radius);
  color: var(--wc-form-color-text, #000);
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  line-height: normal;
  height: auto;
}
.woocommerce form .form-row .input-text:focus,
.woocommerce form .form-row select:focus {
  border-color: currentColor;
}
.woocommerce form .form-row select {
  cursor: pointer;
  /* We hide the default chevron because it cannot be directly modified. Instead, we add a custom chevron using a background image. */
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding-right: 3em;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItY2hldnJvbi1kb3duIj48cG9seWxpbmUgcG9pbnRzPSI2IDkgMTIgMTUgMTggOSI+PC9wb2x5bGluZT48L3N2Zz4=);
  background-repeat: no-repeat;
  background-size: 16px;
  background-position: calc(100% - 0.5em) 50%;
}
.woocommerce form .form-row textarea {
  height: 4em;
  line-height: 1.5;
  box-shadow: none;
}
.woocommerce form .form-row .required {
  color: var(--wc-red);
  font-weight: 700;
  border: 0 !important;
  text-decoration: none;
  visibility: hidden;
}
.woocommerce form .form-row .optional {
  visibility: visible;
}
.woocommerce form .form-row.woocommerce-invalid label {
  color: var(--wc-red);
}
.woocommerce form .form-row.woocommerce-invalid input.input-text,
.woocommerce form .form-row.woocommerce-invalid select {
  border-color: var(--wc-red);
}
.woocommerce form .form-row.woocommerce-invalid .select2-container:not(.select2-container--open) .select2-selection {
  border-color: var(--wc-red);
}
.woocommerce form .form-row.woocommerce-validated input.input-text,
.woocommerce form .form-row.woocommerce-validated select {
  border-color: var(--wc-green);
}
.woocommerce form .form-row.woocommerce-validated .select2-container:not(.select2-container--open) .select2-selection {
  border-color: var(--wc-green);
}
.woocommerce form .form-row ::-webkit-input-placeholder {
  line-height: normal;
}
.woocommerce form .form-row :-moz-placeholder {
  line-height: normal;
}
.woocommerce form .form-row :-ms-input-placeholder {
  line-height: normal;
}

.select2-container {
  width: 100%;
}
.select2-container .select2-selection--single {
  height: auto;
}
.select2-container .select2-selection--single .select2-selection__rendered {
  padding: 0.5em;
  line-height: normal;
  box-sizing: border-box;
  color: var(--wc-form-color-text, #444);
  font-weight: normal;
}
.select2-container .select2-selection--single .select2-selection__placeholder {
  color: #999;
}
.select2-container .select2-selection--single .select2-selection__arrow {
  position: absolute;
  top: 2px;
  right: 0.5em;
  height: 100%;
  width: 16px;
}
.select2-container .select2-selection--single .select2-selection__arrow b {
  border: none;
  display: block;
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItY2hldnJvbi1kb3duIj48cG9seWxpbmUgcG9pbnRzPSI2IDkgMTIgMTUgMTggOSI+PC9wb2x5bGluZT48L3N2Zz4=) no-repeat;
  background-size: 16px;
  width: 16px;
  height: 16px;
  position: absolute;
  top: 50%;
  left: 0;
  margin: -8px 0 0;
}
.select2-container .select2-selection,
.select2-container .select2-dropdown {
  background-color: var(--wc-form-color-background, #fff);
  border: var(--wc-form-border-width, 1px) solid var(--wc-form-border-color, #aaa);
  border-radius: var(--wc-form-border-radius, 4px);
}
.select2-container.select2-container--open .select2-dropdown--above {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.select2-container.select2-container--open .select2-dropdown--below {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.select2-results__options {
  list-style: none;
  margin: 0;
  padding: 0;
}

.select2-results__option {
  margin: 0;
}

/**
 * Global elements
 */
a.button {
  display: inline-block;
  text-align: center;
  box-sizing: border-box;
  word-break: break-word;
  text-decoration: none !important;
}
a.button:hover, a.button:visited {
  text-decoration: underline !important;
}

body {
  --wc-input-border-color: var(--form--border-color);
  --wc-input-border-radius: var(--form--border-radius);
  --wc-form-border-width: var(--form--border-width);
  --wc-form-color-text: var(--form--color-text);
}

.woocommerce form.woocommerce-form-login p,
.woocommerce form.woocommerce-form-login label,
.woocommerce form.woocommerce-form-register p,
.woocommerce form.woocommerce-form-register label {
  font-family: var(--heading--font-family);
}
.woocommerce form.woocommerce-form-login input,
.woocommerce form.woocommerce-form-register input {
  border: 1px solid #ddd;
}
.woocommerce .woocommerce-form-login__rememberme {
  margin: 1rem 0 3rem 0;
}
.woocommerce .show-password-input {
  background-color: transparent !important;
  color: var(--form--color-text) !important;
  display: inherit;
  outline-offset: 0;
}

.woocommerce-notices-wrapper:empty {
  margin: 0 auto;
}

.woocommerce-view-order .woocommerce-MyAccount-content table {
  border: 0;
}
.woocommerce-view-order .woocommerce-MyAccount-content table tbody {
  border-bottom: 1px solid currentcolor;
}
.woocommerce-view-order .woocommerce-MyAccount-content table tfoot tr:last-of-type {
  border-top: 1px solid currentcolor;
}
.woocommerce-view-order .woocommerce-MyAccount-content table tfoot tr:last-of-type .woocommerce-Price-amount {
  font-weight: 700;
}
.woocommerce-view-order .woocommerce-MyAccount-content table td,
.woocommerce-view-order .woocommerce-MyAccount-content table tr,
.woocommerce-view-order .woocommerce-MyAccount-content table th {
  border: 0;
}

.site-main .woocommerce-breadcrumb {
  margin-bottom: var(--global--spacing-vertical);
  font-size: 0.88889em;
  font-family: var(--heading--font-family);
}
.site-main .woocommerce-products-header {
  margin-top: var(--global--spacing-vertical);
}

.woocommerce-pagination {
  font-family: var(--heading--font-family);
  font-size: 0.88889em;
}
.woocommerce-pagination ul.page-numbers {
  margin: 0;
  padding: 0;
  display: block;
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.2;
}
.woocommerce-pagination span.page-numbers,
.woocommerce-pagination a.page-numbers,
.woocommerce-pagination .next.page-numbers,
.woocommerce-pagination .prev.page-numbers {
  padding: 0 0.5rem;
  display: inline-block;
}

.onsale {
  position: absolute;
  top: -0.7rem;
  right: -0.7rem;
  background: var(--wc-highlight, #777335);
  color: #fff;
  font-family: var(--heading--font-family);
  font-size: 1.2rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  z-index: 1;
  border-radius: 50%;
  text-align: center;
  padding: 0.8rem;
  margin: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.onsale::before {
  content: "";
  float: left;
  padding-top: 100%;
}

.onsale + .woocommerce-product-gallery .woocommerce-product-gallery__trigger {
  top: 1em;
  right: 1em;
}

.single-product .type-product.sale > .onsale {
  right: calc(52% - 0.7rem);
}

.price {
  font-family: var(--heading--font-family);
  font-size: 1rem;
}
.price del {
  opacity: 0.5;
  display: inline-block;
}
.price ins {
  display: inline-block;
  text-decoration: none;
}

.woocommerce-message,
.woocommerce-error,
.woocommerce-info {
  color: #000;
  border-top: 3px solid var(--wc-highlight, #777335);
  margin-bottom: 2rem;
  padding: 0;
  margin-left: 0;
  background: #eee;
  font-size: 0.88889em;
  font-family: var(--heading--font-family);
  list-style: none;
  overflow: hidden;
}
.woocommerce-message a.button,
.woocommerce-error a.button,
.woocommerce-info a.button {
  background: #111;
  color: #fff;
}

.woocommerce-store-notice__dismiss-link {
  float: right;
  color: #000;
}
.woocommerce-store-notice__dismiss-link:hover {
  text-decoration: none;
  color: #000;
}

.flex-viewport {
  margin-bottom: 1.5em;
}

#main .post-inner {
  padding-top: 0;
}
#main .wp-block-cover {
  margin-top: 0;
}

.cross-sells .woocommerce-loop-product__title {
  font-family: var(--heading--font-family);
}
.cross-sells .star-rating {
  font-size: 1.4rem;
}

/* Make thumbnails in the gallery affect parent's height and wrapping */
.flex-control-nav::after {
  clear: both;
  content: "";
  display: table;
}

/**
* Tables
*/
.woocommerce.is-dark-theme,
.woocommerce-page.is-dark-theme {
  --wc-form-color-background: var(--global--color-white-90);
}
.woocommerce.is-dark-theme .select2-dropdown,
.woocommerce-page.is-dark-theme .select2-dropdown {
  color: var(--global--color-dark-gray);
}
.woocommerce table.shop_table td,
.woocommerce table.shop_table th,
.woocommerce-page table.shop_table td,
.woocommerce-page table.shop_table th {
  word-break: normal;
  border-left: none;
  border-right: none;
}
.woocommerce table.shop_table .product-thumbnail,
.woocommerce-page table.shop_table .product-thumbnail {
  max-width: 120px;
}

/**
 * Shop page
 */
.woocommerce-result-count,
.woocommerce-ordering {
  margin: 0 0 1rem;
  padding: 0.75rem 0;
}

.woocommerce-ordering > label {
  margin-right: 0.25rem;
}

/**
 * Products
 */
ul.products {
  margin: 0;
  padding: 0;
}
ul.products li.product {
  list-style: none;
}
ul.products li.product .woocommerce-loop-product__link {
  display: block;
  text-decoration: none;
  position: relative;
}
ul.products li.product .woocommerce-loop-product__title {
  margin: 0.5rem 0 0.5rem;
  font-size: 1.5rem;
  font-weight: 400;
}
ul.products li.product .woocommerce-loop-product__title::before {
  content: none;
}
ul.products li.product .woocommerce-loop-product__title,
ul.products li.product .price,
ul.products li.product .star-rating {
  color: currentcolor;
}
ul.products li.product .star-rating {
  margin-bottom: 0.8rem;
}
ul.products li.product .price {
  margin-bottom: 1rem;
}
ul.products li.product .price,
ul.products li.product .star-rating {
  display: block;
}
ul.products li.product .woocommerce-placeholder {
  border: 1px solid #f2f2f2;
}
ul.products li.product .button {
  vertical-align: middle;
  background-color: transparent;
  color: var(--button--color-text-hover);
  text-decoration: none !important;
}
ul.products li.product .button.loading {
  opacity: 0.5;
}
ul.products li.product .button:hover {
  background-color: var(--button--color-background);
  color: var(--button--color-text);
}
ul.products li.product .added_to_cart {
  margin: 0.5rem;
}

.star-rating {
  overflow: hidden;
  position: relative;
  height: 1em;
  line-height: 1;
  font-size: 1em;
  width: 5.4em;
  font-family: WooCommerce;
  margin-bottom: 0.7rem;
}
.star-rating::before {
  content: "sssss";
  float: left;
  top: 0;
  left: 0;
  position: absolute;
}
.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}
.star-rating span::before {
  content: "SSSSS";
  top: 0;
  position: absolute;
  left: 0;
}

a.remove {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 18px;
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  border-radius: 100%;
  text-decoration: none !important;
  background: #fff;
  color: #000;
}
a.remove:hover {
  background: var(--wc-highlight, #777335);
  color: #fff !important;
}

dl.variation,
.wc-item-meta {
  list-style: none outside;
}
dl.variation dt,
dl.variation .wc-item-meta-label,
.wc-item-meta dt,
.wc-item-meta .wc-item-meta-label {
  float: left;
  clear: both;
  margin-right: 0.25rem;
  margin-top: 0;
  list-style: none outside;
  font-weight: 400;
}
dl.variation dd,
.wc-item-meta dd {
  margin: 0;
}
dl.variation p, dl.variation:last-child,
.wc-item-meta p,
.wc-item-meta:last-child {
  margin-bottom: 0;
}

/**
 * Single product
 */
.single-product div.product {
  position: relative;
}
.single-product div.product .product_meta {
  clear: both;
  font-size: 0.7em;
  padding-top: 0.5em;
  margin-top: 3rem;
}
.single-product .single_add_to_cart_button {
  line-height: var(--global--line-height-body) !important;
  padding-top: var(--form--spacing-unit) !important;
  padding-bottom: var(--form--spacing-unit) !important;
  font-size: 1.6rem;
}
.single-product .single-featured-image-header {
  display: none;
}
.single-product.singular .entry-title {
  font-size: var(--global--font-size-xl);
  font-weight: 400;
  margin: 0 0 2.5rem;
}
.single-product.singular .entry-title::before {
  margin-top: 0;
}
.single-product .summary {
  margin-bottom: 8rem;
}
.single-product .summary p.price {
  margin-bottom: 2rem;
}
.single-product .summary .woocommerce-product-details__short-description {
  margin-bottom: 1rem;
}
.single-product .woocommerce-variation-price {
  margin: 2rem 0;
}
.single-product .woocommerce-product-rating {
  margin: -1rem 0 4rem;
  line-height: 1;
  font-size: 1.4rem;
}
.single-product .woocommerce-product-rating .star-rating {
  float: left;
  margin-right: 0.25rem;
}
.single-product form.cart .quantity {
  float: left;
  margin-right: 0.5rem;
}
.single-product form.cart input[type=number] {
  width: 5em;
}
.single-product .woocommerce-variation-add-to-cart .button {
  padding-top: 1.55rem;
  padding-bottom: 1.59rem;
  font-size: 1.6rem;
}
.single-product .woocommerce-variation-add-to-cart .button.disabled {
  opacity: 0.2;
}
.single-product .woocommerce-Tabs-panel--additional_information table,
.single-product .woocommerce-Tabs-panel--reviews table {
  border: 1px solid #ddd;
}
.single-product .woocommerce-Tabs-panel--additional_information table tr,
.single-product .woocommerce-Tabs-panel--additional_information table td,
.single-product .woocommerce-Tabs-panel--additional_information table th,
.single-product .woocommerce-Tabs-panel--reviews table tr,
.single-product .woocommerce-Tabs-panel--reviews table td,
.single-product .woocommerce-Tabs-panel--reviews table th {
  border: 1px solid #ddd;
}
.single-product .woocommerce-Tabs-panel--additional_information p,
.single-product .woocommerce-Tabs-panel--reviews p {
  font-family: var(--heading--font-family);
}
.single-product .woocommerce-Tabs-panel--additional_information input,
.single-product .woocommerce-Tabs-panel--reviews input {
  border: 1px solid #ddd;
}
.single-product .woocommerce-product-attributes-item__value p {
  margin-bottom: 0;
}

table.variations {
  margin: 1rem 0;
}
table.variations label {
  margin: 0;
  padding: 6px 0;
}
table.variations select {
  margin-right: 0.5rem;
}

a.reset_variations {
  margin-left: 0.5em;
}

.woocommerce-product-gallery {
  max-width: 600px;
  position: relative;
  margin-bottom: 2rem;
}
.woocommerce-product-gallery figure {
  margin: 0;
  padding: 0;
}
.woocommerce-product-gallery .woocommerce-product-gallery__wrapper {
  margin: 0;
  padding: 0;
}
.woocommerce-product-gallery .zoomImg {
  background-color: #fff;
  opacity: 0;
}
.woocommerce-product-gallery .woocommerce-product-gallery__image--placeholder {
  border: 1px solid #f2f2f2;
}
.woocommerce-product-gallery .woocommerce-product-gallery__image:nth-child(n+2) {
  width: 25%;
  display: inline-block;
}
.woocommerce-product-gallery .woocommerce-product-gallery__image a {
  display: block;
}
.woocommerce-product-gallery .woocommerce-product-gallery__image a:focus img {
  outline-offset: -2px;
}
.woocommerce-product-gallery .flex-control-thumbs li {
  list-style: none;
  cursor: pointer;
  float: left;
}
.woocommerce-product-gallery .flex-control-thumbs img {
  opacity: 0.5;
}
.woocommerce-product-gallery .flex-control-thumbs img:hover, .woocommerce-product-gallery .flex-control-thumbs img.flex-active {
  opacity: 1;
}
.woocommerce-product-gallery img {
  display: block;
  height: auto;
}

.woocommerce-product-gallery--columns-3 .flex-control-thumbs li {
  width: 33.3333%;
}
.woocommerce-product-gallery--columns-3 .flex-control-thumbs li:nth-child(3n+1) {
  clear: left;
}

.woocommerce-product-gallery--columns-4 ol {
  margin-left: 0;
  margin-bottom: 0;
}
.woocommerce-product-gallery--columns-4 .flex-control-thumbs li {
  width: 14.2857142857%;
  margin: 0 14.2857142857% 1.6em 0;
}
.woocommerce-product-gallery--columns-4 .flex-control-thumbs li:nth-child(4n) {
  margin-right: 0;
}
.woocommerce-product-gallery--columns-4 .flex-control-thumbs li:nth-child(4n+1) {
  clear: left;
}

.woocommerce-product-gallery--columns-5 .flex-control-thumbs li {
  width: 20%;
}
.woocommerce-product-gallery--columns-5 .flex-control-thumbs li:nth-child(5n+1) {
  clear: left;
}

.woocommerce-product-gallery__trigger {
  background: #fff;
  border: none;
  box-sizing: content-box;
  border-radius: 100%;
  cursor: pointer;
  font-size: 2em;
  height: 36px;
  padding: 0;
  position: absolute;
  right: 0.5em;
  text-indent: -9999px;
  top: 0.5em;
  width: 36px;
  z-index: 99;
}
.woocommerce-product-gallery__trigger::before {
  border: 2px solid #000;
  border-radius: 100%;
  box-sizing: content-box;
  content: "";
  display: block;
  height: 10px;
  left: 9px;
  top: 9px;
  position: absolute;
  width: 10px;
}
.woocommerce-product-gallery__trigger::after {
  background: #000;
  border-radius: 6px;
  box-sizing: content-box;
  content: "";
  display: block;
  height: 8px;
  left: 22px;
  position: absolute;
  top: 19px;
  transform: rotate(-45deg);
  width: 2px;
}
.woocommerce-product-gallery__trigger span[aria-hidden=true] {
  border: 0;
  clip-path: inset(50%);
  height: 1px;
  left: 50%;
  margin: -1px;
  overflow: hidden;
  position: absolute;
  top: 50%;
  width: 1px;
}
.woocommerce-product-gallery__trigger:focus {
  outline-offset: 2px;
  outline: 2px dotted var(--form--border-color) !important;
}

.woocommerce-tabs {
  margin: 4rem 0 2rem;
  /* reset description tab width to full width */
  /* reset additional info tab width to full width */
}
.woocommerce-tabs #tab-description h2,
.woocommerce-tabs #tab-description p {
  max-width: 100vw;
  width: 100%;
}
.woocommerce-tabs #tab-additional_information .woocommerce-product-attributes {
  max-width: 100vw;
  width: 100%;
}
.woocommerce-tabs #tab-reviews {
  /* reset reviews tab width to full width */
}
.woocommerce-tabs #tab-reviews .woocommerce-Reviews {
  max-width: 100vw;
  width: 100%;
}
.woocommerce-tabs #tab-reviews #submit {
  float: right;
}
.woocommerce-tabs ul {
  margin: 0 0 1.5rem;
  padding: 0;
  font-family: var(--heading--font-family);
  border-bottom: var(--button--border-width) solid var(--button--color-background);
}
.woocommerce-tabs ul li {
  display: inline-flex !important;
}
.woocommerce-tabs ul li a {
  color: currentcolor;
  text-decoration: none;
  font-weight: 700;
  padding: var(--button--padding-vertical) var(--button--padding-horizontal);
}
.woocommerce-tabs ul li.active a {
  color: var(--button--color-text);
  background-color: var(--button--color-background);
  border: var(--button--border-width) solid var(--button--color-background);
}
.woocommerce-tabs ul li.active a:focus {
  color: currentcolor;
}
.woocommerce-tabs .panel > * {
  margin-top: 0 !important;
}
.woocommerce-tabs .panel h1::before,
.woocommerce-tabs .panel h2::before {
  content: none;
}
.woocommerce-tabs .panel h2:first-of-type {
  font-size: var(--global--font-size-lg);
  margin: 0 0 2rem !important;
}
.woocommerce-tabs #comments {
  padding-top: 0;
}
.woocommerce-tabs .comment-reply-title {
  font-family: var(--heading--font-family);
  font-size: 1em;
  font-weight: 700;
  display: block;
}
.woocommerce-tabs #reviews ol.commentlist {
  padding: 0;
  margin: 0;
}
.woocommerce-tabs #reviews li.review,
.woocommerce-tabs #reviews li.comment {
  list-style: none;
  margin: 0.5rem 0 2.5rem 0;
}
.woocommerce-tabs #reviews li.review .avatar,
.woocommerce-tabs #reviews li.comment .avatar {
  max-height: 36px;
  width: auto;
  float: right;
}
.woocommerce-tabs #reviews li.review p.meta,
.woocommerce-tabs #reviews li.comment p.meta {
  margin-bottom: 0.5em;
}
.woocommerce-tabs #reviews .comment-form-rating label {
  max-width: 58rem;
  margin: 0 auto;
}
.woocommerce-tabs #reviews p.stars {
  margin-top: 0;
}
.woocommerce-tabs #reviews p.stars a {
  position: relative;
  height: 1em;
  width: 1em;
  text-indent: -999em;
  display: inline-block;
  text-decoration: none;
  box-shadow: none;
  font-size: 24px;
}
.woocommerce-tabs #reviews p.stars a::before {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 1em;
  height: 1em;
  line-height: 1;
  font-family: WooCommerce;
  content: "\e021";
  text-indent: 0;
}
.woocommerce-tabs #reviews p.stars a:hover ~ a::before {
  content: "\e021";
}
.woocommerce-tabs #reviews p.stars:hover a::before {
  content: "\e020";
}
.woocommerce-tabs #reviews p.stars.selected a.active::before {
  content: "\e020";
}
.woocommerce-tabs #reviews p.stars.selected a.active ~ a::before {
  content: "\e021";
}
.woocommerce-tabs #reviews p.stars.selected a:not(.active)::before {
  content: "\e020";
}
.woocommerce-tabs #reviews .comment-form-author,
.woocommerce-tabs #reviews .comment-form-email {
  float: none;
  margin-left: auto;
}

/**
 * Related products
 */
.related.products,
.up-sells {
  clear: both;
}
.related.products h2,
.up-sells h2 {
  margin-bottom: 2rem;
}
.related.products ul.products,
.up-sells ul.products {
  display: flex;
  justify-content: space-evenly;
  align-items: stretch;
}
.related.products ul.products li.product,
.up-sells ul.products li.product {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}

/**
 * Widgets
 */
.widget.woocommerce ul {
  padding-left: 0;
}
.widget.woocommerce ul li {
  list-style: none;
}

.widget .product_list_widget,
.site-footer .widget .product_list_widget {
  margin-bottom: 1.5rem;
}
.widget .product_list_widget a,
.site-footer .widget .product_list_widget a {
  display: block;
  box-shadow: none;
}
.widget .product_list_widget a:hover,
.site-footer .widget .product_list_widget a:hover {
  box-shadow: none;
}
.widget .product_list_widget li,
.site-footer .widget .product_list_widget li {
  padding: 0.5rem 0;
}
.widget .product_list_widget li a.remove,
.site-footer .widget .product_list_widget li a.remove {
  float: left;
  margin-top: 7px;
  line-height: 20px;
  color: #fff;
  margin-right: 0.5rem;
}
.widget .product_list_widget img,
.site-footer .widget .product_list_widget img {
  display: none;
}

.widget_shopping_cart .buttons a {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}

.woocommerce-shopping-totals {
  vertical-align: text-top;
}

.widget_layered_nav .chosen::before {
  content: "×";
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  font-size: 16px;
  text-align: center;
  border-radius: 100%;
  border: 1px solid #000;
  margin-right: 0.25rem;
}

.widget_price_filter .price_slider {
  margin-bottom: 1rem;
}
.widget_price_filter .price_slider_amount {
  text-align: right;
  line-height: 2.4;
  font-size: 0.8751em;
}
.widget_price_filter .price_slider_amount .button {
  float: left;
  padding: 0.4rem 1rem;
}
.widget_price_filter .ui-slider {
  position: relative;
  text-align: left;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.widget_price_filter .ui-slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 1em;
  height: 1em;
  background-color: #000;
  border-radius: 1em;
  cursor: ew-resize;
  outline: none;
  top: -0.3em;
  margin-left: -0.5em;
}
.widget_price_filter .ui-slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  font-size: 0.7em;
  display: block;
  border: 0;
  border-radius: 1em;
  background-color: #000;
}
.widget_price_filter .price_slider_wrapper .ui-widget-content {
  border-radius: 1em;
  background-color: #666;
  border: 0;
}
.widget_price_filter .ui-slider-horizontal {
  height: 0.5em;
}
.widget_price_filter .ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%;
}
.widget_price_filter .ui-slider-horizontal .ui-slider-range-min {
  left: -1px;
}
.widget_price_filter .ui-slider-horizontal .ui-slider-range-max {
  right: -1px;
}

.widget_rating_filter li {
  text-align: right;
}
.widget_rating_filter li .star-rating {
  float: left;
  margin-top: 0.3rem;
}

.widget_product_search form {
  position: relative;
}
.widget_product_search .search-field {
  padding-right: 100px;
}
.widget_product_search input[type=submit] {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

/**
 * Account section
 */
.woocommerce-account #main .post-inner {
  padding-top: 0;
}
.woocommerce-account #main .woocommerce {
  max-width: 1600px;
  padding: 0 6vw;
  margin: 0 auto;
}
.woocommerce-account .woocommerce-MyAccount-navigation {
  font-family: var(--heading--font-family);
  margin: 0 0 2rem;
}
.woocommerce-account .woocommerce-MyAccount-navigation ul {
  margin: 0;
  padding: 0;
}
.woocommerce-account .woocommerce-MyAccount-navigation li {
  list-style: none;
  padding: 0.5rem 0;
  font-family: var(--heading--font-family);
  font-size: 2rem;
}
.woocommerce-account .woocommerce-MyAccount-navigation li:first-child {
  padding-top: 0;
}
.woocommerce-account .woocommerce-MyAccount-navigation li a {
  box-shadow: none;
  text-decoration: none;
  font-weight: 600;
  color: #aaa;
}
.woocommerce-account .woocommerce-MyAccount-navigation li a:hover {
  color: #000;
  text-decoration: underline;
}
.woocommerce-account .woocommerce-MyAccount-navigation li.is-active a {
  text-decoration: underline;
  color: var(--wc-highlight, #777335);
}
.woocommerce-account .woocommerce-MyAccount-content p {
  font-family: var(--heading--font-family);
  font-size: 2rem;
}
.woocommerce-account .woocommerce-MyAccount-content form h3 {
  margin-top: 0;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-Addresses {
  margin-top: -1rem;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address-title h3 {
  display: inline-block;
  margin-right: 1rem;
  font-size: 1.8rem;
  margin-top: 2rem;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-Addresses address {
  line-height: 1.8rem;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-address-fields label {
  font-size: 1.5rem;
  margin-bottom: 0.1rem;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-address-fields select,
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-address-fields input,
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-address-fields .selection {
  font-size: 1.5rem;
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-address-fields .form-row {
  margin-top: 1.5rem !important;
  margin-bottom: 0 !important;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-address-fields #billing_company_field {
  padding-top: 1.5rem !important;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-address-fields .woocommerce-address-fields__field-wrapper {
  margin-bottom: 2rem;
}
.woocommerce-account.woocommerce-lost-password .woocommerce {
  max-width: var(--responsive--alignwide-width) !important;
  padding: 0 !important;
  flex-wrap: wrap;
}
.woocommerce-account.woocommerce-lost-password .woocommerce .woocommerce-notices-wrapper {
  flex: 1 0 100%;
}
.woocommerce-account.woocommerce-lost-password .woocommerce .woocommerce-ResetPassword .woocommerce-form-row--first {
  float: none;
}
.woocommerce-account.woocommerce-lost-password .woocommerce .woocommerce-ResetPassword #user_login {
  margin-bottom: 10px;
}
.woocommerce-account table.account-orders-table {
  margin-top: 0;
  border: 0;
}
.woocommerce-account table.account-orders-table tr,
.woocommerce-account table.account-orders-table td,
.woocommerce-account table.account-orders-table th {
  border: 0;
}
.woocommerce-account table.account-orders-table td {
  padding-left: 1.5rem;
}
.woocommerce-account table.account-orders-table thead {
  border-bottom: 1px solid #ddd;
}
.woocommerce-account table.account-orders-table .button {
  margin: 0 0.35rem 0.35rem 0;
  width: 80%;
}
.woocommerce-account table.account-orders-table:not(.has-background) tbody tr:nth-child(2n+1) td {
  background: var(--global--color-background);
  filter: brightness(88%);
}
.is-dark-theme .woocommerce-account table.account-orders-table:not(.has-background) tbody tr:nth-child(2n+1) td {
  filter: brightness(112%);
}
.woocommerce-account .woocommerce-EditAccountForm label {
  font-size: 1.5rem;
}
.woocommerce-account .woocommerce-EditAccountForm input,
.woocommerce-account .woocommerce-EditAccountForm select {
  border: var(--form--border-width) solid var(--form--border-color);
  font-size: 1.5rem;
}
.woocommerce-account .woocommerce-EditAccountForm fieldset {
  border: none;
  padding-left: 0;
  padding-right: 0;
  margin-top: 30px;
}
.woocommerce-account .woocommerce-EditAccountForm fieldset legend {
  display: contents;
  font-size: 2rem;
}
.woocommerce-account .woocommerce-EditAccountForm fieldset p {
  margin-top: 20px;
  margin-bottom: 0 !important;
}
.woocommerce-account .woocommerce-EditAccountForm button {
  margin-top: 0;
}
.woocommerce-account .woocommerce-EditAccountForm #account_display_name + span {
  font-size: 1.5rem;
}
.woocommerce-account .woocommerce-EditAccountForm p {
  margin-top: 20px;
}
.woocommerce-account .woocommerce-EditAccountForm p:nth-of-type(4) {
  margin-top: 30px;
}

.logged-in.woocommerce-account #main .woocommerce {
  display: flex;
  flex-direction: row;
}

.checkout-button {
  display: block;
  padding: 1rem 2rem;
  border: 2px solid #000;
  text-align: center;
  font-weight: 800;
}
.checkout-button:hover {
  border-color: #999;
}
.checkout-button::after {
  content: "→";
  margin-left: 0.5rem;
}

.woocommerce-cart table.woocommerce-cart-form__contents thead,
.woocommerce-cart table.woocommerce-cart-form__contents tfoot {
  text-align: left;
}
.woocommerce-cart .post-inner {
  padding-top: 0;
}
.woocommerce-cart #main .woocommerce {
  max-width: var(--responsive--alignwide-width);
  margin: 0 auto;
}
.woocommerce-cart p.form-row input {
  border: 1px solid #ddd;
}
.woocommerce-cart table.cart img.woocommerce-placeholder {
  height: auto !important;
}

/**
 * Checkout
 */
.woocommerce-form-coupon-toggle .woocommerce-info {
  display: block;
  margin-bottom: 2rem;
  padding: 1rem;
}

.woocommerce-form-coupon {
  background: #eee;
  padding: 1rem;
  font-size: 0.88889em;
  color: var(--form--color-text);
}
.woocommerce-form-coupon #coupon_code {
  border: var(--form--border-width) solid var(--form--border-color);
}
.woocommerce-form-coupon button[name=apply_coupon] {
  padding: 0.5rem;
}
.is-dark-theme .woocommerce-form-coupon button[name=apply_coupon] {
  border-color: var(--global--color-background);
}
.is-dark-theme .woocommerce-form-coupon button[name=apply_coupon]:hover, .is-dark-theme .woocommerce-form-coupon button[name=apply_coupon]:active {
  background: var(--global--color-background);
}

#ship-to-different-address {
  font-size: 1em;
  display: inline-block;
  margin: 1.42em 0;
}
#ship-to-different-address label {
  font-weight: 400;
  cursor: pointer;
}
#ship-to-different-address label span {
  position: relative;
  display: block;
  text-align: right;
  padding-right: 45px;
}
#ship-to-different-address label span::before {
  content: "";
  display: block;
  height: 16px;
  width: 30px;
  border: 2px solid var(--form--border-color);
  background: var(--global--color-primary);
  border-radius: 13rem;
  box-sizing: content-box;
  transition: all ease-in-out 0.3s;
  position: absolute;
  top: 0;
  right: 0;
}
#ship-to-different-address label span::after {
  content: "";
  display: block;
  width: 14px;
  height: 14px;
  background: var(--global--color-background);
  position: absolute;
  top: 3px;
  right: 17px;
  border-radius: 13rem;
  transition: all ease-in-out 0.3s;
}
#ship-to-different-address label input[type=checkbox] {
  display: none;
}
#ship-to-different-address label input[type=checkbox]:checked + span::after {
  right: 3px;
  background: var(--global--color-primary);
}
#ship-to-different-address label input[type=checkbox]:checked + span::before {
  background: var(--global--color-background);
}

.woocommerce-no-js form.woocommerce-form-login,
.woocommerce-no-js form.woocommerce-form-coupon {
  display: block !important;
}
.woocommerce-no-js .woocommerce-form-login-toggle,
.woocommerce-no-js .woocommerce-form-coupon-toggle,
.woocommerce-no-js .showcoupon {
  display: none !important;
}

.woocommerce-terms-and-conditions {
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.05);
}

.woocommerce-terms-and-conditions-link {
  display: inline-block;
}
.woocommerce-terms-and-conditions-link::after {
  content: "";
  display: inline-block;
  border-style: solid;
  margin-bottom: 2px;
  margin-left: 0.25rem;
  border-width: 6px 6px 0 6px;
  border-color: currentcolor transparent transparent transparent;
}
.woocommerce-terms-and-conditions-link.woocommerce-terms-and-conditions-link--open::after {
  border-width: 0 6px 6px 6px;
  border-color: transparent transparent currentcolor transparent;
}

.woocommerce-checkout .woocommerce {
  max-width: var(--responsive--alignwide-width);
  margin: 0 auto;
}
.woocommerce-checkout ul.woocommerce-error {
  flex-direction: column;
  align-items: flex-start;
}
.woocommerce-checkout ul.woocommerce-error li {
  font-family: var(--heading--font-family);
  margin: 0.5rem 0 0.5rem;
}
.woocommerce-checkout .post-inner {
  padding-top: 0;
}
.woocommerce-checkout .woocommerce-billing-fields h3 {
  margin: 2rem 0;
}
.woocommerce-checkout form[name=checkout] {
  display: table;
}
.woocommerce-checkout .blockUI.blockOverlay {
  position: relative;
}
.woocommerce-checkout .blockUI.blockOverlay::before {
  height: 1em;
  width: 1em;
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -0.5em;
  margin-top: -0.5em;
  content: "";
  animation: spin 1s ease-in-out infinite;
  background: url("../images/icons/loader.svg") center center;
  background-size: cover;
  line-height: 1;
  text-align: center;
  font-size: 2em;
  color: rgba(0, 0, 0, 0.75);
}
.woocommerce-checkout form .col2-set {
  width: 50%;
  float: left;
  padding-right: 1.5vw;
}
.woocommerce-checkout form .col2-set .col-1,
.woocommerce-checkout form .col2-set .col-2 {
  float: none;
  width: 100%;
}
.woocommerce-checkout form .col2-set label {
  font-family: var(--heading--font-family);
  letter-spacing: normal;
}
.woocommerce-checkout form .col2-set p {
  margin-bottom: 1.15em;
}
.woocommerce-checkout form #order_review_heading {
  margin-top: 2rem;
}
.woocommerce-checkout form #order_review_heading,
.woocommerce-checkout form #order_review {
  width: 50%;
  padding-left: 1.5vw;
  float: right;
  clear: right;
}
.woocommerce-checkout form #order_review_heading .woocommerce-checkout-review-order-table,
.woocommerce-checkout form #order_review .woocommerce-checkout-review-order-table {
  margin-top: 2rem;
  border: 0;
}
.woocommerce-checkout form #order_review_heading .woocommerce-checkout-review-order-table th,
.woocommerce-checkout form #order_review_heading .woocommerce-checkout-review-order-table td,
.woocommerce-checkout form #order_review .woocommerce-checkout-review-order-table th,
.woocommerce-checkout form #order_review .woocommerce-checkout-review-order-table td {
  border: 0;
}
.woocommerce-checkout form #order_review_heading .woocommerce-checkout-review-order-table thead,
.woocommerce-checkout form #order_review .woocommerce-checkout-review-order-table thead {
  display: none;
}
.woocommerce-checkout form #order_review_heading .woocommerce-checkout-review-order-table .woocommerce-Price-amount,
.woocommerce-checkout form #order_review .woocommerce-checkout-review-order-table .woocommerce-Price-amount {
  font-weight: 700;
}
.woocommerce-checkout form #order_review_heading .woocommerce-checkout-review-order-table .cart-subtotal,
.woocommerce-checkout form #order_review_heading .woocommerce-checkout-review-order-table .order-total,
.woocommerce-checkout form #order_review .woocommerce-checkout-review-order-table .cart-subtotal,
.woocommerce-checkout form #order_review .woocommerce-checkout-review-order-table .order-total {
  border-top: 2px solid var(--form--border-color);
}
.woocommerce-checkout form .form-row.woocommerce-invalid input.input-text {
  border: 2px solid var(--wc-red);
}
.woocommerce-checkout .woocommerce-input-wrapper .description {
  background: #4169e1;
  color: #fff;
  border-radius: 3px;
  padding: 1rem;
  margin: 0.5rem 0 0;
  clear: both;
  display: none;
  position: relative;
}
.woocommerce-checkout .woocommerce-input-wrapper .description a {
  color: #fff;
  text-decoration: underline;
  border: 0;
  box-shadow: none;
}
.woocommerce-checkout .woocommerce-input-wrapper .description::before {
  left: 50%;
  top: 0;
  margin-top: -4px;
  transform: translateX(-50%) rotate(180deg);
  content: "";
  position: absolute;
  border-width: 4px 6px 0 6px;
  border-style: solid;
  border-color: #4169e1 transparent transparent transparent;
  z-index: 100;
  display: block;
}
.woocommerce-checkout .woocommerce-form-login p.form-row.form-row-first,
.woocommerce-checkout .woocommerce-form-login p.form-row.form-row-last {
  float: none;
}

.woocommerce-checkout-review-order-table ul li {
  list-style-type: none;
}
.woocommerce-checkout-review-order-table input[type=radio].shipping_method {
  display: none;
}
.woocommerce-checkout-review-order-table input[type=radio].shipping_method + label::before {
  content: "";
  display: inline-block;
  width: 14px !important;
  height: 14px;
  border: var(--form--border-width) solid var(--form--border-color);
  background: var(--global--color-white);
  margin-left: 4px;
  margin-right: 1.2rem;
  border-radius: 100%;
  transform: translateY(2px);
}
.woocommerce-checkout-review-order-table input[type=radio].shipping_method:checked + label::before {
  background: var(--global--color-border);
}
.is-dark-theme .woocommerce-checkout-review-order-table input[type=radio].shipping_method:checked + label::before {
  background: var(--global--color-background);
}
.woocommerce-checkout-review-order-table td {
  padding: 1rem 0.5em;
}
.woocommerce-checkout-review-order-table dl.variation {
  margin: 0;
}
.woocommerce-checkout-review-order-table dl.variation p {
  margin: 0;
}
.woocommerce-checkout-review-order-table dl.variation dt,
.woocommerce-checkout-review-order-table dl.variation dd {
  font-family: var(--heading--font-family);
}
.woocommerce-checkout-review-order-table dl.variation dt p,
.woocommerce-checkout-review-order-table dl.variation dd p {
  padding-top: 1px;
  font-family: var(--heading--font-family);
}
.woocommerce-checkout-review-order-table tfoot {
  text-align: left;
}

.woocommerce-order-received .woocommerce-order p,
.woocommerce-order-received .woocommerce-order li {
  font-family: var(--heading--font-family);
}
.woocommerce-order-received table {
  border: 0;
}
.woocommerce-order-received table td,
.woocommerce-order-received table th,
.woocommerce-order-received table tr {
  border: 0;
}
.woocommerce-order-received table tr {
  height: 5rem;
}
.woocommerce-order-received table tfoot {
  border-top: 1px solid #ddd;
  /* Targeting total */
}
.woocommerce-order-received table tfoot tr:last-of-type {
  border-top: 1px solid #ddd;
}
.woocommerce-order-received table tfoot tr:last-of-type .woocommerce-Price-amount {
  font-weight: 700;
}

.woocommerce-checkout-review-order ul {
  margin: 2rem 0 3rem;
  padding-left: 0;
}
.woocommerce-checkout-review-order #place_order {
  width: 100%;
}

.wc_payment_method {
  list-style: none;
}
.wc_payment_method .payment_box {
  padding: 1rem;
  background: #eee;
  color: var(--global--color-dark-gray);
}
.wc_payment_method .payment_box a,
.wc_payment_method .payment_box a:hover,
.wc_payment_method .payment_box a:visited {
  color: var(--global--color-dark-gray);
}
.wc_payment_method .payment_box ul:last-of-type,
.wc_payment_method .payment_box ol:last-of-type {
  margin-bottom: 0;
}
.wc_payment_method .payment_box fieldset {
  padding: 1.5rem;
  padding-bottom: 0;
  border: 0;
  background: #f6f6f6;
}
.wc_payment_method .payment_box li {
  list-style: none;
}
.wc_payment_method .payment_box p:first-child {
  margin-top: 0;
}
.wc_payment_method .payment_box p:last-child {
  margin-bottom: 0;
}
.wc_payment_method .payment_box input[type=checkbox] {
  width: 25px !important;
}
.wc_payment_method .payment_box input[type=radio] + label::before {
  background: #fff !important;
  border: var(--form--border-width) solid #000 !important;
}
.wc_payment_method .payment_box input[type=radio]:checked + label::before {
  background: #000 !important;
}
.wc_payment_method > label:first-of-type {
  display: block;
  margin: 1rem 0;
}
.wc_payment_method > label:first-of-type img {
  max-height: 24px;
  max-width: 200px;
  float: right;
}
.wc_payment_method label {
  cursor: pointer;
}
.wc_payment_method input[type=radio] {
  display: none;
}
.wc_payment_method input[type=radio] + label {
  font-family: var(--heading--font-family);
}
.wc_payment_method input[type=radio] + label::before {
  content: "";
  display: inline-block;
  width: 14px;
  height: 14px;
  border: var(--form--border-width) solid var(--form--border-color);
  background: var(--global--color-white);
  margin-left: 4px;
  margin-right: 1.2rem;
  border-radius: 100%;
  transform: translateY(2px);
}
.wc_payment_method input[type=radio]:checked + label::before {
  background: var(--global--color-border);
}
.is-dark-theme .wc_payment_method input[type=radio]:checked + label::before {
  background: var(--global--color-background);
}

.wc_payment_methods .payment_box p {
  font-family: var(--heading--font-family);
}

.account-payment-methods-table {
  padding-top: 0 !important;
  margin-bottom: 1rem;
}
.account-payment-methods-table table,
.account-payment-methods-table tr {
  border-style: hidden;
}
.account-payment-methods-table tr:nth-child(2n) td {
  background: transparent !important;
}
.account-payment-methods-table tr:nth-child(2n+1) td {
  background: var(--global--color-background);
  filter: brightness(88%);
}
.is-dark-theme .account-payment-methods-table tr:nth-child(2n+1) td {
  filter: brightness(112%);
}
.account-payment-methods-table td.payment-method-actions {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
  display: grid;
  border: none;
  font-size: 0;
}
.account-payment-methods-table td.payment-method-actions a {
  width: 100%;
  padding-top: 0.3rem !important;
  padding-bottom: 0.3rem !important;
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
  background-color: transparent !important;
  color: var(--button--color-text-hover) !important;
}
.account-payment-methods-table td.payment-method-actions a:hover {
  background-color: var(--button--color-background) !important;
  color: var(--button--color-text) !important;
  text-decoration: none !important;
}

.woocommerce-terms-and-conditions-wrapper {
  margin-bottom: 5rem;
}
.woocommerce-terms-and-conditions-wrapper .woocommerce-privacy-policy-text p {
  font-family: var(--heading--font-family);
  font-size: 1.6rem;
}

.woocommerce-order-overview {
  margin-bottom: 2rem;
}

.woocommerce-table--order-details {
  margin-bottom: 2rem;
}
.woocommerce-table--order-details thead,
.woocommerce-table--order-details tfoot {
  text-align: left;
}

/**
 * Layout stuff
 */
.woocommerce {
  /* Shop layout */
}
.woocommerce section {
  padding-top: 2rem;
  padding-bottom: 0;
}
.woocommerce .content-area .site-main {
  margin: 0 5vw;
}
.woocommerce ul.products {
  display: flex;
  align-items: stretch;
  flex-direction: row;
  flex-wrap: wrap;
  box-sizing: border-box;
  word-break: break-word;
  min-width: 12vw;
}
.woocommerce ul.products.columns-2 li.product {
  width: calc(50% - 16px) !important;
}
.woocommerce ul.products.columns-3 li.product {
  width: calc(33.3333333333% - 16px) !important;
}
.woocommerce ul.products.columns-4 li.product {
  width: calc(25% - 16px) !important;
}
.woocommerce ul.products.columns-5 li.product {
  width: calc(20% - 16px) !important;
}
.woocommerce ul.products.columns-6 li.product {
  width: calc(16.6666666667% - 16px) !important;
}
.woocommerce ul.products li.product {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin: 0 8px 16px 8px;
  box-sizing: border-box;
}
.woocommerce ul.products li.product img.attachment-woocommerce_thumbnail,
.woocommerce ul.products li.product img.woocommerce-placeholder {
  height: auto !important;
}
.woocommerce ul.products li.product-category a {
  text-align: left;
  text-decoration: none;
}
.woocommerce ul.products li.product-category a h2.woocommerce-loop-category__title {
  margin-top: 0.4rem;
  font-family: var(--heading--font-family);
  font-size: 1.5rem;
}
.woocommerce ul.products li.product-category a h2.woocommerce-loop-category__title .count {
  background-color: transparent;
  color: currentcolor;
}
.woocommerce ul.products li.product-category mark {
  background-color: initial;
}

@media only screen and (max-width: 600px) {
  .woocommerce .woocommerce-ordering {
    float: left;
    clear: both;
    margin-top: 0;
  }
  .woocommerce .woocommerce-result-count {
    margin-top: 0;
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 667px) {
  .woocommerce ul.products[class*=columns-] li.product,
  .woocommerce-page ul.products[class*=columns-] li.product {
    width: auto !important;
    margin-left: auto;
    margin-right: auto;
  }
}
@media only screen and (min-width: 668px) and (max-width: 768px) {
  .woocommerce .related.products ul.products[class*=columns-] li.product,
  .woocommerce-page .related.products ul.products[class*=columns-] li.product {
    padding: 0 2vw 3em 0 !important;
    margin-bottom: 2em;
  }
  .woocommerce ul.products[class*=columns-],
  .woocommerce-page ul.products[class*=columns-] {
    justify-content: center;
  }
  .woocommerce ul.products[class*=columns-] li.product,
  .woocommerce-page ul.products[class*=columns-] li.product {
    width: 50%;
    padding: 0 2vw 3em 0;
  }
  .woocommerce .onsale,
  .woocommerce-page .onsale {
    font-size: 1rem;
  }
}
@media only screen and (max-width: 768px) {
  .woocommerce section.content-area {
    padding-top: 0;
  }
  #main .woocommerce .woocommerce-cart-form .actions .coupon {
    margin-bottom: 2rem;
  }
  #main .woocommerce .woocommerce-cart-form .actions .coupon button {
    width: 100%;
  }
  #main .woocommerce .woocommerce-cart-form #coupon_code {
    width: 100% !important;
  }
  #main #shipping_method li {
    display: flex;
    justify-content: flex-end;
  }
  .woocommerce .onsale,
  .woocommerce-page .onsale {
    right: -0.7rem !important;
  }
  .woocommerce .woocommerce-tabs ul li,
  .woocommerce-page .woocommerce-tabs ul li {
    font-size: 1rem;
  }
  .woocommerce .woocommerce-tabs ul li a,
  .woocommerce-page .woocommerce-tabs ul li a {
    padding: calc(0.75 * var(--button--padding-vertical)) calc(0.75 * var(--button--padding-horizontal));
  }
  .woocommerce table.shop_table_responsive .button,
  .woocommerce-page table.shop_table_responsive .button {
    background-color: transparent !important;
    color: var(--button--color-text-hover) !important;
  }
  .woocommerce table.shop_table_responsive .button:hover,
  .woocommerce-page table.shop_table_responsive .button:hover {
    background-color: var(--button--color-background) !important;
    color: var(--button--color-text) !important;
    text-decoration: none !important;
  }
  .woocommerce table.shop_table_responsive tr,
  .woocommerce-page table.shop_table_responsive tr {
    margin: 0 0 1.5rem;
  }
  .woocommerce table.shop_table_responsive tr:first-child,
  .woocommerce-page table.shop_table_responsive tr:first-child {
    border-top: 1px solid;
  }
  .woocommerce table.shop_table_responsive tr:first-child td.product-remove:first-child,
  .woocommerce-page table.shop_table_responsive tr:first-child td.product-remove:first-child {
    border-top: inherit;
  }
  .woocommerce table.shop_table_responsive tr:last-child,
  .woocommerce-page table.shop_table_responsive tr:last-child {
    margin-bottom: 0;
  }
  .woocommerce table.shop_table_responsive tr:nth-child(2n) td,
  .woocommerce-page table.shop_table_responsive tr:nth-child(2n) td {
    background: transparent;
  }
  .woocommerce table.shop_table_responsive tr:nth-child(2n+1) td,
  .woocommerce-page table.shop_table_responsive tr:nth-child(2n+1) td {
    background: var(--global--color-background);
    filter: brightness(88%);
  }
  .is-dark-theme .woocommerce table.shop_table_responsive tr:nth-child(2n+1) td,
  .is-dark-theme .woocommerce-page table.shop_table_responsive tr:nth-child(2n+1) td {
    filter: brightness(112%);
  }
  .woocommerce table.shop_table_responsive tr td,
  .woocommerce-page table.shop_table_responsive tr td {
    border-bottom-width: 0;
  }
  .woocommerce table.shop_table_responsive tr td:last-child,
  .woocommerce-page table.shop_table_responsive tr td:last-child {
    border-bottom-width: 1px;
  }
  .woocommerce table.shop_table_responsive tr td.product-quantity::before,
  .woocommerce-page table.shop_table_responsive tr td.product-quantity::before {
    padding-top: 0.9rem;
  }
  .woocommerce table.shop_table_responsive tr .product-remove,
  .woocommerce-page table.shop_table_responsive tr .product-remove {
    float: right;
    position: relative;
    z-index: 1;
  }
  .woocommerce table.shop_table_responsive tr .product-thumbnail,
  .woocommerce-page table.shop_table_responsive tr .product-thumbnail {
    display: block;
  }
  .woocommerce table.shop_table_responsive tr .product-thumbnail img,
  .woocommerce-page table.shop_table_responsive tr .product-thumbnail img {
    width: 70px;
  }
  .woocommerce table.shop_table_responsive tr .product-thumbnail::before,
  .woocommerce-page table.shop_table_responsive tr .product-thumbnail::before {
    content: "";
  }
  .woocommerce .woocommerce-breadcrumb,
  .woocommerce-page .woocommerce-breadcrumb {
    margin-bottom: 4rem;
    font-size: 0.8em;
    font-family: var(--heading--font-family);
  }
  .woocommerce .related.products ul.products,
  .woocommerce-page .related.products ul.products {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .woocommerce .related.products ul.products li.product,
  .woocommerce-page .related.products ul.products li.product {
    margin-bottom: 5em;
  }
  .woocommerce .woocommerce-products-header__title.page-title,
  .woocommerce-page .woocommerce-products-header__title.page-title {
    margin: 3rem auto 4rem;
  }
  .woocommerce .woocommerce-result-count,
  .woocommerce .woocommerce-ordering,
  .woocommerce-page .woocommerce-result-count,
  .woocommerce-page .woocommerce-ordering {
    font-size: 0.8em;
  }
  .woocommerce .woocommerce-ordering,
  .woocommerce-page .woocommerce-ordering {
    margin-bottom: 3rem;
  }
  .woocommerce-cart-form table td.product-name {
    padding-left: 0.5em;
  }
  .woocommerce-cart-form table input.qty {
    padding: 1rem 1.5rem;
  }
  .woocommerce-checkout form .col2-set {
    width: 100%;
    float: none;
    padding-right: 0;
  }
  .woocommerce-checkout form .col2-set .col-1,
  .woocommerce-checkout form .col2-set .col-2 {
    float: none;
    width: 100%;
  }
  .woocommerce-checkout form #order_review_heading {
    margin-top: 2rem;
  }
  .woocommerce-checkout form #order_review_heading,
  .woocommerce-checkout form #order_review {
    width: 100%;
    padding-left: 0;
    float: none;
  }
  .woocommerce-checkout form table tbody td.product-total {
    text-align: end;
  }
  .woocommerce-checkout form table tfoot .cart-subtotal td,
  .woocommerce-checkout form table tfoot .order-total td {
    text-align: end;
  }
  .logged-in.woocommerce-account #main .woocommerce {
    flex-direction: column;
  }
  .logged-in.woocommerce-account #main .woocommerce-MyAccount-navigation,
  .logged-in.woocommerce-account #main .woocommerce-MyAccount-content {
    width: 100%;
  }
  .logged-in.woocommerce-account #main table.account-orders-table .button {
    padding-left: 0.5em;
    padding-right: 0.5em;
    width: 100%;
    margin: 2rem 0;
  }
  .logged-in.woocommerce-account table.account-orders-table td {
    padding-bottom: 1.5rem;
  }
}
@media only screen and (min-width: 768px) {
  /**
  * Tables
  */
  .woocommerce table.shop_table tbody tr,
  .woocommerce-page table.shop_table tbody tr {
    font-size: 0.88889em;
  }
  .woocommerce .onsale,
  .woocommerce-page .onsale {
    font-size: 1rem;
  }
  /**
      * Home page
      */
  .home #main [class*="woocommerce columns-"] {
    word-break: break-word;
    max-width: var(--responsive--aligndefault-width);
    margin-left: auto;
    margin-right: auto;
  }
  /**
  * Shop page
  */
  .woocommerce-pagination span.page-numbers,
  .woocommerce-pagination a.page-numbers,
  .woocommerce-pagination .next.page-numbers,
  .woocommerce-pagination .prev.page-numbers {
    padding: 1rem;
  }
  /**
  * Account section
  */
  .woocommerce-account .woocommerce-MyAccount-navigation {
    float: none;
    width: 20%;
    margin-bottom: 1.5rem;
    margin-right: 3rem;
  }
  .woocommerce-account .woocommerce-MyAccount-navigation li {
    margin: 0 1rem 3rem 0;
    padding: 0;
    border-bottom: 0;
  }
  .woocommerce-account .woocommerce-MyAccount-navigation li:last-child {
    margin-right: 0;
  }
  .woocommerce-account .woocommerce-MyAccount-content {
    float: none;
    width: 75%;
  }
  .woocommerce-account table.account-orders-table {
    margin-top: 0;
    border: 0;
    margin-bottom: 1rem;
  }
  .woocommerce-account table.account-orders-table tr,
  .woocommerce-account table.account-orders-table td,
  .woocommerce-account table.account-orders-table th {
    border: 0;
    padding: 0;
  }
  .woocommerce-account table.account-orders-table th,
  .woocommerce-account table.account-orders-table td,
  .woocommerce-account table.account-orders-table td.woocommerce-orders-table__cell-order-actions {
    width: 1%;
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .woocommerce-account table.account-orders-table th a,
  .woocommerce-account table.account-orders-table td a,
  .woocommerce-account table.account-orders-table td.woocommerce-orders-table__cell-order-actions a {
    padding-top: 0.3rem !important;
    padding-bottom: 0.3rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .woocommerce-account table.account-orders-table td.woocommerce-orders-table__cell-order-date {
    padding-right: 0;
  }
  .woocommerce-account table.account-orders-table thead {
    border-bottom: 1px solid currentcolor;
  }
  .woocommerce-account table.account-orders-table .button {
    padding-left: 0.5em;
    padding-right: 0.5em;
    width: 100%;
    margin: 1.5rem 0;
    background-color: transparent !important;
    color: var(--button--color-text-hover) !important;
  }
  .woocommerce-account table.account-orders-table .button:hover {
    background-color: var(--button--color-background) !important;
    color: var(--button--color-text) !important;
    text-decoration: none !important;
  }
  /**
  * Layout stuff
  */
  .woocommerce .content-area {
    margin: 0 auto;
    padding: 0 6vw;
  }
  .woocommerce .content-area .site-main {
    margin: 0;
  }
  .single-product .entry .entry-content,
  .single-product .entry .entry-summary {
    max-width: none;
    margin: 0 0 3rem;
    padding: 0;
  }
  .single-product .entry .entry-content > *,
  .single-product .entry .entry-summary > * {
    max-width: none;
  }
  .woocommerce-breadcrumb {
    margin-bottom: 5rem;
    font-size: 0.88889em;
    font-family: var(--heading--font-family);
  }
  .woocommerce-product-gallery {
    margin-bottom: 8rem;
  }
  .woocommerce-checkout #main .woocommerce {
    max-width: 1600px;
    padding: 0 6vw;
    margin: 0 auto;
  }
}
@media only screen and (min-width: 1168px) {
  .woocommerce .content-area {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 6vw;
  }
  .woocommerce .onsale {
    font-size: 1.2rem;
  }
  .woocommerce-breadcrumb {
    margin-bottom: 5rem;
    font-size: 0.88889em;
    font-family: var(--heading--font-family);
  }
  .woocommerce-product-gallery {
    margin-bottom: 8rem;
  }
  .woocommerce-account table.account-orders-table th,
  .woocommerce-account table.account-orders-table td,
  .woocommerce-account table.account-orders-table td.woocommerce-orders-table__cell-order-actions {
    padding-right: 1.5rem;
    padding-left: 1.5rem;
  }
}
@media only screen and (max-width: 768px) {
  .woocommerce-products-header {
    border-bottom: none !important;
    padding-bottom: 0;
    margin-bottom: 0 !important;
  }
}
@media only screen and (min-width: 600px) {
  .woocommerce-products-header {
    padding-bottom: 1.5vw;
  }
  .woocommerce-ordering,
  .woocommerce-result-count {
    margin-top: 0 !important;
  }
}
@media only screen and (min-width: 690px) {
  .woocommerce-products-header {
    border-bottom: 3px solid var(--global--color-border);
  }
}
.woocommerce-account .woocommerce-MyAccount-content p:first-of-type {
  margin-bottom: 2rem;
}
.woocommerce-account .woocommerce-MyAccount-content #add_payment_method ul {
  list-style-type: none !important;
}
.woocommerce-account .woocommerce-MyAccount-content #add_payment_method .woocommerce-PaymentMethod {
  margin-bottom: 1.5rem;
}
.woocommerce-account .woocommerce-MyAccount-content input[type=radio] {
  float: left;
  margin-top: 0.5rem;
  margin-right: 0.5rem;
}
.woocommerce-account .woocommerce-MyAccount-content label {
  font-size: 1.5rem;
  display: flex;
  justify-content: flex-end;
}
.woocommerce-account .woocommerce-MyAccount-content label img {
  margin-left: 10px !important;
}
.woocommerce-account .woocommerce-MyAccount-content label img:first-child {
  margin-left: auto !important;
}
.woocommerce-account .woocommerce-MyAccount-content label img:last-child {
  margin-right: 5px !important;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-PaymentBox p,
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-PaymentBox label {
  font-size: 1.3rem;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-PaymentBox p {
  margin-bottom: 1.5rem;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-PaymentBox br {
  display: none;
}
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-PaymentBox .woocommerce_error {
  margin-top: 1rem;
  margin-bottom: 0;
}
.woocommerce-account .woocommerce-MyAccount-navigation-link {
  margin-bottom: 20px !important;
}
.woocommerce-account .woocommerce-MyAccount-navigation-link a {
  color: currentcolor !important;
  font-weight: 400 !important;
  font-size: 1.8rem;
}
.woocommerce-account .woocommerce-MyAccount-navigation-link a:hover {
  color: currentcolor !important;
  -webkit-text-decoration: underline solid currentcolor 1px !important;
          text-decoration: underline solid currentcolor 1px !important;
}

.alignwide .woocommerce > * {
  max-width: var(--responsive--alignwide-width);
  display: block;
  margin: var(--global--spacing-vertical) auto;
}

.woocommerce .return-to-shop a.button,
.woocommerce .wc-proceed-to-checkout a.button {
  margin-top: var(--global--spacing-vertical);
  float: left;
  display: inline-block;
  width: 100%;
}
.woocommerce .woocommerce-cart-form {
  text-align: center;
}
.woocommerce .woocommerce-cart-form .shop_table_responsive {
  margin-top: var(--global--spacing-vertical);
  margin-bottom: var(--global--spacing-vertical);
}
.woocommerce .woocommerce-cart-form .shop_table_responsive th {
  border: none;
}
.woocommerce .woocommerce-cart-form .shop_table_responsive input#coupon_code.input-text {
  min-width: 9rem;
  width: auto !important;
}
.woocommerce .woocommerce-cart-form button[name=update_cart],
.woocommerce .woocommerce-cart-form button[name=apply_coupon] {
  padding: 0.5rem;
  color: var(--global--color-primary);
  background: var(--global--color-background);
  border: var(--form--border-width) solid var(--global--color-primary);
}
.woocommerce .woocommerce-cart-form button[name=update_cart]:hover, .woocommerce .woocommerce-cart-form button[name=update_cart]:active,
.woocommerce .woocommerce-cart-form button[name=apply_coupon]:hover,
.woocommerce .woocommerce-cart-form button[name=apply_coupon]:active {
  color: var(--global--color-background);
  background: var(--global--color-primary);
}
.woocommerce .woocommerce-cart-form .product-thumbnail .attachment-woocommerce_thumbnail {
  height: auto !important;
}
.woocommerce .woocommerce-cart-form input.qty {
  width: 6em;
  text-align: center;
}
.woocommerce .cart-collaterals h2 {
  margin-bottom: var(--global--spacing-vertical);
}
.woocommerce .cart-collaterals #shipping_method {
  list-style: none;
  padding-left: 0;
}
.woocommerce .cart-collaterals .shipping-calculator-form p {
  margin-bottom: 0.5rem;
}
.woocommerce .cart-collaterals .cross-sells li {
  list-style: none;
}
.woocommerce .cart-collaterals .cross-sells li > em,
.woocommerce .cart-collaterals .cross-sells a {
  display: inline-block;
}

/**
 * Downloads
 */
.woocommerce-order-downloads {
  padding-top: 0 !important;
}
.woocommerce-order-downloads table,
.woocommerce-order-downloads tr {
  border-style: hidden;
}
.woocommerce-order-downloads table td.download-remaining,
.woocommerce-order-downloads tr td.download-remaining {
  text-align: center !important;
}
.woocommerce-order-downloads tr:nth-child(2n) td {
  background: transparent !important;
}
.woocommerce-order-downloads tr:nth-child(2n+1) td {
  background: var(--global--color-background);
  filter: brightness(88%);
}
.is-dark-theme .woocommerce-order-downloads tr:nth-child(2n+1) td {
  filter: brightness(112%);
}
.woocommerce-order-downloads td.download-file {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
}
.woocommerce-order-downloads td.download-file a {
  width: 100%;
  padding-top: 0.3rem !important;
  padding-bottom: 0.3rem !important;
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
  background-color: transparent !important;
  color: var(--button--color-text-hover) !important;
}
.woocommerce-order-downloads td.download-file a:hover {
  background-color: var(--button--color-background) !important;
  color: var(--button--color-text) !important;
  text-decoration: none !important;
}

.woocommerce-message,
.woocommerce-error li,
.woocommerce-info {
  padding: 1.5rem 3rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.woocommerce-message .button,
.woocommerce-error li .button,
.woocommerce-info .button {
  order: 2;
}

@media only screen and (max-width: 768px) {
  .woocommerce-message,
  .woocommerce-error li,
  .woocommerce-info {
    padding: 1rem 1.5rem;
  }
  .woocommerce-message a.button,
  .woocommerce-error li a.button,
  .woocommerce-info a.button {
    margin-left: 10px;
    min-width: 100px;
    padding: calc(0.7 * var(--button--padding-vertical)) calc(0.5 * var(--button--padding-horizontal));
  }
}
.woocommerce-info {
  border-top-color: var(--wc-blue);
}

.woocommerce-error {
  border-top-color: #b22222;
}
.woocommerce-error > li {
  margin: 0;
}

.woocommerce-store-notice {
  background: #eee;
  color: #000;
  border-top: 2px solid var(--wc-highlight, #777335);
  padding: 2rem;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}

.admin-bar .woocommerce-store-notice {
  top: 32px;
}

.woocommerce-store-notice__dismiss-link {
  float: right;
  color: #000;
}
.woocommerce-store-notice__dismiss-link:hover {
  text-decoration: none;
  color: #000;
}

/**
 * Coupon error notice
 */
.woocommerce-cart td.actions .coupon .coupon-error-notice {
  clear: left;
  color: var(--wc-red);
  flex-basis: 100%;
  float: none;
  font-size: 0.75em;
  margin-bottom: 0;
  margin-top: 8px;
  text-align: left;
  width: auto;
}

form.checkout_coupon .coupon-error-notice {
  color: var(--wc-red);
  display: block;
  font-size: 0.75em;
  margin-top: 8px;
}
form.checkout_coupon .input-text.has-error:focus {
  border-color: var(--wc-red);
}

/**
 * Checkout error message
 */
.checkout .checkout-inline-error-message {
  color: var(--wc-red);
  font-size: 0.75em;
  line-height: 1.3;
  margin-bottom: 0;
  margin-top: 0.5em;
}/*# sourceMappingURL=twenty-twenty-one.css.map */