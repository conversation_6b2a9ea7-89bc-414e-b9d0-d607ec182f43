@charset "UTF-8";
/* stylelint-disable no-descending-specificity */
/**
 * WooCommerce CSS Variables
 */
:root {
  --woocommerce: #720eec;
  --wc-green: #7ad03a;
  --wc-red: #a00;
  --wc-orange: #ffba00;
  --wc-blue: #2ea2cc;
  --wc-primary: #720eec;
  --wc-primary-text: rgb(252.3245945946, 250.73, 254.27);
  --wc-secondary: #e9e6ed;
  --wc-secondary-text: #515151;
  --wc-highlight: rgb(149.3408, 142.38, 8.8592);
  --wc-highligh-text: white;
  --wc-content-bg: #fff;
  --wc-subtext: #767676;
  --wc-form-border-color: rgba(32, 7, 7, 0.8);
  --wc-form-border-radius: 4px;
  --wc-form-border-width: 1px;
}

/* @deprecated 4.6.0 */
body {
  margin: 65px auto 24px;
  box-shadow: none;
  background: #f1f1f1;
  padding: 0;
}

.wc-logo {
  border: 0;
  margin: 0 0 24px;
  padding: 0;
  text-align: center;
}
.wc-logo img {
  max-width: 30%;
}

.wc-setup {
  text-align: center;
}
.wc-setup #wc_tracker_checkbox {
  display: none;
}
.wc-setup .select2-container {
  text-align: left;
  width: auto;
}
.wc-setup .hidden {
  display: none;
}
.wc-setup #tiptip_content {
  background: #5f6973;
}
.wc-setup #tiptip_holder.tip_top #tiptip_arrow_inner {
  border-top-color: #5f6973;
}

.wc-setup-content {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.13);
  padding: 2em;
  margin: 0 0 20px;
  background: #fff;
  overflow: hidden;
  zoom: 1;
  text-align: left;
}
.wc-setup-content h1,
.wc-setup-content h2,
.wc-setup-content h3,
.wc-setup-content table {
  margin: 0 0 20px;
  border: 0;
  padding: 0;
  color: #666;
  clear: none;
  font-weight: 500;
}
.wc-setup-content p {
  margin: 20px 0;
  font-size: 1em;
  line-height: 1.75;
  color: #666;
}
.wc-setup-content table {
  font-size: 1em;
  line-height: 1.75;
  color: #666;
}
.wc-setup-content a {
  color: #720eec;
}
.wc-setup-content a:hover, .wc-setup-content a:focus {
  color: rgb(20.976, 2.576, 43.424);
}
.wc-setup-content .form-table th {
  width: 35%;
  vertical-align: top;
  font-weight: 400;
}
.wc-setup-content .form-table td {
  vertical-align: top;
}
.wc-setup-content .form-table td select,
.wc-setup-content .form-table td input {
  width: 100%;
  box-sizing: border-box;
}
.wc-setup-content .form-table td input[size] {
  width: auto;
}
.wc-setup-content .form-table td .description {
  line-height: 1.5;
  display: block;
  margin-top: 0.25em;
  color: #999;
  font-style: italic;
}
.wc-setup-content .form-table td .input-checkbox,
.wc-setup-content .form-table td .input-radio {
  width: auto;
  box-sizing: inherit;
  padding: inherit;
  margin: 0 0.5em 0 0;
  box-shadow: none;
}
.wc-setup-content .form-table .section_title td {
  padding: 0;
}
.wc-setup-content .form-table .section_title td h2,
.wc-setup-content .form-table .section_title td p {
  margin: 12px 0 0;
}
.wc-setup-content .form-table th,
.wc-setup-content .form-table td {
  padding: 12px 0;
  margin: 0;
  border: 0;
}
.wc-setup-content .form-table th:first-child,
.wc-setup-content .form-table td:first-child {
  padding-right: 1em;
}
.wc-setup-content table.tax-rates {
  width: 100%;
  font-size: 0.92em;
}
.wc-setup-content table.tax-rates th {
  padding: 0;
  text-align: center;
  width: auto;
  vertical-align: middle;
}
.wc-setup-content table.tax-rates td {
  border: 1px solid #f5f5f5;
  padding: 6px;
  text-align: center;
  vertical-align: middle;
}
.wc-setup-content table.tax-rates td input {
  outline: 0;
  border: 0;
  padding: 0;
  box-shadow: none;
  text-align: center;
  width: 100%;
}
.wc-setup-content table.tax-rates td.sort {
  cursor: move;
  color: #ccc;
}
.wc-setup-content table.tax-rates td.sort::before {
  content: "\f333";
  font-family: dashicons; /* stylelint-disable-line font-family-no-missing-generic-family-keyword */
}
.wc-setup-content table.tax-rates td.readonly {
  background: #f5f5f5;
}
.wc-setup-content table.tax-rates .add {
  padding: 1em 0 0 1em;
  line-height: 1;
  font-size: 1em;
  width: 0;
  margin: 6px 0 0;
  height: 0;
  overflow: hidden;
  position: relative;
  display: inline-block;
}
.wc-setup-content table.tax-rates .add::before {
  content: "\f502";
  font-family: dashicons; /* stylelint-disable-line font-family-no-missing-generic-family-keyword */
  position: absolute;
  left: 0;
  top: 0;
}
.wc-setup-content table.tax-rates .remove {
  padding: 1em 0 0 1em;
  line-height: 1;
  font-size: 1em;
  width: 0;
  margin: 0;
  height: 0;
  overflow: hidden;
  position: relative;
  display: inline-block;
}
.wc-setup-content table.tax-rates .remove::before {
  content: "\f182";
  font-family: dashicons; /* stylelint-disable-line font-family-no-missing-generic-family-keyword */
  position: absolute;
  left: 0;
  top: 0;
}
.wc-setup-content .wc-setup-pages {
  width: 100%;
  border-top: 1px solid #eee;
}
.wc-setup-content .wc-setup-pages thead th {
  display: none;
}
.wc-setup-content .wc-setup-pages .page-name {
  width: 30%;
  font-weight: 700;
}
.wc-setup-content .wc-setup-pages th,
.wc-setup-content .wc-setup-pages td {
  padding: 14px 0;
  border-bottom: 1px solid #eee;
}
.wc-setup-content .wc-setup-pages th:first-child,
.wc-setup-content .wc-setup-pages td:first-child {
  padding-right: 9px;
}
.wc-setup-content .wc-setup-pages th {
  padding-top: 0;
}
.wc-setup-content .wc-setup-pages .page-options p {
  color: #777;
  margin: 6px 0 0 24px;
  line-height: 1.75;
}
.wc-setup-content .wc-setup-pages .page-options p input {
  vertical-align: middle;
  margin: 1px 0 0;
  height: 1.75em;
  width: 1.75em;
  line-height: 1.75;
}
.wc-setup-content .wc-setup-pages .page-options p label {
  line-height: 1;
}
@media screen and (max-width: 782px) {
  .wc-setup-content .form-table tbody th {
    width: auto;
  }
}
.wc-setup-content .twitter-share-button {
  float: right;
}
.wc-setup-content .wc-setup-next-steps {
  overflow: hidden;
  margin: 0 0 24px;
  padding-bottom: 2px;
}
.wc-setup-content .wc-setup-next-steps h2 {
  margin-bottom: 12px;
}
.wc-setup-content .wc-setup-next-steps .wc-setup-next-steps-first {
  float: left;
  width: 50%;
  box-sizing: border-box;
}
.wc-setup-content .wc-setup-next-steps .wc-setup-next-steps-last {
  float: right;
  width: 50%;
  box-sizing: border-box;
}
.wc-setup-content .wc-setup-next-steps ul {
  padding: 0 2em 0 0;
  list-style: none outside;
  margin: 0;
}
.wc-setup-content .wc-setup-next-steps ul li a {
  display: block;
  padding: 0 0 0.75em;
}
.wc-setup-content .wc-setup-next-steps ul .setup-product a.button {
  background-color: #f7f7f7;
  border-color: #ccc;
  color: #23282d;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 1px 0 #ccc;
  text-shadow: 1px 0 1px #eee, 0 1px 1px #eee;
  font-size: 1em;
  height: auto;
  line-height: 1.75;
  margin: 0 0 0.75em;
  opacity: 1;
  padding: 1em;
  text-align: center;
}
.wc-setup-content .wc-setup-next-steps ul .setup-product a.button:hover, .wc-setup-content .wc-setup-next-steps ul .setup-product a.button:focus, .wc-setup-content .wc-setup-next-steps ul .setup-product a.button:active {
  background: #f5f5f5;
  border-color: #aaa;
}
.wc-setup-content .wc-setup-next-steps ul .setup-product a.button-primary {
  color: #fff;
  background-color: #720eec;
  border-color: #720eec;
  box-shadow: 0 0 0;
  text-shadow: none;
}
.wc-setup-content .wc-setup-next-steps ul .setup-product a.button-primary:hover, .wc-setup-content .wc-setup-next-steps ul .setup-product a.button-primary:focus, .wc-setup-content .wc-setup-next-steps ul .setup-product a.button-primary:active {
  color: #fff;
  background: rgb(127.432, 33.632, 241.868);
  border-color: rgb(127.432, 33.632, 241.868);
  box-shadow: none;
}
.wc-setup-content .wc-setup-next-steps ul li a::before {
  color: #82878c;
  font: 400 20px/1 dashicons; /* stylelint-disable-line font-family-no-missing-generic-family-keyword */
  speak: never;
  display: inline-block;
  padding: 0 10px 0 0;
  top: 1px;
  position: relative;
  text-decoration: none !important;
  vertical-align: top;
}
.wc-setup-content .wc-setup-next-steps ul .learn-more a::before {
  content: "\f105";
}
.wc-setup-content .wc-setup-next-steps ul .video-walkthrough a::before {
  content: "\f126";
}
.wc-setup-content .wc-setup-next-steps ul .newsletter a::before {
  content: "\f465";
}
.wc-setup-content .woocommerce-newsletter,
.wc-setup-content .updated {
  padding: 24px 24px 0;
  margin: 0 0 24px;
  overflow: hidden;
  background: #f5f5f5;
}
.wc-setup-content .woocommerce-newsletter p,
.wc-setup-content .updated p {
  padding: 0;
  margin: 0 0 12px;
}
.wc-setup-content .woocommerce-newsletter form,
.wc-setup-content .woocommerce-newsletter p:last-child,
.wc-setup-content .updated form,
.wc-setup-content .updated p:last-child {
  margin: 0 0 24px;
}
.wc-setup-content .checkbox input[type=checkbox] {
  opacity: 0;
  position: absolute;
  left: -9999px;
}
.wc-setup-content .checkbox label {
  position: relative;
  display: inline-block;
  padding-left: 28px;
}
.wc-setup-content .checkbox label::before, .wc-setup-content .checkbox label::after {
  position: absolute;
  content: "";
  display: inline-block;
}
.wc-setup-content .checkbox label::before {
  height: 16px;
  width: 16px;
  left: 0;
  top: 3px;
  border: 1px solid #aaa;
  background-color: #fff;
  border-radius: 3px;
}
.wc-setup-content .checkbox label::after {
  height: 5px;
  width: 9px;
  border-left: 2px solid;
  border-bottom: 2px solid;
  transform: rotate(-45deg);
  left: 4px;
  top: 7px;
  color: #fff;
}
.wc-setup-content .checkbox input[type=checkbox] + label::after {
  content: none;
}
.wc-setup-content .checkbox input[type=checkbox]:checked + label::after {
  content: "";
}
.wc-setup-content .checkbox input[type=checkbox]:focus + label::before {
  outline: rgb(59, 153, 252) auto 5px;
}
.wc-setup-content .checkbox input[type=checkbox]:checked + label::before {
  background: #720eec;
  border-color: #720eec;
}

.woocommerce-tracker {
  margin: 24px 0;
  border: 1px solid #eee;
  padding: 20px;
  border-radius: 4px;
  overflow: hidden;
  text-align: left;
}
.woocommerce-tracker h1 {
  border-bottom: 0;
  padding-bottom: 0;
}
.woocommerce-tracker .wc-backbone-modal-header {
  border-bottom: 0;
}
.woocommerce-tracker .wc-backbone-modal-main article {
  padding-top: 0;
}
.woocommerce-tracker .wc-backbone-modal-main footer {
  border-top: 0;
  box-shadow: none;
}
.woocommerce-tracker p {
  font-size: 14px;
  line-height: 1.5;
}
.woocommerce-tracker .woocommerce-tracker-checkbox label {
  margin-top: -4px;
  display: inline-block;
}

.wc-setup-steps {
  padding: 0 0 24px;
  margin: 0;
  list-style: none outside;
  overflow: hidden;
  color: #ccc;
  width: 100%;
  display: inline-flex;
}
.wc-setup-steps li {
  width: 100%;
  float: left;
  padding: 0 0 0.8em;
  margin: 0;
  text-align: center;
  position: relative;
  border-bottom: 4px solid #ccc;
  line-height: 1.4;
}
.wc-setup-steps li a {
  color: #720eec;
  text-decoration: none;
  padding: 1.5em;
  margin: -1.5em;
  position: relative;
  z-index: 1;
}
.wc-setup-steps li a:hover, .wc-setup-steps li a:focus {
  color: #111;
  text-decoration: underline;
}
.wc-setup-steps li::before {
  content: "";
  border: 4px solid #ccc;
  border-radius: 100%;
  width: 4px;
  height: 4px;
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -6px;
  margin-bottom: -8px;
  background: #fff;
}
.wc-setup-steps li.active {
  border-color: #720eec;
  color: #720eec;
  font-weight: 700;
}
.wc-setup-steps li.active::before {
  border-color: #720eec;
}
.wc-setup-steps li.done {
  border-color: #720eec;
  color: #720eec;
}
.wc-setup-steps li.done::before {
  border-color: #720eec;
  background: #720eec;
}

.wc-setup .wc-setup-actions {
  overflow: hidden;
  margin: 20px 0 0;
  position: relative;
}

.wc-setup .wc-setup-actions .button-primary,
.woocommerce-tracker .button-primary {
  background-color: #720eec;
  border-color: #720eec;
  box-shadow: 0 0 0;
  text-shadow: none;
  margin: 0;
  opacity: 1;
}
.wc-setup .wc-setup-actions .button-primary:hover, .wc-setup .wc-setup-actions .button-primary:focus, .wc-setup .wc-setup-actions .button-primary:active,
.woocommerce-tracker .button-primary:hover,
.woocommerce-tracker .button-primary:focus,
.woocommerce-tracker .button-primary:active {
  background: rgb(127.432, 33.632, 241.868);
  border-color: rgb(127.432, 33.632, 241.868);
  box-shadow: 0 0 0;
}

.wc-setup-content p:last-child {
  margin-bottom: 0;
}

.wc-setup-content p.store-setup {
  margin-top: 0;
}

.wc-setup-footer-links {
  font-size: 0.85em;
  color: #7b7b7b;
  margin: 1.18em auto;
  display: inline-block;
  text-align: center;
}

.wc-wizard-storefront .wc-wizard-storefront-intro {
  padding: 40px 40px 0;
  background: #f5f5f5;
  text-align: center;
}
.wc-wizard-storefront .wc-wizard-storefront-intro img {
  margin: 40px 0 0 0;
  width: 100%;
  display: block;
}
.wc-wizard-storefront .wc-wizard-storefront-features {
  list-style: none outside;
  margin: 0 0 20px;
  padding: 0 0 0 30px;
  overflow: hidden;
}
.wc-wizard-storefront .wc-wizard-storefront-feature {
  margin: 0;
  padding: 20px 30px 20px 2em;
  width: 50%;
  box-sizing: border-box;
}
.wc-wizard-storefront .wc-wizard-storefront-feature::before {
  margin-left: -2em;
  position: absolute;
}
.wc-wizard-storefront .wc-wizard-storefront-feature.first {
  clear: both;
  float: left;
}
.wc-wizard-storefront .wc-wizard-storefront-feature.last {
  float: right;
}
.wc-wizard-storefront .wc-wizard-storefront-feature__bulletproof::before {
  content: "🔒";
}
.wc-wizard-storefront .wc-wizard-storefront-feature__mobile::before {
  content: "📱";
}
.wc-wizard-storefront .wc-wizard-storefront-feature__accessibility::before {
  content: "👓";
}
.wc-wizard-storefront .wc-wizard-storefront-feature__search::before {
  content: "🔍";
}
.wc-wizard-storefront .wc-wizard-storefront-feature__compatibility::before {
  content: "🔧";
}
.wc-wizard-storefront .wc-wizard-storefront-feature__extendable::before {
  content: "🎨";
}

.wc-wizard-services {
  border: 1px solid #eee;
  padding: 0;
  margin: 0 0 1em;
  list-style: none outside;
  border-radius: 4px;
  overflow: hidden;
}
.wc-wizard-services p {
  margin: 0 0 1em 0;
  padding: 0;
  font-size: 1em;
  line-height: 1.5;
}

.wc-wizard-service-item,
.wc-wizard-services-list-toggle {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 0;
  border-bottom: 1px solid #eee;
  color: #666;
  align-items: center;
}
.wc-wizard-service-item:last-child,
.wc-wizard-services-list-toggle:last-child {
  border-bottom: 0;
}
.wc-wizard-service-item .payment-gateway-fee,
.wc-wizard-services-list-toggle .payment-gateway-fee {
  color: #a6a6a6;
}
.wc-wizard-service-item .wc-wizard-service-name,
.wc-wizard-services-list-toggle .wc-wizard-service-name {
  flex-basis: 0;
  min-width: 160px;
  text-align: center;
  font-weight: 700;
  padding: 2em 0;
  align-self: stretch;
  display: flex;
  align-items: baseline;
}
.wc-wizard-payment-gateway-form .wc-wizard-service-item .wc-wizard-service-name,
.wc-wizard-payment-gateway-form .wc-wizard-services-list-toggle .wc-wizard-service-name {
  justify-content: center;
}
.wc-wizard-service-item .wc-wizard-service-name img,
.wc-wizard-services-list-toggle .wc-wizard-service-name img {
  max-width: 75px;
}
.wc-wizard-service-item.stripe-logo .wc-wizard-service-name img,
.wc-wizard-services-list-toggle.stripe-logo .wc-wizard-service-name img {
  padding: 8px 0;
}
.wc-wizard-service-item.paypal-logo .wc-wizard-service-name img,
.wc-wizard-services-list-toggle.paypal-logo .wc-wizard-service-name img {
  max-width: 87px;
  padding: 2px 0;
}
.wc-wizard-service-item.klarna-logo .wc-wizard-service-name img,
.wc-wizard-services-list-toggle.klarna-logo .wc-wizard-service-name img {
  max-width: 87px;
  padding: 12px 0;
}
.wc-wizard-service-item.square-logo .wc-wizard-service-name img,
.wc-wizard-services-list-toggle.square-logo .wc-wizard-service-name img {
  max-width: 95px;
  padding: 12px 0;
}
.wc-wizard-service-item.eway-logo .wc-wizard-service-name img,
.wc-wizard-services-list-toggle.eway-logo .wc-wizard-service-name img {
  max-width: 87px;
}
.wc-wizard-service-item.payfast-logo .wc-wizard-service-name img,
.wc-wizard-services-list-toggle.payfast-logo .wc-wizard-service-name img {
  max-width: 140px;
}
.wc-wizard-service-item .wc-wizard-service-description,
.wc-wizard-services-list-toggle .wc-wizard-service-description {
  flex-grow: 1;
  padding: 20px;
}
.wc-wizard-service-item .wc-wizard-service-description p,
.wc-wizard-services-list-toggle .wc-wizard-service-description p {
  margin-bottom: 1em;
}
.wc-wizard-service-item .wc-wizard-service-description p:last-child,
.wc-wizard-services-list-toggle .wc-wizard-service-description p:last-child {
  margin-bottom: 0;
}
.wc-wizard-service-item .wc-wizard-service-description .wc-wizard-service-settings-description,
.wc-wizard-services-list-toggle .wc-wizard-service-description .wc-wizard-service-settings-description {
  display: block;
  font-style: italic;
  color: #999;
}
.wc-wizard-service-item .wc-wizard-service-enable,
.wc-wizard-services-list-toggle .wc-wizard-service-enable {
  flex-basis: 0;
  min-width: 75px;
  text-align: center;
  cursor: pointer;
  padding: 2em 0;
  position: relative;
  max-height: 1.5em;
  align-self: flex-start;
  order: 3;
}
.wc-wizard-service-item .wc-wizard-service-toggle,
.wc-wizard-services-list-toggle .wc-wizard-service-toggle {
  height: 16px;
  width: 32px;
  border: 2px solid #720eec;
  background-color: #720eec;
  display: inline-block;
  text-indent: -9999px;
  border-radius: 10em;
  position: relative;
}
.wc-wizard-service-item .wc-wizard-service-toggle input[type=checkbox],
.wc-wizard-services-list-toggle .wc-wizard-service-toggle input[type=checkbox] {
  display: none;
}
.wc-wizard-service-item .wc-wizard-service-toggle::before,
.wc-wizard-services-list-toggle .wc-wizard-service-toggle::before {
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  background: #fff;
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 100%;
}
.wc-wizard-service-item .wc-wizard-service-toggle.disabled,
.wc-wizard-services-list-toggle .wc-wizard-service-toggle.disabled {
  border-color: #999;
  background-color: #999;
}
.wc-wizard-service-item .wc-wizard-service-toggle.disabled::before,
.wc-wizard-services-list-toggle .wc-wizard-service-toggle.disabled::before {
  right: auto;
  left: 0;
}
.wc-wizard-service-item .wc-wizard-service-settings,
.wc-wizard-services-list-toggle .wc-wizard-service-settings {
  display: none;
  margin-top: 0.75em;
  margin-bottom: 0;
  cursor: default;
}
.wc-wizard-service-item .wc-wizard-service-settings.hide,
.wc-wizard-services-list-toggle .wc-wizard-service-settings.hide {
  display: none;
}
.wc-wizard-service-item.checked .wc-wizard-service-settings,
.wc-wizard-services-list-toggle.checked .wc-wizard-service-settings {
  display: inline-block;
}
.wc-wizard-service-item.checked .wc-wizard-service-settings.hide,
.wc-wizard-services-list-toggle.checked .wc-wizard-service-settings.hide {
  display: none;
}
.wc-wizard-service-item.closed,
.wc-wizard-services-list-toggle.closed {
  border-bottom: 0;
}

.wc-wizard-services-list-toggle {
  cursor: pointer;
}
.wc-wizard-services-list-toggle .wc-wizard-service-enable::before {
  content: "\f343";
  font-family: dashicons; /* stylelint-disable-line font-family-no-missing-generic-family-keyword */
  color: #666;
  font-size: 25px;
  margin-top: -7px;
  margin-left: -5px;
  position: absolute;
  visibility: visible;
}
.wc-wizard-services-list-toggle.closed .wc-wizard-service-enable::before {
  content: "\f347";
}
.wc-wizard-services-list-toggle .wc-wizard-service-enable input {
  visibility: hidden;
  position: relative;
}

.wc-wizard-services.manual .wc-wizard-service-item {
  display: none;
}

.wc-wizard-services.shipping {
  margin: 0;
}
.wc-wizard-services.shipping .wc-wizard-service-name {
  font-weight: 400;
  text-align: left;
  align-items: center;
  max-height: 5em;
  padding: 0;
}
.wc-wizard-services.shipping .wc-wizard-service-item {
  padding-left: 2em;
  padding-top: 0.67em;
}
.wc-wizard-services.shipping .wc-wizard-service-item:first-child {
  border-bottom: 0;
  padding-bottom: 0;
  font-weight: 700;
}
.wc-wizard-services.shipping .wc-wizard-service-item:first-child .wc-wizard-service-name {
  font-weight: 700;
}
.wc-wizard-services.shipping .wc-wizard-shipping-method-select,
.wc-wizard-services.shipping .shipping-method-setting {
  display: flex;
}
.wc-wizard-services.shipping .wc-wizard-shipping-method-select.hide,
.wc-wizard-services.shipping .shipping-method-setting.hide {
  display: none;
}
.wc-wizard-services.shipping .wc-wizard-shipping-method-dropdown,
.wc-wizard-services.shipping .shipping-method-setting input {
  margin-right: 2em;
  margin-bottom: 1em;
}
.wc-wizard-services.shipping .wc-wizard-shipping-method-dropdown .select2,
.wc-wizard-services.shipping .shipping-method-setting input .select2 {
  min-width: 130px;
}
.wc-wizard-services.shipping .wc-wizard-service-description {
  display: flex;
  flex-direction: column;
  color: #a6a6a6;
}
.wc-wizard-services.shipping .wc-wizard-service-item:not(:first-child) .wc-wizard-service-description {
  font-size: 0.92em;
  padding-bottom: 10px;
}
.wc-wizard-services.shipping .shipping-method-setting input {
  width: 95px;
  border: 1px solid #aaa;
  border-color: #ddd;
  border-radius: 4px;
  height: 28px;
  padding-left: 8px;
  padding-right: 24px;
  font-size: 14px;
  color: #444;
  background-color: #fff;
  display: inline-block;
}
.wc-wizard-services.shipping .shipping-method-description,
.wc-wizard-services.shipping .shipping-method-setting .description {
  color: #7e7e7e;
  font-size: 0.9em;
}
.wc-wizard-services.shipping .shipping-method-setting input::-moz-placeholder {
  color: #e1e1e1;
}
.wc-wizard-services.shipping .shipping-method-setting input::placeholder {
  color: #e1e1e1;
}

.wc-setup-shipping-units p {
  line-height: 1.5;
  font-size: 13px;
  margin-bottom: 0.25em;
  text-align: center;
}
.wc-setup-shipping-units .wc-setup-shipping-unit {
  margin-bottom: 1.75em;
}
.wc-setup-shipping-units .wc-setup-shipping-unit .select2 {
  min-width: 125px;
  top: -5px;
}

.hide {
  display: none;
}

.wc-wizard-features {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
}
.wc-wizard-features .wc-wizard-feature-item {
  flex-basis: calc(50% - 4em - 3px);
  border: 1px solid #eee;
  padding: 2em;
}
.wc-wizard-features .wc-wizard-feature-item:nth-child(1) {
  border-radius: 4px 0 0 0;
}
.wc-wizard-features .wc-wizard-feature-item:nth-child(2) {
  border-left: 0;
  border-radius: 0 4px 0 0;
}
.wc-wizard-features .wc-wizard-feature-item:nth-child(3) {
  border-top: 0;
  border-radius: 0 0 0 4px;
}
.wc-wizard-features .wc-wizard-feature-item:nth-child(4) {
  border-top: 0;
  border-left: 0;
  border-radius: 0 0 4px 0;
}
.wc-wizard-features p.wc-wizard-feature-name,
.wc-wizard-features p.wc-wizard-feature-description {
  margin: 0;
  line-height: 1.5;
}

h3.jetpack-reasons {
  text-align: center;
  margin: 3em 0 1em 0;
  font-size: 14px;
}

.jetpack-logo,
.wcs-notice {
  display: block;
  margin: 1.75em auto 2em auto;
  max-height: 175px;
}

.activate-splash .jetpack-logo {
  width: 170px;
  margin-bottom: 0;
}
.activate-splash .wcs-notice {
  margin-top: 1em;
  padding-left: 57px;
}

.wc-setup-step__new_onboarding .wc-logo,
.wc-setup-step__new_onboarding .wc-setup-steps {
  display: none;
}
.wc-setup-step__new_onboarding .wc-setup-step__new_onboarding-wrapper .wc-logo {
  display: block;
}
.wc-setup-step__new_onboarding .wc-setup-step__new_onboarding-wrapper p {
  text-align: center;
}
.wc-setup-step__new_onboarding .wc-setup-step__new_onboarding-wrapper .wc-setup-step__new_onboarding-welcome,
.wc-setup-step__new_onboarding .wc-setup-step__new_onboarding-wrapper .wc-setup-step__new_onboarding-plugin-info {
  color: #7c7c7c;
  font-size: 12px;
}

.step {
  text-align: center;
}

.wc-setup .wc-setup-actions .button {
  font-weight: 300;
  font-size: 16px;
  padding: 1em 2em;
  box-shadow: none;
  min-width: 12em;
  margin-top: 10px;
  line-height: 1;
  margin-right: 0.5em;
  margin-bottom: 2px;
  height: auto;
  border-radius: 4px;
}
.wc-setup .wc-setup-actions .button:focus, .wc-setup .wc-setup-actions .button:hover, .wc-setup .wc-setup-actions .button:active {
  box-shadow: none;
}

.wc-setup .wc-setup-actions .plugin-install-info {
  display: block;
  font-style: italic;
  color: #999;
  font-size: 14px;
  line-height: 1.5;
  margin: 5px 0;
}
.wc-setup .wc-setup-actions .plugin-install-info > * {
  display: block;
}
.wc-setup .wc-setup-actions .plugin-install-info .plugin-install-info-list-item::after {
  content: ", ";
}
.wc-setup .wc-setup-actions .plugin-install-info .plugin-install-info-list-item:last-of-type::after {
  content: ". ";
}
.wc-setup .wc-setup-actions .plugin-install-info a {
  white-space: nowrap;
}
.wc-setup .wc-setup-actions .plugin-install-info a:not(:hover):not(:focus) {
  color: inherit;
}

.plugin-install-source {
  background: rgba(114, 14, 236, 0.15);
}
.plugin-install-source:not(.wc-wizard-service-item) {
  box-shadow: 0 0 0 10px rgba(114, 14, 236, 0.15);
}

.location-prompt {
  color: #666;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 0.5em;
  margin-top: 0.85em;
  display: inline-block;
}

.location-input {
  border: 1px solid #aaa;
  border-color: #ddd;
  border-radius: 4px;
  height: 30px;
  width: calc(100% - 8px - 8px - 2px);
  padding-left: 8px;
  padding-right: 8px;
  font-size: 16px;
  color: #444;
  background-color: #fff;
  display: block;
}
.location-input.dropdown {
  width: 100%;
}

.branch-5-2 .location-input,
.wc-wp-version-gte-53 .location-input {
  margin: 0;
  width: 100%;
}

.address-step .select2 {
  min-width: 100%;
}

.store-address-container .city-and-postcode {
  display: flex;
}
.store-address-container .city-and-postcode div {
  flex-basis: 50%;
  margin-right: 1em;
}
.store-address-container .city-and-postcode div:last-of-type {
  margin-right: 0;
}
.store-address-container input[type=text],
.store-address-container select,
.store-address-container .select2-container {
  margin-bottom: 10px;
}

.product-type-container,
.sell-in-person-container {
  margin-top: 14px;
  margin-bottom: 1px;
}

#woocommerce_sell_in_person {
  margin-left: 0;
  margin-top: 0.425em;
}

.wc-wizard-service-settings .payment-email-input {
  border: 1px solid #aaa;
  border-color: #ddd;
  border-radius: 4px;
  height: 30px;
  padding: 0 8px;
  font-size: 14px;
  color: #444;
  background-color: #fff;
  display: inline-block;
}
.wc-wizard-service-settings .payment-email-input[disabled] {
  color: #aaa;
}

.newsletter-form-container {
  display: flex;
}
.newsletter-form-container .newsletter-form-email {
  border: 1px solid #aaa;
  border-color: #ddd;
  border-radius: 4px;
  height: 42px;
  padding: 0 8px;
  font-size: 16px;
  color: #666;
  background-color: #fff;
  display: inline-block;
  margin-right: 6px;
  flex-grow: 1;
}
.newsletter-form-container .newsletter-form-button-container {
  flex-grow: 0;
}

.wc-setup .wc-setup-actions .button.newsletter-form-button {
  height: 42px;
  padding: 0 1em;
  margin: 0;
}

.wc-wizard-next-steps {
  border: 1px solid #eee;
  border-radius: 4px;
  list-style: none;
  padding: 0;
}
.wc-wizard-next-steps li {
  padding: 0;
}
.wc-wizard-next-steps .wc-wizard-next-step-item {
  display: flex;
  border-top: 1px solid #eee;
}
.wc-wizard-next-steps .wc-wizard-next-step-item:first-child {
  border-top: 0;
}
.wc-wizard-next-steps .wc-wizard-next-step-description {
  flex-grow: 1;
  margin: 1.5em;
}
.wc-wizard-next-steps .wc-wizard-next-step-action {
  flex-grow: 0;
  display: flex;
  align-items: center;
}
.wc-wizard-next-steps .wc-wizard-next-step-action .button {
  margin: 1em 1.5em;
}
.wc-wizard-next-steps p.next-step-heading {
  margin: 0;
  font-size: 0.95em;
  font-weight: 400;
  font-variant: all-petite-caps;
}
.wc-wizard-next-steps p.next-step-extra-info {
  margin: 0;
}
.wc-wizard-next-steps h3.next-step-description {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}
.wc-wizard-next-steps .wc-wizard-additional-steps {
  border-top: 1px solid #eee;
}
.wc-wizard-next-steps .wc-wizard-additional-steps .wc-wizard-next-step-description {
  margin-bottom: 0;
}
.wc-wizard-next-steps .wc-wizard-additional-steps .wc-setup-actions {
  margin: 0 0 1.5em 0;
}
.wc-wizard-next-steps .wc-wizard-additional-steps .wc-setup-actions .button {
  font-size: 15px;
  margin: 1em 0 1em 1.5em;
}

p.next-steps-help-text {
  color: #9f9f9f;
  padding: 0 2em;
  text-align: center;
  font-size: 0.9em;
}

p.jetpack-terms {
  font-size: 0.8em;
  text-align: center;
  max-width: 480px;
  margin: 0 auto;
  line-height: 1.5;
}

.woocommerce-error {
  background: #ffe6e5;
  border-color: #ffc5c2;
  padding: 1em;
  margin-bottom: 1em;
}
.woocommerce-error p {
  margin-top: 0;
  margin-bottom: 0.5em;
  color: #444;
}
.woocommerce-error a {
  color: #ff645c;
}
.woocommerce-error .reconnect-reminder {
  font-size: 0.85em;
}
.woocommerce-error .wc-setup-actions .button {
  font-size: 14px;
}

.wc-wizard-service-setting-stripe_create_account,
.wc-wizard-service-setting-ppec_paypal_reroute_requests {
  display: flex;
  align-items: flex-start;
}
.wc-wizard-service-setting-stripe_create_account .payment-checkbox-input,
.wc-wizard-service-setting-ppec_paypal_reroute_requests .payment-checkbox-input {
  order: 1;
  margin-top: 5px;
  margin-left: 0;
  margin-right: 0;
}
.wc-wizard-service-setting-stripe_create_account .stripe_create_account,
.wc-wizard-service-setting-stripe_create_account .ppec_paypal_reroute_requests,
.wc-wizard-service-setting-ppec_paypal_reroute_requests .stripe_create_account,
.wc-wizard-service-setting-ppec_paypal_reroute_requests .ppec_paypal_reroute_requests {
  order: 2;
  margin-left: 0.3em;
}

.branch-5-2 .wc-wizard-service-setting-stripe_create_account .payment-checkbox-input,
.branch-5-2 .wc-wizard-service-setting-ppec_paypal_reroute_requests .payment-checkbox-input,
.wc-wp-version-gte-53 .wc-wizard-service-setting-stripe_create_account .payment-checkbox-input,
.wc-wp-version-gte-53 .wc-wizard-service-setting-ppec_paypal_reroute_requests .payment-checkbox-input {
  margin-top: 3px;
}

.wc-wizard-service-setting-stripe_email,
.wc-wizard-service-setting-ppec_paypal_email {
  margin-top: 0.75em;
  margin-left: 1.5em;
}
.wc-wizard-service-setting-stripe_email label.stripe_email,
.wc-wizard-service-setting-stripe_email label.ppec_paypal_email,
.wc-wizard-service-setting-ppec_paypal_email label.stripe_email,
.wc-wizard-service-setting-ppec_paypal_email label.ppec_paypal_email {
  position: absolute;
  margin: -1px;
  padding: 0;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  border: 0;
}
.wc-wizard-service-setting-stripe_email input.payment-email-input,
.wc-wizard-service-setting-ppec_paypal_email input.payment-email-input {
  box-sizing: border-box;
  margin-bottom: 0.5em;
  width: 100%;
  height: 32px;
}

.wc-setup-content .recommended-step {
  border: 1px solid #ebebeb;
  border-radius: 4px;
  padding: 2.5em;
}

.wc-setup-content .recommended-item {
  list-style: none;
  margin-bottom: 1.5em;
}
.wc-setup-content .recommended-item:last-child {
  margin-bottom: 0;
}
.wc-setup-content .recommended-item label {
  display: flex;
  align-items: center;
}
.wc-setup-content .recommended-item label::before, .wc-setup-content .recommended-item label::after {
  top: auto;
}
.wc-setup-content .recommended-item label::after {
  margin-top: -1.5px;
}
.wc-setup-content .recommended-item .recommended-item-icon {
  border: 1px solid #fff;
  border-radius: 7px;
  height: 3.5em;
  margin-right: 1em;
  margin-left: 4px;
}
.wc-setup-content .recommended-item .recommended-item-icon.recommended-item-icon-wc_admin {
  background-color: #720eec;
  padding: 0.5em;
  height: 2em;
}
.wc-setup-content .recommended-item .recommended-item-icon.recommended-item-icon-storefront_theme {
  background-color: #f4a224;
  max-height: 3em;
  max-width: 3em;
  padding: 0.25em;
}
.wc-setup-content .recommended-item .recommended-item-icon.recommended-item-icon-automated_taxes {
  background-color: #d0011b;
  max-height: 1.75em;
  padding: 0.875em;
}
.wc-setup-content .recommended-item .recommended-item-icon.recommended-item-icon-mailchimp {
  background-color: #ffe01b;
  height: 2em;
  padding: 0.75em;
}
.wc-setup-content .recommended-item .recommended-item-icon.recommended-item-icon-woocommerce_services {
  background-color: #f0f0f0;
  max-height: 1.5em;
  padding: 1.3em 0.7em;
}
.wc-setup-content .recommended-item .recommended-item-icon.recommended-item-icon-shipstation {
  background-color: #f0f0f0;
  padding: 0.3em;
}
.wc-setup-content .recommended-item .recommended-item-description-container h3 {
  font-size: 15px;
  font-weight: 700;
  letter-spacing: 0.5px;
  margin-bottom: 0;
}
.wc-setup-content .recommended-item .recommended-item-description-container p {
  margin-top: 0;
  line-height: 1.5;
}

.wc-wizard-service-info {
  padding: 1em 2em;
  background-color: #fafafa;
}

.help_tip {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

@media only screen and (max-width: 400px) {
  .wc-logo img {
    max-width: 80%;
  }
  .wc-setup-steps {
    display: none;
  }
  .store-address-container .city-and-postcode {
    display: block;
  }
  .store-address-container .city-and-postcode div {
    margin-right: 0;
  }
  .wc-wizard-service-item,
  .wc-wizard-services-list-toggle {
    flex-wrap: wrap;
  }
  .wc-wizard-service-item .wc-wizard-service-enable,
  .wc-wizard-services-list-toggle .wc-wizard-service-enable {
    order: 2;
    padding: 20px 0 0;
  }
  .wc-wizard-service-item .wc-wizard-service-description,
  .wc-wizard-services-list-toggle .wc-wizard-service-description {
    order: 3;
  }
  .wc-wizard-service-item .wc-wizard-service-name,
  .wc-wizard-services-list-toggle .wc-wizard-service-name {
    padding: 20px 20px 0;
    text-align: left;
    justify-content: space-between !important;
  }
  .wc-wizard-service-item .wc-wizard-service-name img,
  .wc-wizard-services-list-toggle .wc-wizard-service-name img {
    margin: 0;
  }
  .newsletter-form-container {
    display: block;
  }
  .newsletter-form-container .newsletter-form-email {
    display: block;
    box-sizing: border-box;
    width: 100%;
    margin-bottom: 10px;
  }
  .newsletter-form-container .button.newsletter-form-button {
    float: left;
  }
  .wc-wizard-next-steps .wc-wizard-next-step-item {
    flex-wrap: wrap;
  }
  .wc-wizard-next-steps .wc-wizard-next-step-item .wc-wizard-next-step-description {
    margin-bottom: 0;
  }
  .wc-wizard-next-steps .wc-wizard-next-step-item .wc-wizard-next-step-action p {
    margin: 0;
  }
}
/* stylelint-enable *//*# sourceMappingURL=wc-setup.css.map */