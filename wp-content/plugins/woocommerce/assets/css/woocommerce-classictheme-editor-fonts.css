/**
 * woocommerce-classictheme-editor-fonts.scss
 * Governs the fonts loaded in the editor when classic themes are used.
 */
/**
 * Imports
 */
/**
 * _fonts.scss
 * Custom WooCommerce fonts.
 */
@font-face {
  font-family: "star";
  src: url("../fonts/WooCommerce.woff2") format("woff2"), url("../fonts/WooCommerce.woff") format("woff"), url("../fonts/WooCommerce.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "WooCommerce";
  src: url("../fonts/WooCommerce.woff2") format("woff2"), url("../fonts/WooCommerce.woff") format("woff"), url("../fonts/WooCommerce.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/**
 * This class is necessary to ensure that this CSS is loaded in the Gutenberg iframe.
 * For more information, see https://github.com/WordPress/gutenberg/blob/7129676335fe56802978a9511d860865f7e65aec/packages/block-editor/src/components/iframe/get-compatibility-styles.js#L3-L10
 */
/* ! important: this rule must be preserved during build */
.wp-block {
  content: ""; /* Minimal declaration to prevent removal */
}/*# sourceMappingURL=woocommerce-classictheme-editor-fonts.css.map */