/**
 * woocommerce-layout.scss
 * Applies layout to the default WooCommerce frontend design
 */
/**
 * Imports
 */
/**
 * Deprecated
 * Fallback for bourbon equivalent
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include transform(scale(1.5));`
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include box-sizing(border-box);`
 */
/**
 * Objects
 */
/**
 * WooCommerce CSS Variables
 */
:root {
  --woocommerce: #720eec;
  --wc-green: #7ad03a;
  --wc-red: #a00;
  --wc-orange: #ffba00;
  --wc-blue: #2ea2cc;
  --wc-primary: #720eec;
  --wc-primary-text: rgb(252.3245945946, 250.73, 254.27);
  --wc-secondary: #e9e6ed;
  --wc-secondary-text: #515151;
  --wc-highlight: rgb(149.3408, 142.38, 8.8592);
  --wc-highligh-text: white;
  --wc-content-bg: #fff;
  --wc-subtext: #767676;
  --wc-form-border-color: rgba(32, 7, 7, 0.8);
  --wc-form-border-radius: 4px;
  --wc-form-border-width: 1px;
}

/**
 * Styling begins
 */
.woocommerce,
.woocommerce-page {
  /**
   * General layout styles
   */
  /**
   * Product page
   */
  /**
   * Product loops
   */
  /**
   * Cart page
   */
  /**
   * Cart sidebar
   */
  /**
   * Forms
   */
  /**
   * oEmbeds
   */
}
.woocommerce .woocommerce-message .button,
.woocommerce .woocommerce-error .button,
.woocommerce .woocommerce-info .button,
.woocommerce-page .woocommerce-message .button,
.woocommerce-page .woocommerce-error .button,
.woocommerce-page .woocommerce-info .button {
  float: right;
}
.woocommerce .col2-set,
.woocommerce-page .col2-set {
  *zoom: 1;
  width: 100%;
}
.woocommerce .col2-set::before, .woocommerce .col2-set::after,
.woocommerce-page .col2-set::before,
.woocommerce-page .col2-set::after {
  content: " ";
  display: table;
}
.woocommerce .col2-set::after,
.woocommerce-page .col2-set::after {
  clear: both;
}
.woocommerce .col2-set .col-1,
.woocommerce-page .col2-set .col-1 {
  float: left;
  width: 48%;
}
.woocommerce .col2-set .col-2,
.woocommerce-page .col2-set .col-2 {
  float: right;
  width: 48%;
}
.woocommerce img,
.woocommerce-page img {
  height: auto;
  max-width: 100%;
}
.woocommerce div.product div.images,
.woocommerce #content div.product div.images,
.woocommerce-page div.product div.images,
.woocommerce-page #content div.product div.images {
  float: left;
  width: 48%;
}
.woocommerce div.product div.thumbnails,
.woocommerce #content div.product div.thumbnails,
.woocommerce-page div.product div.thumbnails,
.woocommerce-page #content div.product div.thumbnails {
  *zoom: 1;
}
.woocommerce div.product div.thumbnails::before, .woocommerce div.product div.thumbnails::after,
.woocommerce #content div.product div.thumbnails::before,
.woocommerce #content div.product div.thumbnails::after,
.woocommerce-page div.product div.thumbnails::before,
.woocommerce-page div.product div.thumbnails::after,
.woocommerce-page #content div.product div.thumbnails::before,
.woocommerce-page #content div.product div.thumbnails::after {
  content: " ";
  display: table;
}
.woocommerce div.product div.thumbnails::after,
.woocommerce #content div.product div.thumbnails::after,
.woocommerce-page div.product div.thumbnails::after,
.woocommerce-page #content div.product div.thumbnails::after {
  clear: both;
}
.woocommerce div.product div.thumbnails a,
.woocommerce #content div.product div.thumbnails a,
.woocommerce-page div.product div.thumbnails a,
.woocommerce-page #content div.product div.thumbnails a {
  float: left;
  width: 30.75%;
  margin-right: 3.8%;
  margin-bottom: 1em;
}
.woocommerce div.product div.thumbnails a.last,
.woocommerce #content div.product div.thumbnails a.last,
.woocommerce-page div.product div.thumbnails a.last,
.woocommerce-page #content div.product div.thumbnails a.last {
  margin-right: 0;
}
.woocommerce div.product div.thumbnails a.first,
.woocommerce #content div.product div.thumbnails a.first,
.woocommerce-page div.product div.thumbnails a.first,
.woocommerce-page #content div.product div.thumbnails a.first {
  clear: both;
}
.woocommerce div.product div.thumbnails.columns-1 a,
.woocommerce #content div.product div.thumbnails.columns-1 a,
.woocommerce-page div.product div.thumbnails.columns-1 a,
.woocommerce-page #content div.product div.thumbnails.columns-1 a {
  width: 100%;
  margin-right: 0;
  float: none;
}
.woocommerce div.product div.thumbnails.columns-2 a,
.woocommerce #content div.product div.thumbnails.columns-2 a,
.woocommerce-page div.product div.thumbnails.columns-2 a,
.woocommerce-page #content div.product div.thumbnails.columns-2 a {
  width: 48%;
}
.woocommerce div.product div.thumbnails.columns-4 a,
.woocommerce #content div.product div.thumbnails.columns-4 a,
.woocommerce-page div.product div.thumbnails.columns-4 a,
.woocommerce-page #content div.product div.thumbnails.columns-4 a {
  width: 22.05%;
}
.woocommerce div.product div.thumbnails.columns-5 a,
.woocommerce #content div.product div.thumbnails.columns-5 a,
.woocommerce-page div.product div.thumbnails.columns-5 a,
.woocommerce-page #content div.product div.thumbnails.columns-5 a {
  width: 16.9%;
}
.woocommerce div.product div.summary,
.woocommerce #content div.product div.summary,
.woocommerce-page div.product div.summary,
.woocommerce-page #content div.product div.summary {
  float: right;
  width: 48%;
  clear: none;
}
.woocommerce div.product .woocommerce-tabs,
.woocommerce #content div.product .woocommerce-tabs,
.woocommerce-page div.product .woocommerce-tabs,
.woocommerce-page #content div.product .woocommerce-tabs {
  clear: both;
}
.woocommerce div.product .woocommerce-tabs ul.tabs,
.woocommerce #content div.product .woocommerce-tabs ul.tabs,
.woocommerce-page div.product .woocommerce-tabs ul.tabs,
.woocommerce-page #content div.product .woocommerce-tabs ul.tabs {
  *zoom: 1;
}
.woocommerce div.product .woocommerce-tabs ul.tabs::before, .woocommerce div.product .woocommerce-tabs ul.tabs::after,
.woocommerce #content div.product .woocommerce-tabs ul.tabs::before,
.woocommerce #content div.product .woocommerce-tabs ul.tabs::after,
.woocommerce-page div.product .woocommerce-tabs ul.tabs::before,
.woocommerce-page div.product .woocommerce-tabs ul.tabs::after,
.woocommerce-page #content div.product .woocommerce-tabs ul.tabs::before,
.woocommerce-page #content div.product .woocommerce-tabs ul.tabs::after {
  content: " ";
  display: table;
}
.woocommerce div.product .woocommerce-tabs ul.tabs::after,
.woocommerce #content div.product .woocommerce-tabs ul.tabs::after,
.woocommerce-page div.product .woocommerce-tabs ul.tabs::after,
.woocommerce-page #content div.product .woocommerce-tabs ul.tabs::after {
  clear: both;
}
.woocommerce div.product .woocommerce-tabs ul.tabs li,
.woocommerce #content div.product .woocommerce-tabs ul.tabs li,
.woocommerce-page div.product .woocommerce-tabs ul.tabs li,
.woocommerce-page #content div.product .woocommerce-tabs ul.tabs li {
  display: inline-block;
}
.woocommerce div.product #reviews .comment,
.woocommerce #content div.product #reviews .comment,
.woocommerce-page div.product #reviews .comment,
.woocommerce-page #content div.product #reviews .comment {
  *zoom: 1;
}
.woocommerce div.product #reviews .comment::before, .woocommerce div.product #reviews .comment::after,
.woocommerce #content div.product #reviews .comment::before,
.woocommerce #content div.product #reviews .comment::after,
.woocommerce-page div.product #reviews .comment::before,
.woocommerce-page div.product #reviews .comment::after,
.woocommerce-page #content div.product #reviews .comment::before,
.woocommerce-page #content div.product #reviews .comment::after {
  content: " ";
  display: table;
}
.woocommerce div.product #reviews .comment::after,
.woocommerce #content div.product #reviews .comment::after,
.woocommerce-page div.product #reviews .comment::after,
.woocommerce-page #content div.product #reviews .comment::after {
  clear: both;
}
.woocommerce div.product #reviews .comment img,
.woocommerce #content div.product #reviews .comment img,
.woocommerce-page div.product #reviews .comment img,
.woocommerce-page #content div.product #reviews .comment img {
  float: right;
  height: auto;
}
.woocommerce ul.products,
.woocommerce-page ul.products {
  clear: both;
  *zoom: 1;
}
.woocommerce ul.products::before, .woocommerce ul.products::after,
.woocommerce-page ul.products::before,
.woocommerce-page ul.products::after {
  content: " ";
  display: table;
}
.woocommerce ul.products::after,
.woocommerce-page ul.products::after {
  clear: both;
}
.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
  float: left;
  margin: 0 3.8% 2.992em 0;
  padding: 0;
  position: relative;
  width: 22.05%;
  margin-left: 0;
}
.woocommerce ul.products li.first,
.woocommerce-page ul.products li.first {
  clear: both;
}
.woocommerce ul.products li.last,
.woocommerce-page ul.products li.last {
  margin-right: 0;
}
.woocommerce ul.products.columns-1 li.product,
.woocommerce-page ul.products.columns-1 li.product {
  width: 100%;
  margin-right: 0;
}
.woocommerce ul.products.columns-2 li.product,
.woocommerce-page ul.products.columns-2 li.product {
  width: 48%;
}
.woocommerce ul.products.columns-3 li.product,
.woocommerce-page ul.products.columns-3 li.product {
  width: 30.75%;
}
.woocommerce ul.products.columns-5 li.product,
.woocommerce-page ul.products.columns-5 li.product {
  width: 16.95%;
}
.woocommerce ul.products.columns-6 li.product,
.woocommerce-page ul.products.columns-6 li.product {
  width: 13.5%;
}
.woocommerce.columns-1 ul.products li.product,
.woocommerce-page.columns-1 ul.products li.product {
  width: 100%;
  margin-right: 0;
}
.woocommerce.columns-2 ul.products li.product,
.woocommerce-page.columns-2 ul.products li.product {
  width: 48%;
}
.woocommerce.columns-3 ul.products li.product,
.woocommerce-page.columns-3 ul.products li.product {
  width: 30.75%;
}
.woocommerce.columns-5 ul.products li.product,
.woocommerce-page.columns-5 ul.products li.product {
  width: 16.95%;
}
.woocommerce.columns-6 ul.products li.product,
.woocommerce-page.columns-6 ul.products li.product {
  width: 13.5%;
}
.woocommerce .woocommerce-result-count,
.woocommerce-page .woocommerce-result-count {
  float: left;
}
.woocommerce .woocommerce-ordering,
.woocommerce-page .woocommerce-ordering {
  float: right;
}
.woocommerce .woocommerce-pagination ul.page-numbers,
.woocommerce-page .woocommerce-pagination ul.page-numbers {
  *zoom: 1;
}
.woocommerce .woocommerce-pagination ul.page-numbers::before, .woocommerce .woocommerce-pagination ul.page-numbers::after,
.woocommerce-page .woocommerce-pagination ul.page-numbers::before,
.woocommerce-page .woocommerce-pagination ul.page-numbers::after {
  content: " ";
  display: table;
}
.woocommerce .woocommerce-pagination ul.page-numbers::after,
.woocommerce-page .woocommerce-pagination ul.page-numbers::after {
  clear: both;
}
.woocommerce .woocommerce-pagination ul.page-numbers li,
.woocommerce-page .woocommerce-pagination ul.page-numbers li {
  display: inline-block;
}
.woocommerce table.cart img,
.woocommerce #content table.cart img,
.woocommerce-page table.cart img,
.woocommerce-page #content table.cart img {
  height: auto;
}
.woocommerce table.cart td.actions,
.woocommerce #content table.cart td.actions,
.woocommerce-page table.cart td.actions,
.woocommerce-page #content table.cart td.actions {
  text-align: right;
}
.woocommerce table.cart td.actions .input-text,
.woocommerce #content table.cart td.actions .input-text,
.woocommerce-page table.cart td.actions .input-text,
.woocommerce-page #content table.cart td.actions .input-text {
  width: 80px;
}
.woocommerce table.cart td.actions .coupon,
.woocommerce #content table.cart td.actions .coupon,
.woocommerce-page table.cart td.actions .coupon,
.woocommerce-page #content table.cart td.actions .coupon {
  float: left;
}
.woocommerce .cart-collaterals,
.woocommerce-page .cart-collaterals {
  *zoom: 1;
  width: 100%;
}
.woocommerce .cart-collaterals::before, .woocommerce .cart-collaterals::after,
.woocommerce-page .cart-collaterals::before,
.woocommerce-page .cart-collaterals::after {
  content: " ";
  display: table;
}
.woocommerce .cart-collaterals::after,
.woocommerce-page .cart-collaterals::after {
  clear: both;
}
.woocommerce .cart-collaterals .related,
.woocommerce-page .cart-collaterals .related {
  width: 30.75%;
  float: left;
}
.woocommerce .cart-collaterals .cross-sells,
.woocommerce-page .cart-collaterals .cross-sells {
  width: 48%;
  float: left;
}
.woocommerce .cart-collaterals .cross-sells ul.products,
.woocommerce-page .cart-collaterals .cross-sells ul.products {
  float: none;
}
.woocommerce .cart-collaterals .cross-sells ul.products li,
.woocommerce-page .cart-collaterals .cross-sells ul.products li {
  width: 48%;
}
.woocommerce .cart-collaterals .shipping_calculator,
.woocommerce-page .cart-collaterals .shipping_calculator {
  width: 48%;
  *zoom: 1;
  clear: right;
  float: right;
}
.woocommerce .cart-collaterals .shipping_calculator::before, .woocommerce .cart-collaterals .shipping_calculator::after,
.woocommerce-page .cart-collaterals .shipping_calculator::before,
.woocommerce-page .cart-collaterals .shipping_calculator::after {
  content: " ";
  display: table;
}
.woocommerce .cart-collaterals .shipping_calculator::after,
.woocommerce-page .cart-collaterals .shipping_calculator::after {
  clear: both;
}
.woocommerce .cart-collaterals .shipping_calculator .col2-set .col-1,
.woocommerce .cart-collaterals .shipping_calculator .col2-set .col-2,
.woocommerce-page .cart-collaterals .shipping_calculator .col2-set .col-1,
.woocommerce-page .cart-collaterals .shipping_calculator .col2-set .col-2 {
  width: 47%;
}
.woocommerce .cart-collaterals .cart_totals,
.woocommerce-page .cart-collaterals .cart_totals {
  float: right;
  width: 48%;
}
.woocommerce ul.cart_list li,
.woocommerce ul.product_list_widget li,
.woocommerce-page ul.cart_list li,
.woocommerce-page ul.product_list_widget li {
  *zoom: 1;
}
.woocommerce ul.cart_list li::before, .woocommerce ul.cart_list li::after,
.woocommerce ul.product_list_widget li::before,
.woocommerce ul.product_list_widget li::after,
.woocommerce-page ul.cart_list li::before,
.woocommerce-page ul.cart_list li::after,
.woocommerce-page ul.product_list_widget li::before,
.woocommerce-page ul.product_list_widget li::after {
  content: " ";
  display: table;
}
.woocommerce ul.cart_list li::after,
.woocommerce ul.product_list_widget li::after,
.woocommerce-page ul.cart_list li::after,
.woocommerce-page ul.product_list_widget li::after {
  clear: both;
}
.woocommerce ul.cart_list li img,
.woocommerce ul.product_list_widget li img,
.woocommerce-page ul.cart_list li img,
.woocommerce-page ul.product_list_widget li img {
  float: right;
  height: auto;
}
.woocommerce form .form-row,
.woocommerce-page form .form-row {
  *zoom: 1;
}
.woocommerce form .form-row::before, .woocommerce form .form-row::after,
.woocommerce-page form .form-row::before,
.woocommerce-page form .form-row::after {
  content: " ";
  display: table;
}
.woocommerce form .form-row::after,
.woocommerce-page form .form-row::after {
  clear: both;
}
.woocommerce form .form-row label,
.woocommerce-page form .form-row label {
  display: block;
}
.woocommerce form .form-row label.checkbox,
.woocommerce-page form .form-row label.checkbox {
  display: inline;
}
.woocommerce form .form-row select,
.woocommerce-page form .form-row select {
  width: 100%;
}
.woocommerce form .form-row .input-text,
.woocommerce-page form .form-row .input-text {
  box-sizing: border-box;
  width: 100%;
}
.woocommerce form .form-row-first,
.woocommerce form .form-row-last,
.woocommerce-page form .form-row-first,
.woocommerce-page form .form-row-last {
  width: 47%;
  overflow: visible;
}
.woocommerce form .form-row-first,
.woocommerce-page form .form-row-first {
  float: left;
  /*rtl:raw:
  float: right;
  */
}
.woocommerce form .form-row-last,
.woocommerce-page form .form-row-last {
  float: right;
}
.woocommerce form .form-row-wide,
.woocommerce-page form .form-row-wide {
  clear: both;
}
.woocommerce form .password-input,
.woocommerce-page form .password-input {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  /* Hide the Edge "reveal password" native button */
}
.woocommerce form .password-input input[type=password],
.woocommerce-page form .password-input input[type=password] {
  padding-right: 2.5rem;
}
.woocommerce form .password-input input::-ms-reveal,
.woocommerce-page form .password-input input::-ms-reveal {
  display: none;
}
.woocommerce form .show-password-input,
.woocommerce-page form .show-password-input {
  background-color: transparent;
  border-radius: 0;
  border: 0;
  color: var(--wc-form-color-text, #000);
  cursor: pointer;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  position: absolute;
  right: 0.7em;
  text-decoration: none;
  top: 50%;
  transform: translateY(-50%);
  -moz-osx-font-smoothing: inherit;
  -webkit-appearance: none;
  -webkit-font-smoothing: inherit;
}
.woocommerce form .show-password-input::before,
.woocommerce-page form .show-password-input::before {
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.3 3.3C16.9 2.9 16.2 2.9 15.7 3.3L13.3 5.7C12.2437 5.3079 11.1267 5.1048 10 5.1C6.2 5.2 2.8 7.2 1 10.5C1.2 10.9 1.5 11.3 1.8 11.7C2.6 12.8 3.6 13.7 4.7 14.4L3 16.1C2.6 16.5 2.5 17.2 3 17.7C3.4 18.1 4.1 18.2 4.6 17.7L17.3 4.9C17.7 4.4 17.7 3.7 17.3 3.3ZM6.7 12.3L5.4 13.6C4.2 12.9 3.1 11.9 2.3 10.7C3.5 9 5.1 7.8 7 7.2C5.7 8.6 5.6 10.8 6.7 12.3ZM10.1 9C9.6 8.5 9.7 7.7 10.2 7.2C10.7 6.8 11.4 6.8 11.9 7.2L10.1 9ZM18.3 9.5C17.8 8.8 17.2 8.1 16.5 7.6L15.5 8.6C16.3 9.2 17 9.9 17.6 10.8C15.9 13.4 13 15 9.9 15H9.1L8.1 16C8.8 15.9 9.4 16 10 16C13.3 16 16.4 14.4 18.3 11.7C18.6 11.3 18.8 10.9 19.1 10.5C18.8 10.2 18.6 9.8 18.3 9.5ZM14 10L10 14C12.2 14 14 12.2 14 10Z" fill="%23111111"/></svg>');
  content: "";
  display: block;
  height: 22px;
  width: 22px;
}
.woocommerce form .show-password-input.display-password::before,
.woocommerce-page form .show-password-input.display-password::before {
  background-image: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.3 9.49999C15 4.89999 8.50002 3.79999 3.90002 7.19999C2.70002 8.09999 1.70002 9.29999 0.900024 10.6C1.10002 11 1.40002 11.4 1.70002 11.8C5.00002 16.4 11.3 17.4 15.9 14.2C16.8 13.5 17.6 12.8 18.3 11.8C18.6 11.4 18.8 11 19.1 10.6C18.8 10.2 18.6 9.79999 18.3 9.49999ZM10.1 7.19999C10.6 6.69999 11.4 6.69999 11.9 7.19999C12.4 7.69999 12.4 8.49999 11.9 8.99999C11.4 9.49999 10.6 9.49999 10.1 8.99999C9.60003 8.49999 9.60003 7.69999 10.1 7.19999ZM10 14.9C6.90002 14.9 4.00002 13.3 2.30002 10.7C3.50002 8.99999 5.10002 7.79999 7.00002 7.19999C6.30002 7.99999 6.00002 8.89999 6.00002 9.89999C6.00002 12.1 7.70002 14 10 14C12.2 14 14.1 12.3 14.1 9.99999V9.89999C14.1 8.89999 13.7 7.89999 13 7.19999C14.9 7.79999 16.5 8.99999 17.7 10.7C16 13.3 13.1 14.9 10 14.9Z" fill="%23111111"/></svg>');
}
.woocommerce #payment .form-row select,
.woocommerce-page #payment .form-row select {
  width: auto;
}
.woocommerce #payment .wc-terms-and-conditions,
.woocommerce #payment .terms,
.woocommerce-page #payment .wc-terms-and-conditions,
.woocommerce-page #payment .terms {
  text-align: left;
  padding: 0 1em 0 0;
  float: left;
}
.woocommerce #payment #place_order,
.woocommerce-page #payment #place_order {
  float: right;
}
.woocommerce .woocommerce-billing-fields,
.woocommerce .woocommerce-shipping-fields,
.woocommerce-page .woocommerce-billing-fields,
.woocommerce-page .woocommerce-shipping-fields {
  *zoom: 1;
}
.woocommerce .woocommerce-billing-fields::before, .woocommerce .woocommerce-billing-fields::after,
.woocommerce .woocommerce-shipping-fields::before,
.woocommerce .woocommerce-shipping-fields::after,
.woocommerce-page .woocommerce-billing-fields::before,
.woocommerce-page .woocommerce-billing-fields::after,
.woocommerce-page .woocommerce-shipping-fields::before,
.woocommerce-page .woocommerce-shipping-fields::after {
  content: " ";
  display: table;
}
.woocommerce .woocommerce-billing-fields::after,
.woocommerce .woocommerce-shipping-fields::after,
.woocommerce-page .woocommerce-billing-fields::after,
.woocommerce-page .woocommerce-shipping-fields::after {
  clear: both;
}
.woocommerce .woocommerce-terms-and-conditions,
.woocommerce-page .woocommerce-terms-and-conditions {
  margin-bottom: 1.618em;
  padding: 1.618em;
}
.woocommerce .woocommerce-oembed,
.woocommerce-page .woocommerce-oembed {
  position: relative;
}

.woocommerce-account .woocommerce-MyAccount-navigation {
  float: left;
  width: 30%;
}
.woocommerce-account .woocommerce-MyAccount-content {
  float: right;
  width: 68%;
}
.woocommerce-account .woocommerce-MyAccount-content mark {
  background-color: transparent;
  color: inherit;
  font-weight: 700;
}

/**
 * Twenty Eleven specific styles
 */
.woocommerce-page.left-sidebar #content.twentyeleven {
  width: 58.4%;
  margin: 0 7.6%;
  float: right;
}
.woocommerce-page.right-sidebar #content.twentyeleven {
  margin: 0 7.6%;
  width: 58.4%;
  float: left;
}

/**
 * Twenty Fourteen specific styles
 */
.twentyfourteen .tfwc {
  padding: 12px 10px 0;
  max-width: 474px;
  margin: 0 auto;
}
.twentyfourteen .tfwc .product .entry-summary {
  padding: 0 !important;
  margin: 0 0 1.618em !important;
}
.twentyfourteen .tfwc div.product.hentry.has-post-thumbnail {
  margin-top: 0;
}

@media screen and (min-width: 673px) {
  .twentyfourteen .tfwc {
    padding-right: 30px;
    padding-left: 30px;
  }
}
@media screen and (min-width: 1040px) {
  .twentyfourteen .tfwc {
    padding-right: 15px;
    padding-left: 15px;
  }
}
@media screen and (min-width: 1110px) {
  .twentyfourteen .tfwc {
    padding-right: 30px;
    padding-left: 30px;
  }
}
@media screen and (min-width: 1218px) {
  .twentyfourteen .tfwc {
    margin-right: 54px;
  }
  .full-width .twentyfourteen .tfwc {
    margin-right: auto;
  }
}
/**
 * Twenty Fifteen specific styles
 */
.twentyfifteen .t15wc {
  padding-left: 7.6923%;
  padding-right: 7.6923%;
  padding-top: 7.6923%;
  margin-bottom: 7.6923%;
  background: #fff;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.15);
}
.twentyfifteen .t15wc .page-title {
  margin-left: 0;
}

@media screen and (min-width: 38.75em) {
  .twentyfifteen .t15wc {
    margin-right: 7.6923%;
    margin-left: 7.6923%;
    margin-top: 8.3333%;
  }
}
@media screen and (min-width: 59.6875em) {
  .twentyfifteen .t15wc {
    margin-left: 8.3333%;
    margin-right: 8.3333%;
    padding: 10%;
  }
  .single-product .twentyfifteen .entry-summary {
    padding: 0 !important;
  }
}
/**
 * Twenty Sixteen specific styles
 */
.twentysixteen .site-main {
  margin-right: 7.6923%;
  margin-left: 7.6923%;
}
.twentysixteen .entry-summary {
  margin-right: 0;
  margin-left: 0;
}

#content .twentysixteen div.product div.images,
#content .twentysixteen div.product div.summary {
  width: 46.42857%;
}

@media screen and (min-width: 44.375em) {
  .twentysixteen .site-main {
    margin-right: 23.0769%;
  }
}
@media screen and (min-width: 56.875em) {
  .twentysixteen .site-main {
    margin-right: 0;
    margin-left: 0;
  }
  .no-sidebar .twentysixteen .site-main {
    margin-right: 15%;
    margin-left: 15%;
  }
  .no-sidebar .twentysixteen .entry-summary {
    margin-right: 0;
    margin-left: 0;
  }
}
/**
 * RTL styles.
 */
.rtl .woocommerce .col2-set .col-1,
.rtl .woocommerce-page .col2-set .col-1 {
  float: right;
}
.rtl .woocommerce .col2-set .col-2,
.rtl .woocommerce-page .col2-set .col-2 {
  float: left;
}/*# sourceMappingURL=woocommerce-layout.css.map */