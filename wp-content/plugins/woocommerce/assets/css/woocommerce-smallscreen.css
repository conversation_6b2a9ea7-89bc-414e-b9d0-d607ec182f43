/**
 * woocommerce-smallscreen.scss
 * Optimises the default WooCommerce frontend layout when viewed on smaller screens.
 */
/**
 * Imports
 */
/**
 * Deprecated
 * Fallback for bourbon equivalent
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Vendor prefix no longer required.
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include transform(scale(1.5));`
 */
/**
 * Deprecated
 * Use bourbon mixin instead `@include box-sizing(border-box);`
 */
/**
 * Objects
 */
/**
 * WooCommerce CSS Variables
 */
:root {
  --woocommerce: #720eec;
  --wc-green: #7ad03a;
  --wc-red: #a00;
  --wc-orange: #ffba00;
  --wc-blue: #2ea2cc;
  --wc-primary: #720eec;
  --wc-primary-text: rgb(252.3245945946, 250.73, 254.27);
  --wc-secondary: #e9e6ed;
  --wc-secondary-text: #515151;
  --wc-highlight: rgb(149.3408, 142.38, 8.8592);
  --wc-highligh-text: white;
  --wc-content-bg: #fff;
  --wc-subtext: #767676;
  --wc-form-border-color: rgba(32, 7, 7, 0.8);
  --wc-form-border-radius: 4px;
  --wc-form-border-width: 1px;
}

/**
 * Style begins
 */
.woocommerce,
.woocommerce-page {
  /**
   * General layout
   */
  /**
   * Products
   */
  /**
   * Product Details
   */
  /**
   * Cart
   */
  /**
   * Checkout
   */
  /**
   * Account
   */
}
.woocommerce table.shop_table_responsive thead,
.woocommerce-page table.shop_table_responsive thead {
  display: none;
}
.woocommerce table.shop_table_responsive tbody tr:first-child td:first-child,
.woocommerce-page table.shop_table_responsive tbody tr:first-child td:first-child {
  border-top: 0;
}
.woocommerce table.shop_table_responsive tbody th,
.woocommerce-page table.shop_table_responsive tbody th {
  display: none;
}
.woocommerce table.shop_table_responsive tr,
.woocommerce-page table.shop_table_responsive tr {
  display: block;
}
.woocommerce table.shop_table_responsive tr td,
.woocommerce-page table.shop_table_responsive tr td {
  display: block;
  text-align: right !important;
}
.woocommerce table.shop_table_responsive tr td.order-actions,
.woocommerce-page table.shop_table_responsive tr td.order-actions {
  text-align: left !important;
}
.woocommerce table.shop_table_responsive tr td::before,
.woocommerce-page table.shop_table_responsive tr td::before {
  content: attr(data-title) ": ";
  font-weight: 700;
  float: left;
}
.woocommerce table.shop_table_responsive tr td.product-remove::before, .woocommerce table.shop_table_responsive tr td.actions::before,
.woocommerce-page table.shop_table_responsive tr td.product-remove::before,
.woocommerce-page table.shop_table_responsive tr td.actions::before {
  display: none;
}
.woocommerce table.shop_table_responsive tr:nth-child(2n) td,
.woocommerce-page table.shop_table_responsive tr:nth-child(2n) td {
  background-color: rgba(0, 0, 0, 0.025);
}
.woocommerce table.my_account_orders tr td.order-actions,
.woocommerce-page table.my_account_orders tr td.order-actions {
  text-align: left;
}
.woocommerce table.my_account_orders tr td.order-actions::before,
.woocommerce-page table.my_account_orders tr td.order-actions::before {
  display: none;
}
.woocommerce table.my_account_orders tr td.order-actions .button,
.woocommerce-page table.my_account_orders tr td.order-actions .button {
  float: none;
  margin: 0.125em 0.25em 0.125em 0;
}
.woocommerce .col2-set .col-1,
.woocommerce .col2-set .col-2,
.woocommerce-page .col2-set .col-1,
.woocommerce-page .col2-set .col-2 {
  float: none;
  width: 100%;
}
.woocommerce ul.products[class*=columns-] li.product,
.woocommerce-page ul.products[class*=columns-] li.product {
  width: 48%;
  float: left;
  clear: both;
  margin: 0 0 2.992em;
}
.woocommerce ul.products[class*=columns-] li.product:nth-child(2n),
.woocommerce-page ul.products[class*=columns-] li.product:nth-child(2n) {
  float: right;
  clear: none !important;
}
.woocommerce div.product div.images,
.woocommerce div.product div.summary,
.woocommerce #content div.product div.images,
.woocommerce #content div.product div.summary,
.woocommerce-page div.product div.images,
.woocommerce-page div.product div.summary,
.woocommerce-page #content div.product div.images,
.woocommerce-page #content div.product div.summary {
  float: none;
  width: 100%;
}
.woocommerce table.cart .product-thumbnail,
.woocommerce #content table.cart .product-thumbnail,
.woocommerce-page table.cart .product-thumbnail,
.woocommerce-page #content table.cart .product-thumbnail {
  display: none;
}
.woocommerce table.cart td.actions,
.woocommerce #content table.cart td.actions,
.woocommerce-page table.cart td.actions,
.woocommerce-page #content table.cart td.actions {
  text-align: left;
}
.woocommerce table.cart td.actions .coupon,
.woocommerce #content table.cart td.actions .coupon,
.woocommerce-page table.cart td.actions .coupon,
.woocommerce-page #content table.cart td.actions .coupon {
  float: none;
  *zoom: 1;
  padding-bottom: 0.5em;
}
.woocommerce table.cart td.actions .coupon::before, .woocommerce table.cart td.actions .coupon::after,
.woocommerce #content table.cart td.actions .coupon::before,
.woocommerce #content table.cart td.actions .coupon::after,
.woocommerce-page table.cart td.actions .coupon::before,
.woocommerce-page table.cart td.actions .coupon::after,
.woocommerce-page #content table.cart td.actions .coupon::before,
.woocommerce-page #content table.cart td.actions .coupon::after {
  content: " ";
  display: table;
}
.woocommerce table.cart td.actions .coupon::after,
.woocommerce #content table.cart td.actions .coupon::after,
.woocommerce-page table.cart td.actions .coupon::after,
.woocommerce-page #content table.cart td.actions .coupon::after {
  clear: both;
}
.woocommerce table.cart td.actions .coupon input,
.woocommerce table.cart td.actions .coupon .button,
.woocommerce table.cart td.actions .coupon .input-text,
.woocommerce #content table.cart td.actions .coupon input,
.woocommerce #content table.cart td.actions .coupon .button,
.woocommerce #content table.cart td.actions .coupon .input-text,
.woocommerce-page table.cart td.actions .coupon input,
.woocommerce-page table.cart td.actions .coupon .button,
.woocommerce-page table.cart td.actions .coupon .input-text,
.woocommerce-page #content table.cart td.actions .coupon input,
.woocommerce-page #content table.cart td.actions .coupon .button,
.woocommerce-page #content table.cart td.actions .coupon .input-text {
  width: 48%;
  box-sizing: border-box;
}
.woocommerce table.cart td.actions .coupon .input-text + .button,
.woocommerce table.cart td.actions .coupon .button.alt,
.woocommerce #content table.cart td.actions .coupon .input-text + .button,
.woocommerce #content table.cart td.actions .coupon .button.alt,
.woocommerce-page table.cart td.actions .coupon .input-text + .button,
.woocommerce-page table.cart td.actions .coupon .button.alt,
.woocommerce-page #content table.cart td.actions .coupon .input-text + .button,
.woocommerce-page #content table.cart td.actions .coupon .button.alt {
  float: right;
}
.woocommerce table.cart td.actions .coupon .coupon-error-notice,
.woocommerce #content table.cart td.actions .coupon .coupon-error-notice,
.woocommerce-page table.cart td.actions .coupon .coupon-error-notice,
.woocommerce-page #content table.cart td.actions .coupon .coupon-error-notice {
  clear: left;
  color: var(--wc-red);
  float: left;
  font-size: 0.75em;
  margin-bottom: 0;
  text-align: left;
  width: 48%;
}
.woocommerce table.cart td.actions .button,
.woocommerce #content table.cart td.actions .button,
.woocommerce-page table.cart td.actions .button,
.woocommerce-page #content table.cart td.actions .button {
  display: block;
  width: 100%;
}
.woocommerce .cart-collaterals .cart_totals,
.woocommerce .cart-collaterals .shipping_calculator,
.woocommerce .cart-collaterals .cross-sells,
.woocommerce-page .cart-collaterals .cart_totals,
.woocommerce-page .cart-collaterals .shipping_calculator,
.woocommerce-page .cart-collaterals .cross-sells {
  width: 100%;
  float: none;
  text-align: left;
}
.woocommerce.woocommerce-checkout form.login .form-row,
.woocommerce-page.woocommerce-checkout form.login .form-row {
  width: 100%;
  float: none;
}
.woocommerce #payment .terms,
.woocommerce-page #payment .terms {
  text-align: left;
  padding: 0;
}
.woocommerce #payment #place_order,
.woocommerce-page #payment #place_order {
  float: none;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 1em;
}
.woocommerce .lost_reset_password .form-row-first,
.woocommerce .lost_reset_password .form-row-last,
.woocommerce-page .lost_reset_password .form-row-first,
.woocommerce-page .lost_reset_password .form-row-last {
  width: 100%;
  float: none;
  margin-right: 0;
}

.woocommerce-account .woocommerce-MyAccount-navigation,
.woocommerce-account .woocommerce-MyAccount-content {
  float: none;
  width: 100%;
}

/**
 * Twenty Thirteen specific styles
 */
.single-product .twentythirteen .panel {
  padding-left: 20px !important;
  padding-right: 20px !important;
}/*# sourceMappingURL=woocommerce-smallscreen.css.map */