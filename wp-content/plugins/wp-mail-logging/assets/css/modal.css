#wp-mail-logging-modal-wrap {
  display: none;
}
#wp-mail-logging-modal-backdrop {
  background: none repeat scroll 0 0 #3c434a;
  bottom: 0;
  left: 0;
  min-height: 360px;
  opacity: 0.75;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 159900;
}
#wp-mail-logging-modal-content {
  background: none repeat scroll 0 0 #fff;
  border-radius: 6px;
  font-size: 14px;
  margin: 40px auto 0;
  max-width: 688px;
  max-height: 761px;
}
#wp-mail-logging-modal-content-wrap {
  bottom: 30px;
  left: 30px;
  position: fixed;
  right: 30px;
  top: 30px;
  z-index: 300010;
}
#wp-mail-logging-modal-content-header {
  align-items: center;
  background-color: #F0F0F1;
  border-radius: 6px 6px 0px 0px;
  color: #1E2327;
  display: flex;
  font-size: 20px;
  font-weight: 600;
  justify-content: space-between;
  padding: 20px 30px;
}
#wp-mail-logging-modal-content-header-format-switch {
  border-bottom: 1px solid #DCDCDE;
  margin: 20px 0 30px 0;
}
#wp-mail-logging-modal-content-header-format-switch ul {
  margin: 0;
  padding-bottom: 13.5px;
}
#wp-mail-logging-modal-content-header-format-switch ul li {
  padding: 0 10px;
  display: inline;
  text-transform: uppercase;
}
#wp-mail-logging-modal-content-header-format-switch ul li:first-of-type {
  padding-left: 0;
}
#wp-mail-logging-modal-content-header-format-switch ul li:last-of-type {
  padding-right: 0;
}
#wp-mail-logging-modal-content-header-close {
  color: #C2C2C2;
  text-decoration: none;
}
#wp-mail-logging-modal-content-header-close:hover {
  color: #72777C;
}
#wp-mail-logging-modal-content-body {
  padding: 0 30px 30px;
  overflow: auto;
  max-height: 701px;
}
#wp-mail-logging-modal-content-body-content {
  position: relative;
}
#wp-mail-logging-modal-content-body .info {
  border-left: 4px solid #ffba00;
  display: block;
  background: #FFF9E9;
  -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
  margin: 5px 5px 2px;
  padding: 1px 12px;
}
#wp-mail-logging-modal-content-body .title {
  font-weight: bold;
  display: block;
}
#wp-mail-logging-modal-content .notice.wp-mail-logging-html-error-notice {
  background: #F0F6FC;
  border: none;
  border-radius: 3px;
  color: #646970;
  margin: 10px 0 0;
  padding: 10px;
}
#wp-mail-logging-modal-content .notice.wp-mail-logging-html-error-notice p {
  margin: 0;
}

#wp-mail-logging-modal-content-body-table {
  border: 1px solid #DCDCDE;
  border-radius: 6px;
}
#wp-mail-logging-modal-content-body-table .wp-mail-logging-modal-row .wp-mail-logging-modal-row-html-container--raw {
  overflow: auto;
}

.wp-mail-logging-modal-row {
  border-bottom: 1px solid #DCDCDE;
  padding: 10px 14px;
}
.wp-mail-logging-modal-row:last-of-type {
  border-bottom: 0;
}
.wp-mail-logging-modal-row-label, .wp-mail-logging-modal-row-label-message {
  color: #3C434A;
  display: block;
  float: left;
  font-weight: 600;
  width: 160px;
}
.wp-mail-logging-modal-row-label-message {
  float: none;
  padding-bottom: 10px;
}
.wp-mail-logging-modal-row-value {
  color: #50575E;
  font-weight: 400;
  display: block;
  margin-left: 160px;
}
.wp-mail-logging-modal-row-value-error {
  color: #D63637;
  font-size: 13px;
}
.wp-mail-logging-modal-row-value-message {
  margin-left: 0;
  max-height: 370px;
  overflow-x: scroll;
}
.wp-mail-logging-modal-row-error-value {
  color: #D63637;
}

.wp-mail-logging-modal-clear::before, .wp-mail-logging-modal-clear::after {
  content: " ";
  display: table;
}
.wp-mail-logging-modal-clear::after {
  clear: both;
}

.wp-mail-logging-modal-format {
  color: #787C82;
  text-decoration: none;
}
.wp-mail-logging-modal-format:focus {
  color: inherit;
  box-shadow: none;
  outline: none;
}
.wp-mail-logging-modal-format.wp-mail-logging-active-format, .wp-mail-logging-modal-format:hover, .wp-mail-logging-modal-format:focus {
  border-bottom: 4px solid #a7aaad;
  color: #2C3338;
  padding-bottom: 10.5px;
}
.wp-mail-logging-modal-format.wp-mail-logging-active-format {
  border-color: #E77C5C;
}
.wp-mail-logging-modal-format.wp-mail-logging-active-format:focus, .wp-mail-logging-modal-format.wp-mail-logging-active-format:active {
  border-color: #E77C5C;
}

.wp-mail-logging-modal-row-value-attachments a {
  font-size: 18px;
  text-decoration: none;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
